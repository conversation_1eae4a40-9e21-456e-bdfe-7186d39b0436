## Brief overview
This set of guidelines is project-specific for the Gameplan-HTMX application, focusing on coding conventions, communication style, and development workflow to ensure consistency and efficiency in development.

## Communication style
- Maintain a direct and technical tone in all interactions, avoiding conversational fluff.
- Focus on clarity and precision when asking for clarification or providing updates on task progress.

## Development workflow
- Prioritize iterative development by breaking tasks into smaller, manageable steps, confirming success at each stage before proceeding.
- Use tools like `search_files` and `list_code_definition_names` to gain context before making changes to the codebase.
- Ensure all new files or major changes are discussed in PLAN MODE before implementation in ACT MODE when applicable.

## Coding best practices
- Adhere to the existing project structure and naming conventions, such as using camelCase for Go variables and functions as seen in the codebase.
- Follow the formatting and style enforced by tools like <PERSON><PERSON>er, as indicated by the presence of a `.prettierrc` file.
- Ensure compatibility with HTMX and Go templates when modifying frontend or backend code, respecting the integration seen in files like `templates/app/` and `frontend/main.js`.

## Project context
- Recognize the use of HTMX for dynamic content loading and ensure any frontend changes align with this approach.
- Maintain consistency with the existing Go backend structure, using packages like `handlers/`, `db/`, and `utils/` for respective functionalities.
- Consider the internationalization (i18n) setup when adding or modifying user-facing content, ensuring locale files are updated accordingly.

## Other guidelines
- When adding new dependencies, ensure they are compatible with the existing tech stack (Go, HTMX, Vite for frontend bundling).
- Document any significant changes or additions in a way that aligns with existing documentation or README files for clarity to other developers.
