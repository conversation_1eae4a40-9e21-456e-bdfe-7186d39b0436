# Handling Language from Cookies

## Overview
This rule documents how to retrieve and handle language preferences from cookies in the application. The language is stored in a cookie named "lang" and can be accessed using the `GetLanguageFromCookie` function in the `cookies` package.

## Implementation
The `GetLanguageFromCookie` function is defined in `cookies/cookies.go` and handles reading the "lang" cookie, defaulting to "en" if not present. It uses Echo's context to access cookies and returns the language string.

## Usage
In handlers, use this function to get the language preference. For example, in a handler method:
```go
lang := cookies.GetLanguageFromCookie(c)
```
This ensures the language is dynamically retrieved and used for rendering localized content.

## Best Practices
- Always use `GetLanguageFromCookie` for consistency across the codebase.
- Validate the language code against a list of supported languages (e.g., "en", "fr") to handle invalid values gracefully.
- Set the "lang" cookie when users change their language preference, such as in settings pages, to persist the choice.
- Ensure that the cookie is set with appropriate security flags (e.g., HttpOnly, Secure) as defined in the `cookies` package.