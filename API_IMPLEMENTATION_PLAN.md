# CoachPad HTTP API Implementation Plan

## Overview

This document outlines the complete implementation plan for the CoachPad HTTP API system, providing programmatic access to all major resources with full CRUD operations and comprehensive authentication.

## System Design

### API Key System
- **Format**: `cpb_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` (production) / `cpb_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` (development)
- **Length**: 44 characters (8 char prefix + 36 char secret)
- **Security**: Bcrypt hashed storage, never store plaintext
- **Rate Limiting**: 1000 requests/hour, 100 requests/minute per key
- **Management**: User-friendly UI in settings with one-time key display

### Authentication Flow
1. User generates API key via settings UI
2. API key stored as bcrypt hash in database
3. Client includes key in `Authorization: Bearer <key>` or `X-API-Key` header
4. Middleware validates key and sets user context
5. Existing permission system applies unchanged

### API Structure
- **Base URL**: `/api/v1` (versioned for future compatibility)
- **Authentication**: All endpoints require API keys (no public endpoints)
- **Response Format**: Consistent JSON with standard error codes
- **Permissions**: Full user permissions (no API key scoping initially)

## Complete Resource Coverage

### Core Resources (Full CRUD)
1. **Players** (`/api/v1/players`) - Player management with custom fields
2. **Teams** (`/api/v1/teams`) - Team creation and player associations  
3. **Seasons** (`/api/v1/seasons`) - Season management and configuration
4. **Matches** (`/api/v1/seasons/{id}/matches`) - Match scoring and results
5. **Custom Columns** (`/api/v1/custom-columns`) - User-defined field schemas

### Permission-Scoped Resources
6. **Season Permissions** (`/api/v1/seasons/{id}/permissions`) - Access control
7. **Season Settings** (`/api/v1/seasons/{id}/settings`) - Season-specific config

### Notification Resources  
8. **Notifications** (`/api/v1/notifications`) - Read and mark as read
9. **Email Reminders** (`/api/v1/email-reminders`) - Scheduled email management

### System Resources
10. **User Profile** (`/api/v1/profile`) - Current user information
11. **Public Schedules** (`/api/v1/seasons/{id}/public-schedule`) - Schedule access (requires auth)

## Detailed API Endpoints

### User & Profile
- `GET /api/v1/profile` - Current user info
- `PATCH /api/v1/profile` - Update profile

### Players (User-scoped)
- `GET /api/v1/players` - List with pagination, filtering, search
- `POST /api/v1/players` - Create player
- `GET /api/v1/players/{id}` - Get player details
- `PUT /api/v1/players/{id}` - Full update
- `PATCH /api/v1/players/{id}` - Partial update  
- `DELETE /api/v1/players/{id}` - Delete player

### Teams (User-scoped)
- `GET /api/v1/teams` - List teams
- `POST /api/v1/teams` - Create team
- `GET /api/v1/teams/{id}` - Get team details
- `PUT /api/v1/teams/{id}` - Update team
- `DELETE /api/v1/teams/{id}` - Delete team
- `POST /api/v1/teams/{id}/players` - Add player to team
- `DELETE /api/v1/teams/{id}/players/{player_id}` - Remove player

### Seasons (User-scoped)
- `GET /api/v1/seasons` - List seasons
- `POST /api/v1/seasons` - Create season
- `GET /api/v1/seasons/{id}` - Get season details
- `PUT /api/v1/seasons/{id}` - Update season
- `DELETE /api/v1/seasons/{id}` - Delete season
- `POST /api/v1/seasons/{id}/generate-matches` - Generate matches

### Matches (Season-scoped, permission-controlled)
- `GET /api/v1/seasons/{season_id}/matches` - List matches
- `POST /api/v1/seasons/{season_id}/matches` - Create match
- `GET /api/v1/seasons/{season_id}/matches/{id}` - Get match
- `PUT /api/v1/seasons/{season_id}/matches/{id}` - Update match
- `DELETE /api/v1/seasons/{season_id}/matches/{id}` - Delete match

### Season Permissions (Owner/Admin only)  
- `GET /api/v1/seasons/{id}/permissions` - List permissions
- `POST /api/v1/seasons/{id}/permissions` - Grant access
- `PUT /api/v1/seasons/{id}/permissions/{user_id}` - Update level
- `DELETE /api/v1/seasons/{id}/permissions/{user_id}` - Revoke access

### Custom Columns
- `GET /api/v1/custom-columns/players` - Player field schemas
- `POST /api/v1/custom-columns/players` - Create player field
- `GET /api/v1/custom-columns/matches` - Match field schemas
- `POST /api/v1/custom-columns/matches` - Create match field
- `PUT /api/v1/custom-columns/{id}` - Update field
- `DELETE /api/v1/custom-columns/{id}` - Delete field

### Notifications
- `GET /api/v1/notifications` - List notifications
- `PATCH /api/v1/notifications/{id}` - Mark as read
- `POST /api/v1/notifications/mark-all-read` - Mark all read

### Public Access (API key required)
- `GET /api/v1/seasons/{id}/public-schedule` - Public schedule
- `GET /api/v1/seasons/{id}/public-scoreboard` - Public scoreboard

### Query Parameters
- **Pagination**: `?page=1&limit=50`
- **Sorting**: `?sort=name&order=asc`  
- **Search**: `?search=john`
- **Filtering**: `?team_id=123&active=true`

### Response Formats
**Success (List)**:
```json
{
  "data": [...],
  "meta": {"total": 150, "page": 1, "limit": 50, "pages": 3},
  "links": {"self": "...", "next": "...", "prev": null}
}
```

**Success (Single)**:
```json
{
  "data": {...}
}
```

**Error**:
```json
{
  "error": {
    "code": "VALIDATION_ERROR", 
    "message": "Invalid request data",
    "details": [{"field": "email", "message": "Email required"}]
  }
}
```

## Permission Model

### User-Scoped Resources
- **Players, Teams, Seasons**: User owns these resources
- **Access**: Full CRUD for owner only

### Season-Scoped Resources  
- **Matches, Permissions**: Existing permission hierarchy applies
- **Owner**: Full CRUD access to everything
- **Admin**: Manage players, matches, settings (not permissions)
- **Manager**: Match CRUD, read-only players/teams  
- **Viewer**: Read-only access

## Implementation Phases

### ✅ Phase 1: Foundation & Authentication (COMPLETED - 3 weeks)
**Core Infrastructure**
- ✅ Database schema for API keys + SQLC queries
- ✅ API key generation, hashing, management UI in settings
- ✅ API authentication middleware with rate limiting
- ✅ Base API framework (routing, JSON responses, error handling)
- ✅ Integration with existing permission system

**Deliverable**: Users can generate API keys and authenticate against basic endpoints

### Phase 2: Core User Resources (2-3 weeks)  
**Primary CRUD APIs**
- [ ] Players API (full CRUD with custom fields)
- [ ] Teams API (full CRUD with player associations)
- [ ] Seasons API (full CRUD with match generation)
- [ ] Custom Columns API (player/match field management)

**Deliverable**: Complete management of user-owned resources via API

### Phase 3: Season-Scoped Resources (1-2 weeks)
**Permission-Controlled APIs**
- [ ] Matches API (CRUD with season permissions)
- [ ] Season Permissions API (grant/revoke access)
- [ ] Integration testing with permission hierarchy

**Deliverable**: Full match management respecting Owner/Admin/Manager/Viewer permissions

### Phase 4: Supporting Features (1-2 weeks)
**Completeness Features**
- [ ] Notifications API (read, mark as read)
- [ ] User Profile API  
- [ ] Pagination, filtering, search for all list endpoints
- [ ] Enhanced error handling and validation

**Deliverable**: Feature parity with web application

### Phase 5: Public API & Documentation (1 week)
**Polish & Access**
- [ ] Public schedule/scoreboard endpoints (with auth)
- [ ] OpenAPI/Swagger documentation
- [ ] API usage analytics in user settings
- [ ] Performance optimization

**Deliverable**: Production-ready API with comprehensive documentation

### Phase 6: Future Enhancements
- [ ] Webhook system for real-time notifications
- [ ] API key scoping (read-only, resource-specific)
- [ ] Bulk operations and data export
- [ ] Advanced filtering and reporting

## Technical Implementation Details

### Database Schema
```sql
CREATE TABLE api_keys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    key_hash TEXT NOT NULL, -- bcrypt hash of the full key
    name VARCHAR(255) NOT NULL, -- user-friendly name
    prefix VARCHAR(16) NOT NULL, -- first 8 chars for UI identification
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL, -- optional expiration
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);
```

### Middleware Stack
```go
// API routes use this stack:
api := e.Group("/api/v1")
api.Use(middleware.APIKeyAuth()) // New API key middleware
api.Use(middleware.RequireAuth()) // Existing auth requirement  
api.Use(middleware.SeasonPermissions()) // Existing permission checks
```

### Error Codes
- **401**: Invalid/missing API key, expired key
- **403**: Insufficient permissions  
- **429**: Rate limit exceeded with `Retry-After` header
- **400**: Validation errors
- **404**: Resource not found
- **500**: Internal server error

## Files Created/Modified

### New Files (Phase 1)
- `middleware/api_auth.go` - API key authentication middleware
- `handlers/api_keys.go` - API key management handler
- `handlers/api_base.go` - Base API framework
- `utils/apikey/generator.go` - API key generation utilities
- `templates/app/settings/apiKeysSection.go` - API keys UI section
- `templates/app/settings/apiKeyForm.go` - API key forms UI

### Modified Files (Phase 1)
- `schema.sql` - Added API keys table
- `query.sql` - Added API key SQLC queries  
- `main.go` - Integrated API routes and middleware
- `templates/app/settings/appsettingsPageContent.go` - Added API keys UI

### Planned Files (Phases 2-5)
- `handlers/api_players.go` - Players API handlers
- `handlers/api_teams.go` - Teams API handlers
- `handlers/api_seasons.go` - Seasons API handlers
- `handlers/api_matches.go` - Matches API handlers
- `handlers/api_notifications.go` - Notifications API handlers
- `docs/api_documentation.md` - API documentation

## Testing Strategy

### Unit Tests
- [ ] API key generation and validation
- [ ] Authentication middleware
- [ ] Rate limiting functionality
- [ ] CRUD operations for each resource

### Integration Tests
- [ ] End-to-end API workflows
- [ ] Permission system integration
- [ ] Rate limiting under load
- [ ] Error handling scenarios

### Performance Tests
- [ ] API response times
- [ ] Rate limiting effectiveness
- [ ] Database query optimization
- [ ] Concurrent user scenarios

## Security Considerations

### API Key Security
- ✅ Bcrypt hashing for storage
- ✅ One-time display of full keys
- ✅ Secure random generation
- ✅ Rate limiting per key

### Additional Security
- [ ] Request logging and audit trails
- [ ] API usage analytics
- [ ] Suspicious activity detection
- [ ] Key rotation recommendations

## Documentation Plan

### User Documentation
- [ ] Getting started guide
- [ ] Authentication setup
- [ ] API reference for each endpoint
- [ ] Code examples in multiple languages
- [ ] Rate limiting guidelines

### Developer Documentation  
- [ ] OpenAPI/Swagger specification
- [ ] Postman collection
- [ ] SDK development guide
- [ ] Webhook integration guide

## Monitoring & Analytics

### API Metrics
- [ ] Request count per endpoint
- [ ] Response times
- [ ] Error rates
- [ ] Rate limit hits

### User Analytics
- [ ] API key usage patterns
- [ ] Most popular endpoints
- [ ] User adoption metrics
- [ ] Feature usage statistics

## Timeline Summary

**Total Estimated Timeline**: 8-10 weeks for production-ready API

- ✅ **Phase 1**: 3 weeks (COMPLETED)
- **Phase 2**: 2-3 weeks  
- **Phase 3**: 1-2 weeks
- **Phase 4**: 1-2 weeks
- **Phase 5**: 1 week
- **Phase 6**: Ongoing enhancements

## Success Criteria

### Phase Completion Criteria
1. **Phase 1**: ✅ API key system functional with basic endpoints
2. **Phase 2**: Users can manage all core resources via API
3. **Phase 3**: Season collaboration works with API
4. **Phase 4**: Feature parity with web application
5. **Phase 5**: Production-ready with documentation

### Overall Success Metrics
- [ ] 100% feature parity with web application
- [ ] Sub-200ms average response times
- [ ] 99.9% uptime for API endpoints
- [ ] Comprehensive test coverage (>80%)
- [ ] Complete API documentation
- [ ] User adoption by existing customers

---

**Current Status**: Phase 1 Complete ✅  
**Next Phase**: Phase 2 - Core User Resources  
**Ready to Proceed**: Yes 🚀