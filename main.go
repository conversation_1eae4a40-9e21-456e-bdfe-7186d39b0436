package main

import (
	"context"
	"embed"
	"fmt"
	"io"
	"io/fs"
	"log"
	"mime"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"

	"github.com/go-playground/validator/v10"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/emailreminders"
	embedFS "github.com/j-em/coachpad/embed"
	"github.com/j-em/coachpad/handlers"
	"github.com/j-em/coachpad/mailgun"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/notifications"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/pkg/logger"
	"github.com/j-em/coachpad/sse"
	"github.com/j-em/coachpad/stripe"
	"github.com/j-em/coachpad/templates"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
)

//go:embed dist public/images dist/.vite templates
var embeddedFiles embed.FS

type CustomValidator struct {
	validator *validator.Validate
}

func (cv *CustomValidator) Validate(i any) error {
	return cv.validator.Struct(i)
}

func setupLogging() (*os.File, *logger.Logger, error) {
	// Ensure tmp directory exists
	if err := os.MkdirAll("tmp", 0755); err != nil {
		return nil, nil, fmt.Errorf("failed to create tmp directory: %v", err)
	}

	// Create structured logger
	config := logger.ConfigFromEnv()
	appLogger := logger.New(config)

	// Maintain backward compatibility with existing log setup
	logFile, err := os.OpenFile("tmp/app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to open log file: %v", err)
	}

	// Set up multi-writer to log to both file and console for existing log.Printf calls
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(multiWriter)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	return logFile, appLogger, nil
}

func setupValidator(e *echo.Echo) {
	v := validator.New()
	e.Validator = &CustomValidator{validator: v}
}

func main() {
	// Set up logging to file and console
	logFile, appLogger, err := setupLogging()
	if err != nil {
		log.Fatalf("Failed to set up logging: %v", err)
	}
	defer logFile.Close()

	// Ensure correct MIME types for static assets
	_ = mime.AddExtensionType(".js", "application/javascript")
	_ = mime.AddExtensionType(".css", "text/css")
	_ = mime.AddExtensionType(".svg", "image/svg+xml")
	_ = mime.AddExtensionType(".json", "application/json")
	_ = mime.AddExtensionType(".woff2", "font/woff2")
	_ = mime.AddExtensionType(".woff", "font/woff")
	_ = mime.AddExtensionType(".ttf", "font/ttf")
	_ = mime.AddExtensionType(".map", "application/json")
	// Load .env file if available
	godotenv.Load()

	// Set the embedded filesystem in the embed package
	embedFS.SetEmbeddedFiles(embeddedFiles)

	// Get port from environment variable
	// In production: systemd sets COACHPAD_BACKEND_PORT
	// In development: .env sets it (default: 8000)
	// CRASH if not set - port must be explicitly configured
	port := os.Getenv("COACHPAD_BACKEND_PORT")
	if port == "" {
		log.Fatal("COACHPAD_BACKEND_PORT environment variable is required")
	}

	// Connect to the database
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		log.Fatal("DATABASE_URL environment variable is required")
	}

	// Create a connection pool
	dbPool, err := pgxpool.New(context.Background(), dbURL)
	if err != nil {
		log.Fatalf("Unable to create connection pool: %v\n", err)
	}
	defer dbPool.Close()

	// Create database queries
	queries := db.New(dbPool)

	// Check environment variables for critical services (Stripe, Stytch, Mailgun)
	requiredVars := map[string]string{
		"STRIPE_PUBLISHABLE_KEY":           os.Getenv("STRIPE_PUBLISHABLE_KEY"),
		"STRIPE_PRO_PLAN_PRICE_ID":         os.Getenv("STRIPE_PRO_PLAN_PRICE_ID"),
		"STRIPE_WEBHOOK_SECRET":            os.Getenv("STRIPE_WEBHOOK_SECRET"),
		"STYTCH_PROJECT_ID":                os.Getenv("STYTCH_PROJECT_ID"),
		"STYTCH_SECRET":                    os.Getenv("STYTCH_SECRET"),
		"MAILGUN_API_KEY":                  os.Getenv("MAILGUN_API_KEY"),
		"MAILGUN_DOMAIN":                   os.Getenv("MAILGUN_DOMAIN"),
		"EMAIL_CONFIRMATION_ENABLED":       os.Getenv("EMAIL_CONFIRMATION_ENABLED"),
		"MAILGUN_CONFIRMATION_TEMPLATE_EN": os.Getenv("MAILGUN_CONFIRMATION_TEMPLATE_EN"),
	}

	// Log environment variable status for debugging
	log.Printf("Environment variables status:")
	for varName, value := range requiredVars {
		if value == "" {
			log.Printf("  %s: NOT SET", varName)
		} else {
			// Show length and prefix for sensitive variables
			if strings.Contains(varName, "SECRET") || strings.Contains(varName, "KEY") {
				log.Printf("  %s: SET (length: %d, prefix: %s...)", varName, len(value), func() string {
					if len(value) > 8 {
						return value[:8]
					}
					return value
				}())
			} else {
				log.Printf("  %s: SET (%s)", varName, value)
			}
		}
	}

	stytchClient, err := stytchapi.NewClient(requiredVars["STYTCH_PROJECT_ID"], requiredVars["STYTCH_SECRET"])
	if err != nil {
		log.Printf("Stytch client initialization failed with detailed info:")
		log.Printf("  Error: %v", err)
		log.Printf("  Project ID: %s", requiredVars["STYTCH_PROJECT_ID"])
		log.Printf("  Secret length: %d characters", len(requiredVars["STYTCH_SECRET"]))
		log.Printf("  Secret prefix: %s...", func() string {
			if len(requiredVars["STYTCH_SECRET"]) > 8 {
				return requiredVars["STYTCH_SECRET"][:8]
			}
			return requiredVars["STYTCH_SECRET"]
		}())

		// Check if this looks like a URL/endpoint issue
		if strings.Contains(err.Error(), "404") {
			log.Printf("  This appears to be a 404 error - possible causes:")
			log.Printf("    - Incorrect Stytch project ID")
			log.Printf("    - Project may not exist or be accessible")
			log.Printf("    - Network connectivity issues")
			log.Printf("    - Stytch service endpoint issues")
		}

		log.Fatalf("Failed to initialize Stytch client: %v", err)
	}

	for varName, value := range requiredVars {
		if value == "" {
			panic(fmt.Sprintf("ERROR: %s environment variable is not set. This is required for the application to run.", varName))
		}
	}

	// Initialize Stripe client
	stripeClient := stripe.GetClient()

	// Initialize Mailgun client for email confirmation
	mailgunClient, err := mailgun.NewMailgunClient()
	if err != nil {
		log.Printf("Warning: Failed to initialize Mailgun client: %v. Email confirmation will be disabled.", err)
		mailgunClient = &mailgun.MailgunClient{IsEnabled: false}
	} else if mailgunClient.IsEmailConfirmationEnabled() {
		log.Println("Mailgun client initialized successfully. Email confirmation is enabled.")
	} else {
		log.Println("Mailgun client initialized, but email confirmation is disabled via configuration.")
	}

	// Initialize email reminder service
	emailReminderService, err := emailreminders.NewEmailReminderService(mailgunClient, queries)
	if err != nil {
		log.Printf("Warning: Failed to initialize email reminder service: %v. Email reminders will be disabled.", err)
	} else if emailReminderService.IsEnabled() {
		log.Println("Email reminder service initialized successfully. Email reminders are enabled.")
	} else {
		log.Println("Email reminder service initialized, but email reminders are disabled via configuration.")
	}

	// Initialize and start email scheduler
	emailScheduler, err := emailreminders.NewEmailScheduler(queries, emailReminderService)
	if err != nil {
		log.Printf("Warning: Failed to initialize email scheduler: %v", err)
	}

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start email reminder workers and scheduler
	if emailReminderService != nil && emailReminderService.IsEnabled() {
		emailReminderService.StartWorkers(ctx)

		if emailScheduler != nil {
			if err := emailScheduler.Start(ctx); err != nil {
				log.Printf("Warning: Failed to start email scheduler: %v", err)
			}
		}
	}

	// Start rate limiter cleanup routine
	if err := middleware.StartRateLimiterCleanup(ctx); err != nil {
		log.Printf("Warning: Failed to start rate limiter cleanup: %v", err)
	}

	e := echo.New()

	// Add request ID middleware for correlation
	e.Use(middleware.RequestID())

	// Simplified request logging middleware
	e.Use(middleware.CreateRequestLoggingMiddleware(appLogger))

	// Setup custom validator
	setupValidator(e)

	// Password reset handler
	passwordResetHandler := &handlers.PasswordResetHandler{
		StytchClient: stytchClient,
		Queries:      queries,
	}
	passwordResetHandler.RegisterRoutes(e)

	// Custom HTTP error handler
	e.HTTPErrorHandler = func(err error, c echo.Context) {
		if c.Response().Committed {
			return
		}

		code := http.StatusInternalServerError
		message := http.StatusText(code)
		if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
			if he.Message != nil {
				message = fmt.Sprintf("%v", he.Message)
			}
		}

		fmt.Println("Error:", err)

		// Check if the request expects JSON response
		if c.Request().Header.Get("Content-Type") == "application/json" ||
			c.Request().Header.Get("Accept") == "application/json" {
			// Return JSON error for API requests
			c.JSON(code, map[string]any{
				"error": message,
				"code":  code,
			})
			return
		}

		// Redirect to app error page for HTML requests
		redirectURL := fmt.Sprintf("/app-error?code=%d&message=%s", code, url.QueryEscape(message))

		// Check if this is an HTMX request
		if c.Request().Header.Get("HX-Request") == "true" {
			// Use HTMX redirect for HTMX requests
			c.Response().Header().Set("HX-Redirect", redirectURL)
			c.Response().WriteHeader(code)
		} else {
			// Use standard HTTP redirect for browser requests
			c.Redirect(http.StatusFound, redirectURL)
		}
	}

	// Serve /images from embedded public/images
	log.Printf("serving images from embedded filesystem")
	publicFS := embedFS.GetPublicFS()
	imagesSubFS, err := fs.Sub(publicFS, "images")
	if err != nil {
		log.Printf("error accessing embedded images: %v", err)
		panic(fmt.Sprintf("failed to access embedded images: %v", err))
	}
	e.GET("/images/*", echo.WrapHandler(http.StripPrefix("/images/", http.FileServer(http.FS(imagesSubFS)))))

	// Serve /assets from embedded dist/assets
	log.Printf("serving assets from embedded filesystem")
	distFS := embedFS.GetDistFS()
	assetsSubFS, err := fs.Sub(distFS, "assets")
	if err != nil {
		log.Printf("error accessing embedded assets: %v", err)
		panic(fmt.Sprintf("failed to access embedded assets: %v", err))
	}
	e.GET("/assets/*", echo.WrapHandler(http.StripPrefix("/assets/", http.FileServer(http.FS(assetsSubFS)))))

	languageHandler := &handlers.LanguageHandler{}

	// Language routes
	e.PUT("/lang", languageHandler.UpdateLanguage)

	// Public routes
	e.GET("/", func(c echo.Context) error {
		writer := c.Response().Writer
		html := templates.Landing(c, stytchClient, queries)
		c.Response().Status = http.StatusOK
		html.Render(writer)
		return nil
	})

	// Restricted group with JWT middleware
	// The StytchJWT middleware validates JWT tokens from either:
	// 1. The Authorization header (Bearer token)
	// 2. The session cookie
	// If valid, it adds the user ID to the context for use in route handlers
	r := e.Group("/app", middleware.StytchJWT(middleware.StytchJWTConfig{
		StytchClient: stytchClient,
	}))

	// Initialize SSE manager
	sseManager := sse.NewManager()

	// Initialize notification service
	notificationService := notifications.NewService(queries, sseManager)

	// Check if the application is running in development mode
	developmentHandler := &handlers.DevelopmentHandler{
		Queries:             queries,
		StytchClient:        stytchClient,
		StripeClient:        stripeClient,
		NotificationService: notificationService,
	}
	appEnv := os.Getenv("APP_ENV")
	if appEnv == "development" {
		developmentHandler.RegisterRoutes(e)
	}

	// Initialize permission middleware
	permissionMiddleware := middleware.NewPermissionMiddleware(queries)

	// Initialize limits service and middleware
	limitsService := limits.NewService(queries)
	limitsMiddleware := middleware.NewLimitsMiddleware(limitsService)

	// Initialize handlers
	playersHandler := &handlers.PlayersHandler{
		Queries:          queries,
		LimitsMiddleware: limitsMiddleware,
		LimitsService:    limitsService,
	}
	teamsHandler := &handlers.TeamsHandler{
		Queries:          queries,
		LimitsMiddleware: limitsMiddleware,
		LimitsService:    limitsService,
	}
	spendingHandler := &handlers.SpendingHandler{
		Queries:             queries,
		NotificationService: notificationService,
	}
	seasonsHandler := handlers.NewSeasonsHandler(queries, emailReminderService, permissionMiddleware, limitsMiddleware, limitsService)

	// Configure session duration from environment variable with default of 7 days (10080 minutes)
	sessionDurationMinutes := int32(10080) // Default: 7 days
	if sessionDurationStr := os.Getenv("STYTCH_SESSION_DURATION_MINUTES"); sessionDurationStr != "" {
		if parsed, err := strconv.Atoi(sessionDurationStr); err != nil {
			log.Printf("Warning: Invalid STYTCH_SESSION_DURATION_MINUTES value '%s', using default of %d minutes (%d days)",
				sessionDurationStr, sessionDurationMinutes, sessionDurationMinutes/1440)
		} else if parsed <= 0 || parsed > 527040 { // Validate: 1 minute to 1 year
			log.Printf("Warning: STYTCH_SESSION_DURATION_MINUTES value %d is out of range (1-527040), using default of %d minutes (%d days)",
				parsed, sessionDurationMinutes, sessionDurationMinutes/1440)
		} else {
			sessionDurationMinutes = int32(parsed)
		}
	}
	log.Printf("Stytch session duration configured: %d minutes (%d days)", sessionDurationMinutes, sessionDurationMinutes/1440)

	authHandler := &handlers.AuthHandler{
		StytchClient:  stytchClient,
		Queries:       queries,
		MailgunClient: mailgunClient,
		StripeClient:  stripeClient,
	}
	permissionsHandler := handlers.NewPermissionsHandler(queries, permissionMiddleware)

	// Initialize email verification handler
	emailVerificationHandler := &handlers.EmailVerificationHandler{
		StytchClient: stytchClient,
		Queries:      queries,
	}

	settingsHandler := &handlers.SettingsHandler{
		Queries:                  queries,
		StripeClient:             stripeClient,
		StytchClient:             stytchClient,
		EmailVerificationHandler: emailVerificationHandler,
		SSEManager:               sseManager,
		NotificationService:      notificationService,
		LimitsService:            limitsService,
	}
	appHandler := &handlers.AppHandler{
		Queries:             queries,
		SSEManager:          sseManager,
		NotificationService: notificationService,
	}

	// Initialize notifications handler
	notificationsHandler := handlers.NewNotificationsHandler(queries, notificationService)

	// Set loggers for handlers that need them
	permissionsHandler.SetLogger(appLogger)

	// Register HTML routes using handlers
	playersHandler.RegisterRoutes(r)
	teamsHandler.RegisterRoutes(r)
	spendingHandler.RegisterRoutes(r)
	seasonsHandler.RegisterRoutes(r)
	permissionsHandler.RegisterRoutes(r)
	appHandler.RegisterRoutes(r)
	notificationsHandler.RegisterRoutes(r)
	authHandler.RegisterRoutes(e)

	// Register development routes if in development mode
	if appEnv == "development" {
		developmentHandler.RegisterAppRoutes(r)
	}

	settingsHandler.RegisterRoutes(r)
	languageHandler.RegisterRoutes(e)

	// Register email verification routes
	emailVerificationHandler.RegisterRoutes(e, r)

	// Register public routes (no authentication required)
	publicHandler := &handlers.PublicHandler{
		Queries: queries,
	}
	publicHandler.RegisterRoutes(e)

	// Register changelog routes
	changelogHandler := &handlers.ChangelogHandler{}
	changelogHandler.RegisterRoutes(r)

	// Register feedback routes
	feedbackHandler := &handlers.FeedbackHandler{
		Queries: queries,
	}
	feedbackHandler.RegisterRoutes(r)

	// Register help routes
	helpHandler := &handlers.HelpHandler{}
	helpHandler.RegisterRoutes(r)

	// Register app error handler
	appErrorHandler := &handlers.AppErrorHandler{}
	appErrorHandler.RegisterRoutes(e)

	// Register MatchesHandler routes
	matchesHandler := &handlers.MatchesHandler{
		Queries:          queries,
		EmailService:     emailReminderService,
		LimitsMiddleware: limitsMiddleware,
	}
	matchesHandler.RegisterRoutes(r)

	// JSON Data API routes (protected by JWT)
	apiGroup := e.Group("/api", middleware.StytchJWT(middleware.StytchJWTConfig{
		StytchClient: stytchClient,
	}))
	// Register StripeHandler routes
	stripeHandler := &handlers.StripeHandler{
		Queries:      queries,
		StripeClient: stripeClient,
		SSEManager:   sseManager,
	}
	stripeHandler.RegisterRoutes(apiGroup)
	stripeHandler.RegisterWebhook(e)
	playersHandler.RegisterAPIRoutes(apiGroup)

	// API v1 routes (protected by API key authentication)
	apiV1Group := e.Group("/api/v1", middleware.APIKeyAuth(middleware.APIKeyConfig{
		Queries: queries,
		Logger:  appLogger,
	}))

	// Initialize API handlers
	apiKeyHandler := &handlers.APIKeyHandler{
		Queries: queries,
	}
	apiBaseHandler := &handlers.APIBaseHandler{
		Queries: queries,
	}

	// Register API v1 routes
	apiBaseHandler.RegisterRoutes(apiV1Group)

	// Register API key management routes in the regular authenticated group
	apiKeyHandler.RegisterRoutes(r)

	// Set up graceful shutdown
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		<-c

		log.Println("Shutting down email reminder service...")
		if emailReminderService != nil {
			emailReminderService.StopWorkers()
		}
		if emailScheduler != nil {
			emailScheduler.Stop()
		}

		log.Println("Shutting down rate limiter cleanup...")
		middleware.StopRateLimiterCleanup()

		cancel()

		log.Println("Shutting down server...")
		if err := e.Shutdown(context.Background()); err != nil {
			log.Fatal("Server forced to shutdown:", err)
		}
	}()

	log.Printf("Starting server on :%s", port)
	if err := e.Start(":" + port); err != nil && err != http.ErrServerClosed {
		log.Fatal("Server failed to start:", err)
	}
}
