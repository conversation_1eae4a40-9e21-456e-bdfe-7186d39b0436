package notifications

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/sse"
	"github.com/jackc/pgx/v5/pgtype"
)

// Service handles notification creation and delivery
type Service struct {
	queries    *db.Queries
	sseManager *sse.Manager
}

// NotificationData represents additional contextual data for notifications
type NotificationData struct {
	MatchID      *int32 `json:"match_id,omitempty"`
	SeasonID     *int32 `json:"season_id,omitempty"`
	PlayerID     *int32 `json:"player_id,omitempty"`
	OriginalDate string `json:"original_date,omitempty"`
	NewDate      string `json:"new_date,omitempty"`
	Score1       *int32 `json:"score1,omitempty"`
	Score2       *int32 `json:"score2,omitempty"`
	WinnerName   string `json:"winner_name,omitempty"`
}

// NotificationFilters represents filters for querying notifications
type NotificationFilters struct {
	UserID int32
	Type   string
	Limit  int32
	Offset int32
}

// NotificationPreferences represents user notification preferences
type NotificationPreferences struct {
	InAppEnabled    bool
	MatchUpdates    bool
	ScheduleChanges bool
	Results         bool
	Announcements   bool
}

// NewService creates a new notification service
func NewService(queries *db.Queries, sseManager *sse.Manager) *Service {
	return &Service{
		queries:    queries,
		sseManager: sseManager,
	}
}

// CreateNotification creates a new notification and sends it via SSE
func (s *Service) CreateNotification(ctx context.Context, userID int32, notifType, title, message string, data *NotificationData) (*db.Notification, error) {
	// Check if user has notifications enabled for this type
	prefs, err := s.GetUserPreferences(ctx, userID)
	if err != nil {
		// If no preferences found, create default ones
		defaultPrefs := NotificationPreferences{
			InAppEnabled:    true,
			MatchUpdates:    true,
			ScheduleChanges: true,
			Results:         true,
			Announcements:   true,
		}
		if _, err := s.UpdateUserPreferences(ctx, userID, defaultPrefs); err != nil {
			log.Printf("Failed to create default notification preferences for user %d: %v", userID, err)
		}
	} else if !s.isNotificationTypeEnabled(prefs, notifType) {
		// User has disabled this notification type
		return nil, nil
	}

	// Convert data to JSONB
	var dataJSON []byte
	if data != nil {
		dataJSON, err = json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal notification data: %w", err)
		}
	}

	// Prepare database parameters
	params := db.CreateNotificationParams{
		UserID:  userID,
		Type:    notifType,
		Title:   title,
		Message: message,
		Data:    dataJSON,
	}

	// Set optional references
	if data != nil {
		if data.MatchID != nil {
			params.MatchID = pgtype.Int4{Int32: *data.MatchID, Valid: true}
		}
		if data.SeasonID != nil {
			params.SeasonID = pgtype.Int4{Int32: *data.SeasonID, Valid: true}
		}
		if data.PlayerID != nil {
			params.PlayerID = pgtype.Int4{Int32: *data.PlayerID, Valid: true}
		}
	}

	// Create notification in database
	notification, err := s.queries.CreateNotification(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// Send real-time notification via SSE
	if err := s.SendRealTimeNotification(userID, &notification); err != nil {
		log.Printf("Failed to send real-time notification %d: %v", notification.ID, err)
		// Don't return error here - notification was created successfully
	}

	return &notification, nil
}

// SendRealTimeNotification sends a notification via SSE
func (s *Service) SendRealTimeNotification(userID int32, notification *db.Notification) error {
	// Parse notification data
	var data NotificationData
	if notification.Data != nil {
		if err := json.Unmarshal(notification.Data, &data); err != nil {
			log.Printf("Failed to unmarshal notification data: %v", err)
		}
	}

	// Send notification event
	notificationEvent := sse.Event{
		Event: "notification",
		Data: map[string]interface{}{
			"id":         notification.ID,
			"type":       notification.Type,
			"title":      notification.Title,
			"message":    notification.Message,
			"created_at": notification.CreatedAt,
			"data":       data,
		},
	}

	s.sseManager.SendToUser(userID, notificationEvent)

	// Send updated unread count
	count, err := s.GetUnreadCount(context.Background(), userID)
	if err != nil {
		log.Printf("Failed to get unread count for user %d: %v", userID, err)
	} else {
		countEvent := sse.Event{
			Event: "notification_count",
			Data: map[string]interface{}{
				"unread_count": count,
			},
		}
		s.sseManager.SendToUser(userID, countEvent)
	}

	return nil
}

// GetUserNotifications retrieves notifications for a user with pagination
func (s *Service) GetUserNotifications(ctx context.Context, filters NotificationFilters) ([]db.Notification, error) {
	return s.queries.GetUserNotifications(ctx, db.GetUserNotificationsParams{
		UserID: filters.UserID,
		Limit:  filters.Limit,
		Offset: filters.Offset,
	})
}

// GetNotificationsByType retrieves notifications of a specific type
func (s *Service) GetNotificationsByType(ctx context.Context, filters NotificationFilters) ([]db.Notification, error) {
	return s.queries.GetNotificationsByType(ctx, db.GetNotificationsByTypeParams{
		UserID: filters.UserID,
		Type:   filters.Type,
		Limit:  filters.Limit,
		Offset: filters.Offset,
	})
}

// MarkAsRead marks a notification as read
func (s *Service) MarkAsRead(ctx context.Context, notificationID, userID int32) error {
	return s.queries.MarkNotificationAsRead(ctx, db.MarkNotificationAsReadParams{
		ID:     notificationID,
		UserID: userID,
	})
}

// MarkAllAsRead marks all notifications as read for a user
func (s *Service) MarkAllAsRead(ctx context.Context, userID int32) error {
	return s.queries.MarkAllNotificationsAsRead(ctx, userID)
}

// GetUnreadCount returns the number of unread notifications for a user
func (s *Service) GetUnreadCount(ctx context.Context, userID int32) (int64, error) {
	return s.queries.GetUnreadNotificationCount(ctx, userID)
}

// GetUserPreferences retrieves user notification preferences
func (s *Service) GetUserPreferences(ctx context.Context, userID int32) (*db.NotificationPreference, error) {
	prefs, err := s.queries.GetNotificationPreferences(ctx, userID)
	if err != nil {
		return nil, err
	}
	return &prefs, nil
}

// UpdateUserPreferences updates user notification preferences
func (s *Service) UpdateUserPreferences(ctx context.Context, userID int32, prefs NotificationPreferences) (*db.NotificationPreference, error) {
	result, err := s.queries.UpsertNotificationPreferences(ctx, db.UpsertNotificationPreferencesParams{
		UserID:          userID,
		InAppEnabled:    pgtype.Bool{Bool: prefs.InAppEnabled, Valid: true},
		MatchUpdates:    pgtype.Bool{Bool: prefs.MatchUpdates, Valid: true},
		ScheduleChanges: pgtype.Bool{Bool: prefs.ScheduleChanges, Valid: true},
		Results:         pgtype.Bool{Bool: prefs.Results, Valid: true},
		Announcements:   pgtype.Bool{Bool: prefs.Announcements, Valid: true},
	})
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// isNotificationTypeEnabled checks if a notification type is enabled for a user
func (s *Service) isNotificationTypeEnabled(prefs *db.NotificationPreference, notifType string) bool {
	if !prefs.InAppEnabled.Valid || !prefs.InAppEnabled.Bool {
		return false
	}

	switch notifType {
	case "match_update":
		return prefs.MatchUpdates.Valid && prefs.MatchUpdates.Bool
	case "schedule_change":
		return prefs.ScheduleChanges.Valid && prefs.ScheduleChanges.Bool
	case "result":
		return prefs.Results.Valid && prefs.Results.Bool
	case "announcement":
		return prefs.Announcements.Valid && prefs.Announcements.Bool
	default:
		return true
	}
}

// CreateMatchUpdateNotification creates a notification for match updates
func (s *Service) CreateMatchUpdateNotification(ctx context.Context, userID int32, match *db.Match, updateType string) error {
	var title, message string
	data := &NotificationData{
		MatchID: &match.ID,
	}

	if match.SeasonID.Valid {
		data.SeasonID = &match.SeasonID.Int32
	}

	switch updateType {
	case "score_update":
		title = "Match Score Updated"
		message = "A match score has been updated"
		if match.PlayerId1Points.Valid && match.PlayerId2Points.Valid {
			data.Score1 = &match.PlayerId1Points.Int32
			data.Score2 = &match.PlayerId2Points.Int32
		}
	case "date_change":
		title = "Match Date Changed"
		message = "A match date has been changed"
		if match.MatchDate.Valid {
			data.NewDate = match.MatchDate.Time.Format("2006-01-02")
		}
	case "player_swap":
		title = "Match Player Changed"
		message = "A player in your match has been changed"
	default:
		title = "Match Updated"
		message = "One of your matches has been updated"
	}

	_, err := s.CreateNotification(ctx, userID, "match_update", title, message, data)
	return err
}

// CreateScheduleChangeNotification creates a notification for schedule changes
func (s *Service) CreateScheduleChangeNotification(ctx context.Context, userID int32, seasonID int32, changeType string) error {
	title := "Schedule Updated"
	message := "Your schedule has been updated"

	data := &NotificationData{
		SeasonID: &seasonID,
	}

	switch changeType {
	case "new_matches":
		message = "New matches have been added to your schedule"
	case "matches_rescheduled":
		message = "Some matches in your schedule have been rescheduled"
	}

	_, err := s.CreateNotification(ctx, userID, "schedule_change", title, message, data)
	return err
}

// CreateResultNotification creates a notification for match results
func (s *Service) CreateResultNotification(ctx context.Context, userID int32, match *db.Match, winnerName string) error {
	title := "Match Result"
	message := fmt.Sprintf("Match completed. Winner: %s", winnerName)

	data := &NotificationData{
		MatchID:    &match.ID,
		WinnerName: winnerName,
	}

	if match.SeasonID.Valid {
		data.SeasonID = &match.SeasonID.Int32
	}

	if match.PlayerId1Points.Valid && match.PlayerId2Points.Valid {
		data.Score1 = &match.PlayerId1Points.Int32
		data.Score2 = &match.PlayerId2Points.Int32
	}

	_, err := s.CreateNotification(ctx, userID, "result", title, message, data)
	return err
}

// CreateAnnouncementNotification creates a notification for announcements
func (s *Service) CreateAnnouncementNotification(ctx context.Context, userID int32, title, message string, seasonID *int32) error {
	data := &NotificationData{}
	if seasonID != nil {
		data.SeasonID = seasonID
	}

	_, err := s.CreateNotification(ctx, userID, "announcement", title, message, data)
	return err
}
