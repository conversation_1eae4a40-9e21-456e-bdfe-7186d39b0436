#!/bin/bash

# CoachPad Database Backup Script
# This script creates a database backup with optional compression

set -e  # Exit on any error

# Configuration
BACKUP_BASE_DIR="${BACKUP_DIR:-./backups}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="coachpad_db_backup_${TIMESTAMP}"

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-postgres}"
DB_USER="${DB_USER:-coachpad}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Function to check if command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error "$1 command not found. Please install it."
        exit 1
    fi
}

# Usage function
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --compress         Compress the backup file with gzip"
    echo "  --backup-dir PATH  Backup directory (default: ./backups)"
    echo "  --help             Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  DATABASE_URL       Complete database connection string (preferred)"
    echo "  DB_HOST           Database host (default: localhost)"
    echo "  DB_PORT           Database port (default: 5432)"
    echo "  DB_NAME           Database name (default: postgres)"
    echo "  DB_USER           Database user (default: coachpad)"
    exit 1
}

# Parse command line arguments
COMPRESS=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --compress)
            COMPRESS=true
            shift
            ;;
        --backup-dir)
            BACKUP_BASE_DIR="$2"
            shift 2
            ;;
        --help)
            usage
            ;;
        -*)
            error "Unknown option: $1"
            usage
            ;;
        *)
            error "Unexpected argument: $1"
            usage
            ;;
    esac
done

# Check required commands
check_command pg_dump

log "Starting database backup for CoachPad..."

# Create backup directory
mkdir -p "$BACKUP_BASE_DIR"

# Set backup file name
if [ "$COMPRESS" = true ]; then
    BACKUP_FILE="${BACKUP_BASE_DIR}/${BACKUP_NAME}.sql.gz"
else
    BACKUP_FILE="${BACKUP_BASE_DIR}/${BACKUP_NAME}.sql"
fi

log "Backup will be saved to: ${BACKUP_FILE}"

# Perform database backup
log "Creating database dump..."
if [ -n "$DATABASE_URL" ]; then
    # Use DATABASE_URL if available
    if [ "$COMPRESS" = true ]; then
        if ! pg_dump "$DATABASE_URL" \
            --clean \
            --create \
            --if-exists \
            --format=plain \
            --no-owner \
            --no-privileges 2>/dev/null | gzip > "$BACKUP_FILE"; then
            error "Database backup failed using DATABASE_URL"
            rm -f "$BACKUP_FILE"
            exit 1
        fi
    else
        if ! pg_dump "$DATABASE_URL" \
            --clean \
            --create \
            --if-exists \
            --format=plain \
            --no-owner \
            --no-privileges \
            > "$BACKUP_FILE" 2>/dev/null; then
            error "Database backup failed using DATABASE_URL"
            rm -f "$BACKUP_FILE"
            exit 1
        fi
    fi
else
    # Use individual connection parameters
    if [ "$COMPRESS" = true ]; then
        if ! pg_dump \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            --clean \
            --create \
            --if-exists \
            --format=plain \
            --no-owner \
            --no-privileges \
            "$DB_NAME" 2>/dev/null | gzip > "$BACKUP_FILE"; then
            error "Database backup failed using connection parameters"
            rm -f "$BACKUP_FILE"
            exit 1
        fi
    else
        if ! pg_dump \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            --clean \
            --create \
            --if-exists \
            --format=plain \
            --no-owner \
            --no-privileges \
            "$DB_NAME" > "$BACKUP_FILE" 2>/dev/null; then
            error "Database backup failed using connection parameters"
            rm -f "$BACKUP_FILE"
            exit 1
        fi
    fi
fi

if [ $? -eq 0 ]; then
    # Verify backup file exists and is not empty
    if [ ! -f "$BACKUP_FILE" ]; then
        error "Backup file was not created: $BACKUP_FILE"
        exit 1
    fi
    
    if [ ! -s "$BACKUP_FILE" ]; then
        error "Backup file is empty: $BACKUP_FILE"
        rm -f "$BACKUP_FILE"
        exit 1
    fi
    
    BACKUP_SIZE=$(du -sh "$BACKUP_FILE" | cut -f1)
    log "Database backup completed successfully!"
    log "Backup saved to: $BACKUP_FILE"
    log "Backup size: $BACKUP_SIZE"
    
    # Clean up old backups (keep last 14 days by default)
    RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-14}"
    log "Cleaning up backups older than ${RETENTION_DAYS} days..."
    if ! find "$BACKUP_BASE_DIR" -name "coachpad_db_backup_*.sql*" -mtime +${RETENTION_DAYS} -delete 2>/dev/null; then
        warn "Failed to clean up old backups (this is not critical)"
    fi
    
    echo
    echo "To restore from this backup, use:"
    echo "  ./deployment/restore-db.sh $BACKUP_FILE"
else
    error "Database backup failed!"
    rm -f "$BACKUP_FILE"
    exit 1
fi