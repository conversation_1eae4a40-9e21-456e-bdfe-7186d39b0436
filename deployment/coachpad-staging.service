[Unit]
Description=CoachPad Application (Staging)
Wants=postgresql.service
After=network.target postgresql.service

[Service]
Type=simple
User=coachpad
Group=coachpad
WorkingDirectory=/opt/coachpad-staging
ExecStart=/opt/coachpad-staging/coachpad
Restart=always
RestartSec=5
# Import environment variables from .env.staging file
EnvironmentFile=/opt/coachpad-staging/.env.staging
# Override port setting as required by systemd service
Environment=COACHPAD_BACKEND_PORT=9000

# Security and isolation options
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ReadWritePaths=/opt/coachpad-staging/tmp /opt/coachpad-staging/logs
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=true

# Network restrictions
# Disabled to allow API calls to Stytch, Stripe, etc.
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
# IPAddressDeny=any
# IPAddressAllow=localhost
# IPAddressAllow=10.0.0.0/8
# IPAddressAllow=**********/12
# IPAddressAllow=***********/16

# Capability restrictions
CapabilityBoundingSet=
AmbientCapabilities=

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
TasksMax=4096

# Additional hardening
SystemCallArchitectures=native
SystemCallFilter=@system-service
SystemCallFilter=~@debug @mount @cpu-emulation @obsolete @privileged @reboot @swap @raw-io

[Install]
WantedBy=multi-user.target
