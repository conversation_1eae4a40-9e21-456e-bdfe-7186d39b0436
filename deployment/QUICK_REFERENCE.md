# CoachPad A/B Deployment - Quick Reference

## Architecture at a Glance

```
📁 Environments:
├── 🧪 Staging: /opt/coachpad-staging (port 9000) → staging.coachpad.ca
├── 🟦 Blue:    /opt/coachpad-blue    (port 8080) → coachpad.ca
└── 🟩 Green:   /opt/coachpad-green   (port 8081) → coachpad.ca
```

## Essential Commands

### Deploy to Staging
```bash
./deploy_prod.sh app.tar.gz --host server.com --environment staging
```

### Deploy to Production
```bash
# Deploys to inactive slot automatically
./deploy_prod.sh app.tar.gz --host server.com --environment production
```

### Switch Traffic
```bash
# Zero-downtime switch
./deploy_prod.sh --switch-to green --host server.com
./deploy_prod.sh --switch-to blue --host server.com
```

### Check Status
```bash
./deployment/deployment-status.sh --host server.com
```

### Migration from Old Setup
```bash
./deployment/migrate-to-ab.sh --host server.com
```

## Workflow Examples

### Safe Production Deployment
```bash
# 1. Test on staging
./deploy_prod.sh v1.2.0.tar.gz --environment staging --host server.com
# Test at https://staging.coachpad.ca

# 2. Deploy to inactive production slot  
./deploy_prod.sh v1.2.0.tar.gz --environment production --host server.com
# Test at http://server.com:8081 (if green slot)

# 3. Switch production traffic
./deploy_prod.sh --switch-to green --host server.com
# Live at https://coachpad.ca

# 4. Rollback if needed
./deploy_prod.sh --switch-to blue --host server.com
```

### Emergency Rollback
```bash
# Instant rollback (< 5 seconds)
./deploy_prod.sh --switch-to blue --host server.com
```

## Environment URLs

- **Production**: https://coachpad.ca (switches between blue/green)
- **Staging**: https://staging.coachpad.ca (always port 9000)
- **Direct Testing**: 
  - Blue: http://server.com:8080
  - Green: http://server.com:8081

## Quick Troubleshooting

### Check Services
```bash
systemctl status coachpad-blue coachpad-green coachpad-staging
```

### Check Active Slot
```bash
grep proxy_pass /etc/nginx/sites-available/coachpad-production
```

### View Logs
```bash
tail -f /opt/coachpad-blue/tmp/app.log
journalctl -u coachpad-blue.service -f
```

### Test Health
```bash
curl http://localhost:8080/health  # Blue
curl http://localhost:8081/health  # Green  
curl http://localhost:9000/health  # Staging
```

## Directory Structure Quick Reference

```
/opt/
├── coachpad-staging/     # Staging (port 9000)
├── coachpad-blue/        # Production Blue (port 8080) 
└── coachpad-green/       # Production Green (port 8081)

/etc/nginx/sites-available/
├── coachpad-production   # Production A/B config
└── coachpad-staging      # Staging config

/etc/systemd/system/
├── coachpad-blue.service     # Blue slot service
├── coachpad-green.service    # Green slot service
└── coachpad-staging.service  # Staging service
```

## Key Benefits

✅ **Zero-downtime deployments** - nginx reload switches instantly  
✅ **Safe testing** - test before switching traffic  
✅ **Easy rollbacks** - switch back in seconds  
✅ **Staging isolation** - separate database and environment  
✅ **Minimal changes** - builds on existing deployment patterns
