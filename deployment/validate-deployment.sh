#!/bin/bash

# CoachPad A/B Deployment Validation Script
# Tests all deployment environments and validates the setup

set -e

# Configuration
REMOTE_USER="${DEPLOY_USER:-root}"
REMOTE_HOST="${DEPLOY_HOST}"
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-coachpad.ca}"
STAGING_DOMAIN="${STAGING_DOMAIN:-staging.coachpad.ca}"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        --production-domain)
            PRODUCTION_DOMAIN="$2"
            shift 2
            ;;
        --staging-domain)
            STAGING_DOMAIN="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Validates A/B deployment setup and connectivity"
            echo ""
            echo "OPTIONS:"
            echo "  --host HOST               Remote host to validate"
            echo "  --production-domain DOMAIN Production domain (default: coachpad.ca)"
            echo "  --staging-domain DOMAIN   Staging domain (default: staging.coachpad.ca)"
            echo "  --help                    Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  DEPLOY_HOST - Default remote host"
            echo "  DEPLOY_USER - Remote user (default: root)"
            exit 0
            ;;
        *)
            shift
            ;;
    esac
done

# Validate arguments
if [ -z "$REMOTE_HOST" ]; then
    echo "Error: Remote host is required"
    echo "Use --host HOST or set DEPLOY_HOST environment variable"
    exit 1
fi

echo "=== CoachPad A/B Deployment Validation ==="
echo "Host: $REMOTE_HOST"
echo "Production: $PRODUCTION_DOMAIN"
echo "Staging: $STAGING_DOMAIN"
echo ""

# Test server connectivity and services
echo "🔍 Testing server connectivity and services..."

ssh "$REMOTE_USER@$REMOTE_HOST" "
    ERRORS=0
    
    # Test nginx status
    echo '1. Nginx service:'
    if systemctl is-active --quiet nginx; then
        echo '   ✓ Running'
    else
        echo '   ❌ Not running'
        ERRORS=\$((ERRORS + 1))
    fi
    
    # Test nginx config
    echo '2. Nginx configuration:'
    if nginx -t >/dev/null 2>&1; then
        echo '   ✓ Valid'
    else
        echo '   ❌ Invalid configuration'
        ERRORS=\$((ERRORS + 1))
    fi
    
    # Test PostgreSQL
    echo '3. PostgreSQL service:'
    if systemctl is-active --quiet postgresql; then
        echo '   ✓ Running'
    else
        echo '   ❌ Not running'
        ERRORS=\$((ERRORS + 1))
    fi
    
    # Test production slots
    echo '4. Production slots:'
    for slot in blue green; do
        echo \"   \$slot slot:\"
        if [ -d \"/opt/coachpad-\$slot\" ]; then
            echo \"     ✓ Directory exists\"
            if systemctl is-active --quiet \"coachpad-\$slot.service\"; then
                echo \"     ✓ Service running\"
            else
                echo \"     ⚠️  Service not running\"
            fi
        else
            echo \"     - Not deployed\"
        fi
    done
    
    # Test staging
    echo '5. Staging environment:'
    if [ -d '/opt/coachpad-staging' ]; then
        echo '   ✓ Directory exists'
        if systemctl is-active --quiet coachpad-staging.service; then
            echo '   ✓ Service running'
        else
            echo '   ⚠️  Service not running'
        fi
    else
        echo '   - Not deployed'
    fi
    
    echo \"\"
    echo \"Summary: \$ERRORS critical errors found\"
    exit \$ERRORS
"

SERVER_STATUS=$?

if [ $SERVER_STATUS -ne 0 ]; then
    echo "❌ Server validation failed. Fix critical errors before continuing."
    exit 1
fi

echo "✅ Server validation passed"
echo ""

# Test health endpoints
echo "🩺 Testing health endpoints..."

test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    echo -n "   $name: "
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        if [ "$response" = "$expected_status" ]; then
            echo "✓ $response"
        else
            echo "❌ $response (expected $expected_status)"
            return 1
        fi
    else
        echo "⚠️  curl not available"
        return 1
    fi
}

# Test direct port access
echo "Direct port access:"
test_endpoint "Blue slot (8080)" "http://$REMOTE_HOST:8080/health" || true
test_endpoint "Green slot (8081)" "http://$REMOTE_HOST:8081/health" || true  
test_endpoint "Staging (9000)" "http://$REMOTE_HOST:9000/health" || true

echo ""

# Test domain access
echo "Domain access:"
test_endpoint "Production HTTPS" "https://$PRODUCTION_DOMAIN/health" || true
test_endpoint "Staging HTTPS" "https://$STAGING_DOMAIN/health" || true

echo ""

# Test active slot detection
echo "🎯 Testing active slot detection..."

ssh "$REMOTE_USER@$REMOTE_HOST" "
    if [ -f /etc/nginx/sites-available/coachpad-production ]; then
        ACTIVE_PORT=\$(grep -o 'proxy_pass http://localhost:[0-9]*' /etc/nginx/sites-available/coachpad-production | grep -o '[0-9]*' || echo 'unknown')
        
        if [ \"\$ACTIVE_PORT\" = '8080' ]; then
            echo '   ✓ Active slot: BLUE (port 8080)'
        elif [ \"\$ACTIVE_PORT\" = '8081' ]; then
            echo '   ✓ Active slot: GREEN (port 8081)'
        else
            echo '   ⚠️  Active slot: UNKNOWN (port \$ACTIVE_PORT)'
        fi
    else
        echo '   ❌ Production nginx config not found'
    fi
"

echo ""

# Summary and recommendations
echo "🎯 Validation Summary"
echo ""
echo "✅ Basic connectivity and services checked"
echo "✅ Health endpoints tested"  
echo "✅ Active slot configuration verified"
echo ""
echo "Next steps:"
echo "1. Deploy to staging: ./deploy_prod.sh app.tar.gz --environment staging --host $REMOTE_HOST"
echo "2. Deploy to production: ./deploy_prod.sh app.tar.gz --environment production --host $REMOTE_HOST"
echo "3. Switch traffic: ./deploy_prod.sh --switch-to [blue|green] --host $REMOTE_HOST"
echo "4. Monitor: ./deployment/deployment-status.sh --host $REMOTE_HOST"
