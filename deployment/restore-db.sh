#!/bin/bash

# CoachPad Database Restore Script
# This script restores a database backup (plain SQL or gzipped)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Function to check if command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error "$1 command not found. Please install it."
        exit 1
    fi
}

# Function to prompt for confirmation
confirm() {
    read -p "$1 (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
}

# Usage function
usage() {
    echo "Usage: $0 <backup_file> [options]"
    echo ""
    echo "Arguments:"
    echo "  backup_file        Path to SQL backup file (.sql or .sql.gz)"
    echo ""
    echo "Options:"
    echo "  --create-db        Create database if it doesn't exist"
    echo "  --drop-existing    Drop existing database before restore"
    echo "  --force            Skip confirmation prompts"
    echo "  --help             Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  DATABASE_URL       Complete database connection string (preferred)"
    echo "  DB_HOST           Database host (default: localhost)"
    echo "  DB_PORT           Database port (default: 5432)"
    echo "  DB_NAME           Database name (default: postgres)"
    echo "  DB_USER           Database user (default: coachpad)"
    echo ""
    echo "Examples:"
    echo "  $0 coachpad_db_backup_20240615_143022.sql"
    echo "  $0 backup.sql.gz --drop-existing --force"
    exit 1
}

# Default values
BACKUP_FILE=""
CREATE_DB=false
DROP_EXISTING=false
FORCE=false

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-postgres}"
DB_USER="${DB_USER:-coachpad}"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --create-db)
            CREATE_DB=true
            shift
            ;;
        --drop-existing)
            DROP_EXISTING=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            usage
            ;;
        -*)
            error "Unknown option: $1"
            usage
            ;;
        *)
            if [ -z "$BACKUP_FILE" ]; then
                BACKUP_FILE="$1"
            else
                error "Multiple backup files specified"
                usage
            fi
            shift
            ;;
    esac
done

# Validate arguments
if [ -z "$BACKUP_FILE" ]; then
    error "No backup file specified"
    usage
fi

if [ ! -f "$BACKUP_FILE" ]; then
    error "Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Check required commands
check_command psql
if [[ "$BACKUP_FILE" == *.gz ]]; then
    check_command gunzip
fi

log "Starting database restoration from backup: $BACKUP_FILE"

# Display database connection info
if [ -n "$DATABASE_URL" ]; then
    info "Using DATABASE_URL for connection"
else
    info "Database connection: ${DB_USER}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
fi

# Confirmation prompt
if [ "$FORCE" = false ]; then
    warn "This will restore the database and may overwrite existing data."
    if [ "$DROP_EXISTING" = true ]; then
        warn "The existing database will be DROPPED and recreated."
    fi
    warn "Make sure you have backed up any important data before proceeding."
    confirm "Do you want to continue with the restoration?"
fi

# Function to execute SQL commands
execute_sql() {
    local sql_command="$1"
    if [ -n "$DATABASE_URL" ]; then
        if ! echo "$sql_command" | psql "$DATABASE_URL" 2>/dev/null; then
            error "Failed to execute SQL command using DATABASE_URL"
            return 1
        fi
    else
        if ! echo "$sql_command" | psql \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            -d postgres 2>/dev/null; then  # Connect to default postgres db for admin operations
            error "Failed to execute SQL command using connection parameters"
            return 1
        fi
    fi
}

# Function to restore database
restore_database() {
    local backup_file="$1"
    if [ -n "$DATABASE_URL" ]; then
        if [[ "$backup_file" == *.gz ]]; then
            if ! gunzip -c "$backup_file" | psql "$DATABASE_URL" 2>/dev/null; then
                error "Failed to restore compressed backup using DATABASE_URL"
                return 1
            fi
        else
            if ! psql "$DATABASE_URL" < "$backup_file" 2>/dev/null; then
                error "Failed to restore backup using DATABASE_URL"
                return 1
            fi
        fi
    else
        if [[ "$backup_file" == *.gz ]]; then
            if ! gunzip -c "$backup_file" | psql \
                -h "$DB_HOST" \
                -p "$DB_PORT" \
                -U "$DB_USER" \
                -d "$DB_NAME" 2>/dev/null; then
                error "Failed to restore compressed backup using connection parameters"
                return 1
            fi
        else
            if ! psql \
                -h "$DB_HOST" \
                -p "$DB_PORT" \
                -U "$DB_USER" \
                -d "$DB_NAME" < "$backup_file" 2>/dev/null; then
                error "Failed to restore backup using connection parameters"
                return 1
            fi
        fi
    fi
}

# Drop existing database if requested
if [ "$DROP_EXISTING" = true ]; then
    log "Dropping existing database..."
    if ! execute_sql "DROP DATABASE IF EXISTS $DB_NAME;"; then
        error "Failed to drop existing database"
        exit 1
    fi
    log "Database dropped successfully"
fi

# Create database if requested or if it was dropped
if [ "$CREATE_DB" = true ] || [ "$DROP_EXISTING" = true ]; then
    log "Creating database..."
    if ! execute_sql "CREATE DATABASE $DB_NAME;"; then
        error "Failed to create database"
        exit 1
    fi
    log "Database created successfully"
fi

# Test database connection
log "Testing database connection..."
if [ -n "$DATABASE_URL" ]; then
    if ! psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
        error "Database connection test failed using DATABASE_URL"
        error "Please check:"
        error "  1. DATABASE_URL is correct"
        error "  2. Database server is running"
        error "  3. Network connectivity"
        error "  4. Database credentials"
        exit 1
    fi
else
    if ! psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        -c "SELECT 1;" >/dev/null 2>&1; then
        error "Database connection test failed using connection parameters"
        error "Please check:"
        error "  1. Database server is running at $DB_HOST:$DB_PORT"
        error "  2. Database $DB_NAME exists"
        error "  3. User $DB_USER has access"
        error "  4. Password is correct"
        exit 1
    fi
fi
log "Database connection successful"

# Restore the database
log "Restoring database from backup..."
if restore_database "$BACKUP_FILE"; then
    log "Database restoration completed successfully!"
else
    error "Database restoration failed!"
    exit 1
fi

# Verify restoration
log "Verifying database restoration..."
if [ -n "$DATABASE_URL" ]; then
    TABLE_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
else
    TABLE_COUNT=$(psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
fi

if [ $? -ne 0 ]; then
    error "Failed to verify database restoration"
    exit 1
fi

if [ "$TABLE_COUNT" -gt 0 ]; then
    info "✓ Database restored successfully with $TABLE_COUNT tables"
else
    warn "⚠ Database appears to be empty or restoration may have failed"
    warn "Please check the backup file and try again"
fi

log "Database restoration process completed!"
echo
echo "Next steps:"
echo "1. Test database connectivity from your application"
echo "2. Verify data integrity by checking key tables"
echo "3. Restart your application if it was running"
echo
echo "If you encounter issues, check the PostgreSQL logs for detailed error messages."