# CoachPad Production A/B Deployment Configuration
# This file is managed by the deployment script

# HTTPS server - Production
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    server_name coachpad.ca www.coachpad.ca;
    
    # SSL configuration managed by Certbot
    ssl_certificate /etc/letsencrypt/live/coachpad.ca/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/coachpad.ca/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Application root
    root /opt/coachpad-blue/public;
    index index.html;
    
    # Proxy to active production slot
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection settings for better reliability
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Static assets
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    
    server_name coachpad.ca www.coachpad.ca;
    
    return 301 https://$host$request_uri;
}
