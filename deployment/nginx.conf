# CoachPad Nginx Configuration

# HTTPS server - Main application
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    server_name coachpad.ca api.coachpad.ca dev.coachpad.ca www.coachpad.ca staging.coachpad.ca;
    
    # SSL configuration managed by Certbot
    ssl_certificate /etc/letsencrypt/live/coachpad.ca/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/coachpad.ca/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Application root - adjust this path to your app
    root /opt/coachpad/public;
    index index.html;
    
    # Proxy to Go application
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static assets (if served directly by nginx)
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    
    server_name coachpad.ca api.coachpad.ca dev.coachpad.ca www.coachpad.ca staging.coachpad.ca;
    
    return 301 https://$host$request_uri;
}