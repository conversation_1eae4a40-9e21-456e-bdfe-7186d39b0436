#!/bin/bash

# CoachPad A/B Deployment Status Script
# Shows the status of all deployment environments

set -e

# Configuration
REMOTE_USER="${DEPLOY_USER:-root}"
REMOTE_HOST="${DEPLOY_HOST}"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "OPTIONS:"
            echo "  --host HOST    Remote host to check"
            echo "  --help         Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  DEPLOY_HOST - Default remote host"
            echo "  DEPLOY_USER - Remote user (default: root)"
            exit 0
            ;;
        *)
            shift
            ;;
    esac
done

# Validate arguments
if [ -z "$REMOTE_HOST" ]; then
    echo "Error: Remote host is required"
    echo "Use --host HOST or set DEPLOY_HOST environment variable"
    exit 1
fi

echo "=== CoachPad Deployment Status ==="
echo "Host: $REMOTE_HOST"
echo ""

# Check deployment status
ssh "$REMOTE_USER@$REMOTE_HOST" '
    echo "=== Environment Status ==="
    
    # Check staging
    echo "📋 STAGING:"
    if [ -d /opt/coachpad-staging ]; then
        echo "  ✓ Directory exists: /opt/coachpad-staging"
        if systemctl is-active --quiet coachpad-staging.service; then
            echo "  ✓ Service: RUNNING (port 9000)"
        else
            echo "  ❌ Service: STOPPED"
        fi
        if [ -f /etc/nginx/sites-enabled/coachpad-staging ]; then
            echo "  ✓ Nginx: ENABLED (staging.coachpad.ca)"
        else
            echo "  ⚠️  Nginx: NOT ENABLED"
        fi
    else
        echo "  ❌ NOT DEPLOYED"
    fi
    echo ""
    
    # Check production blue slot
    echo "🟦 PRODUCTION BLUE SLOT:"
    if [ -d /opt/coachpad-blue ]; then
        echo "  ✓ Directory exists: /opt/coachpad-blue"
        if systemctl is-active --quiet coachpad-blue.service; then
            echo "  ✓ Service: RUNNING (port 8080)"
        else
            echo "  ❌ Service: STOPPED"
        fi
    else
        echo "  ❌ NOT DEPLOYED"
    fi
    echo ""
    
    # Check production green slot
    echo "🟩 PRODUCTION GREEN SLOT:"
    if [ -d /opt/coachpad-green ]; then
        echo "  ✓ Directory exists: /opt/coachpad-green"
        if systemctl is-active --quiet coachpad-green.service; then
            echo "  ✓ Service: RUNNING (port 8081)"
        else
            echo "  ❌ Service: STOPPED"
        fi
    else
        echo "  ❌ NOT DEPLOYED"
    fi
    echo ""
    
    # Check production nginx and active slot
    echo "🌐 PRODUCTION TRAFFIC:"
    if [ -f /etc/nginx/sites-available/coachpad-production ]; then
        ACTIVE_PORT=$(grep -o "proxy_pass http://localhost:[0-9]*" /etc/nginx/sites-available/coachpad-production | grep -o "[0-9]*" || echo "unknown")
        ACTIVE_ROOT=$(grep -o "root /opt/coachpad-[^/]*" /etc/nginx/sites-available/coachpad-production | grep -o "coachpad-[^/]*" || echo "unknown")
        
        if [ "$ACTIVE_PORT" = "8080" ]; then
            echo "  ✓ Active Slot: BLUE (port 8080)"
        elif [ "$ACTIVE_PORT" = "8081" ]; then
            echo "  ✓ Active Slot: GREEN (port 8081)"
        else
            echo "  ⚠️  Active Slot: UNKNOWN (port $ACTIVE_PORT)"
        fi
        
        if [ -f /etc/nginx/sites-enabled/coachpad-production ]; then
            echo "  ✓ Nginx: ENABLED (coachpad.ca)"
        else
            echo "  ❌ Nginx: NOT ENABLED"
        fi
    else
        echo "  ❌ Production nginx config not found"
    fi
    echo ""
    
    # Check nginx status
    echo "🔧 NGINX STATUS:"
    if systemctl is-active --quiet nginx; then
        echo "  ✓ Nginx service: RUNNING"
    else
        echo "  ❌ Nginx service: STOPPED"
    fi
    
    # Test nginx config
    if nginx -t >/dev/null 2>&1; then
        echo "  ✓ Nginx config: VALID"
    else
        echo "  ❌ Nginx config: INVALID"
    fi
    echo ""
    
    # Show recent deployments
    echo "📁 RECENT DEPLOYMENTS:"
    for dir in /opt/coachpad-staging /opt/coachpad-blue /opt/coachpad-green; do
        if [ -d "$dir" ]; then
            BINARY_DATE=$(stat -c "%y" "$dir/coachpad" 2>/dev/null | cut -d"." -f1 || echo "unknown")
            ENV_NAME=$(basename "$dir" | sed "s/coachpad-//")
            echo "  $(printf "%-8s" "$ENV_NAME"): $BINARY_DATE"
        fi
    done
'
