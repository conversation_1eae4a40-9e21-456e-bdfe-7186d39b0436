# CoachPad Deployment Files

This directory contains all deployment-related configuration files and scripts for CoachPad's A/B deployment architecture.

## 📁 Directory Contents

### Configuration Files

- **`nginx-ab.conf`** - Complete nginx configuration with A/B deployment support
- **`nginx-production.conf`** - Production-only nginx config (used by deployment script)
- **`nginx-staging.conf`** - Staging-only nginx config
- **`nginx.conf`** - Original single-deployment nginx config (legacy)

### Systemd Service Files

- **`coachpad-blue.service`** - Blue production slot (port 8080)
- **`coachpad-green.service`** - Green production slot (port 8081)
- **`coachpad-staging.service`** - Staging environment (port 9000)
- **`coachpad.service`** - Original single-deployment service (legacy)

### Management Scripts

- **`deployment-status.sh`** - Check status of all environments
- **`migrate-to-ab.sh`** - Migrate from single deployment to A/B
- **`validate-deployment.sh`** - Validate A/B deployment setup
- **`backup-db.sh`** - Database backup utility
- **`restore-db.sh`** - Database restore utility

### Documentation

- **`QUICK_REFERENCE.md`** - Essential commands and workflows
- **`../docs/AB_DEPLOYMENT.md`** - Complete A/B deployment guide

## 🚀 Quick Start

### 1. Check Current Status
```bash
./deployment/deployment-status.sh --host your-server.com
```

### 2. Deploy to Staging
```bash
./deploy_prod.sh app.tar.gz --environment staging --host your-server.com
```

### 3. Deploy to Production
```bash
./deploy_prod.sh app.tar.gz --environment production --host your-server.com
```

### 4. Switch Traffic
```bash
./deploy_prod.sh --switch-to green --host your-server.com
```

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   🧪 Staging    │    │   🟦 Blue Slot  │    │  🟩 Green Slot  │
│   Port 9000     │    │   Port 8080     │    │   Port 8081     │
│                 │    │                 │    │                 │
│ staging.coach   │    │  coachpad.ca    │    │  coachpad.ca    │
│ pad.ca          │    │ (when active)   │    │ (when active)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       └───────┬───────────────┘
         │                               │
         │              ┌─────────────────────────────┐
         │              │      Nginx Traffic          │
         └──────────────┤      Management             │
                        │                             │
                        │ • staging.coachpad.ca → :9000│
                        │ • coachpad.ca → :8080/:8081  │
                        └─────────────────────────────┘
```

## 🛠️ Management Tasks

### Migration from Single Deployment
If you have an existing `/opt/coachpad` deployment:

```bash
./deployment/migrate-to-ab.sh --host your-server.com
```

### Validation and Testing
```bash
# Validate entire setup
./deployment/validate-deployment.sh --host your-server.com

# Check specific environment status
./deployment/deployment-status.sh --host your-server.com
```

### Emergency Rollback
```bash
# Instant rollback (< 5 seconds)
./deploy_prod.sh --switch-to blue --host your-server.com
```

### Database Management
```bash
# Backup before deployment
./deployment/backup-db.sh

# Restore if needed
./deployment/restore-db.sh backup-file.sql
```

## 🔧 Environment Details

### Staging Environment
- **Path**: `/opt/coachpad-staging`
- **Port**: 9000
- **Database**: `coachpad_staging` (isolated)
- **Domain**: `staging.coachpad.ca`
- **Purpose**: Safe testing before production

### Production Blue Slot
- **Path**: `/opt/coachpad-blue`
- **Port**: 8080
- **Database**: `coachpad_production` (shared)
- **Domain**: `coachpad.ca` (when active)
- **Purpose**: Production deployment slot A

### Production Green Slot
- **Path**: `/opt/coachpad-green`
- **Port**: 8081
- **Database**: `coachpad_production` (shared)
- **Domain**: `coachpad.ca` (when active)
- **Purpose**: Production deployment slot B

## 📝 Configuration Notes

### Environment Variables
Each environment reads the same `.env` file but systemd services override the port:

```ini
# In .env file
COACHPAD_BACKEND_PORT=8000  # Default, will be overridden

# In systemd service files
Environment=COACHPAD_BACKEND_PORT=8080  # Blue
Environment=COACHPAD_BACKEND_PORT=8081  # Green
Environment=COACHPAD_BACKEND_PORT=9000  # Staging
```

### Nginx Configuration
- Production traffic switching managed by updating `proxy_pass` port
- Zero-downtime switches using `nginx reload`
- Health check endpoints at `/health` for all environments

### Security
All service files include comprehensive hardening:
- Private temporary directories
- Restricted system calls
- Limited file system access
- Resource limits

## 🆘 Troubleshooting

### Service Issues
```bash
# Check service status
systemctl status coachpad-blue coachpad-green coachpad-staging

# View service logs
journalctl -u coachpad-blue.service -f
```

### Nginx Issues
```bash
# Test configuration
nginx -t

# Check active configuration
grep proxy_pass /etc/nginx/sites-available/coachpad-production
```

### Application Issues
```bash
# Check application logs
tail -f /opt/coachpad-blue/tmp/app.log
tail -f /opt/coachpad-green/tmp/app.log
tail -f /opt/coachpad-staging/tmp/app.log
```

### Port Testing
```bash
# Test direct port access
curl http://localhost:8080/health  # Blue
curl http://localhost:8081/health  # Green
curl http://localhost:9000/health  # Staging
```

## 📚 Related Documentation

- **[AB_DEPLOYMENT.md](../docs/AB_DEPLOYMENT.md)** - Complete A/B deployment guide
- **[QUICK_REFERENCE.md](QUICK_REFERENCE.md)** - Essential commands
- **[DEPLOYMENT.md](../DEPLOYMENT.md)** - Legacy single deployment guide

## 🤝 Support

For issues or questions about the A/B deployment system:

1. Check the deployment status: `./deployment/deployment-status.sh`
2. Validate the setup: `./deployment/validate-deployment.sh`
3. Review logs for specific environments
4. Use the quick reference for common tasks
