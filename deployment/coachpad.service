[Unit]
Description=CoachPad Application
Wants=postgresql.service
After=network.target postgresql.service

[Service]
Type=simple
WorkingDirectory=/opt/coachpad
ExecStart=/opt/coachpad/coachpad
Restart=always
RestartSec=5
EnvironmentFile=/opt/coachpad/.env

# Security and isolation options
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ReadWritePaths=/opt/coachpad/tmp /opt/coachpad/logs
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=true

# Network restrictions
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
# Allow outbound internet access for API calls (Stytch, Stripe, etc.)
# IPAddressDeny=any
# IPAddressAllow=localhost
# IPAddressAllow=10.0.0.0/8
# IPAddressAllow=**********/12
# IPAddressAllow=***********/16

# Capability restrictions
CapabilityBoundingSet=
AmbientCapabilities=

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
TasksMax=4096

# Additional hardening
SystemCallArchitectures=native
SystemCallFilter=@system-service
SystemCallFilter=~@debug @mount @cpu-emulation @obsolete @privileged @reboot @swap @raw-io

[Install]
WantedBy=multi-user.target