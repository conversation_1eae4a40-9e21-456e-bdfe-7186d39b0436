# CoachPad A/B Deployment Nginx Configuration - Production

# Upstream configuration for production A/B slots
upstream coachpad_production {
    # This will be updated by deployment script to point to active slot
    server localhost:8080;  # Default to blue slot
}

# HTTPS server - Production (coachpad.ca)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    server_name coachpad.ca www.coachpad.ca;
    
    # SSL configuration managed by Certbot
    ssl_certificate /etc/letsencrypt/live/coachpad.ca/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/coachpad.ca/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Application root
    root /opt/coachpad-blue/public;  # Use blue as default
    index index.html;
    
    # Proxy to active production slot
    location / {
        proxy_pass http://coachpad_production;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection settings for better reliability
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Health check - retry on upstream errors
        proxy_next_upstream error timeout http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 10s;
    }
    
    # Static assets (if served directly by nginx)
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check endpoint for load balancers
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTPS server - Staging (staging.coachpad.ca)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    server_name staging.coachpad.ca;
    
    # SSL configuration managed by Certbot
    ssl_certificate /etc/letsencrypt/live/coachpad.ca/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/coachpad.ca/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Application root
    root /opt/coachpad-staging/public;
    index index.html;
    
    # Proxy to staging environment
    location / {
        proxy_pass http://localhost:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection settings
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Static assets
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "staging-healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    
    server_name coachpad.ca www.coachpad.ca staging.coachpad.ca;
    
    return 301 https://$host$request_uri;
}
