# Port Configuration Documentation

## Overview
This document explains how port configuration works in the CoachPad application after the **Port Configuration Nightmare** fix.

## Problem Solved
Previously, ports were being set in **3 different places**:
1. systemd service files (`Environment=COACHPAD_BACKEND_PORT=8080`)
2. Deploy scripts (`sed -i 's/COACHPAD_BACKEND_PORT=.*/COACHPAD_BACKEND_PORT=8080/'`)
3. Environment files (`.env` files)

This caused conflicts, made deployments unreliable, and created a maintenance nightmare.

## Solution: Single Source of Truth

### Production & Staging: systemd is Authority
- **Production Blue**: `Environment=COACHPAD_BACKEND_PORT=8080` in `coachpad-blue.service`
- **Production Green**: `Environment=COACHPAD_BACKEND_PORT=8081` in `coachpad-green.service` 
- **Staging**: `Environment=COACHPAD_BACKEND_PORT=9000` in `coachpad-staging.service`

### Development: .env Files
- **Development**: `COACHPAD_BACKEND_PORT=8000` in `.env.development`

## Port Assignments

| Environment | Port | Source |
|-------------|------|--------|
| Development | 8000 | `.env.development` |
| Staging     | 9000 | `coachpad-staging.service` |
| Production Blue | 8080 | `coachpad-blue.service` |
| Production Green | 8081 | `coachpad-green.service` |

## Implementation Details

### Application Behavior
- **CRASHES** if `COACHPAD_BACKEND_PORT` is not set (intentional fail-fast)
- No default fallback - port must be explicitly configured
- Clear error message: "COACHPAD_BACKEND_PORT environment variable is required"

### Deploy Script Changes
- **REMOVED** all `sed` commands that modify `.env` files for ports
- Deploy scripts no longer touch port configuration
- Cleaner, more reliable deployments

### Environment File Changes
- **Production/Staging**: `.env` files do NOT contain `COACHPAD_BACKEND_PORT`
- **Development**: `.env.development` contains `COACHPAD_BACKEND_PORT=8000`
- Clear comments explain the port configuration strategy

## Benefits

✅ **Single source of truth** - systemd controls production ports  
✅ **No .env corruption** - deploy scripts don't modify files  
✅ **Environment separation** - dev uses .env, prod uses systemd  
✅ **Fail-safe behavior** - app crashes if port not properly configured  
✅ **Clear documentation** - port assignments are obvious  
✅ **Reliable deployments** - no more port configuration conflicts

## Troubleshooting

### App Won't Start - Port Not Set
```
FATAL: COACHPAD_BACKEND_PORT environment variable is required
```

**Solution**: Check your environment configuration:
- **Development**: Ensure `.env.development` has `COACHPAD_BACKEND_PORT=8000`
- **Production**: Verify systemd service file has `Environment=COACHPAD_BACKEND_PORT=XXXX`

### Wrong Port in Production
**Don't modify .env files!** The port is controlled by systemd.

Check systemd service files:
```bash
# Blue slot
sudo systemctl cat coachpad-blue.service | grep COACHPAD_BACKEND_PORT

# Green slot  
sudo systemctl cat coachpad-green.service | grep COACHPAD_BACKEND_PORT
```

## Migration Notes
- Existing deployments will continue to work
- Next deployment will use the new systemd-only port configuration
- Old `.env` port settings will be ignored in favor of systemd settings
