# Analytics Dashboard Specification

## 🎯 **Vision & Goals**

Transform raw data into actionable insights for coaches and team managers. The analytics dashboard should answer key questions like "Who are my strongest players?", "How is my team improving over time?", and "Where am I spending the most money?"

## 📊 **Dashboard Architecture**

### **Layout Structure**
```
┌─────────────────────────────────────────────────────────────┐
│ Analytics Dashboard                    [Date Range Selector] │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ Total       │ │ Win Rate    │ │ Active      │ │ Avg     │ │
│ │ Matches     │ │ 68%         │ │ Players     │ │ PPG     │ │
│ │ 142         │ │ ↗ +5%       │ │ 23          │ │ 2.4     │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────┐ ┌─────────────────────────────┐ │
│ │ Match Activity Over Time│ │ Player Performance Matrix   │ │
│ │ [Line Chart]            │ │ [Bubble Chart]              │ │
│ │                         │ │                             │ │
│ └─────────────────────────┘ └─────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────┐ ┌─────────────────────────────┐ │
│ │ Top Players Leaderboard │ │ Spending Breakdown          │ │
│ │ [Ranked List]           │ │ [Donut Chart + Table]       │ │
│ │                         │ │                             │ │
│ └─────────────────────────┘ └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📈 **Core Metrics & KPIs**

### **Summary Cards (Top Row)**
1. **Total Matches**: Count of all matches in selected period
2. **Overall Win Rate**: Percentage with trend indicator
3. **Active Players**: Players who played in selected period
4. **Average Points Per Game**: Across all matches

### **Match Analytics**
- **Match Activity Timeline**: Line chart showing matches per week/month
- **Win/Loss Trends**: Stacked bar chart over time
- **Match Frequency**: Heatmap by day of week/hour
- **Season Progression**: Match count by season with completion rates

### **Player Performance**
- **Player Performance Matrix**: Bubble chart (X: matches played, Y: win rate, Size: avg points)
- **Top Performers**: Leaderboard by win rate, points, matches played
- **Player Trends**: Individual player performance over time
- **Head-to-Head Records**: Win/loss matrix between players

### **Financial Analytics**
- **Spending Trends**: Line chart of expenses over time
- **Category Breakdown**: Donut chart by spending category
- **Cost Per Match**: Average spending per match played
- **Budget vs Actual**: Progress bars for budget tracking

## 🔧 **Technical Implementation**

### **Database Queries**

```sql
-- Match activity over time
WITH match_timeline AS (
  SELECT 
    DATE_TRUNC('week', match_date) as week,
    COUNT(*) as match_count,
    AVG(CASE WHEN winner_id IS NOT NULL THEN 1 ELSE 0 END) as completion_rate
  FROM matches 
  WHERE user_id = $1 AND match_date >= $2 AND match_date <= $3
  GROUP BY week
  ORDER BY week
)

-- Player performance matrix
WITH player_stats AS (
  SELECT 
    p.id, p.name,
    COUNT(m.id) as matches_played,
    COUNT(CASE WHEN m.winner_id = p.id THEN 1 END) as wins,
    AVG(CASE WHEN m.player_id1 = p.id THEN m.player_id1_points 
             WHEN m.player_id2 = p.id THEN m.player_id2_points END) as avg_points
  FROM players p
  LEFT JOIN matches m ON (m.player_id1 = p.id OR m.player_id2 = p.id)
  WHERE p.user_id = $1 AND m.match_date >= $2 AND m.match_date <= $3
  GROUP BY p.id, p.name
)

-- Spending trends
SELECT 
  DATE_TRUNC('month', date) as month,
  category,
  SUM(amount) as total_amount,
  COUNT(*) as transaction_count
FROM spending 
WHERE user_id = $1 AND date >= $2 AND date <= $3
GROUP BY month, category
ORDER BY month, category
```

### **Go Backend Structure**

```go
type AnalyticsHandler struct {
    Queries *db.Queries
}

type AnalyticsData struct {
    SummaryCards    SummaryMetrics    `json:"summary"`
    MatchActivity   []ActivityPoint   `json:"matchActivity"`
    PlayerMatrix    []PlayerMetrics   `json:"playerMatrix"`
    SpendingTrends  []SpendingPoint   `json:"spendingTrends"`
    TopPlayers      []PlayerRanking   `json:"topPlayers"`
}

type SummaryMetrics struct {
    TotalMatches    int     `json:"totalMatches"`
    WinRate         float64 `json:"winRate"`
    WinRateTrend    float64 `json:"winRateTrend"`
    ActivePlayers   int     `json:"activePlayers"`
    AvgPointsPerGame float64 `json:"avgPointsPerGame"`
}

type PlayerMetrics struct {
    PlayerID      int     `json:"playerId"`
    Name          string  `json:"name"`
    MatchesPlayed int     `json:"matchesPlayed"`
    WinRate       float64 `json:"winRate"`
    AvgPoints     float64 `json:"avgPoints"`
}
```

### **Frontend Chart Components**

```go
// Chart.js integration via Alpine.js components
func AnalyticsCharts() gomponents.Node {
    return html.Div(
        gomponents.Attr("x-data", "analyticsCharts()"),
        gomponents.Attr("x-init", "initCharts()"),
        
        // Match Activity Line Chart
        html.Div(
            html.Canvas(html.ID("matchActivityChart")),
        ),
        
        // Player Bubble Chart  
        html.Div(
            html.Canvas(html.ID("playerMatrixChart")),
        ),
        
        // Spending Donut Chart
        html.Div(
            html.Canvas(html.ID("spendingChart")),
        ),
    )
}
```

## 🎨 **User Experience Design**

### **Date Range Selector**
- **Presets**: Last 7 days, 30 days, 3 months, 6 months, 1 year, All time
- **Custom Range**: Date picker for specific periods
- **Season Filter**: Dropdown to filter by specific seasons

### **Interactive Features**
- **Drill-down**: Click chart elements to see detailed breakdowns
- **Hover Tooltips**: Rich information on chart hover
- **Export Options**: PNG/PDF export of charts and data
- **Responsive Design**: Mobile-optimized chart layouts

### **Progressive Enhancement**
- **Base HTML**: Static tables and basic metrics
- **Enhanced**: Interactive charts via Chart.js
- **Advanced**: Real-time updates via HTMX

## 📱 **Mobile Considerations**

### **Responsive Layout**
- **Desktop**: 2x2 grid layout for main charts
- **Tablet**: 2x1 stacked layout
- **Mobile**: Single column with swipeable chart carousel

### **Touch Interactions**
- **Swipe**: Navigate between chart views
- **Pinch/Zoom**: Zoom into time periods on charts
- **Long Press**: Access chart options menu

## 🔒 **Security & Privacy**

### **Data Access Control**
- All queries filtered by `user_id`
- Season-level permissions for shared access
- API rate limiting for chart data endpoints

### **Data Aggregation**
- No individual player identification in exported data
- Anonymized benchmarking against other users (future)

## 🧪 **Testing Strategy**

### **Test Data Scenarios**
```go
// Fixture for analytics testing
type AnalyticsFixture struct {
    User       db.User
    Players    []db.Player    // 10 players with varied performance
    Matches    []db.Match     // 50 matches over 6 months
    Spending   []db.Spending  // 20 spending entries across categories
    Seasons    []db.Season    // 3 seasons with different player sets
}
```

### **E2E Test Cases**
- Chart rendering with different date ranges
- Interactive drill-down functionality
- Export functionality
- Mobile responsive behavior
- Empty state handling (no data scenarios)

### **Performance Tests**
- Large dataset handling (1000+ matches)
- Chart rendering performance
- Query optimization validation

## 🚀 **Implementation Phases**

### **Phase 1: Foundation (2-3 hours)**
1. Database queries for basic metrics
2. Analytics handler with summary cards
3. Basic HTML template with static data
4. Navigation integration

### **Phase 2: Core Charts (3-4 hours)**
5. Chart.js integration and Alpine.js components
6. Match activity timeline chart
7. Player performance matrix
8. Date range selector functionality

### **Phase 3: Financial Analytics (2-3 hours)**
9. Spending trends chart
10. Category breakdown visualization
11. Budget tracking components

### **Phase 4: Polish & Enhancement (2-3 hours)**
12. Interactive features and drill-down
13. Export functionality
14. Mobile responsive optimization
15. Empty state handling

**Total Estimated Time: 9-13 hours**

## 📊 **Chart Specifications**

### **Match Activity Timeline**
- **Type**: Line chart with area fill
- **X-axis**: Time (weeks/months based on range)
- **Y-axis**: Number of matches
- **Features**: Zoom, pan, point hover details

### **Player Performance Matrix**
- **Type**: Bubble chart
- **X-axis**: Matches played
- **Y-axis**: Win rate (%)
- **Bubble size**: Average points scored
- **Colors**: Performance tiers (top/middle/bottom third)

### **Spending Breakdown**
- **Type**: Donut chart + data table
- **Segments**: Spending categories
- **Center**: Total amount spent
- **Features**: Category filtering, drill-down to transactions

## 🔮 **Future Enhancements**

### **Advanced Analytics**
- **Predictive Modeling**: Win probability calculations
- **Seasonal Comparisons**: Year-over-year performance
- **Benchmarking**: Anonymous comparison with other users
- **Goal Tracking**: Set and track performance goals

### **Collaboration Features**
- **Shared Dashboards**: Team-level analytics for multiple coaches
- **Report Generation**: Automated weekly/monthly reports
- **Presentation Mode**: Full-screen charts for team meetings

### **Integration Opportunities**
- **Calendar Integration**: Match scheduling analytics
- **Weather API**: Performance correlation with weather
- **Social Features**: Share achievements and milestones

## 📁 **Files to Create/Modify**

### **New Files**
- `handlers/analytics.go` - Analytics handler with data aggregation
- `templates/app/analytics/analyticsPage.go` - Main analytics page template
- `templates/app/analytics/analyticsPageContent.go` - Dashboard content
- `templates/app/analytics/summaryCards.go` - KPI summary cards
- `templates/app/analytics/charts.go` - Chart components
- `templates/app/analytics/analyticsPage.locales.json` - Localization
- `frontend/analytics.js` - Chart.js integration and Alpine.js components

### **Modified Files**
- `query.sql` - Add analytics queries
- `templates/app/nav/navMainContent.go` - Add analytics navigation link
- `main.go` - Register analytics routes

### **Database Migrations**
- No new tables required - uses existing data
- Consider adding indexes for performance on large datasets

## 🎯 **Success Metrics**

### **User Engagement**
- Time spent on analytics page
- Most viewed charts/metrics
- Export usage frequency
- Date range selection patterns

### **Business Value**
- Improved team performance correlation
- Better budget management
- Increased user retention
- Feature adoption rates

This analytics dashboard would transform the app from a data collection tool into a comprehensive performance management platform, providing coaches with the insights they need to improve their teams' performance.