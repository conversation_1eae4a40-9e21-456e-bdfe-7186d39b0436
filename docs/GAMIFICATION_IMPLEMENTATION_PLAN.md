# Gamification System Implementation Plan

Based on analysis of the app's architecture, here's a comprehensive high-level plan for implementing gamification features:

## 🏗️ **System Architecture Overview**

### **Core Components**
1. **Achievement System**: Defines accomplishments and tracks user progress
2. **Trigger Engine**: Evaluates user actions against achievement conditions  
3. **Notification Integration**: Leverages existing SSE notification system
4. **Achievement UI**: Dedicated page and embedded progress indicators

## 📊 **Database Schema Design**

### **New Tables Required**

**`achievements` table**:
- `id`, `key` (unique identifier like "first_win"), `title`, `description`
- `category` (sports, financial, consistency, social)
- `difficulty` (bronze, silver, gold, platinum)
- `points_value`, `unlock_condition` (JSON), `icon_name`
- `is_active`, `created_at`

**`user_achievements` table**:
- `id`, `user_id`, `achievement_id`, `unlocked_at`, `progress_data` (JSON)
- `current_value`, `target_value` (for tracking progress)

## 🎯 **Achievement Categories & Examples**

### **Sports Achievements**
- **"First Victory"**: Win your first match (1 point)
- **"Hat Trick Hero"**: Score 3+ goals in a single match (5 points)
- **"Winning Streak"**: Win 5 matches in a row (10 points)
- **"Season Champion"**: Win a season tournament (25 points)

### **Financial Achievements**  
- **"Budget Conscious"**: Stay under budget for 3 months (5 points)
- **"Expense Tracker"**: Log 50 spending entries (3 points)
- **"Frugal Manager"**: Save 20% on team expenses (15 points)

### **Consistency Achievements**
- **"Regular Player"**: Play 10 matches (3 points)
- **"Dedicated Coach"**: Log activity 30 days in a row (10 points)
- **"Long Timer"**: Use app for 1 year (20 points)

### **Social Achievements**
- **"Team Builder"**: Create 5 teams (5 points)
- **"Player Manager"**: Add 20 players (5 points)

## ⚙️ **Achievement Service Architecture**

### **Core Service Structure**
```go
type AchievementService struct {
    queries    *db.Queries
    notifSvc   *notifications.NotificationService
    achievements map[string]*Achievement // cached definitions
}
```

### **Key Methods**
- `EvaluateAchievements(userID int, triggerType string, data map[string]interface{})`
- `UnlockAchievement(userID, achievementID int)`
- `GetUserProgress(userID int) []UserAchievement`
- `GetAvailableAchievements(userID int) []Achievement`

### **Trigger Integration Points**
- **Match Handler**: After match creation/update → check sports achievements
- **Spending Handler**: After expense logging → check financial achievements  
- **Season Handler**: After season completion → check tournament achievements
- **Daily Cron Job**: Check consistency/streak achievements

## 🔔 **Notification System Integration**

### **New Notification Type**: `achievement`
- Leverage existing SSE infrastructure
- Special achievement notification template with celebration styling
- Higher priority than regular notifications (shown first)

### **Achievement Notification Flow**
1. User action triggers achievement evaluation
2. Achievement unlocked → `NotificationService.CreateAchievementNotification()`
3. SSE pushes notification to user's stream
4. UI shows celebration toast + updates notification bell
5. Achievement appears in notifications modal with special "🏆" icon

## 🎨 **UI Components & User Experience**

### **Achievement Page** (`/app/achievements`)
- **Layout**: Grid of achievement cards with categories (All, Sports, Financial, etc.)
- **Card States**: Locked (grayed), Unlocked (colorful), In Progress (progress bar)
- **Filters**: Category tabs, difficulty levels, completion status
- **Summary Stats**: Total points, completion percentage, recent unlocks

### **Achievement Card Component**
```
┌─────────────────────────┐
│ 🏆 [Icon]              │
│ Achievement Title       │
│ Description text...     │
│ ████████░░ 80%         │ ← Progress bar for in-progress
│ 5 points • Bronze       │
│ Unlocked 2 hours ago    │
└─────────────────────────┘
```

### **Achievement Unlock Animation**
- Modal popup with celebration animation
- Achievement details + points earned
- "Share" functionality (future feature)
- Auto-closes after 5 seconds

### **Embedded Progress Indicators**
- Dashboard: "Next Achievement" widget showing closest unlock
- Match pages: Progress toward sports achievements
- Spending pages: Progress toward financial achievements

## 🚀 **Implementation Strategy**

### **Phase 1: Core Foundation** (Highest Priority)
1. Database schema and migrations
2. Achievement definitions and service layer
3. Basic trigger integration in match/spending handlers
4. Notification system integration

### **Phase 2: User Interface** (Medium Priority)  
5. Achievement listing page and navigation
6. Achievement card components and progress displays
7. Achievement unlock modal with animations

### **Phase 3: Enhancement & Polish** (Lower Priority)
8. Embedded progress indicators across app
9. Achievement analytics and engagement tracking
10. Advanced features (sharing, leaderboards)

## 📱 **Navigation Integration**

Add to main navigation in `navMainContent.go`:
```go
NavLink("/app/achievements", props.Locales["achievements"], heroicons.Trophy(), "/app/achievements" == props.ActiveLink),
```

Position: After "Spending" section, maintaining logical flow from operational features to achievements/analytics.

## 🔄 **Data Flow Example**

**Scenario**: User wins their 5th consecutive match

1. **Trigger**: Match result updated in `MatchHandler.UpdateMatch()`
2. **Evaluation**: `achievementService.EvaluateAchievements(userID, "match_won", matchData)`
3. **Detection**: Service detects "Winning Streak" achievement should unlock
4. **Unlock**: Creates user_achievements record, calculates points
5. **Notification**: `notificationService.CreateAchievementNotification()`
6. **Real-time Update**: SSE pushes to user's notification stream
7. **UI Response**: Notification bell updates, unlock modal appears

## 🧪 **Testing Strategy**

### **E2E Testing**
- Achievement unlock flows for each category
- UI interactions (filtering, progress viewing)
- Notification integration testing

### **Fixture Data**
- Pre-defined achievement scenarios
- Test users with various achievement states
- Automated cleanup after tests

## 📈 **Success Metrics**

### **Engagement Metrics**
- Achievement unlock rate by category
- Time between app usage sessions (stickiness)
- Feature usage before/after gamification

### **User Behavior**  
- Most popular achievement categories
- Average time to first achievement unlock
- Correlation between achievements and retention

This comprehensive plan leverages the existing robust architecture while adding meaningful gamification that enhances user engagement without disrupting current workflows.