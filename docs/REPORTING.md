# Advanced Reporting System Specification

## Overview

The Advanced Reporting system transforms Coachpad's rich data into professional, customizable reports for coaches and team managers. The system leverages existing data models and follows established patterns while adding powerful export and automation capabilities.

## Architecture Integration

### Database Schema Extensions

```sql
-- Report templates for reusable report configurations
CREATE TABLE report_templates (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    season_id INTEGER REFERENCES seasons(id) ON DELETE CASCADE, -- NULL for user-level templates
    name VARCHAR(255) NOT NULL,
    description TEXT,
    report_type VARCHAR(50) NOT NULL, -- 'season_summary', 'player_performance', 'financial', 'custom'
    config JSONB NOT NULL, -- Report configuration and filters
    is_public BOOLEAN DEFAULT FALSE, -- Shareable templates
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Scheduled reports for automation
CREATE TABLE scheduled_reports (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL REFERENCES report_templates(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    season_id INTEGER REFERENCES seasons(id) ON DELETE CASCADE,
    frequency VARCHAR(20) NOT NULL, -- 'weekly', 'monthly', 'end_of_season'
    email_recipients TEXT[], -- JSON array of email addresses
    export_formats VARCHAR(20)[] DEFAULT ARRAY['pdf'], -- 'pdf', 'excel', 'csv'
    next_run_at TIMESTAMP,
    last_run_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Generated reports storage and history
CREATE TABLE generated_reports (
    id SERIAL PRIMARY KEY,
    template_id INTEGER REFERENCES report_templates(id) ON DELETE SET NULL,
    scheduled_report_id INTEGER REFERENCES scheduled_reports(id) ON DELETE SET NULL,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    season_id INTEGER REFERENCES seasons(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500), -- Path to generated file
    file_size INTEGER,
    format VARCHAR(10) NOT NULL, -- 'pdf', 'excel', 'csv'
    status VARCHAR(20) DEFAULT 'generating', -- 'generating', 'completed', 'failed'
    error_message TEXT,
    generated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP -- For cleanup
);

-- Indexes for performance
CREATE INDEX idx_report_templates_user_season ON report_templates(user_id, season_id);
CREATE INDEX idx_scheduled_reports_next_run ON scheduled_reports(next_run_at) WHERE is_active = TRUE;
CREATE INDEX idx_generated_reports_user_season ON generated_reports(user_id, season_id);
```

### Handler Structure

Following the existing handler pattern:

```go
// handlers/reports/reports.go
type ReportsHandler struct {
    queries *db.Queries
    mailer  *email.Client
    storage *storage.Client // For file storage
}

func (h *ReportsHandler) RegisterRoutes(g *echo.Group) {
    // Report builder UI
    g.GET("/reports", h.ListReports)
    g.GET("/reports/builder", h.ReportBuilder)
    g.GET("/reports/templates", h.ListTemplates)
    
    // Template management
    g.POST("/reports/templates", h.CreateTemplate)
    g.GET("/reports/templates/:id", h.GetTemplate)
    g.PUT("/reports/templates/:id", h.UpdateTemplate)
    g.DELETE("/reports/templates/:id", h.DeleteTemplate)
    
    // Report generation
    g.POST("/reports/generate", h.GenerateReport)
    g.GET("/reports/download/:id", h.DownloadReport)
    g.GET("/reports/preview", h.PreviewReport)
    
    // Scheduled reports
    g.GET("/reports/scheduled", h.ListScheduledReports)
    g.POST("/reports/scheduled", h.CreateScheduledReport)
    g.PUT("/reports/scheduled/:id", h.UpdateScheduledReport)
    g.DELETE("/reports/scheduled/:id", h.DeleteScheduledReport)
    
    // Public templates (pro feature)
    g.GET("/reports/public-templates", h.ListPublicTemplates)
}
```

## Report Builder UI

### Component Architecture

```go
// templates/app/reports/builder/builder.go
func ReportBuilder(props ReportBuilderProps, lang string) gomponents.Node {
    locales, _ := i18n.LoadTemplateLocales("./templates/app/reports/builder/builder.locales.json", lang)
    
    return pagebuilder.NewPage("/app/reports/builder").
        WithTitle(locales["page_title"]).
        WithClass("reports-builder").
        RenderContent(func() gomponents.Node {
            return html.Div(
                html.Class("max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"),
                
                // Step-by-step wizard
                buildWizardSteps(locales),
                
                // Main builder interface
                html.Div(
                    html.Class("mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8"),
                    
                    // Left panel: Report configuration
                    buildConfigPanel(props, locales),
                    
                    // Center: Live preview
                    buildPreviewPanel(locales),
                    
                    // Right panel: Export options
                    buildExportPanel(locales),
                ),
            )
        })
}

func buildConfigPanel(props ReportBuilderProps, locales map[string]string) gomponents.Node {
    return html.Div(
        html.Class("lg:col-span-1"),
        gomponents.Attr("x-data", "reportBuilder()"),
        
        // Report type selection
        ui.Card(ui.CardConfig{
            Title: locales["report_type_title"],
            Content: html.Div(
                // Season Summary
                buildReportTypeOption("season_summary", "Season Summary", "Complete overview of season performance", locales),
                
                // Player Performance
                buildReportTypeOption("player_performance", "Player Performance", "Individual player statistics and trends", locales),
                
                // Financial Report
                buildReportTypeOption("financial", "Financial Report", "Spending analysis and budget tracking", locales),
                
                // Custom Report
                buildReportTypeOption("custom", "Custom Report", "Build your own report with selected data", locales),
            ),
        }),
        
        // Dynamic configuration based on report type
        html.Div(
            gomponents.Attr("x-show", "reportType"),
            gomponents.Attr("x-transition"),
            html.Div(
                gomponents.Attr("hx-get", "/app/reports/config"),
                gomponents.Attr("hx-trigger", "reportTypeChanged from:body"),
                gomponents.Attr("hx-target", "this"),
                gomponents.Attr("hx-include", "closest form"),
                html.Class("mt-6"),
            ),
        ),
    )
}
```

### Report Configuration Types

```go
// Report configuration structures
type ReportConfig struct {
    Type     string                 `json:"type"`
    SeasonID *int                  `json:"season_id,omitempty"`
    DateRange *DateRange           `json:"date_range,omitempty"`
    Filters   map[string]interface{} `json:"filters"`
    Sections  []ReportSection       `json:"sections"`
    Options   ReportOptions         `json:"options"`
}

type ReportSection struct {
    ID       string                 `json:"id"`
    Type     string                 `json:"type"` // 'table', 'chart', 'summary', 'text'
    Title    string                 `json:"title"`
    Config   map[string]interface{} `json:"config"`
    Order    int                   `json:"order"`
    Visible  bool                  `json:"visible"`
}

type ReportOptions struct {
    IncludeLogo     bool   `json:"include_logo"`
    ShowPageNumbers bool   `json:"show_page_numbers"`
    HeaderText      string `json:"header_text"`
    FooterText      string `json:"footer_text"`
    ColorScheme     string `json:"color_scheme"` // 'default', 'monochrome', 'team_colors'
}

// Pre-configured report types
var ReportTemplates = map[string]ReportConfig{
    "season_summary": {
        Type: "season_summary",
        Sections: []ReportSection{
            {ID: "overview", Type: "summary", Title: "Season Overview", Order: 1, Visible: true},
            {ID: "standings", Type: "table", Title: "Final Standings", Order: 2, Visible: true},
            {ID: "match_results", Type: "table", Title: "Match Results", Order: 3, Visible: true},
            {ID: "player_stats", Type: "table", Title: "Player Statistics", Order: 4, Visible: true},
            {ID: "financial_summary", Type: "summary", Title: "Financial Summary", Order: 5, Visible: true},
        },
    },
    // Other templates...
}
```

## Export Engine

### PDF Generation

```go
// internal/reports/pdf/generator.go
type PDFGenerator struct {
    templatePath string
    fontPath     string
}

func (g *PDFGenerator) GenerateReport(config ReportConfig, data ReportData) (*bytes.Buffer, error) {
    pdf := gofpdf.New("P", "mm", "A4", "")
    
    // Add fonts and styling
    g.setupPDFStyling(pdf)
    
    // Generate sections based on config
    for _, section := range config.Sections {
        if !section.Visible {
            continue
        }
        
        switch section.Type {
        case "summary":
            g.addSummarySection(pdf, section, data)
        case "table":
            g.addTableSection(pdf, section, data)
        case "chart":
            g.addChartSection(pdf, section, data)
        }
    }
    
    var buf bytes.Buffer
    err := pdf.Output(&buf)
    return &buf, err
}

func (g *PDFGenerator) addTableSection(pdf *gofpdf.Fpdf, section ReportSection, data ReportData) {
    // Dynamic table generation based on section config
    tableData := data.GetTableData(section.ID)
    
    pdf.AddPage()
    pdf.SetFont("Arial", "B", 16)
    pdf.Cell(0, 10, section.Title)
    pdf.Ln(15)
    
    // Table headers
    pdf.SetFont("Arial", "B", 10)
    for _, header := range tableData.Headers {
        pdf.Cell(40, 8, header)
    }
    pdf.Ln(-1)
    
    // Table rows
    pdf.SetFont("Arial", "", 9)
    for _, row := range tableData.Rows {
        for _, cell := range row {
            pdf.Cell(40, 6, cell)
        }
        pdf.Ln(-1)
    }
}
```

### Excel Generation

```go
// internal/reports/excel/generator.go
type ExcelGenerator struct {
    templatePath string
}

func (g *ExcelGenerator) GenerateReport(config ReportConfig, data ReportData) (*bytes.Buffer, error) {
    f := excelize.NewFile()
    
    // Create sheets based on sections
    sheetIndex := 0
    for _, section := range config.Sections {
        if !section.Visible {
            continue
        }
        
        sheetName := section.Title
        if sheetIndex == 0 {
            f.SetSheetName("Sheet1", sheetName)
        } else {
            f.NewSheet(sheetName)
        }
        
        switch section.Type {
        case "table":
            g.addTableSheet(f, sheetName, section, data)
        case "summary":
            g.addSummarySheet(f, sheetName, section, data)
        }
        
        sheetIndex++
    }
    
    var buf bytes.Buffer
    err := f.Write(&buf)
    return &buf, err
}
```

## Scheduled Reports System

### Background Job Processing

```go
// internal/jobs/reports.go
type ReportScheduler struct {
    queries   *db.Queries
    generator *reports.Generator
    mailer    *email.Client
}

func (s *ReportScheduler) ProcessScheduledReports(ctx context.Context) error {
    // Find reports due for generation
    dueReports, err := s.queries.GetDueScheduledReports(ctx, time.Now())
    if err != nil {
        return err
    }
    
    for _, report := range dueReports {
        go s.processReport(ctx, report) // Process in background
    }
    
    return nil
}

func (s *ReportScheduler) processReport(ctx context.Context, report db.ScheduledReport) {
    // Generate report
    generatedReport, err := s.generator.GenerateFromTemplate(ctx, report.TemplateID)
    if err != nil {
        s.logError(report.ID, err)
        return
    }
    
    // Save to storage
    filePath, err := s.saveReport(generatedReport)
    if err != nil {
        s.logError(report.ID, err)
        return
    }
    
    // Send via email if configured
    if len(report.EmailRecipients) > 0 {
        s.emailReport(report, filePath)
    }
    
    // Update next run time
    s.updateNextRunTime(report)
}
```

### Integration with Existing Reminder System

```go
// Leverage existing reminder infrastructure
func (h *ReportsHandler) scheduleReports() {
    // Use similar pattern to match reminders
    ticker := time.NewTicker(1 * time.Hour)
    go func() {
        for range ticker.C {
            ctx := context.Background()
            scheduler := &ReportScheduler{
                queries:   h.queries,
                generator: h.reportGenerator,
                mailer:    h.mailer,
            }
            scheduler.ProcessScheduledReports(ctx)
        }
    }()
}
```

## Permission Integration

```go
// Respect existing permission system
func (h *ReportsHandler) checkReportPermissions(c echo.Context, seasonID int) error {
    userID := getUserIDFromContext(c)
    
    // Check season permissions (reuse existing logic)
    permission, err := h.queries.GetUserSeasonPermission(c.Request().Context(), db.GetUserSeasonPermissionParams{
        UserID:   userID,
        SeasonID: seasonID,
    })
    
    if err != nil || permission.Permission == "" {
        return echo.NewHTTPError(http.StatusForbidden, "No access to season")
    }
    
    // Different permissions for different report features
    switch permission.Permission {
    case "viewer":
        // Can view and generate basic reports
        return nil
    case "manager", "admin", "owner":
        // Can create templates, schedule reports
        return nil
    default:
        return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
    }
}
```

## Subscription Tier Integration

```go
// Feature gating based on subscription
func (h *ReportsHandler) checkSubscriptionLimits(c echo.Context, reportType string) error {
    user := getUserFromContext(c)
    
    switch user.SubscriptionTier {
    case "free":
        // Limited to basic reports, no scheduling
        if reportType == "custom" || reportType == "advanced" {
            return echo.NewHTTPError(http.StatusPaymentRequired, "Upgrade to Pro for advanced reports")
        }
    case "pro":
        // Full access to all features
        return nil
    }
    
    return nil
}
```

## Frontend Components

### Report Builder Alpine.js Component

```javascript
// static/js/reports.js
Alpine.data('reportBuilder', () => ({
    reportType: '',
    config: {},
    previewLoading: false,
    
    init() {
        this.$watch('reportType', (type) => {
            this.loadReportConfig(type);
            this.$dispatch('reportTypeChanged', { type });
        });
    },
    
    loadReportConfig(type) {
        this.config = this.getDefaultConfig(type);
        this.updatePreview();
    },
    
    updatePreview() {
        this.previewLoading = true;
        // HTMX will handle the actual preview update
    },
    
    generateReport(format) {
        const data = {
            type: this.reportType,
            config: this.config,
            format: format
        };
        
        // Use HTMX for generation
        htmx.ajax('POST', '/app/reports/generate', {
            values: data,
            target: '#generation-status',
            swap: 'innerHTML'
        });
    }
}));
```

### Report Template Management

```go
// templates/app/reports/templates/templates.go
func TemplatesList(props TemplatesListProps, lang string) gomponents.Node {
    locales, _ := i18n.LoadTemplateLocales("./templates/app/reports/templates/templates.locales.json", lang)
    
    return html.Div(
        html.Class("space-y-6"),
        
        // Header with create button
        html.Div(
            html.Class("flex justify-between items-center"),
            html.H2(
                html.Class("text-lg font-medium text-gray-900 dark:text-white"),
                gomponents.Text(locales["templates_title"]),
            ),
            button.PrimaryButton(button.BaseButtonConfig{
                Text: locales["create_template"],
                HxGet: "/app/reports/templates/new",
                HxTarget: "#modal-body-container",
                HxSwap: "innerHTML",
                DataTestId: "create-template-btn",
            }),
        ),
        
        // Templates grid
        html.Div(
            html.Class("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"),
            gomponents.Group(props.Templates.Transform(func(template db.ReportTemplate) gomponents.Node {
                return buildTemplateCard(template, locales)
            })),
        ),
    )
}
```

## File Storage & Cleanup

```go
// internal/storage/reports.go
type ReportsStorage struct {
    basePath string
    maxSize  int64 // Max file size in bytes
    maxAge   time.Duration // Auto-cleanup after this duration
}

func (s *ReportsStorage) SaveReport(userID int, seasonID int, data []byte, format string) (string, error) {
    // Organize by user/season for easy cleanup
    filename := fmt.Sprintf("%d/%d/%s_%s.%s", 
        userID, seasonID, 
        time.Now().Format("2006-01-02_15-04-05"),
        uuid.New().String()[:8],
        format)
    
    fullPath := filepath.Join(s.basePath, filename)
    
    // Ensure directory exists
    if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
        return "", err
    }
    
    // Write file
    if err := os.WriteFile(fullPath, data, 0644); err != nil {
        return "", err
    }
    
    return filename, nil
}

func (s *ReportsStorage) CleanupExpiredReports() error {
    // Regular cleanup job to remove old reports
    cutoff := time.Now().Add(-s.maxAge)
    
    return filepath.Walk(s.basePath, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return err
        }
        
        if !info.IsDir() && info.ModTime().Before(cutoff) {
            return os.Remove(path)
        }
        
        return nil
    })
}
```

## Email Templates

```go
// templates/email/reports/scheduled_report.go
func ScheduledReportEmail(props ScheduledReportEmailProps, lang string) gomponents.Node {
    locales, _ := i18n.LoadTemplateLocales("./templates/email/reports/scheduled_report.locales.json", lang)
    
    return html.Html(
        html.Body(
            html.Div(
                html.Class("max-w-2xl mx-auto p-6 bg-white"),
                
                // Header
                html.H1(
                    html.Class("text-2xl font-bold text-gray-900 mb-4"),
                    gomponents.Text(fmt.Sprintf(locales["subject"], props.ReportName)),
                ),
                
                // Report info
                html.P(
                    html.Class("text-gray-600 mb-6"),
                    gomponents.Text(fmt.Sprintf(locales["generated_info"], 
                        props.SeasonName, 
                        props.GeneratedAt.Format("January 2, 2006"))),
                ),
                
                // Download links
                html.Div(
                    html.Class("space-y-3"),
                    gomponents.Group(props.Formats.Transform(func(format string) gomponents.Node {
                        return html.A(
                            html.Href(fmt.Sprintf("%s/app/reports/download/%d?format=%s", 
                                props.BaseURL, props.ReportID, format)),
                            html.Class("inline-block px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"),
                            gomponents.Text(fmt.Sprintf(locales["download_format"], strings.ToUpper(format))),
                        )
                    })),
                ),
            ),
        ),
    )
}
```

## Testing Strategy

```typescript
// e2e/reports.spec.ts
test.describe('Advanced Reporting', () => {
    test('should create and generate season summary report', async ({ authenticatedPage, seasonFixture }) => {
        const page = authenticatedPage;
        
        // Navigate to reports
        await page.goto('/app/reports');
        await page.getByTestId('create-report-btn').click();
        
        // Select report type
        await page.getByTestId('report-type-season-summary').click();
        
        // Configure report
        await page.getByTestId('season-select').selectOption(seasonFixture.season.id.toString());
        await page.getByTestId('include-player-stats').check();
        
        // Generate PDF
        await page.getByTestId('generate-pdf-btn').click();
        
        // Wait for generation
        await expect(page.getByTestId('generation-status')).toContainText('Report generated successfully');
        
        // Verify download link
        const downloadLink = page.getByTestId('download-report-link');
        await expect(downloadLink).toBeVisible();
    });
    
    test('should schedule weekly report', async ({ authenticatedPage, seasonFixture }) => {
        const page = authenticatedPage;
        
        // Create template first
        await page.goto('/app/reports/templates');
        await page.getByTestId('create-template-btn').click();
        
        // Fill template details
        await page.getByTestId('template-name').fill('Weekly Team Report');
        await page.getByTestId('template-type-player-performance').click();
        await page.getByTestId('save-template-btn').click();
        
        // Schedule the template
        await page.getByTestId('schedule-report-btn').click();
        await page.getByTestId('frequency-weekly').click();
        await page.getByTestId('email-recipients').fill('<EMAIL>');
        await page.getByTestId('create-schedule-btn').click();
        
        // Verify scheduled report appears in list
        await expect(page.getByTestId('scheduled-reports-list')).toContainText('Weekly Team Report');
    });
});
```

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1)
- Database schema and migrations
- Basic handler structure
- Report configuration system
- PDF generation foundation

### Phase 2: Report Builder UI (Week 2)
- Report builder interface
- Template management
- Live preview system
- Basic export functionality

### Phase 3: Advanced Features (Week 3)
- Excel and CSV export
- Scheduled reports system
- Email integration
- File storage and cleanup

### Phase 4: Polish & Integration (Week 4)
- Permission integration
- Subscription tier gating
- Performance optimization
- Comprehensive testing

## Performance Considerations

- **Background Processing**: Use goroutines for report generation to avoid blocking UI
- **File Cleanup**: Automatic cleanup of old reports to manage disk space
- **Caching**: Cache common report data queries
- **Streaming**: Stream large reports directly to client to reduce memory usage
- **Rate Limiting**: Prevent abuse of report generation system

## Security Considerations

- **Access Control**: Respect existing permission system
- **File Access**: Secure file serving with proper authentication
- **Input Validation**: Validate all report configuration inputs
- **Rate Limiting**: Prevent excessive report generation
- **File Size Limits**: Prevent large file generation that could impact system

This specification provides a comprehensive advanced reporting system that integrates seamlessly with Coachpad's existing architecture while adding significant professional value for coaches and team managers.