# UI Component Development Guide

This guide documents the patterns and conventions for creating reusable UI components in our Go web application using gomponents, HTMX, and Alpine.js.

## Architecture Overview

Our component system follows a **server-side rendered with progressive enhancement** approach:
- **Base Layer**: HTML generated server-side using gomponents (type-safe Go HTML components)
- **Interactive Layer**: HTMX handles server communication and partial page updates
- **Reactive Layer**: Alpine.js adds client-side reactivity and state management

## Directory Structure

```
templates/
├── ui/           # Reusable UI primitives (buttons, forms, icons, modals)
├── components/   # App-specific components (appheader, notification bell)
├── app/          # Feature-specific templates with co-located .locales.json
├── layouts/      # Layout components (HTML structure, app layout)
└── public/       # Public-facing templates
```

## Core Component Patterns

### 1. Function Signature Pattern

All components follow this consistent signature:

```go
func ComponentName(props ComponentNameProps) gomponents.Node
```

**Props Structure:**
```go
type ComponentNameProps struct {
    // Required fields first
    ID          string
    Text        string
    
    // Optional styling and behavior
    Class       string
    DataTestID  string
    
    // State modifiers
    Required    bool
    Disabled    bool
    
    // HTMX integration
    HxGet       string
    HxTarget    string
    HxTrigger   string
    
    // Alpine.js integration  
    XModel      string
    XShow       string
    XDataTestId string
    
    // Content and extensibility
    Icon        gomponents.Node
    Content     gomponents.Node
    Attrs       []gomponents.Node
}
```

### 2. Component Composition Pattern

Components use a **Base + Variants** approach for maximum reusability:

```go
// Base component with full configuration
func BaseButton(config BaseButtonConfig) gomponents.Node {
    mergedConfig := []gomponents.Node{
        html.Type(config.ButtonType),
        html.Class(rootBaseButton + " " + config.Class),
        gomponents.Text(config.Text),
    }
    
    // Conditional attributes
    if config.ID != "" {
        mergedConfig = append([]gomponents.Node{html.ID(config.ID)}, mergedConfig...)
    }
    if config.DataTestId != "" {
        mergedConfig = append(mergedConfig, gomponents.Attr("data-testid", config.DataTestId))
    }
    
    // Dynamic Alpine.js attributes
    if config.XDataTestId != "" {
        mergedConfig = append(mergedConfig, gomponents.Attr("x-bind:data-testid", config.XDataTestId))
    }
    
    // Additional attributes
    if config.Attrs != nil {
        mergedConfig = append(mergedConfig, config.Attrs...)
    }
    
    return html.Button(mergedConfig...)
}

// Styled variants
func PrimaryButton(config BaseButtonConfig) gomponents.Node {
    config.Class = getButtonClass(PrimaryStyle) + " " + config.Class
    return BaseButton(config)
}

func SecondaryButton(config BaseButtonConfig) gomponents.Node {
    config.Class = getButtonClass(SecondaryStyle) + " " + config.Class
    return BaseButton(config)
}
```

### 3. Localization Pattern

Every component requiring text content includes localization support:

```go
func MyComponent(props MyComponentProps, lang string) gomponents.Node {
    // Load locales with automatic fallback to empty map
    locales := i18n.MustLoadTemplateLocales("./templates/ui/mycomponent/mycomponent.locales.json", lang)
    
    return html.Div(
        gomponents.Text(locales["default_text"]),
        // ... component content
    )
}
```

**Co-located `.locales.json` files:**
```json
{
    "en": {
        "default_text": "Default Text",
        "close_label": "Close"
    },
    "fr": {
        "default_text": "Texte par défaut",
        "close_label": "Fermer"
    }
}
```

## HTMX Integration Patterns

### Server Communication
```go
// Standard HTMX attributes
htmx.Get("/app/endpoint"),
htmx.Target("#target-element"),
htmx.Trigger("input changed delay:500ms"),
htmx.Swap("innerHTML"),

// Loading indicators
gomponents.Attr("hx-indicator", "#loading-spinner"),

// Include additional data
htmx.Include("closest form"),
```

### Event-Driven Updates
```go
// Trigger custom events
htmx.Trigger("dataUpdated from:body"),

// Listen for events
gomponents.Attr("hx-trigger", "dataUpdated from:body"),

// Inline editing with debounced saves
htmx.Put(fmt.Sprintf("/app/resource/%d/inline", id)),
htmx.Trigger("input changed delay:900ms"),
```

### Form Handling
```go
// Form submission with validation
htmx.Post("/app/form-endpoint"),
htmx.Target("#form-container"),
htmx.Swap("outerHTML"),

// Email uniqueness validation
gomponents.Attr("hx-post", "/check-email"),
gomponents.Attr("hx-trigger", "blur changed"),
gomponents.Attr("hx-target", fmt.Sprintf("#%s-validation", props.ID)),
```

## Alpine.js Integration Patterns

### Component State Management
```go
// Simple state
gomponents.Attr("x-data", "{ open: false }"),
gomponents.Attr("x-show", "open"),
gomponents.Attr("@click", "open = !open"),

// Complex component state
gomponents.Attr("x-data", "modalComponent('modalId')"),
gomponents.Attr("x-show", "modalId_modalOpen"),
gomponents.Attr("@click", "toggleModal()"),
```

### Transitions and Animations
```go
// Smooth transitions
gomponents.Attr("x-transition:enter", "transition ease-out duration-300"),
gomponents.Attr("x-transition:enter-start", "opacity-0 transform scale-95"),
gomponents.Attr("x-transition:enter-end", "opacity-100 transform scale-100"),

// Modal teleportation
gomponents.Attr("x-teleport", "body"),
gomponents.Attr("x-teleport", "#modal-body-container"),
```

### Global Event Handling
```go
// Listen to global events
gomponents.Attr("@close-all-modals.window", "closeModal()"),
gomponents.Attr("@keydown.escape.window", "closeModal()"),

// Form validation integration
gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", props.ID)),
gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", props.ID)),
```


## Design System Integration

### Styling Constants
```go
const (
    // Base classes for consistency
    baseClasses = "cursor-pointer flex items-center gap-2 justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
    
    // Component root classes for CSS targeting
    rootBaseButton     = "coachpad-base-button"
    rootIconButton     = "coachpad-icon-button"
    rootModalContainer = "coachpad-modal"
)
```

### Dark Mode Support
```go
// Always include dark mode variants
primaryButtonClasses := "border-transparent text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600"

secondaryButtonClasses := "border-gray-300 text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:text-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
```

### Responsive Design
```go
// Mobile-first responsive classes
html.Class("w-full sm:w-auto md:w-1/2 lg:w-1/3 xl:w-1/4")

// Responsive typography
html.Class("text-sm sm:text-base md:text-lg")
```

## Testing Integration

### Test ID Patterns
```go
// Static test IDs
gomponents.Attr("data-testid", props.DataTestID)

// Dynamic test IDs with Alpine.js
gomponents.Attr("x-bind:data-testid", props.XDataTestId)

// Component-specific test IDs
gomponents.Attr("data-testid", fmt.Sprintf("%s-button", props.ID))
```

### E2E Test Support
All components should include predictable test identifiers for Playwright tests:

```go
// Example usage in tests
const newPlayer = seasonFixture.players.find(p => p.id !== currentMatchPlayer1Id);
await page.getByTestId('player-select-dropdown').click();
await page.getByTestId(`player-option-${newPlayer.id}`).click();
```

## Performance Considerations
### Minimal JavaScript
- Use HTMX for server communication instead of fetch APIs
- Alpine.js only for client-side reactivity
- Avoid custom JavaScript - leverage HTMX and Alpine.js directives

### Layout Shift Prevention
```go
// Fixed-height containers for dynamic content
html.Div(
    html.Class("h-5"), // Fixed height prevents layout shift
    html.Div(
        html.Class("text-red-500 text-sm"),
        gomponents.Attr("x-show", "hasError"),
        gomponents.Attr("x-text", "errorMessage"),
    ),
)
```

## Best Practices

### 1. Component Design
- ✅ **Composition over Inheritance**: Use base components with variants
- ✅ **Progressive Enhancement**: Work without JavaScript, enhanced with HTMX/Alpine.js
- ✅ **Type Safety**: Leverage Go's type system with gomponents
- ✅ **Accessibility**: Include proper ARIA attributes and semantic HTML

### 2. State Management
- ✅ **Server State**: Use HTMX for server communication and updates
- ✅ **Client State**: Use Alpine.js for temporary UI state (modals, dropdowns)
- ✅ **Global State**: Minimize - prefer server state or Alpine.js stores

### 3. Styling
- ✅ **Consistent Classes**: Use Tailwind utility classes consistently
- ✅ **Dark Mode**: Always include dark mode variants
- ✅ **Responsive**: Mobile-first responsive design
- ✅ **Component Classes**: Add root CSS classes for component targeting

### 4. Performance
- ✅ **Minimal Bundle**: Server-side rendering reduces client-side JavaScript
- ✅ **Efficient Updates**: HTMX provides targeted DOM updates
- ✅ **Caching**: Leverage HTTP caching for component templates

## Common Anti-Patterns

### ❌ Don't Do This
```go
// Don't create large monolithic components
func MegaComponent(props MegaProps) gomponents.Node {
    // 200+ lines of component logic
}

// Don't mix server and client state management
func BadComponent() gomponents.Node {
    return html.Div(
        // Don't use fetch() - use HTMX instead
        gomponents.Attr("@click", "fetch('/api/data').then(...)"),
    )
}

// Don't skip localization
func BadComponent() gomponents.Node {
    return html.Button(
        gomponents.Text("Hard-coded English text"), // ❌ No i18n
    )
}
```

### ✅ Do This Instead
```go
// Create focused, composable components
func SimpleButton(props ButtonProps) gomponents.Node {
    return BaseButton(BaseButtonConfig{
        Text:  props.Text,
        Class: getButtonClass(props.Style),
        Attrs: props.Attrs,
    })
}

// Use HTMX for server communication
func GoodComponent() gomponents.Node {
    return html.Div(
        htmx.Get("/api/data"),
        htmx.Target("#data-container"),
        gomponents.Attr("@click", "loading = true"),
    )
}

// Always include localization
func GoodComponent(lang string) gomponents.Node {
    locales, _ := i18n.LoadTemplateLocales("./component.locales.json", lang)
    return html.Button(
        gomponents.Text(locales["button_text"]),
    )
}
```

## Creating New Components

### Checklist for New Components:

1. **📁 Structure**
   - [ ] Create component in appropriate directory (`ui/` for primitives, `components/` for app-specific)
   - [ ] Follow `ComponentName(props ComponentNameProps) gomponents.Node` signature
   - [ ] Create props struct with clear field organization

2. **🌐 Localization**
   - [ ] Add `.locales.json` file if component contains text
   - [ ] Use `i18n.MustLoadTemplateLocales()` helper function
   - [ ] Accept `lang string` parameter if localized

3. **🎨 Styling**
   - [ ] Use consistent Tailwind classes
   - [ ] Include dark mode variants
   - [ ] Add responsive classes where appropriate
   - [ ] Include root CSS class for component targeting

4. **⚡ Interactivity**
   - [ ] Use HTMX for server communication
   - [ ] Use Alpine.js for client-side reactivity
   - [ ] Add proper loading states and error handling

5. **🧪 Testing**
   - [ ] Include `data-testid` attributes
   - [ ] Support dynamic test IDs with `x-bind:data-testid`
   - [ ] Follow predictable naming conventions

6. **♿ Accessibility**
   - [ ] Use semantic HTML elements
   - [ ] Include proper ARIA attributes
   - [ ] Ensure keyboard navigation support
   - [ ] Test with screen readers

This component system provides a scalable, maintainable foundation for building complex web applications while maintaining excellent performance and user experience.