# CoachPad A/B Deployment Architecture

This document describes the enhanced A/B (Blue/Green) deployment architecture for CoachPad that provides zero-downtime deployments while maintaining a completely isolated staging environment.

## Architecture Overview

### Three-Environment Setup

```
Staging (Isolated):           Production A/B:
├── /opt/coachpad-staging/    ├── /opt/coachpad-blue/   (Port 8080)
├── Port 9000                 ├── /opt/coachpad-green/  (Port 8081)
├── coachpad_staging DB       └── Shared coachpad_production DB
└── staging.coachpad.ca       └── coachpad.ca (switches between slots)
```

### Port Management Strategy

- **Blue slot**: Port 8080 (via systemd `Environment=COACHPAD_BACKEND_PORT=8080`)
- **Green slot**: Port 8081 (via systemd `Environment=COACHPAD_BACKEND_PORT=8081`)
- **Staging**: Port 9000 (via systemd `Environment=COACHPAD_BACKEND_PORT=9000`)
- Same `.env` files across slots - port is overridden by systemd service files

## Deployment Commands

### Staging Deployment (Isolated Testing)

```bash
# Deploy to staging environment
./deploy_prod.sh app.tar.gz --host server.com --environment staging

# Check deployment status
./deployment/deployment-status.sh --host server.com
```

### Production A/B Deployment

```bash
# Deploy to inactive production slot (auto-detected)
./deploy_prod.sh app.tar.gz --host server.com --environment production

# Switch production traffic (zero-downtime)
./deploy_prod.sh --switch-to green --host server.com

# Quick rollback if needed
./deploy_prod.sh --switch-to blue --host server.com
```

### Status and Management

```bash
# Check all environment status
./deployment/deployment-status.sh --host server.com

# Get help
./deploy_prod.sh --help
```

## Migration from Single Deployment

If you have an existing single deployment at `/opt/coachpad`, use the migration script:

```bash
# Migrate existing deployment to A/B architecture
./deployment/migrate-to-ab.sh --host server.com
```

This will:
1. Stop the existing service
2. Copy `/opt/coachpad` to `/opt/coachpad-blue`
3. Start the blue slot service
4. Update nginx configuration for A/B deployment
5. Preserve the old deployment for rollback

## Configuration Files

### Systemd Services

- `coachpad-blue.service` - Blue slot (port 8080)
- `coachpad-green.service` - Green slot (port 8081) 
- `coachpad-staging.service` - Staging (port 9000)

### Nginx Configurations

- `nginx-production.conf` - Production A/B config
- `nginx-staging.conf` - Staging config
- `nginx-ab.conf` - Complete combined config (reference)

## Typical Workflow

### 1. Test on Staging

```bash
# Deploy to staging for testing
./deploy_prod.sh feature.tar.gz --host server.com --environment staging

# Test at https://staging.coachpad.ca
```

### 2. Deploy to Production (Inactive Slot)

```bash
# Deploy to inactive slot (script auto-detects blue/green)
./deploy_prod.sh feature.tar.gz --host server.com --environment production

# Test on direct port (e.g., http://server.com:8081 if deployed to green)
```

### 3. Switch Production Traffic

```bash
# Zero-downtime traffic switch
./deploy_prod.sh --switch-to green --host server.com

# Now live at https://coachpad.ca
```

### 4. Rollback if Needed

```bash
# Instant rollback to previous slot
./deploy_prod.sh --switch-to blue --host server.com
```

## Key Benefits

### Zero-Downtime Deployments
- Nginx reload switches traffic instantly between slots
- No service interruption during deployments

### Safe Testing
- Test new versions on alternate ports before switching
- Staging environment completely isolated with own database

### Easy Rollbacks
- Switch nginx back to previous slot in seconds
- Both slots can remain running for instant rollback

### Staging Isolation
- Completely separate environment with `coachpad_staging` database
- Different subdomain: `staging.coachpad.ca`

### Minimal Infrastructure Changes
- Builds on existing deployment patterns
- Same build process with enhanced deployment script

## Directory Structure

```
/opt/
├── coachpad-staging/          # Staging environment
│   ├── coachpad              # Application binary
│   ├── .env                  # Staging environment config
│   ├── tmp/                  # Temporary files
│   ├── logs/                 # Application logs
│   └── deployment/           # Deployment configs
├── coachpad-blue/            # Production blue slot
│   ├── coachpad              # Application binary
│   ├── .env                  # Production environment config
│   ├── tmp/                  # Temporary files
│   ├── logs/                 # Application logs
│   └── deployment/           # Deployment configs
└── coachpad-green/           # Production green slot
    ├── coachpad              # Application binary
    ├── .env                  # Production environment config
    ├── tmp/                  # Temporary files
    ├── logs/                 # Application logs
    └── deployment/           # Deployment configs
```

## Environment Variables

The Go application reads the port from `COACHPAD_BACKEND_PORT` environment variable:

```go
port := os.Getenv("COACHPAD_BACKEND_PORT")
if port == "" {
    port = "8000"  // fallback
}
```

Each systemd service sets this appropriately:
- Blue: `Environment=COACHPAD_BACKEND_PORT=8080`
- Green: `Environment=COACHPAD_BACKEND_PORT=8081` 
- Staging: `Environment=COACHPAD_BACKEND_PORT=9000`

## Nginx Traffic Management

### Production Traffic Switching

The production nginx config (`/etc/nginx/sites-available/coachpad-production`) uses:

```nginx
location / {
    proxy_pass http://localhost:8080;  # Updated by deployment script
    # ... other proxy settings
}
```

Traffic switching updates this port atomically and reloads nginx with zero downtime.

### Health Checks

All environments include health check endpoints:

```nginx
location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}
```

## Troubleshooting

### Check Service Status

```bash
# Check all services
systemctl status coachpad-blue coachpad-green coachpad-staging

# Check specific service
systemctl status coachpad-blue.service
```

### Check Nginx Configuration

```bash
# Test nginx config
nginx -t

# Check which slot is active
grep proxy_pass /etc/nginx/sites-available/coachpad-production
```

### View Logs

```bash
# Application logs
tail -f /opt/coachpad-blue/tmp/app.log
tail -f /opt/coachpad-green/tmp/app.log
tail -f /opt/coachpad-staging/tmp/app.log

# Service logs
journalctl -u coachpad-blue.service -f
```

### Manual Port Testing

```bash
# Test blue slot directly
curl http://localhost:8080/health

# Test green slot directly  
curl http://localhost:8081/health

# Test staging directly
curl http://localhost:9000/health
```

## Security Considerations

All service files include comprehensive security hardening:

- `NoNewPrivileges=true`
- `PrivateTmp=true`
- `ProtectSystem=strict`
- Restricted system calls and capabilities
- Read-write access only to necessary directories

Network restrictions are disabled for production to allow API calls to Stytch, Stripe, etc.

## Database Configuration

- **Production**: Both blue and green slots share `coachpad_production` database
- **Staging**: Uses separate `coachpad_staging` database for complete isolation

Make sure your `.env` files configure the correct database for each environment.

## Build Integration

The A/B deployment works with your existing build process:

```bash
# Build as usual
./build_prod.sh

# Deploy with new system
./deploy_prod.sh coachpad-production.tar.gz --host server.com --environment production
```

## Monitoring and Observability

With A/B deployment, you can:

1. **Monitor both slots**: Different log files and service status
2. **Gradual rollouts**: Deploy to one slot, test thoroughly, then switch
3. **Performance comparison**: Compare metrics between slots
4. **Canary deployments**: Route small percentage of traffic to new slot (requires load balancer)

This architecture provides enterprise-grade deployment capabilities while maintaining simplicity and building on your existing patterns.
