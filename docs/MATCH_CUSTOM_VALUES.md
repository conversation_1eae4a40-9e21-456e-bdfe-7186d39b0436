# Match Custom Values Implementation Plan

## 🎯 Overview

Implement the ability for users to edit custom column values in the matches table. The UI is already built and sends PUT requests to `/app/matches/{matchId}/custom-values` when users edit custom fields. We need to implement the backend handler to process these updates.

## 📊 Current State Analysis

### Database Schema ✅
- **match_custom_columns** table exists with columns: `id`, `user_id`, `name`, `field_type`, `description`, `is_required`, `is_active`, `display_order`
- **match_custom_values** table exists with columns: `id`, `match_id`, `column_id`, `value` (with unique constraint on match_id, column_id)

### Existing Queries ✅
- `GetMatchCustomColumns` - fetches active custom columns for a user
- `CreateMatchCustomColumn` - creates a new custom column
- `UpdateMatchCustomColumn` - updates an existing custom column
- `DeleteMatchCustomColumn` - deletes a custom column
- `GetMatchCustomValues` - fetches custom values for a specific match

### UI Implementation ✅
- `MatchesTable` displays custom columns as table headers
- `MatchCustomCell` component renders input fields with HTMX PUT requests
- Frontend already sends requests with `column_id` and `value` parameters

### Missing Components ❌
- `UpsertMatchCustomValue` SQL query
- `UpdateCustomValue` handler in `handlers/matches.go` (currently commented out at line 35)

## 🔨 Implementation Plan

### Phase 1: Database Layer (15 minutes)

**Add to `query.sql`:**
```sql
-- name: UpsertMatchCustomValue :one
INSERT INTO match_custom_values (match_id, column_id, value)
VALUES ($1, $2, $3)
ON CONFLICT(match_id, column_id) DO UPDATE SET
    value = excluded.value,
    updated_at = CURRENT_TIMESTAMP
RETURNING *;

-- name: GetMatchCustomColumnByID :one
SELECT * FROM match_custom_columns 
WHERE id = $1 AND is_active = true;
```

**Files to modify:**
- `/var/home/<USER>/coachpad/query.sql`

### Phase 2: Generate Type-Safe Code (2 minutes)

**Command to run:**
```bash
sqlc generate
```

**Generated files:**
- Updates `/var/home/<USER>/coachpad/db/query.sql.go`

### Phase 3: Handler Implementation (20 minutes)

**Add to `handlers/matches.go`:**

```go
// Request struct for custom value updates
type UpdateCustomValueRequest struct {
    ColumnID int64  `form:"column_id" validate:"required"`
    Value    string `form:"value"`
}

// UpdateCustomValue handles PUT /matches/:matchId/custom-values
func (h *MatchesHandler) UpdateCustomValue(c echo.Context) error {
    ctx := c.Request().Context()
    lang := cookies.GetLanguageFromCookie(c)
    
    // Parse match ID from URL
    matchID, err := strconv.ParseInt(c.Param("matchId"), 10, 64)
    if err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Invalid match ID",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Bind and validate request
    var req UpdateCustomValueRequest
    if err := c.Bind(&req); err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast", 
            Message: "Invalid request data",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    if err := c.Validate(&req); err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Missing required fields", 
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Get user from context
    user := auth.GetUserFromContext(c)
    if user == nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Authentication required",
            Style: "error", 
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Verify match belongs to user (security check)
    match, err := h.Queries.GetMatchByID(ctx, matchID)
    if err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Match not found",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    if match.UserID != user.ID {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Access denied",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Verify column belongs to user
    column, err := h.Queries.GetMatchCustomColumnByID(ctx, req.ColumnID)
    if err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Custom column not found",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    if column.UserID != user.ID {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Access denied",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Validate value based on field type
    if err := validateCustomValue(req.Value, column.FieldType); err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: fmt.Sprintf("Invalid %s value", column.FieldType),
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Upsert the custom value
    _, err = h.Queries.UpsertMatchCustomValue(ctx, db.UpsertMatchCustomValueParams{
        MatchID:  matchID,
        ColumnID: req.ColumnID,
        Value:    req.Value,
    })
    
    if err != nil {
        return toast.Toast(toast.ToastConfig{
            ID: "error-toast",
            Message: "Failed to update custom value",
            Style: "error",
            AutoClose: false,
            Lang: lang,
        }).Render(c.Response().Writer)
    }
    
    // Return success toast
    return toast.Toast(toast.ToastConfig{
        ID: "success-toast",
        Message: "Custom value updated successfully",
        Style: "success",
        AutoClose: true,
        Lang: lang,
    }).Render(c.Response().Writer)
}

// Helper function to validate custom values based on field type
func validateCustomValue(value string, fieldType string) error {
    if value == "" {
        return nil // Allow empty values
    }
    
    switch fieldType {
    case "number":
        _, err := strconv.ParseFloat(value, 64)
        return err
    case "date":
        _, err := time.Parse("2006-01-02", value)
        return err
    case "boolean":
        _, err := strconv.ParseBool(value)
        return err
    case "text":
        return nil
    default:
        return nil
    }
}
```

**Uncomment route registration in `RegisterRoutes` method:**
```go
r.PUT("/matches/:matchId/custom-values", h.UpdateCustomValue)
```

**Files to modify:**
- `/var/home/<USER>/coachpad/handlers/matches.go`

### Phase 4: Enhanced UX (Optional - 10 minutes)

Instead of just returning a toast, return the updated cell for immediate visual feedback:

```go
// Alternative ending for UpdateCustomValue handler
// Return updated cell component instead of just toast
return matchcustomcell.MatchCustomCell(
    matchcustomcell.MatchCustomCellProps{
        MatchID:    matchID,
        Column:     column,
        Value:      req.Value,
        DataTestID: fmt.Sprintf("match-%d-custom-%d", matchID, req.ColumnID),
    },
).Render(c.Response().Writer)
```

### Phase 5: Testing (30 minutes)

**Test Cases to Verify:**
- ✅ Valid text field update
- ✅ Valid number field update  
- ✅ Valid date field update
- ✅ Valid boolean field update
- ✅ Clear existing value (empty string)
- ✅ Invalid match ID returns error
- ✅ Match not owned by user returns error
- ✅ Invalid column ID returns error
- ✅ Invalid value for field type returns error
- ✅ Missing column_id parameter returns error

**E2E Testing:**
1. Navigate to matches table
2. Click on custom column cell
3. Edit value and press Enter
4. Verify value updates immediately
5. Verify success toast appears
6. Refresh page and verify value persists

### Phase 6: Error Handling & Edge Cases (15 minutes)

**Additional Considerations:**
1. **Empty values**: Allow clearing custom values by setting value to empty string
2. **Field validation**: Validate based on `field_type` (number, date, boolean, text)
3. **Permissions**: Ensure user owns both the match and the custom column
4. **Concurrent updates**: Database handles with UPSERT and unique constraint
5. **Required fields**: Check `is_required` flag and prevent clearing if required
6. **Inactive columns**: Only allow updates to active columns

## 📁 Files to Modify

1. **`/var/home/<USER>/coachpad/query.sql`**
   - Add `UpsertMatchCustomValue` query
   - Add `GetMatchCustomColumnByID` query

2. **`/var/home/<USER>/coachpad/handlers/matches.go`**
   - Add `UpdateCustomValueRequest` struct
   - Implement `UpdateCustomValue` handler
   - Add `validateCustomValue` helper function
   - Uncomment route registration

3. **Generated automatically:**
   - `/var/home/<USER>/coachpad/db/query.sql.go` (via `sqlc generate`)

## ⏱️ Time Estimates

- **Phase 1**: 15 minutes (SQL queries)
- **Phase 2**: 2 minutes (code generation)
- **Phase 3**: 20 minutes (handler implementation)
- **Phase 4**: 10 minutes (enhanced UX - optional)
- **Phase 5**: 30 minutes (testing)
- **Phase 6**: 15 minutes (edge cases)

**Total Estimated Time: 1.5 hours**

## 🚀 Quick Start

1. Add SQL queries to `query.sql`
2. Run `sqlc generate`
3. Implement handler in `handlers/matches.go`
4. Uncomment route registration
5. Test functionality manually
6. Run full test suite

## 🎉 Success Criteria

- Users can edit custom column values in matches table
- Values persist to database correctly
- Appropriate error messages for invalid data
- Toast notifications for success/failure
- Security checks prevent unauthorized access
- Field type validation works correctly
- Empty values can be set/cleared appropriately

## 🔄 Future Enhancements

- **Bulk Edit**: Allow editing multiple custom values at once
- **History**: Track changes to custom values over time
- **Import/Export**: CSV import/export with custom columns
- **Advanced Validation**: Custom validation rules per column
- **Default Values**: Set default values for new matches