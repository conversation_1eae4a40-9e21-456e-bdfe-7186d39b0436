# CoachPad Deployment Scripts

Quick reference for building and deploying CoachPad to production.

## Prerequisites

- Homebrew: `brew install goose`
- SSH access to production server
- Environment variables configured on production server at `/opt/coachpad/.env`
- PostgreSQL configured with username and password authentication

## Quick Commands

```bash
# Build and deploy in one command
npm run deploy:quick -- --host your-server.com

# Or step by step:
npm run build:prod                    # Creates tarball
./deploy_prod.sh coachpad-*.tar.gz --host your-server.com
```

## Build Script (`./build_prod.sh`)

Creates a production-ready tarball with:
- Compiled Go binary for Linux
- Built frontend assets (Vite)
- All runtime dependencies (templates, locales, etc.)
- Database migrations
- Deployment configuration

**Options:**
- `--skip-tests` - Skip running tests (faster for hotfixes)
- `--help` - Show usage information

## Deploy Script (`./deploy_prod.sh`)

Deploys a tarball to production server with:
- Database backup (automatic)
- Service stop/start
- Database migrations
- Binary backup for rollback

**Options:**
- `--host HOST` - Production server hostname
- `--user USER` - SSH user (default: root)  
- `--skip-migrations` - Skip database migrations
- `--skip-backup` - Skip database backup
- `--dry-run` - Show what would be done

**Examples:**
```bash
# Basic deployment
./deploy_prod.sh coachpad-production-20250107_120000.tar.gz --host myserver.com

# Emergency deployment (no backup, no migrations)
./deploy_prod.sh coachpad-production-20250107_120000.tar.gz --host myserver.com --skip-backup --skip-migrations

# Test what would be deployed
./deploy_prod.sh coachpad-production-20250107_120000.tar.gz --host myserver.com --dry-run
```

## Environment Variables

Set these for easier deployment:
```bash
export DEPLOY_HOST=your-server.com
export DEPLOY_USER=root  # optional, defaults to root
```

Then you can deploy with just:
```bash
./deploy_prod.sh coachpad-production-*.tar.gz
```

## Troubleshooting

**Build fails with goimports warnings:** This is usually safe to ignore - the warnings about case-insensitive import collisions don't prevent the build.

**SSH connection fails:** Ensure your SSH key is uploaded to Vultr and you can manually SSH to the server.

**Service fails to start:** Check logs with:
```bash
ssh <EMAIL> 'journalctl -u coachpad -n 20'
```

**Need to rollback:** See the rollback section in DEPLOYMENT.md.

## Full Documentation

See [DEPLOYMENT.md](./DEPLOYMENT.md) for complete deployment documentation including first-time setup.
