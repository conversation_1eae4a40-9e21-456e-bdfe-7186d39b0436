# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## UI Component Guide

@docs/UI_COMPONENT_GUIDE.md

Use this guide when you need to create a new modal:
@llm/server-controlled-modal-pattern.md

## Common Development Commands

### Running the Application

Assume that the application is always running in the background, live reloading at every changes you make.

To check if the application is currently running:

```bash
curl -I http://localhost:8080  # Quick health check (uses COACHPAD_BACKEND_PORT from .env, defaults to 8000)
lsof -i :8080                  # Check if port 8080 is in use
```

### Database Management

```bash
./bootstrap-db-dev.sh  # Initialize/reset development database (uses Podman with postgres:16 image)
./backup-db.sh         # Backup database
./clear-db-volume.sh   # Clear database volume
```

### Testing

```bash
npm test                       # Run all tests
```

To run specific tests or spec files:

```bash
npm test -- --grep "exact test name"  # Run a specific test by name
npm test -- matches.spec.ts           # Run a specific test file
```


### Code Generation
```bash
sqlc generate  # Regenerate type-safe Go code from SQL queries (after editing query.sql)
````

### Go Development

```bash
go fmt ./...      # Format Go code
goimports -w .    # Organize imports and format
go mod tidy       # Clean up dependencies
```

## Architecture Overview

This is a server-side rendered Go web application using:

- **Backend**: Go with Echo v4 framework, PostgreSQL, SQLC for type-safe queries
- **Frontend**: HTMX for server-driven UI updates, Alpine.js for reactive components, Tailwind CSS
- **Authentication**: Stytch (third-party service)
- **Payments**: Stripe integration
- **Email**: Mailgun

### Key Architectural Decisions

1. **Server-Side Rendering with Progressive Enhancement**

   - HTML is generated server-side using gomponents (type-safe Go HTML components)
   - HTMX handles partial page updates without full reloads
   - Alpine.js adds client-side reactivity where needed

2. **Type-Safe Database Layer**

   - SQL queries are written in `query.sql`
   - SQLC generates type-safe Go code from these queries
   - Database models are in `/db/`

3. **Handler-Based Request Processing**

   - Each feature has a handler struct in `/handlers/` with a `Queries *db.Queries` field
   - Handlers register their routes via `RegisterRoutes(*echo.Group)` method and contain business logic
   - Templates are organized by feature in `/templates/`

4. **Component-Based UI**

   - UI components are built with gomponents in Go
   - Each component is a function returning `gomponents.Node`
   - Localization files (`.locales.json`) are co-located with templates

5. **Authentication Flow**

   - JWT tokens from Stytch stored in cookies
   - Auth middleware validates tokens on protected routes
   - User context passed through Echo context

6. **Page Builder Pattern**
   - `@templates/pagebuilder/pagebuilder.go` provides a fluent interface for building consistent top-level views
   - Uses builder pattern: start with `NewPage(route)` and chain configuration methods like `WithTitle()`, `WithTarget()`, `WithClass()`
   - Automatically handles locale loading based on route structure and generates HTMX attributes
   - Two rendering modes: `RenderContent()` for HTMX swaps targeting containers like `#app-layout-content`, and `RenderFullPage()` for initial page loads with complete app layout
   - Standardizes localization, HTMX configuration, and layout integration across all pages

### Toast Implementation Pattern

For any form/action that needs toast feedback:

1. Frontend: Target the global container with stacking:
   hx-target="#toast-body-container"
   hx-swap="afterbegin"

2. Backend: Return toast components directly from handlers:
   // Error case
   return toast.Toast(toast.ToastConfig{
   ID: "error-toast",
   Message: "Something went wrong",
   Style: "error",
   AutoClose: false, // Errors persist
   Lang: lang,
   }).Render(c.Response().Writer)

// Success case  
 return toast.Toast(toast.ToastConfig{
ID: "success-toast",
Message: "Action completed",
Style: "success",
AutoClose: true, // Success auto-closes in 5s
Lang: lang,
}).Render(c.Response().Writer)

### Important Patterns

- **HTMX Interactions**: Use `hx-*` attributes for server communication
- **Alpine.js Components**: Register with `Alpine.data()` in frontend JavaScript
- **Localization**: Add translations to `.locales.json` files for each template
- **Testing**: E2E tests use Playwright with fixture-based test data, isolated user contexts, and worker-level storage states for parallel execution
  - **Test Data Pattern**: Use fixture data instead of dynamic selection for predictable tests. Example: `const newPlayer = seasonFixture.players.find(p => p.id !== currentMatchPlayer1Id);` instead of finding "any different player"
  - **Fixture with Auth Pattern**: When using data fixtures (like `spendingFixture`), always use `isolatedUser.page` for page interactions to ensure proper cookie consent handling and authentication state, while accessing the fixture data separately. Example: `async ({ spendingFixture, isolatedUser }) => { await isolatedUser.page.goto('/app/spending'); }`
- **Container Management**: Uses Podman for PostgreSQL development database
- **Static Assets**: Vite builds frontend assets with manifest for production integration

### Server-Triggered Events Pattern

The Server-Triggered Events Pattern decouples data changes from UI updates in HTMX applications. Instead of returning updated UI directly, the server sends a minimal response with a custom event header (like `newItemAdded`). Components across the page listen for these events and automatically refresh themselves, creating a modular system where forms handle data creation and tables handle data display independently.

### Development Memories

- **NEVER define default localization values as fallbacks** when importing locale JSON files. If a locale file fails to load, log the error and use empty locales rather than hardcoded fallback strings. This ensures locale files are always maintained and prevents inconsistencies between hardcoded fallbacks and actual translations.
- **Template Rendering Pattern**: In handlers, always get the response writer with `writer := c.Response().Writer` and pass it directly to template `Render(writer)` method. Never use buffers or `c.HTML()` for gomponents templates.
- Use cookies.getLanguageFromCookie when you need to get the current language in a request handler
- **Submit Button Pattern**: Use simple button components without custom onclick handlers. Most forms use `button.PrimaryButton` or `button.PrimaryIconButton` with just `ButtonType: "submit"` - avoid manual double-click prevention ceremony.
- Echo structs use form:"field", query:"param", or json:"key" tags to bind request data, combined with validate:"rules" tags for automatic validation.
- Prefer shorter commit messages
- Never put request-level data at the package level in var declarations
- **Log Inspection**: Live logs for the app are here: tmp/app.log
- To find out if the request results from a boosted anchor or form, look for HX-Boosted in the request header
- Read stripe local webhook hook here: tmp/stripe_webhook.log
- To connect to the database, use this: `podman exec -t postgres psql -c -U postgres -d postgres`
- When working with SSE, always really rely on htmx-sse. Never try to do it yourself with custom client code.
- Do not handle transitioning elements using CSS class yourself, always use x-transition:\* properties
- Regularly check the IDE diagnostics and propose to fix them if there's any
- When writing tests, ALWAYS use data-testid attributes to locate elements in the DOM.
- When reading the app log file, grep for specific keywords instead of taking a small sample of lines
- During e2e, make sure that you reuse the page context from the auth fixture to already have the cookies dialog pre accepted and hidden
- **Inline Edit Pattern**: Use the reusable `@templates/ui/inlineedit/inlineedit.go` component instead of manually creating contenteditable spans with HTMX attributes
- **Date Input Pattern**: For date fields, ALWAYS use the `@templates/ui/inlinedatepicker/inlinedatepicker.go` component or the full `@templates/ui/datepicker/datepicker.go` component. NEVER use contenteditable text fields for dates - they provide poor UX and no validation
- **Handler HTML Generation**: NEVER create HTML components directly in handlers using `html.Div()`, `html.Span()`, etc. Always use dedicated template components from `@templates/` and call their `Render()` method. Handlers should only contain business logic and delegate UI rendering to templates
