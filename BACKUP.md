# Database Backup and Restore Procedures

This document outlines database backup and restore procedures for the CoachPad application, enabling database recovery and migration to new environments.

## Overview

The backup system focuses on capturing the PostgreSQL database:
- **Database**: Complete PostgreSQL dump with schema and data
- **Compression**: Optional gzip compression for storage efficiency
- **Flexibility**: Support for both development and production environments

## Backup Types

### 1. Development Database Backup
Use the existing script for quick database backups during development:

```bash
./backup-db.sh
```

### 2. Production Database Backup
Use the deployment script with additional options:

```bash
./deployment/backup-db.sh
./deployment/backup-db.sh --compress
./deployment/backup-db.sh --backup-dir /opt/backups
```

## Database Backup Procedure

### Prerequisites
- PostgreSQL client tools (`pg_dump`)
- Sufficient disk space (typically 1-2x database size)
- Database connection credentials
- Write access to backup directory

### Running a Database Backup

```bash
# Basic backup
./deployment/backup-db.sh

# Compressed backup (recommended for production)
./deployment/backup-db.sh --compress

# Custom backup directory
./deployment/backup-db.sh --backup-dir /opt/backups --compress
```

The script will:
1. Connect to the PostgreSQL database
2. Create a complete database dump with schema and data
3. Optionally compress the backup with gzip
4. Clean up old backups (keeps 14 days by default)

### Backup Location
Backups are stored in `./backups/` by default:
```
coachpad_db_backup_YYYYMMDD_HHMMSS.sql
coachpad_db_backup_YYYYMMDD_HHMMSS.sql.gz  (if compressed)
```

### Environment Variables
The backup script uses these environment variables:
- `DATABASE_URL`: Complete database connection string (preferred)
- `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`: Individual connection parameters
- `BACKUP_RETENTION_DAYS`: Number of days to keep backups (default: 14)

**Note**: Uses username and password authentication - database credentials are provided via environment variables or the DATABASE_URL.

## Database Restore Procedure

### Database Restore

The restore script can restore a database backup from SQL or compressed SQL files:

```bash
# Basic restore (interactive)
./deployment/restore-db.sh /path/to/backup.sql

# Restore compressed backup
./deployment/restore-db.sh backup.sql.gz

# Advanced restore with options
./deployment/restore-db.sh backup.sql \
    --drop-existing \
    --force
```

### Restore Options

- `--create-db`: Create database if it doesn't exist
- `--drop-existing`: Drop existing database before restore (destructive!)
- `--force`: Skip confirmation prompts

### What Gets Restored

1. **Database Schema**: All tables, indexes, constraints, and functions
2. **Database Data**: All table data and sequences
3. **Database Users**: User accounts and permissions (if included in backup)
4. **Database Configuration**: Settings and extensions (if included in backup)

## Automated Backup Setup

### Daily Backup Cron Job

Create a cron job for automated daily database backups:

```bash
# Edit crontab
crontab -e

# Add this line for daily compressed backup at 2 AM
0 2 * * * /opt/coachpad/deployment/backup-db.sh --compress --backup-dir /opt/backups >> /var/log/coachpad-backup.log 2>&1
```

### Weekly Offsite Backup

For production systems, consider uploading backups to cloud storage:

```bash
#!/bin/bash
# weekly-offsite-backup.sh

# Run compressed database backup
/opt/coachpad/deployment/backup-db.sh --compress --backup-dir /opt/backups

# Find the latest backup
LATEST_BACKUP=$(ls -t /opt/backups/coachpad_db_backup_*.sql.gz | head -n 1)

# Upload to cloud storage (example with AWS S3)
aws s3 cp "$LATEST_BACKUP" s3://your-backup-bucket/coachpad/

# Or use rsync to remote server
# rsync -avz "$LATEST_BACKUP" backup-server:/backups/coachpad/
```

## Disaster Recovery Scenarios

### Database Corruption

1. **Stop the application**:
   ```bash
   sudo systemctl stop coachpad
   ```
2. **Restore database from backup**:
   ```bash
   ./deployment/restore-db.sh /path/to/backup.sql --drop-existing --force
   ```
3. **Start the application**:
   ```bash
   sudo systemctl start coachpad
   ```

### Accidental Data Loss

1. **Immediately stop the application** to prevent further data changes:
   ```bash
   sudo systemctl stop coachpad
   ```
2. **Identify the most recent clean backup**:
   ```bash
   ls -lt ./backups/coachpad_db_backup_*.sql*
   ```
3. **Restore from backup**:
   ```bash
   ./deployment/restore-db.sh ./backups/coachpad_db_backup_YYYYMMDD_HHMMSS.sql.gz --force
   ```
4. **Restart the application**:
   ```bash
   sudo systemctl start coachpad
   ```

### Database Migration

1. **Create backup from source database**:
   ```bash
   ./deployment/backup-db.sh --compress
   ```
2. **Transfer backup to target server**:
   ```bash
   scp backup.sql.gz user@new-server:/tmp/
   ```
3. **Restore on target server**:
   ```bash
   ./deployment/restore-db.sh /tmp/backup.sql.gz --create-db --force
   ```

## Migration to New Environment

### Development to Production Migration

1. **Create backup from development**:
   ```bash
   ./deployment/backup-db.sh --compress
   ```

2. **Transfer backup to production server**:
   ```bash
   scp backup.sql.gz user@prod-server:/tmp/
   ```

3. **Restore on production server**:
   ```bash
   # Create production database
   ./deployment/restore-db.sh /tmp/backup.sql.gz --create-db --force
   ```

### Cross-Platform Migration

Database backups are platform-independent, making migration simple:

1. **Backup source database**:
   ```bash
   ./deployment/backup-db.sh --compress
   ```

2. **Transfer to target system**:
   ```bash
   scp backup.sql.gz user@target-server:/tmp/
   ```

3. **Restore on target system**:
   ```bash
   ./deployment/restore-db.sh /tmp/backup.sql.gz --create-db --force
   ```

## Backup Verification

### Testing Backup Integrity

Regularly test your backups by performing test restores:

```bash
# Create a test database
createdb coachpad_test

# Restore backup to test database (using username and password authentication)
DATABASE_URL="postgresql://coachpad:password@localhost/coachpad_test" \
    ./deployment/restore-db.sh /path/to/backup.sql.gz --force

# Verify tables and data
psql coachpad_test -c "\dt"
psql coachpad_test -c "SELECT COUNT(*) FROM users;"

# Clean up test database
dropdb coachpad_test
```

### Backup Monitoring

Monitor backup success and failures:

```bash
# Check recent backup logs
tail -f /var/log/coachpad-backup.log

# List available backups
ls -lah ./backups/

# Check backup sizes over time
du -h ./backups/coachpad_db_backup_*.sql*
```

## Troubleshooting

### Common Backup Issues

1. **Insufficient disk space**:
   - Check available space: `df -h`
   - Clean old backups: `find ./backups -name "*.sql*" -mtime +30 -delete`

2. **Database connection fails**:
   - Verify DATABASE_URL or connection parameters
   - Check PostgreSQL service status: `systemctl status postgresql`
   - Test connection: `psql $DATABASE_URL -c "SELECT 1;"`

3. **Permission denied errors**:
   - Check backup directory permissions
   - Ensure database user has necessary privileges

### Common Restore Issues

1. **Database connection fails during restore**:
   - Verify DATABASE_URL or connection parameters
   - Check PostgreSQL service: `systemctl status postgresql`
   - Ensure target database exists (use `--create-db` if needed)

2. **Restore hangs or fails**:
   - Check PostgreSQL logs: `sudo tail -f /var/log/postgresql/postgresql-*.log`
   - Verify backup file integrity: `gzip -t backup.sql.gz`
   - Ensure sufficient disk space for database restoration

3. **Permission errors during restore**:
   - Use a database superuser account for restoration
   - Check that the restore user has CREATE DATABASE privileges

## Security Considerations

### Backup Security

- **Encrypt sensitive backups** before storing offsite
- **Secure backup storage** with appropriate access controls
- **Rotate backup encryption keys** regularly
- **Audit backup access** and monitor for unauthorized access

### Environment Variables

- **Never commit `.env` files** to version control
- **Use secure methods** to transfer environment files
- **Regularly rotate secrets** (API keys, passwords, tokens)
- **Consider using secrets management** tools for production

## Retention Policy

### Recommended Retention Schedule

- **Daily backups**: Keep for 30 days
- **Weekly backups**: Keep for 12 weeks  
- **Monthly backups**: Keep for 12 months
- **Quarterly backups**: Keep for 7 years (compliance)

### Implementing Retention

The backup script automatically cleans up old backups, but you can customize the retention policy:

```bash
#!/bin/bash
# cleanup-old-backups.sh

BACKUP_DIR="./backups"

# Keep daily backups for 30 days
find "$BACKUP_DIR" -name "coachpad_db_backup_*.sql*" -mtime +30 -delete

# Archive weekly backups (keep first backup of each week for 12 weeks)
# Archive monthly backups (keep first backup of each month for 12 months)
# Implementation depends on your specific needs
```

You can also set the retention period via environment variable:

```bash
# Set retention to 7 days
BACKUP_RETENTION_DAYS=7 ./deployment/backup-db.sh --compress
```