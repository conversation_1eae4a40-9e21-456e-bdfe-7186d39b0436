// Centralized test data generation for E2E tests
import { v4 as uuidv4 } from "uuid";

// Base utility function for generating random strings
export function generateRandomString(length: number = 8): string {
  return Math.random()
    .toString(36)
    .substring(2, 2 + length);
}

// Centralized test data generator
export const TestDataGenerator = {
  // Season data generation
  season: () => ({
    name: `Test Season ${generateRandomString(8)}`,
    description: `Season description for testing ${generateRandomString(10)}`,
  }),

  // Team data generation
  team: () => ({
    name: `Team ${generateRandomString(6)}`,
    description: `Team description for testing ${generateRandomString(10)}`,
  }),

  // Player data generation
  player: () => ({
    name: generateRandomName(),
    email: generateRandomEmail(),
    phone: generateRandomPhoneNumber(),
  }),

  // Match data generation
  match: () => ({
    title: `Match ${generateRandomString(6)}`,
    description: `Match description ${generateRandomString(8)}`,
  }),

  // Generic string generation for various purposes
  string: (prefix: string = "", length: number = 8) => 
    prefix ? `${prefix}_${generateRandomString(length)}` : generateRandomString(length),

  // Random number generation for IDs and selections
  number: (max: number = 10000) => Math.floor(Math.random() * max),

  // Random date generation
  date: {
    past: (): Date => {
      const start = new Date(1950, 0, 1); // Jan 1, 1950
      const end = new Date(2025, 4, 1); // May 1, 2025 (month is 0-based)
      const randomTime =
        start.getTime() + Math.random() * (end.getTime() - start.getTime());
      const randomDate = new Date(randomTime);
      // Ensure day is between 1 and 28 to avoid invalid days in short months
      randomDate.setDate(Math.floor(Math.random() * 28) + 1);
      return randomDate;
    },
    
    future: (daysFromNow: number = 30): Date => {
      const now = new Date();
      const randomDays = Math.floor(Math.random() * daysFromNow);
      const futureDate = new Date(now);
      futureDate.setDate(now.getDate() + randomDays);
      return futureDate;
    },
  },
};

// Individual generator functions (for backward compatibility)
export function generateRandomEmail(): string {
  return `test_${uuidv4()}@example.com`;
}

export function generateRandomName(): string {
  return `User_${generateRandomString()}`;
}

export function generateRandomPhoneNumber(): string {
  // US-style 10-digit phone number, no country code, for form input
  const area = Math.floor(Math.random() * 900) + 100;
  const prefix = Math.floor(Math.random() * 900) + 100;
  const line = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, "0");
  return `${area}${prefix}${line}`;
}

// Convenience functions for common patterns
export function generateRandomSeasonName(): string {
  return TestDataGenerator.season().name;
}

export function generateRandomTeamName(): string {
  return TestDataGenerator.team().name;
}

export function generateRandomDescription(): string {
  return `Description ${generateRandomString(10)}`;
}

// Legacy support - maintain the random date function with the exact same name
export function getRandomPastDate(): Date {
  return TestDataGenerator.date.past();
}
