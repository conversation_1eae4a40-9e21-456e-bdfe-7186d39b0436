// @ts-check
import { test, expect } from "../app/seasonFixture";

test.describe("Public Schedule Access", () => {
  test("can see the schedule of a player that has matches scheduled to him", async ({
    seasonFixture,
    isolatedUser,
  }) => {
    const { seasons, page } = seasonFixture;

    // First, go to the season details page to get matches set up
    await page.goto(`/app/seasons/${seasons[0].id}`);

    // Verify we're on the season details page
    await expect(page).toHaveTitle(/Season/);

    // Wait for the matches table to load and verify it has matches
    await page.waitForSelector("table tbody tr", { timeout: 10000 });

    // Count the number of matches visible
    const matchRows = await page.locator("table tbody tr:visible").count();
    expect(matchRows).toBeGreaterThan(0);

    // Get player names from the first match to verify later
    const firstMatchRow = page.locator("table tbody tr:visible").first();
    const player1Name = await firstMatchRow
      .locator('[data-testid^="player-1-dropdown-"]')
      .textContent();
    const player2Name = await firstMatchRow
      .locator('[data-testid^="player-2-dropdown-"]')
      .textContent();

    console.log(
      `Found match with players: "${player1Name}" vs "${player2Name}"`,
    );

    // Now get the public schedule link by clicking the dropdown actions
    const actionsDropdown = page.locator(
      '[data-testid="season-actions-dropdown"]',
    );
    await expect(actionsDropdown).toBeVisible();
    await actionsDropdown.click();

    // Click on "Get Public Schedule Link" option
    const publicLinkOption = page.locator(
      '[data-testid="public-schedule-link-button"]',
    );
    await expect(publicLinkOption).toBeVisible();

    // Listen for the HTMX response that contains the public link
    let publicLink = "";
    page.on("response", async (response) => {
      if (response.url().includes("/public-link")) {
        const headers = response.headers();
        const triggerHeader = headers["hx-trigger-after-swap"];
        if (triggerHeader) {
          try {
            const triggerData = JSON.parse(triggerHeader);
            if (triggerData.copyToClipboard) {
              publicLink = triggerData.copyToClipboard;
              console.log(`Captured public link: ${publicLink}`);
            }
          } catch (e) {
            console.log("Failed to parse trigger header:", e);
          }
        }
      }
    });

    await publicLinkOption.click();

    // Wait for the success toast to appear
    await page.waitForSelector('[data-testid="public-link-copied-toast"]', {
      timeout: 5000,
    });

    // Wait a moment for the link to be captured
    await page.waitForTimeout(1000);

    // If we didn't capture the link via response headers, extract it from the page URL structure
    if (!publicLink) {
      const currentUrl = page.url();
      const seasonId = seasons[0].id;
      publicLink = `${new URL(currentUrl).origin}/public/seasons/${seasonId}`;
      console.log(`Constructed public link: ${publicLink}`);
    }

    expect(publicLink).toBeTruthy();
    expect(publicLink).toContain("/public/seasons/");

    // Now open a new page (simulating a different user without authentication)
    const publicPage = await page.context().newPage();

    // Navigate to the public schedule link
    await publicPage.goto(publicLink);

    // Verify we're on the public schedule page
    await expect(publicPage).toHaveTitle(
      new RegExp(seasons[0].name || "Schedule"),
    );

    // Verify the public header is present (with Sign In/Sign Up buttons)
    const signInButton = publicPage.getByRole("link", { name: "Sign In" });
    await expect(signInButton).toBeVisible();

    const signUpButton = publicPage.getByRole("link", { name: "Sign Up" });
    await expect(signUpButton).toBeVisible();

    // Verify the public navigation is present
    const scheduleNavLink = publicPage.getByRole("link", { name: "Schedule" });
    await expect(scheduleNavLink).toBeVisible();

    const scoreboardNavLink = publicPage.getByRole("link", {
      name: "Scoreboard",
    });
    await expect(scoreboardNavLink).toBeVisible();

    // Verify the season name is displayed
    const seasonHeading = publicPage.locator("h1");
    await expect(seasonHeading).toContainText(seasons[0].name);

    // Verify the matches table is present and contains data
    await publicPage.waitForSelector("table", { timeout: 5000 });

    // Check that the table has the expected headers
    await expect(publicPage.locator("th")).toContainText([
      "Date",
      "Player 1",
      "Score",
      "Player 2",
      "Group",
    ]);

    // Verify that we can see the matches in the public view
    const publicMatchRows = await publicPage.locator("table tbody tr").count();
    expect(publicMatchRows).toBeGreaterThan(0);

    // Verify that player names appear in the public schedule
    const tableContent = await publicPage.locator("table tbody").textContent();

    // Check if either player name appears in the table (they might be trimmed/formatted differently)
    const player1Appears =
      player1Name && tableContent?.includes(player1Name.trim());
    const player2Appears =
      player2Name && tableContent?.includes(player2Name.trim());

    // At least one of the players should be visible in the public schedule
    expect(player1Appears || player2Appears).toBeTruthy();

    console.log(
      `Public schedule verification complete. Found ${publicMatchRows} matches visible to public.`,
    );

    // Test navigation to scoreboard
    await scoreboardNavLink.click();

    // Construct the scoreboard URL
    const scoreboardUrl = publicLink + "/scoreboard";
    await expect(publicPage).toHaveURL(scoreboardUrl);

    // Verify scoreboard elements are present
    await expect(publicPage.locator("h1")).toContainText(["Scoreboard"]);

    // The scoreboard should show a table with rankings
    const scoreboardTable = publicPage.locator("table");
    if ((await scoreboardTable.count()) > 0) {
      // If there's data, verify the headers
      await expect(publicPage.locator("th")).toContainText([
        "Rank",
        "Player",
        "Wins",
      ]);
    } else {
      // If no data, should show "No players found" message
      await expect(publicPage.locator("text=No players found")).toBeVisible();
    }

    // Navigate back to schedule to verify navigation works
    await scheduleNavLink.click();
    await expect(publicPage).toHaveURL(publicLink);
    await expect(publicPage.locator("h1")).toContainText(seasons[0].name);

    // Close the public page
    await publicPage.close();
  });

  test("public schedule handles invalid season IDs gracefully", async ({
    page,
  }) => {
    // Test with an invalid season ID
    const invalidUrl = "/public/seasons/99999";

    const response = await page.goto(invalidUrl);

    // Should get a 404 response or redirect to error page
    const status = response?.status();
    expect([404, 500, 200].includes(status || 0)).toBeTruthy();

    // Should show some kind of error message
    const bodyText = await page.textContent("body");
    expect(bodyText).toMatch(/(not found|error|404|season.*not.*found)/i);
  });

  test("public navigation is responsive on mobile", async ({
    seasonFixture,
    isolatedUser,
  }) => {
    const { seasons } = seasonFixture;
    const { page } = isolatedUser;

    // Get the public link first (simplified version)
    await page.goto(`/app/seasons/${seasons[0].id}`);
    const publicLink = `/public/seasons/${seasons[0].id}`;

    // Open public page
    const publicPage = await page.context().newPage();

    // Set mobile viewport
    await publicPage.setViewportSize({ width: 375, height: 667 });

    await publicPage.goto(publicLink);

    // Verify mobile menu button is visible (it contains an SVG hamburger icon, not text)
    const mobileMenuButton = publicPage
      .locator('button[type="button"]')
      .first();
    await expect(mobileMenuButton).toBeVisible();

    // Test mobile menu functionality
    const mobileMenu = publicPage.locator("#mobile-menu");

    // Initially the mobile menu should be hidden
    await expect(mobileMenu).toHaveClass(/md:hidden hidden/);

    await mobileMenuButton.click();

    // After clicking, the mobile menu should only have md:hidden class (hidden class removed)
    await expect(mobileMenu).toHaveClass(/^md:hidden$/);
    await expect(mobileMenu).not.toHaveClass(/md:hidden hidden/);

    // Navigation links should be visible in mobile menu
    const mobileScheduleLink = mobileMenu.getByRole("link", {
      name: "Schedule",
    });
    const mobileScoreboardLink = mobileMenu.getByRole("link", {
      name: "Scoreboard",
    });

    await expect(mobileScheduleLink).toBeVisible();
    await expect(mobileScoreboardLink).toBeVisible();

    await publicPage.close();
  });
});
