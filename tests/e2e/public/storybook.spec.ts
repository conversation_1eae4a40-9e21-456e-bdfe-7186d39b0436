import { test, expect } from "../app/cookieConsentFixture";

test.describe("Storybook UI Components", () => {
  test("user can open select component", async ({
    page,
    cookieConsentFixture,
  }) => {
    // Navigate to storybook page
    await page.goto("/dev/storybook");

    // Wait for page to load completely
    await page.waitForLoadState("networkidle");

    // Locate the select button (the visible, clickable element)
    const selectButton = page.locator("#storybook-fruit-select-button");
    await expect(selectButton).toBeVisible();

    // Verify initial state shows placeholder
    await expect(selectButton).toContainText("Select a fruit");

    // Click to open the dropdown
    await selectButton.click();

    // Wait a moment for Alpine.js animations
    await page.waitForTimeout(200);

    // Assert that the dropdown menu is visible using data-testid
    const dropdownMenu = page.locator(
      '[data-testid="storybook-fruit-select-dropdown"]',
    );
    await expect(dropdownMenu).toBeVisible();

    // Verify specific fruit options are present
    await expect(dropdownMenu.locator("text=Apple")).toBeVisible();
    await expect(dropdownMenu.locator("text=Banana")).toBeVisible();
    await expect(dropdownMenu.locator("text=Cherry")).toBeVisible();
    await expect(dropdownMenu.locator("text=Elderberry")).toBeVisible();

    // Verify disabled option exists and has correct data attribute
    const disabledOption = dropdownMenu.locator('li:has-text("Date")');
    await expect(disabledOption).toBeVisible();
    await expect(disabledOption).toHaveAttribute("data-disabled", "true");
  });
});
