import { test, expect } from "../app/cookieConsentFixture";

test.describe("401 Unauthorized Error Page", () => {
  test("should display branded 401 error page when accessing protected route without authentication", async ({
    page,
    cookieConsentFixture,
  }) => {
    // Navigate to a protected route without authentication
    await page.goto("/app/home");

    // Check for the 401 error code display
    await expect(page.locator("text=401")).toBeVisible();

    // Check for the main heading "Access Denied" using data-testid
    await expect(page.locator('[data-testid="401-heading"]')).toBeVisible();

    // Check for the description text about needing to sign in
    await expect(page.locator('[data-testid="401-description"]')).toBeVisible();

    // Verify the page title
    await expect(page).toHaveTitle(/Access Denied/);
  });
});
