// @ts-check
import { test, expect } from "../app/seasonFixture";

test.describe("Public Schedule Print", () => {
  test("can user print public schedule page", async ({ seasonFixture }) => {
    const { seasons, page } = seasonFixture;

    // Directly construct the public schedule URL
    const scheduleUrl = `/public/seasons/${seasons[0].id}`;

    // Navigate directly to the public schedule page
    await page.goto(scheduleUrl);

    // Wait for the page to load
    await page.waitForSelector("h1", { timeout: 10000 });

    // Verify we're on the public schedule page
    const pageTitle = await page.title();
    expect(pageTitle).toContain(seasons[0].name);

    // Wait for the actions dropdown to be visible
    await page.waitForSelector('[data-testid="schedule-actions-dropdown"]', {
      timeout: 10000,
    });

    // Find and click the schedule actions dropdown button
    const scheduleActionsDropdown = page.locator(
      '[data-testid="schedule-actions-dropdown"]',
    );
    await expect(scheduleActionsDropdown).toBeVisible();
    await scheduleActionsDropdown.click();

    // Verify the print button is visible in the dropdown
    const printButton = page.locator('[data-testid="print-schedule-button"]');
    await expect(printButton).toBeVisible();
    await expect(printButton).toContainText("Print Schedule");

    // Set up listener for new popup window before clicking print
    const popupPromise = page.waitForEvent("popup");

    // Click the print button
    await printButton.click();

    // Wait for the popup window to open
    const popup = await popupPromise;

    // Wait for content to be written to the popup
    await popup.waitForLoadState("networkidle");
    await popup.waitForTimeout(1000);

    // Get the popup content
    const popupContent = await popup.content();

    // Verify the popup contains proper HTML structure
    expect(popupContent).toContain("<!DOCTYPE html>");
    expect(popupContent).toContain("<html");
    expect(popupContent).toContain("<body>");

    // Check that the popup contains the schedule title
    expect(popupContent).toContain("Schedule");

    // Check that the popup contains schedule table structure
    expect(popupContent).toContain("<table");
    expect(popupContent).toContain("Date");
    expect(popupContent).toContain("Player 1");
    expect(popupContent).toContain("Score");
    expect(popupContent).toContain("Player 2");
    expect(popupContent).toContain("Group");

    // Check that print-specific CSS is included
    expect(popupContent).toContain("@media print");

    // Check that window.print() call is included
    expect(popupContent).toContain("window.print()");

    // Close the popup
    await popup.close();
  });
});
