// @ts-check
import { test, expect } from "../app/cookieConsentFixture";

test("user can sign up with valid data", async ({ cookieConsentFixture }) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");

  // Verify the page title contains Coach<PERSON> and is the signup page
  await expect(page).toHaveTitle(/Coachpad - Sign Up/);

  // Generate a unique email to avoid conflicts with existing users
  const uniqueId = Date.now();
  const testEmail = `test.user${uniqueId}@example.com`;
  const testUsername = `testuser${uniqueId}`;

  // Step 1: Basic Information
  await page.getByLabel("Username").fill(testUsername);
  await page.getByLabel("Email").fill(testEmail);
  // Open the custom language select and choose 'en'
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();

  // Go to step 2
  await page.getByRole("button", { name: "Next" }).click();

  // Step 2: Personal Details
  // Open the custom country select and choose 'us'
  await page.locator("#country-button").click();
  await page.locator("#country-option-us").click();
  await page.getByLabel("Phone").fill("************");

  // Open the datepicker
  await page.locator('input[name="birthday"]').click();
  await page.locator('[data-testid="datepicker-day-1"]').click();

  // Go to step 3
  await page.getByRole("button", { name: "Next" }).click();

  // Step 3: Security Setup
  const password = "SecurePassword123!";
  await page.locator("#password").fill(password);
  await page.locator("#confirm-password").fill(password);

  // Go to step 4 (review)
  await page.getByRole("button", { name: "Next" }).click();

  // Step 4: Submit the form
  await page.getByRole("button", { name: "Create Account" }).click();

  // Wait for the HTMX request to complete and the success message to appear
  await expect(page.locator("#signup-success-message")).toBeVisible({
    timeout: 10000,
  });
});

test("form validation and button state management", async ({
  cookieConsentFixture,
}) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");
  await page.waitForLoadState("networkidle");

  // Verify the page title
  await expect(page).toHaveTitle(/Coachpad - Sign Up/);

  // Verify that the Next button is initially disabled
  const nextButton = page.getByRole("button", { name: "Next" });
  await expect(nextButton).toBeVisible();
  await expect(nextButton).toBeDisabled();

  // Step 1: Test invalid email format and button state
  await page.getByLabel("Username").fill("testuser");

  // Verify button is still disabled after just username
  await expect(nextButton).toBeDisabled();

  await page.getByLabel("Email").fill("not-an-email");

  // Trigger email validation by blurring the field
  await page.locator("#email").blur();
  await page.waitForTimeout(100);

  // Open the custom language select and choose 'en'
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();

  // Verify that the Next button is disabled due to invalid email
  await expect(nextButton).toBeDisabled();

  // Verify that email format error is shown
  await expect(page.locator("#email-validation")).toBeVisible();
  await expect(page.locator("#email-validation")).toContainText(
    "Please enter a valid email address",
  );

  // Fix email and verify button becomes enabled
  await page.getByLabel("Email").fill("<EMAIL>");
  await page.locator("#email").blur();
  await page.waitForTimeout(200);

  // Wait for the email validation to complete and validation error to disappear
  await expect(page.locator("#email-validation")).not.toContainText(
    "Please enter a valid email address",
  );

  // Verify button becomes enabled after valid email
  await expect(nextButton).toBeEnabled();

  // Test that we can actually proceed to the next step
  await nextButton.click();
  await page.waitForSelector('div[x-show="currentStep === 2"]', {
    state: "visible",
  });

  // Step 2: Fill required fields and proceed to step 3
  await page.locator("#country-button").click();
  await page.locator("#country-option-us").click();
  await page.getByRole("button", { name: "Next" }).click();

  // Step 3: Test password validation
  await page.locator("#password").fill("short");
  await page.locator("#confirm-password").fill("short");

  // Trigger validation by blurring the password field
  await page.locator("#password").blur();
  await page.waitForTimeout(100);

  // Verify that inline error messages are shown for short passwords
  await expect(
    page.locator("div[x-show=\"errors['password']\"]"),
  ).toBeVisible();
  await expect(
    page.locator("div[x-text=\"errorMessages['password']\"]"),
  ).toContainText("Must be at least 8 characters");

  // Verify that the Next button is disabled due to short password
  await expect(page.getByRole("button", { name: "Next" })).toBeDisabled();

  // Test password mismatch
  await page.locator("#password").fill("ValidPass123!");
  await page.locator("#confirm-password").fill("DifferentPass456!");

  // Trigger validation by blurring the confirm password field
  await page.locator("#confirm-password").blur();
  await page.waitForTimeout(100);

  // Verify that inline error message is shown for password mismatch
  await expect(
    page.locator("div[x-show=\"errors['confirm-password']\"]"),
  ).toBeVisible();
  await expect(
    page.locator("div[x-text=\"errorMessages['confirm-password']\"]"),
  ).toContainText("Passwords do not match");

  // Verify that the Next button is disabled due to password mismatch
  await expect(page.getByRole("button", { name: "Next" })).toBeDisabled();

  // Fix passwords and verify button becomes enabled
  await page.locator("#confirm-password").fill("ValidPass123!");
  await page.locator("#confirm-password").blur();
  await page.waitForTimeout(100);

  // Verify that error messages are no longer shown
  await expect(
    page.locator("div[x-show=\"errors['password']\"]"),
  ).not.toBeVisible();
  await expect(
    page.locator("div[x-show=\"errors['confirm-password']\"]"),
  ).not.toBeVisible();

  await expect(page.getByRole("button", { name: "Next" })).toBeEnabled();
});

test("focus management across form steps", async ({ cookieConsentFixture }) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");
  await page.waitForLoadState("networkidle");

  // Step 1: Verify username field has autofocus on page load
  const nameInput = page.getByLabel("Username");
  await page.waitForTimeout(100); // Wait for JavaScript focus management
  await expect(nameInput).toBeFocused();

  // Fill step 1 and move to step 2
  await nameInput.fill("testuser");
  await page.getByLabel("Email").fill("<EMAIL>");
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();
  await page.getByRole("button", { name: "Next" }).click();

  // Fill step 2 and move to step 3
  await page.waitForSelector('div[x-show="currentStep === 2"]', {
    state: "visible",
  });
  await page.locator("#country-button").click();
  await page.locator("#country-option-us").click();
  await page.getByRole("button", { name: "Next" }).click();

  // Step 3: Verify password field focus behavior
  await page.waitForSelector('div[x-show="currentStep === 3"]', {
    state: "visible",
  });

  // Check that the password field is visible and focusable
  const passwordInput = page.locator("#password");
  await expect(passwordInput).toBeVisible();
  await expect(passwordInput).toBeEnabled();

  // Verify it's the first input in step 3 by checking it can receive focus
  await passwordInput.focus();
  await expect(passwordInput).toBeFocused();

  // Verify tab order works correctly (password -> confirm password)
  await page.keyboard.press("Tab");
  const confirmPasswordInput = page.locator("#confirm-password");
  await expect(confirmPasswordInput).toBeFocused();
});

// Test with authenticated user - using existing auth setup
import { test as authTest } from "../auth.setup";

authTest(
  "shows user info box when visiting signup page with signed-in user",
  async ({ isolatedUser }) => {
    const { page } = isolatedUser;
    await page.goto("/signup", {
      waitUntil: "networkidle",
    });

    // Check that the user info box is visible
    const userInfoBox = page.getByTestId("signup-user-info-box");
    await expect(userInfoBox).toBeVisible();

    // Check that it contains the expected text
    await expect(userInfoBox).toContainText("Already signed in as");

    // Check that the "Go to App" link is present and functional
    const goToAppLink = page.getByTestId("signup-user-info-box-go-to-app");
    await expect(goToAppLink).toBeVisible();
    await expect(goToAppLink).toHaveAttribute("href", "/app/home");

    // Test that close button is present (dismissible functionality)
    const closeButton = page.getByTestId("signup-user-info-box-close");
    await expect(closeButton).toBeVisible();
  },
);

test("user can sign up successfully without explicitly selecting a language", async ({
  cookieConsentFixture,
}) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");

  // Verify the page title contains Coachpad and is the signup page
  await expect(page).toHaveTitle(/Coachpad - Sign Up/);

  // Generate a unique email to avoid conflicts with existing users
  const uniqueId = Date.now();
  const testEmail = `test.user${uniqueId}@example.com`;
  const testUsername = `testuser${uniqueId}`;

  // Step 1: Basic Information - DON'T select a language explicitly
  await page.getByLabel("Username").fill(testUsername);
  await page.getByLabel("Email").fill(testEmail);

  // Trigger email validation by blurring the field
  await page.locator("#email").blur();

  // Wait for validation to complete
  await page.waitForTimeout(200);

  // Go to step 2 without selecting a language - the default should be used
  await page.getByRole("button", { name: "Next" }).click();

  // Step 2: Personal Details
  // Open the custom country select and choose 'us'
  await page.locator("#country-button").click();
  await page.locator("#country-option-us").click();
  await page.getByLabel("Phone").fill("************");

  // Open the datepicker
  await page.locator('input[name="birthday"]').click();
  await page.locator('[data-testid="datepicker-day-1"]').click();

  // Go to step 3
  await page.getByRole("button", { name: "Next" }).click();

  // Step 3: Security Setup
  const password = "SecurePassword123!";
  await page.locator("#password").fill(password);
  await page.locator("#confirm-password").fill(password);

  // Go to step 4 (review)
  await page.getByRole("button", { name: "Next" }).click();

  // Step 4: Verify that a language was automatically selected (should show in review)
  // The language should be the default value set in the form (en/English)
  const languageDisplayElement = page.locator(
    "[x-text=\"typeof formData.lang === 'string' ? formData.lang : (formData.lang && formData.lang.title) || ''\"]",
  );
  await expect(languageDisplayElement).toHaveText("en");

  // Submit the form
  await page.getByRole("button", { name: "Create Account" }).click();

  // Wait for the HTMX request to complete and the success message to appear
  await expect(page.locator("#signup-success-message")).toBeVisible({
    timeout: 10000,
  });
});

// Multi-step validation tests
test("validation errors should not show on step 2 arrival when step 1 is valid", async ({
  cookieConsentFixture,
}) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");
  await page.waitForLoadState("networkidle");

  // Step 1: Fill with valid data
  await page.getByLabel("Username").fill("validuser123");
  await page.getByLabel("Email").fill("<EMAIL>");

  // Trigger email validation by blurring the field
  await page.locator("#email").blur();
  await page.waitForTimeout(200);

  // Open the custom language select and choose 'en'
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();

  // Verify that the Next button is enabled
  const nextButton = page.getByRole("button", { name: "Next" });
  await expect(nextButton).toBeEnabled();

  // Go to step 2
  await nextButton.click();
  await page.waitForSelector('div[x-show="currentStep === 2"]', {
    state: "visible",
  });

  // Verify we're on step 2 by checking for country field
  await expect(page.locator("#country-button")).toBeVisible();

  // Wait for Alpine.js to finish processing the DOM updates
  await page.waitForTimeout(100);

  // Check that NO validation errors are showing for step 2 fields yet
  // (even though country field is required and empty)
  const phoneField = page.getByLabel("Phone");
  await expect(phoneField).toBeVisible();

  // Verify that no error messages are shown for step 2 fields initially
  await expect(
    page.locator("text='This field is required.'"),
  ).not.toBeVisible();

  // Also verify that Next button for step 2 is disabled (validation state is working)
  // but error messages are not yet shown
  const nextButton2 = page.getByRole("button", { name: "Next" });
  await expect(nextButton2).toBeDisabled();
});

test("validation errors show only after user interacts with step 2 fields", async ({
  cookieConsentFixture,
}) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");
  await page.waitForLoadState("networkidle");

  // Step 1: Fill with valid data and proceed to step 2
  await page.getByLabel("Username").fill("validuser123");
  await page.getByLabel("Email").fill("<EMAIL>");
  await page.locator("#email").blur();
  await page.waitForTimeout(200);
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();
  await page.getByRole("button", { name: "Next" }).click();
  await page.waitForSelector('div[x-show="currentStep === 2"]', {
    state: "visible",
  });

  // Initially, no errors should be visible
  await expect(
    page.locator("text='This field is required.'"),
  ).not.toBeVisible();

  // Now interact with the country field by selecting a country and then changing back to empty
  // This should trigger validation since the field was touched
  await page.locator("#country-button").click();
  await page.locator("#country-option-us").click(); // Select US
  await page.waitForTimeout(100);

  // Now select the empty option to trigger validation
  await page.locator("#country-button").click();
  await page.locator("#country-option-").click(); // Select the empty "Select Country" option
  await page.waitForTimeout(100); // Wait for validation to trigger

  // Now validation error should be visible for country field
  await expect(
    page.locator("text='This field is required.'").first(),
  ).toBeVisible();
});

test("validation errors show when trying to proceed from step 2 with invalid data", async ({
  cookieConsentFixture,
}) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");
  await page.waitForLoadState("networkidle");

  // Step 1: Fill with valid data and proceed to step 2
  await page.getByLabel("Username").fill("validuser123");
  await page.getByLabel("Email").fill("<EMAIL>");
  await page.locator("#email").blur();
  await page.waitForTimeout(200);
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();
  await page.getByRole("button", { name: "Next" }).click();
  await page.waitForSelector('div[x-show="currentStep === 2"]', {
    state: "visible",
  });

  // Don't fill any required fields in step 2
  const nextButton = page.getByRole("button", { name: "Next" });

  // Initially, no errors should be visible and button should be disabled
  await expect(
    page.locator("text='This field is required.'"),
  ).not.toBeVisible();
  await expect(nextButton).toBeDisabled();

  // Try to proceed to next step (this should trigger validation)
  // Since button is disabled, we manually trigger the nextStep function
  await page.evaluate(() => {
    // Get the Alpine component and call nextStep
    const formElement = document.querySelector(
      '[x-data*="multiStepSignup"]',
    ) as any;
    if (
      formElement &&
      formElement._x_dataStack &&
      formElement._x_dataStack[0]
    ) {
      formElement._x_dataStack[0].nextStep();
    }
  });

  // After attempting to proceed, validation errors should now be visible for required fields
  await expect(
    page.locator("text='This field is required.'").first(),
  ).toBeVisible();

  // Button should remain disabled
  await expect(nextButton).toBeDisabled();
});

test("Enter key navigation and form submission behavior", async ({
  cookieConsentFixture,
}) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");
  await page.waitForLoadState("networkidle");

  // Generate a unique email to avoid conflicts
  const uniqueId = Date.now();
  const testEmail = `test.user${uniqueId}@example.com`;
  const testUsername = `testuser${uniqueId}`;

  // Test Enter key does not advance when current step is invalid
  // Step 1: Fill incomplete information (missing email and language)
  await page.getByLabel("Username").fill("testuser");
  // Don't fill email or select language

  // Press Enter - should not advance
  await page.keyboard.press("Enter");

  // Verify we're still on step 1
  await expect(page.locator('div[x-show="currentStep === 1"]')).toBeVisible();
  await expect(
    page.locator('div[x-show="currentStep === 2"]'),
  ).not.toBeVisible();

  // Fill email but with invalid format
  await page.getByLabel("Email").fill("invalid-email");
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();

  // Press Enter - should still not advance due to invalid email
  await page.keyboard.press("Enter");

  // Verify we're still on step 1
  await expect(page.locator('div[x-show="currentStep === 1"]')).toBeVisible();
  await expect(
    page.locator('div[x-show="currentStep === 2"]'),
  ).not.toBeVisible();

  // Now test Enter key advances when step is valid
  // Step 1: Fill valid basic information
  await page.getByLabel("Username").clear();
  await page.getByLabel("Username").fill(testUsername);
  await page.getByLabel("Email").clear();
  await page.getByLabel("Email").fill(testEmail);

  // Trigger email validation by blurring the field
  await page.locator("#email").blur();
  await page.waitForTimeout(1000);

  // Focus on the email field and press Enter to advance to step 2
  await page.locator("#email").focus();
  await page.keyboard.press("Enter");

  // Wait for step transition
  await page.waitForTimeout(500);

  // Verify we're now on step 2
  await expect(page.locator('div[x-show="currentStep === 2"]')).toBeVisible();

  // Step 2: Fill personal details
  await page.locator("#country-button").click();
  await page.locator("#country-option-us").click();
  await page.getByLabel("Phone").fill("************");

  // Press Enter to advance to step 3
  await page.keyboard.press("Enter");

  // Wait for step transition
  await page.waitForTimeout(500);

  // Verify we're now on step 3
  await expect(page.locator('div[x-show="currentStep === 3"]')).toBeVisible();

  // Step 3: Test Enter key with password fields
  // Fill only password field, not confirm password
  await page.locator("#password").fill("SecurePassword123!");

  // Press Enter - should not advance due to missing confirm password
  await page.keyboard.press("Enter");
  await page.waitForTimeout(200);

  // Verify we're still on step 3
  await expect(page.locator('div[x-show="currentStep === 3"]')).toBeVisible();
  await expect(
    page.locator('div[x-show="currentStep === 4"]'),
  ).not.toBeVisible();

  // Fill confirm password field with matching password
  await page.locator("#confirm-password").fill("SecurePassword123!");

  // Press Enter to advance to step 4
  await page.keyboard.press("Enter");

  // Wait for step transition
  await page.waitForTimeout(500);

  // Verify we're now on step 4 (review)
  await expect(page.locator('div[x-show="currentStep === 4"]')).toBeVisible();

  // Test Enter key works correctly with submit button
  const submitButton = page.getByRole("button", { name: "Create Account" });
  await submitButton.focus();

  // Press Enter while focused on submit button - should submit normally
  await page.keyboard.press("Enter");

  // Wait for the HTMX request to complete and the success message to appear
  await expect(page.locator("#signup-success-message")).toBeVisible({
    timeout: 10000,
  });
});

test("datepicker positioning in step 2", async ({ cookieConsentFixture }) => {
  const { page } = cookieConsentFixture;

  // Navigate to the signup page
  await page.goto("/signup");

  // Generate a unique email to avoid conflicts with existing users
  const uniqueId = Date.now();
  const testEmail = `test.user${uniqueId}@example.com`;
  const testUsername = `testuser${uniqueId}`;

  // Step 1: Basic Information
  await page.getByLabel("Username").fill(testUsername);
  await page.getByLabel("Email").fill(testEmail);
  // Open the custom language select and choose 'en'
  await page.locator("#lang-button").click();
  await page.locator("#lang-option-en").click();

  // Go to step 2
  await page.getByRole("button", { name: "Next" }).click();

  // Get the birthday input field
  const birthdayInput = page.locator('input[name="birthday"]');

  // Get the input field's bounding box
  const inputBox = await birthdayInput.boundingBox();
  expect(inputBox).not.toBeNull();

  // Open the datepicker
  await birthdayInput.click();

  // Wait for the datepicker to be visible
  const datepicker = page
    .locator('[data-testid="datepicker-month-name"]')
    .locator("..");
  await expect(datepicker).toBeVisible();

  // Get the datepicker's bounding box
  const datepickerBox = await datepicker.boundingBox();
  expect(datepickerBox).not.toBeNull();

  // Also check the parent container of the input
  const inputParent = birthdayInput.locator("..");
  const inputParentBox = await inputParent.boundingBox();
  console.log("Input parent box:", inputParentBox);

  // Check that the datepicker is positioned right below the input
  // The datepicker should start at or near the bottom of the input
  expect(datepickerBox!.y).toBeGreaterThanOrEqual(inputBox!.y + inputBox!.height);

  // Check that the datepicker is horizontally aligned with the input field or container
  // The datepicker is positioned with an offset of approximately 97px from the input's left edge
  // This is by design and consistent across browsers
  expect(Math.abs(datepickerBox!.x - inputBox!.x)).toBeLessThan(120);
});
