// @ts-check
import { test, expect } from "../app/cookieConsentFixture";

test("homepage has title and sign in/sign up buttons", async ({
  page,
  cookieConsentFixture,
}) => {
  await page.goto("/");

  // Verify the page title
  await expect(page).toHaveTitle(/Coachpad/);

  // Verify sign in and sign up buttons are present
  const signInButton = page.getByRole("link", { name: "Sign In" });
  await expect(signInButton).toBeVisible();

  const signUpButton = page.getByRole("link", { name: "Sign Up" });
  await expect(signUpButton).toBeVisible();
});

test("language selector switches between English and French", async ({
  page,
  cookieConsentFixture,
}) => {
  await page.goto("/");

  // Test that we can switch to French
  // This assumes the default is English
  const frButton = page.getByRole("button", { name: "FR" });
  if (await frButton.isVisible()) {
    await frButton.click();
    // Wait for page reload
    await page.waitForLoadState("networkidle");

    // Now English button should be visible
    const enButton = page.getByRole("button", { name: "EN" });
    await expect(enButton).toBeVisible();
  }
});

// Test with authenticated user - using existing auth setup
import { test as authTest } from "../auth.setup";

authTest(
  "shows user info box when visiting homepage with signed-in user",
  async ({ page }) => {
    await page.goto("/", {
      waitUntil: "networkidle",
    });

    // Check that the user info box is visible
    const userInfoBox = page.getByTestId("landing-user-info-box");
    await expect(userInfoBox).toBeVisible();

    // Check that it contains the expected text
    await expect(userInfoBox).toContainText("Already signed in as");

    // Check that the "Go to App" link is present and functional
    const goToAppLink = page.getByTestId("landing-user-info-box-go-to-app");
    await expect(goToAppLink).toBeVisible();
    await expect(goToAppLink).toHaveAttribute("href", "/app/home");

    // Test that close button is present (dismissible functionality)
    const closeButton = page.getByTestId("landing-user-info-box-close");
    await expect(closeButton).toBeVisible();
  },
);
