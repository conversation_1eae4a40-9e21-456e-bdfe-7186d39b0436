// @ts-check
import { test, expect } from "../app/seasonFixture";

test.describe("Public Scoreboard Print", () => {
  test("can user print public season scoreboard", async ({ seasonFixture }) => {
    const { seasons, page } = seasonFixture;

    // Directly construct the public scoreboard URL
    const scoreboardUrl = `/public/seasons/${seasons[0].id}/scoreboard`;

    // Navigate directly to the public scoreboard page
    await page.goto(scoreboardUrl);

    // Wait for the page to load
    await page.waitForSelector("h1", { timeout: 10000 });

    // Verify we're on the public scoreboard page
    const pageTitle = await page.title();
    expect(pageTitle).toContain("Scoreboard");

    // Wait for the actions dropdown to be visible
    await page.waitForSelector('[data-testid="scoreboard-actions-dropdown"]', {
      timeout: 10000,
    });

    // Find and click the scoreboard actions dropdown button
    const scoreboardActionsDropdown = page.locator(
      '[data-testid="scoreboard-actions-dropdown"]',
    );
    await expect(scoreboardActionsDropdown).toBeVisible();
    await scoreboardActionsDropdown.click();

    // Verify the print button is visible in the dropdown
    const printButton = page.locator('[data-testid="print-scoreboard-button"]');
    await expect(printButton).toBeVisible();
    await expect(printButton).toContainText("Print Scoreboard");

    // Set up listener for new popup window before clicking print
    const popupPromise = page.waitForEvent("popup");

    // Click the print button
    await printButton.click();

    // Wait for the popup window to open
    const popup = await popupPromise;

    // Wait for content to be written to the popup
    await popup.waitForLoadState("networkidle");
    await popup.waitForTimeout(1000);

    // Get the popup content
    const popupContent = await popup.content();

    // Verify the popup contains proper HTML structure
    expect(popupContent).toContain("<!DOCTYPE html>");
    expect(popupContent).toContain("<html");
    expect(popupContent).toContain("<body>");

    // Check that the popup contains the scoreboard title
    expect(popupContent).toContain("Scoreboard");

    // Check that the popup contains scoreboard table structure
    expect(popupContent).toContain("<table");
    expect(popupContent).toContain("Rank");
    expect(popupContent).toContain("Player");
    expect(popupContent).toContain("Wins");

    // Check that print-specific CSS is included
    expect(popupContent).toContain("@media print");

    // Check that window.print() call is included
    expect(popupContent).toContain("window.print()");

    // Close the popup
    await popup.close();
  });
});
