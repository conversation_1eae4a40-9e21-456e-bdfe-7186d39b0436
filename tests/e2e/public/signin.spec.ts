import { test, expect } from "../auth.setup";

test("user can sign in with valid credentials", async ({ isolatedUser }) => {
  // Navigate to the sign-in page as the isolated user
  await isolatedUser.page.goto("/signin");

  // Fill in the sign-in form with the isolated user's credentials
  await isolatedUser.page.fill("#email", isolatedUser.email);
  await isolatedUser.page.fill("#password", "testpassword123");

  // Submit the form and wait for navigation to home
  await isolatedUser.page.click('[data-testid="signin-submit-btn"]');

  // Assert that we are redirected to /home
  await expect(isolatedUser.page).toHaveURL(/\/home$/);
});

test("shows error message when signing in with invalid credentials", async ({
  isolatedUser,
}) => {
  const { page } = isolatedUser;
  // Navigate to the sign-in page
  await page.goto("/signin");

  // Fill in the sign-in form with invalid credentials
  await page.fill("#email", "<EMAIL>");
  await page.fill("#password", "wrongpassword");

  // Submit the form
  await page.click('[data-testid="signin-submit-btn"]');

  // Expect the error message to be visible and contain the correct title
  const error = page.locator('[data-testid="signin-error"]');
  await expect(error).toBeVisible();
});

test("user can open forgot password modal on signin page", async ({
  isolatedUser,
}) => {
  const { page } = isolatedUser;
  // Navigate to the sign-in page
  await page.goto("/signin");

  // Click the forgot password button
  await page.click("#forgot-password-btn");

  // Check that the modal is visible
  const modal = page.getByTestId("forgotPassword_modal");
  await expect(modal).toBeVisible();
});

// Test with authenticated user - using existing auth setup
import { test as authTest } from "../auth.setup";

authTest(
  "shows user info box when visiting signin page with signed-in user",
  async ({ page }) => {
    await page.goto("/signin", {
      waitUntil: "networkidle",
    });

    // Check that the user info box is visible
    const userInfoBox = page.getByTestId("signin-user-info-box");
    await expect(userInfoBox).toBeVisible();

    // Check that it contains the expected text
    await expect(userInfoBox).toContainText("Already signed in as");

    // Check that the "Go to App" link is present and functional
    const goToAppLink = page.getByTestId("signin-user-info-box-go-to-app");
    await expect(goToAppLink).toBeVisible();
    await expect(goToAppLink).toHaveAttribute("href", "/app/home");

    // Test that close button is present (dismissible functionality)
    const closeButton = page.getByTestId("signin-user-info-box-close");
    await expect(closeButton).toBeVisible();
  },
);
