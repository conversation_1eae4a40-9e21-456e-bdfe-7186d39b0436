// @ts-check
import { test, expect } from "../auth.setup";
import { getBaseURL } from "../config";

test.describe("Signup with duplicate email", () => {
  // Test that uses configurableUser fixture to create a user first, then tests duplicate signup

  test("cannot signup with email that is already registered", async ({
    browser,
  }) => {
    // Create a fresh browser context to test signup
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
      // Set cookie consent
      await page.context().addInitScript(() => {
        window.localStorage.setItem("cookieConsent", "true");
      });

      // First, create a test user directly
      const existingEmail = `duplicate-test-${Date.now()}@example.com`;
      const createResponse = await page.request.post(
        `${getBaseURL()}/dev/create-test-user`,
        {
          data: {
            email: existingEmail,
            password: "testpassword123",
            name: "Duplicate Test User",
          },
        },
      );

      if (!createResponse.ok()) {
        throw new Error(
          `Failed to create test user: ${await createResponse.text()}`,
        );
      }

      // Navigate to the signup page
      await page.goto(`${getBaseURL()}/signup`);

      // Verify the page title
      await expect(page).toHaveTitle(/Coachpad - Sign Up/);

      // Fill out the form with the same email that already exists
      await page.getByLabel("Username").fill("duplicate-user");
      await page.getByLabel("Email").fill(existingEmail);

      // Check that the email validation shows it's already registered
      // Wait for the HTMX response after blurring the email field
      await page.locator("#email").blur();

      // Wait a bit for the HTMX request to complete
      await page.waitForTimeout(2000);

      // Verify that the email validation message appears
      await expect(page.locator("#email-validation")).toContainText(
        "Email is already registered",
      );

      // Verify that there's only one error message displayed (no duplicate)
      const errorMessages = await page
        .locator("text=Email is already registered")
        .count();
      expect(errorMessages).toBe(1);

      // Continue filling step 1 to test that progression is prevented
      console.log("Clicking language selector button...");
      await page.locator("#lang-button").click();

      // Wait for dropdown to open and option to be visible
      console.log("Waiting for language dropdown to open...");
      await page
        .locator("#lang-option-en")
        .waitFor({ state: "visible", timeout: 5000 });

      console.log("Clicking English language option...");
      await page.locator("#lang-option-en").click();

      console.log(
        "Language selection completed, verifying form progression is prevented...",
      );

      // Verify that the Next button is disabled due to duplicate email
      console.log("Checking if Next button is disabled...");
      const nextButton = page.getByRole("button", { name: "Next" });
      await expect(nextButton).toBeDisabled();

      console.log(
        "Next button is correctly disabled. Verifying we are still on step 1...",
      );
      // Verify we're still on step 1 by checking if username field is still visible
      await expect(page.getByLabel("Username")).toBeVisible();

      // Also verify that step 2 elements are not visible (indicating we haven't progressed)
      console.log("Verifying step 2 content is not visible...");
      await expect(page.getByLabel("Country")).not.toBeVisible();

      console.log("Test completed successfully!");
    } finally {
      await context.close();
    }
  });

  test("shows available message for new email", async ({ browser }) => {
    // Create a fresh browser context
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
      // Set cookie consent
      await page.context().addInitScript(() => {
        window.localStorage.setItem("cookieConsent", "true");
      });

      // Navigate to the signup page
      await page.goto(`${getBaseURL()}/signup`);

      // Fill in a unique email that doesn't exist
      const uniqueEmail = `unique-email-${Date.now()}@example.com`;
      console.log("Testing with unique email:", uniqueEmail);
      await page.getByLabel("Email").fill(uniqueEmail);

      // Blur the field to trigger validation
      await page.locator("#email").blur();

      console.log("Waiting for email availability validation...");

      await expect(page.locator("#email-validation")).toContainText(
        "Email is available",
      );
    } finally {
      await context.close();
    }
  });
});
