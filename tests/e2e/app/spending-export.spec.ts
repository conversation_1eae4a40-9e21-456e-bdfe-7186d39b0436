import { test, expect } from "./spendingFixture";

test.describe("Spending Export", () => {
  test("user can export spending to CSV", async ({ spendingFixture }) => {
    const { page } = spendingFixture;
    
    await page.goto("/app/spending");

    // Wait for spending page to load
    await expect(page.locator("h1:has-text('Spending')")).toBeVisible();

    // Check if export CSV button is visible (it should be visible on spending page)
    const exportCsvButton = page.locator('[data-testid="export-spending-csv-button"]');
    
    await expect(exportCsvButton).toBeVisible();
    await expect(exportCsvButton).toContainText("Export CSV");

    // Verify the export CSV button has the correct href attribute
    await expect(exportCsvButton).toHaveAttribute(
      "href",
      "/app/spending/export/csv"
    );

    // Test that export CSV is accessible (but don't actually click it as it causes navigation)
    await expect(exportCsvButton).toBeEnabled();
  });
});
