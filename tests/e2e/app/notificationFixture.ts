// Import our seasonFixture since notifications may relate to seasons
import { test as seasonTest, SeasonFixture } from './seasonFixture';

// Define interfaces for type safety
interface NotificationConfig {
  count: number;
  type: string;
  title?: string;
  message?: string;
  markAsRead?: boolean;
}

interface TestNotification {
  id: number;
  type: string;
  title: string;
  message: string;
  is_read: boolean;
  created_at: string;
}

interface NotificationFixture {
  notifications: TestNotification[];
  unreadCount: number;
}

// Helper functions
import { generateRandomString } from "../testData";

function generateRandomTitle(type: string): string {
  const titles = {
    match_update: 'Match Score Updated',
    schedule_change: 'Schedule Updated', 
    result: 'Match Result',
    announcement: 'New Announcement'
  };
  return titles[type] || `Test ${type} ${generateRandomString(4)}`;
}

function generateRandomMessage(type: string): string {
  const messages = {
    match_update: 'A match score has been updated',
    schedule_change: 'Your schedule has been updated',
    result: 'Match completed. Winner: Test Player',
    announcement: 'Important announcement for your season'
  };
  return messages[type] || `Test notification message ${generateRandomString(4)}`;
}

// Default configuration
export const defaultNotificationConfig: NotificationConfig = {
  count: 3,                    // Number of notifications to create
  type: 'announcement',        // Type of notification
  markAsRead: false           // Whether to mark notifications as read after creation
};

// Extend the season test to add notificationFixture
export const test = seasonTest.extend<{
  notificationConfig: NotificationConfig;
  notificationFixture: NotificationFixture;
  seasonFixture: SeasonFixture;
}>({
  // Accept notificationConfig as an object with default values
  notificationConfig: [defaultNotificationConfig, { option: true }],

  // Create the notification fixture
  notificationFixture: async ({ request, isolatedUser, notificationConfig }, use) => {
    const page = isolatedUser.page;
    const createdNotifications: TestNotification[] = [];

    // First, ensure the user has notification preferences set to allow all notifications
    const prefsResponse = await request.put('/app/notifications/preferences', {
      data: {
        in_app_enabled: true,
        match_updates: true,
        schedule_changes: true,
        results: true,
        announcements: true
      }
    });
    
    if (!prefsResponse.ok()) {
      console.log(`Failed to set notification preferences: ${await prefsResponse.text()}`);
    }

    // Create notifications via development API
    for (let i = 0; i < notificationConfig.count; i++) {
      const notificationData = {
        userId: isolatedUser.userId,
        type: notificationConfig.type,
        title: notificationConfig.title || generateRandomTitle(notificationConfig.type),
        message: notificationConfig.message || generateRandomMessage(notificationConfig.type),
      };

      // Create the notification via API and ensure it's committed
      const response = await request.post('/dev/notifications', {
        data: notificationData
      });

      if (!response.ok()) {
        const errorText = await response.text();
        console.log(`Notification creation failed: ${errorText}`);
        throw new Error(`Failed to create notification: ${errorText}`);
      }

      const notification: TestNotification = await response.json();
      createdNotifications.push(notification);
      console.log(`Created notification ${notification.id} for user ${isolatedUser.userId}`);
    }

    // Navigate to the app to ensure page context is authenticated
    await page.goto('/app/home');

    // Mark some as read if requested
    if (notificationConfig.markAsRead) {
      for (const notification of createdNotifications) {
        await page.evaluate(async (notificationId) => {
          const response = await fetch(`/app/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          });
          if (!response.ok) {
            throw new Error(`Failed to mark notification ${notificationId} as read`);
          }
        }, notification.id);
        notification.is_read = true;
      }
    }

    // Wait a bit for database transactions to commit
    await page.waitForTimeout(500);

    // Get final unread count using the page context (which has proper authentication)
    const unreadCount = await page.evaluate(async () => {
      const response = await fetch('/app/notifications/count');
      if (!response.ok) {
        throw new Error(`API returned ${response.status}`);
      }
      const data = await response.json();
      return data.unread_count;
    });
    console.log(`Fixture: Created ${createdNotifications.length} notifications, API returns unread count: ${unreadCount}`);

    // Pass the created notifications to the test
    await use({ 
      notifications: createdNotifications, 
      unreadCount: unreadCount 
    });

    // Clean up the fixture: delete all created notifications
    for (const notification of createdNotifications) {
      await request.delete(`/dev/notifications/${notification.id}`);
    }
  },
});

export const expect = seasonTest.expect;