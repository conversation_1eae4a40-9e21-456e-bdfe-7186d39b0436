import { test, expect } from "../auth.setup";

test.describe("SSE Functionality", () => {
  test("should display SSE test section on development page", async ({
    page,
  }) => {
    await page.goto("/app/dev");

    // Check that the SSE test card exists
    await expect(page.locator("text=SSE Test")).toBeVisible();
    await expect(page.locator("#test-sse-btn")).toBeVisible();
    await expect(page.locator("#sse-messages")).toBeVisible();
  });

  test("should receive SSE messages when button is clicked", async ({
    page,
  }) => {
    // Navigate to development page
    await page.goto("/app/dev");

    // Wait for page to load
    await expect(page.locator("#sse-messages")).toBeVisible();
    await page.waitForTimeout(2000);

    // Click the test SSE button
    await page.click("#test-sse-btn");

    // Wait for any message to appear (raw JSON is fine for now)
    await expect(page.locator("#sse-messages")).toContainText(
      "Hello from SSE!",
      { timeout: 5000 },
    );

    // Verify the message contains expected data
    await expect(page.locator("#sse-messages")).toContainText("timestamp");
  });

  test("should handle multiple SSE test messages", async ({ isolatedUser }) => {
    const page = isolatedUser.page;

    await page.goto("/app/dev");
    await page.waitForTimeout(2000);

    // Send first message
    await page.click("#test-sse-btn");
    await expect(page.locator("#sse-messages")).toContainText(
      "Hello from SSE!",
      { timeout: 3000 },
    );

    // Get the first message timestamp to verify it changes
    const firstMessageContent = await page
      .locator("#sse-messages")
      .textContent();
    const firstTimestamp = firstMessageContent?.match(
      /"timestamp":"([^"]+)"/,
    )?.[1];

    // Wait a moment to ensure timestamp difference
    await page.waitForTimeout(1100);

    // Send second message
    await page.click("#test-sse-btn");
    await page.waitForTimeout(2000);

    // Verify the message content updated (most recent message displayed)
    await expect(page.locator("#sse-messages")).toContainText(
      "Hello from SSE!",
    );
    const secondMessageContent = await page
      .locator("#sse-messages")
      .textContent();
    const secondTimestamp = secondMessageContent?.match(
      /"timestamp":"([^"]+)"/,
    )?.[1];

    // Verify we got a new timestamp (indicating a new message was received)
    expect(secondTimestamp).toBeDefined();
    expect(secondTimestamp).not.toBe(firstTimestamp);
  });
});
