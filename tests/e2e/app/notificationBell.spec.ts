import { test } from "./notificationFixture";
import { test as seasonTest } from "./seasonFixture";
import { expect } from "@playwright/test";

test("user can open notifications modal with new notifications", async ({
  notificationFixture,
  isolatedUser,
}) => {
  // Use the isolated user's page instead of the worker-level page
  const page = isolatedUser.page;

  // Go to /app/home
  await page.goto("/app/home");

  // Handle cookie consent if it appears
  const cookieAccept = page.locator('button:has-text("Accept")');
  if (await cookieAccept.isVisible()) {
    await cookieAccept.click();
  }

  // Wait for the page to load
  await page.waitForTimeout(1000);

  // Check that the page title includes the notification count
  const expectedCount = notificationFixture.unreadCount;
  
  if (expectedCount > 0) {
    const expectedTitlePattern = expectedCount > 99 ? `(99+)` : `(${expectedCount})`;
    const escapedPattern = expectedTitlePattern.replace(/[()]/g, '\\$&');
    await expect(page).toHaveTitle(new RegExp(`.*${escapedPattern}.*`));
  }

  // Locate the visible notification bell button
  const notificationBells = page.locator(
    '[data-testid="notification-bell-button"]',
  );
  const count = await notificationBells.count();

  let notificationBell: any = null;
  for (let i = 0; i < count; i++) {
    const bell = notificationBells.nth(i);
    if (await bell.isVisible()) {
      notificationBell = bell;
      break;
    }
  }

  if (!notificationBell) {
    throw new Error("No visible notification bell found");
  }

  // Click on the bell to open notifications modal
  await notificationBell.click();

  // Wait for the modal to load
  await page.waitForTimeout(1000);

  // Verify that the modal content was loaded

  // Assert that the notification modal opened by looking for the "Mark all read" link
  // This confirms the notifications panel/modal is visible and functional
  const markAllReadElements = page.locator("text=Mark all read");
  let foundVisibleMarkAllRead = false;

  const markAllReadCount = await markAllReadElements.count();
  for (let i = 0; i < markAllReadCount; i++) {
    const element = markAllReadElements.nth(i);
    if (await element.isVisible()) {
      foundVisibleMarkAllRead = true;
      break;
    }
  }

  if (!foundVisibleMarkAllRead) {
    throw new Error(
      'Notification modal did not open - no visible "Mark all read" found',
    );
  }
});

test("user can open notification modal with new notifications from header", async ({
  notificationFixture,
  isolatedUser,
}) => {
  // Use the isolated user's page instead of the worker-level page
  const page = isolatedUser.page;

  // Go to /app/home
  await page.goto("/app/home");

  // Check that the page title includes the notification count
  const expectedCount = notificationFixture.unreadCount;
  
  if (expectedCount > 0) {
    const expectedTitlePattern = expectedCount > 99 ? `(99+)` : `(${expectedCount})`;
    const escapedPattern = expectedTitlePattern.replace(/[()]/g, '\\$&');
    await expect(page).toHaveTitle(new RegExp(`.*${escapedPattern}.*`));
  }

  // Close the sidebar to make the header notification bell visible
  const navbarToggleButtons = page.locator(
    '[data-testid="nav-collapse-button"]',
  );
  const toggleCount = await navbarToggleButtons.count();

  let visibleToggle: any = null;
  for (let i = 0; i < toggleCount; i++) {
    const toggle = navbarToggleButtons.nth(i);
    if (await toggle.isVisible()) {
      visibleToggle = toggle;
      break;
    }
  }

  if (visibleToggle) {
    await visibleToggle.click();
    // Wait for the sidebar to collapse and header to appear
    await page.waitForTimeout(500);
  }

  // Now target the notification bell in the header (which should be visible after sidebar closes)
  const headerBell = page.locator(
    'header [data-testid="notification-bell-button"]',
  );

  // Wait for it to be visible
  await headerBell.waitFor({ state: "visible", timeout: 5000 });

  const notificationBell = headerBell;

  // Click on the bell to open notifications modal
  await notificationBell.click();

  // Wait for the modal to load
  await page.waitForTimeout(1000);

  // Verify that the modal content was loaded

  // Assert that the notification modal opened by looking for the "Mark all read" link
  // This confirms the notifications panel/modal is visible and functional
  const markAllReadElements = page.locator("text=Mark all read");
  let foundVisibleMarkAllRead = false;

  const markAllReadCount = await markAllReadElements.count();
  for (let i = 0; i < markAllReadCount; i++) {
    const element = markAllReadElements.nth(i);
    if (await element.isVisible()) {
      foundVisibleMarkAllRead = true;
      break;
    }
  }

  if (!foundVisibleMarkAllRead) {
    throw new Error(
      'Notification modal did not open - no visible "Mark all read" found',
    );
  }
});

seasonTest(
  "user can click on notification bell with no notifications and see the standard 'no notifications' message",
  async ({ isolatedUser }) => {
    // Use the isolated user's page
    const page = isolatedUser.page;

    // Go to /app/home
    await page.goto("/app/home");

    // Check that the page title does NOT include notification count when there are no notifications
    await expect(page).toHaveTitle(/^(?!.*\(\d+\)).*$/); // Should not contain (number)

    // Locate the visible notification bell button
    const notificationBells = page.locator(
      '[data-testid="notification-bell-button"]',
    );
    const count = await notificationBells.count();

    let notificationBell: any = null;
    for (let i = 0; i < count; i++) {
      const bell = notificationBells.nth(i);
      if (await bell.isVisible()) {
        notificationBell = bell;
        break;
      }
    }

    // Click on the bell to open notifications modal
    await notificationBell.click();

    // Wait for the modal to load
    await page.waitForTimeout(1000);

    // Verify that the modal opened with the empty state message
    const noNotificationsMessage = page.locator("text=No notifications yet");

    await noNotificationsMessage.waitFor({ state: "visible", timeout: 5000 });

    // Also verify that "Mark all read" button is NOT visible since there are no notifications
    const markAllReadButton = page.locator("text=Mark all read");
    const markAllReadCount = await markAllReadButton.count();

    // Check if any "Mark all read" buttons are visible
    let foundVisibleMarkAllRead = false;
    for (let i = 0; i < markAllReadCount; i++) {
      const element = markAllReadButton.nth(i);
      if (await element.isVisible()) {
        foundVisibleMarkAllRead = true;
        break;
      }
    }
  },
);
