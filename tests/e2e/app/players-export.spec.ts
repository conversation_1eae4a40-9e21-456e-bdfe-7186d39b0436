import { test, expect } from "./playerFixture";

test.describe("Players Export", () => {
  test("user can export players to CSV", async ({ playerFixture }) => {
    const { page } = playerFixture;

    // Go to players page
    await page.goto("/app/players");

    // Wait for players table to load
    await expect(
      page.locator('[data-testid="players-table"]')
    ).toBeVisible();

    // Open actions dropdown 
    const actionsDropdown = page.locator('[data-testid="players-actions-dropdown"]');
    await actionsDropdown.click();

    // Check that export CSV button is visible
    const exportCsvButton = page.locator('[data-testid="export-players-csv-button"]');
    await expect(exportCsvButton).toBeVisible();
    await expect(exportCsvButton).toContainText("Export CSV");

    // Verify the export CSV button has the correct href attribute
    await expect(exportCsvButton).toHaveAttribute(
      "href",
      "/app/players/export/csv"
    );

    // Test that export CSV is accessible (but don't actually click it as it causes navigation)
    await expect(exportCsvButton).toBeEnabled();
  });

  test("export CSV button is not visible when no players exist", async ({ playerFixture }) => {
    // This test will use a fixture without players - we need to create this scenario
    const { page } = playerFixture;

    // For now, skip this test as it's complex to set up a fixture without players
    test.skip();
  });
});
