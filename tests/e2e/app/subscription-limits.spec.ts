import { test, expect } from "./limitFixture";

// Get limits from environment variables
const FREE_PLAYERS_LIMIT = parseInt(
  process.env.COACHPAD_FREE_USER_MAX_PLAYERS || "100",
);
const FREE_TEAMS_LIMIT = parseInt(
  process.env.COACHPAD_FREE_USER_MAX_TEAMS || "15",
);
const FREE_SEASONS_LIMIT = parseInt(
  process.env.COACHPAD_FREE_USER_MAX_SEASONS || "30",
);

test.describe("Subscription Limits", () => {
  test.describe("Banner Threshold Testing", () => {
    test.describe("Players at 60% usage", () => {
      test.use({
        playerConfig: { playerCount: Math.floor(FREE_PLAYERS_LIMIT * 0.6) }, // 60% of limit
      });

      test("shows 60% usage banner for players on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        // Look for limit banner with specific data-testid
        const limitBanner = page.locator(
          '[data-testid="limit-banner-players"]',
        );
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(
          Math.floor(FREE_PLAYERS_LIMIT * 0.6).toString(),
        );
        expect(bannerText).toContain(FREE_PLAYERS_LIMIT.toString());
        expect(bannerText).toContain("players");
      });
    });

    test.describe("Players at 80% usage", () => {
      test.use({
        playerConfig: { playerCount: Math.floor(FREE_PLAYERS_LIMIT * 0.8) }, // 80% of limit
      });

      test("shows 80% usage banner for players on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator(
          '[data-testid="limit-banner-players"]',
        );
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(
          Math.floor(FREE_PLAYERS_LIMIT * 0.8).toString(),
        );
        expect(bannerText).toContain(FREE_PLAYERS_LIMIT.toString());
        expect(bannerText).toContain("players");
      });
    });

    test.describe("Players at 100% usage", () => {
      test.use({
        playerConfig: { playerCount: FREE_PLAYERS_LIMIT }, // 100% of limit
      });

      test("shows 100% usage banner for players on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator(
          '[data-testid="limit-banner-players"]',
        );
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(FREE_PLAYERS_LIMIT.toString());
        expect(bannerText).toContain(FREE_PLAYERS_LIMIT.toString());
        expect(bannerText).toContain("players");
      });
    });

    test.describe("Teams at 80% usage", () => {
      test.use({
        teamConfig: { teamCount: Math.floor(FREE_TEAMS_LIMIT * 0.8) }, // 80% of limit
      });

      test("shows 80% usage banner for teams on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-teams"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(
          Math.floor(FREE_TEAMS_LIMIT * 0.8).toString(),
        );
        expect(bannerText).toContain(FREE_TEAMS_LIMIT.toString());
        expect(bannerText).toContain("teams");
      });
    });

    test.describe("Teams at 100% usage", () => {
      test.use({
        teamConfig: { teamCount: FREE_TEAMS_LIMIT }, // 100% of limit
      });

      test("shows 100% usage banner for teams on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-teams"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(FREE_TEAMS_LIMIT.toString());
        expect(bannerText).toContain(FREE_TEAMS_LIMIT.toString());
        expect(bannerText).toContain("teams");
      });
    });

    test.describe("Seasons at 60% usage", () => {
      test.use({
        seasonConfig: { seasonCount: Math.floor(FREE_SEASONS_LIMIT * 0.6) }, // 60% of limit
        playerConfig: { playerCount: 20 },
      });

      test("shows 60% usage banner for seasons on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator(
          '[data-testid="limit-banner-seasons"]',
        );
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(
          Math.floor(FREE_SEASONS_LIMIT * 0.6).toString(),
        );
        expect(bannerText).toContain(FREE_SEASONS_LIMIT.toString());
        expect(bannerText).toContain("seasons");
      });
    });

    test.describe("Seasons at 100% usage", () => {
      test.use({
        seasonConfig: { seasonCount: FREE_SEASONS_LIMIT }, // 100% of limit
        playerConfig: { playerCount: 20 },
      });

      test("shows 100% usage banner for seasons on settings page", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator(
          '[data-testid="limit-banner-seasons"]',
        );
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain(FREE_SEASONS_LIMIT.toString());
        expect(bannerText).toContain(FREE_SEASONS_LIMIT.toString());
        expect(bannerText).toContain("seasons");
      });
    });
  });

  test.describe("Limit Enforcement", () => {
    test.describe("Player limit at 100/100", () => {
      test.use({
        playerConfig: { playerCount: FREE_PLAYERS_LIMIT },
      });

      test("prevents creating 101st player when at 100/100 limit", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        // Go to players page and click add new player button
        await page.goto("/app/players");
        await page.waitForLoadState("networkidle");

        // Click the add new player button to open modal
        await page.click("#add-new-player-btn");
        await page.waitForLoadState("networkidle");

        // Fill the form in the modal
        const modal = page.locator("#playersNewForm");
        await modal
          .locator('input[name="name"]')
          .fill(`Player ${FREE_PLAYERS_LIMIT + 1}`);
        await modal
          .locator('input[name="email"]')
          .fill(`player${FREE_PLAYERS_LIMIT + 1}@test.com`);
        await modal.locator('button[type="submit"]').click();

        // Should see error toast
        const toast = page.locator("#toast-body-container");
        await expect(toast).toBeVisible();
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain("limit");
      });
    });

    test.describe("Season limit at 30/30", () => {
      test.use({
        seasonConfig: { seasonCount: FREE_SEASONS_LIMIT },
        playerConfig: { playerCount: 20 },
      });

      test("prevents creating 31st season when at 30/30 limit", async ({
        limitFixture,
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");

        // Step 1: Basic Information
        await page.fill(
          'input[name="name"]',
          `Season ${FREE_SEASONS_LIMIT + 1}`,
        );
        await page.click("#next-step-btn");
        await page.waitForTimeout(500);

        // Step 2: Schedule Setup - use default start date
        await page.click("#next-step-btn");
        await page.waitForTimeout(500);

        // Step 3: Season Settings
        await page.click('[data-testid="seasontype-button"]');
        await page.waitForTimeout(200);
        await page.click('[data-testid="seasontype-dropdown"] li:first-child');
        await page.waitForTimeout(200);

        await page.click('[data-testid="frequency-button"]');
        await page.waitForTimeout(200);
        await page.click('[data-testid="frequency-dropdown"] li:first-child');
        await page.waitForTimeout(200);

        await page.fill('input[name="amountOfTables"]', "1");
        await page.click("#next-step-btn");
        await page.waitForTimeout(500);

        // Step 4: Player Selection
        const playerCheckboxes = page.locator(
          'input[type="checkbox"][name="playerIds[]"]',
        );
        const checkboxCount = await playerCheckboxes.count();
        if (checkboxCount >= 2) {
          await playerCheckboxes.nth(0).check();
          await playerCheckboxes.nth(1).check();
        }
        await page.click("#next-step-btn");
        await page.waitForTimeout(500);

        // Step 5: Review & Confirm - NOW we can submit
        await page.click("#create-season-submit");
        await page.waitForTimeout(3000); // Wait for form submission

        // Check if we're still on the form page (indicating submission failed due to limit)
        // OR if we were redirected to an error page
        const currentUrl = page.url();
        console.log("Current URL after submission:", currentUrl);

        const isStillOnForm = currentUrl.includes("/seasons/new");
        const isOnErrorPage =
          currentUrl.includes("/error") || currentUrl.includes("/app-error");

        // Either we should still be on the form page OR on an error page
        const submissionBlocked = isStillOnForm || isOnErrorPage;
        expect(submissionBlocked).toBe(true);
      });
    });

    test.describe("Team limit at 100%", () => {
      test.use({
        teamConfig: { teamCount: FREE_TEAMS_LIMIT },
      });

      test("prevents creating team beyond limit", async ({ limitFixture }) => {
        const { page } = limitFixture;

        // Go to teams page and click add new team button
        await page.goto("/app/teams");
        await page.waitForLoadState("networkidle");

        // Click the add new team button to open modal
        await page.click("#add-new-team-btn");
        await page.waitForLoadState("networkidle");

        // Fill the form in the modal
        const modal = page.locator("#teamsNewForm");
        await modal
          .locator('input[name="name"]')
          .fill(`Team ${FREE_TEAMS_LIMIT + 1}`);

        // Submit the form and wait for response
        await modal.locator('button[type="submit"]').click();
        await page.waitForTimeout(2000); // Wait for HTMX response

        // Check if the modal is still open (indicating form submission failed)
        const modalStillOpen = await modal.isVisible();
        expect(modalStillOpen).toBe(true);

        // Check for any error indication in the modal or page
        const errorElements = page.locator(
          '.bg-red-100, .text-red-700, [data-testid*="error"], .error',
        );
        const hasError = (await errorElements.count()) > 0;
        expect(hasError).toBe(true);

        // Alternative: Check if the team was NOT created by checking the teams count
        // Use per_page=9001 to show all teams (pagination shows 10 by default)
        await page.goto("/app/teams?per_page=9001");
        await page.waitForLoadState("networkidle");

        // Count team rows - should still be at limit
        const teamRows = page.locator(
          '[data-testid^="team-row-"], .team-row, tbody tr',
        );
        const teamCount = await teamRows.count();
        expect(teamCount).toBe(FREE_TEAMS_LIMIT);
      });
    });

    test.describe("Multiple Resource Limits", () => {
      test.describe("Multiple resources near limits", () => {
        test.use({
          playerConfig: { playerCount: Math.floor(FREE_PLAYERS_LIMIT * 0.95) }, // 95% of limit
          seasonConfig: { seasonCount: FREE_SEASONS_LIMIT }, // 100% of limit
          teamConfig: { teamCount: Math.floor(FREE_TEAMS_LIMIT * 0.8) }, // 80% of limit
        });

        test("shows multiple limit banners when near limits on multiple resources", async ({
          limitFixture,
        }) => {
          const { page } = limitFixture;

          // Test on settings page where all limit banners are shown
          await page.goto("/app/settings/app");
          await page.waitForLoadState("networkidle");

          // Check for players limit banner
          const playersLimitBanner = page.locator(
            '[data-testid="limit-banner-players"]',
          );
          await expect(playersLimitBanner).toBeVisible();

          // Check for seasons limit banner
          const seasonsLimitBanner = page.locator(
            '[data-testid="limit-banner-seasons"]',
          );
          await expect(seasonsLimitBanner).toBeVisible();

          // Check for teams limit banner
          const teamsLimitBanner = page.locator(
            '[data-testid="limit-banner-teams"]',
          );
          await expect(teamsLimitBanner).toBeVisible();
        });
      });

      test.describe("All resources at limit", () => {
        test.use({
          playerConfig: { playerCount: FREE_PLAYERS_LIMIT }, // 100% of limit
          seasonConfig: { seasonCount: FREE_SEASONS_LIMIT }, // 100% of limit
          teamConfig: { teamCount: FREE_TEAMS_LIMIT }, // 100% of limit
        });

        test("prevents resource creation when multiple resources are at limit", async ({
          limitFixture,
        }) => {
          const { page } = limitFixture;

          // Try to create player - should fail (this one should show toast since players form targets toast container)
          await page.goto("/app/players");
          await page.waitForLoadState("networkidle");

          await page.click("#add-new-player-btn");
          await page.waitForLoadState("networkidle");

          const playerModal = page.locator("#playersNewForm");
          await playerModal
            .locator('input[name="name"]')
            .fill(`Player ${FREE_PLAYERS_LIMIT + 1}`);
          await playerModal
            .locator('input[name="email"]')
            .fill(`player${FREE_PLAYERS_LIMIT + 1}@test.com`);
          await playerModal.locator('button[type="submit"]').click();
          await page.waitForTimeout(1000);

          // For players, check toast since it's configured to show toasts
          const toast = page.locator("#toast-body-container");
          await expect(toast).toBeVisible();
          const toastText = await toast.textContent();
          expect(toastText?.toLowerCase()).toContain("limit");

          // Try to create team - should fail (check modal stays open)
          await page.goto("/app/teams");
          await page.waitForLoadState("networkidle");

          await page.click("#add-new-team-btn");
          await page.waitForLoadState("networkidle");

          const teamModal = page.locator("#teamsNewForm");
          await teamModal
            .locator('input[name="name"]')
            .fill(`Team ${FREE_TEAMS_LIMIT + 1}`);
          await teamModal.locator('button[type="submit"]').click();
          await page.waitForTimeout(2000);

          // Check modal is still open (indicating failure)
          const teamModalStillOpen = await teamModal.isVisible();
          expect(teamModalStillOpen).toBe(true);

          // Try to create season - should fail (check we stay on form page)
          await page.goto("/app/seasons/new");
          await page.waitForLoadState("networkidle");

          // Step 1: Basic Information
          await page.fill(
            'input[name="name"]',
            `Season ${FREE_SEASONS_LIMIT + 1}`,
          );
          await page.click("#next-step-btn");
          await page.waitForTimeout(500);

          // Step 2: Schedule Setup - use default start date
          await page.click("#next-step-btn");
          await page.waitForTimeout(500);

          // Step 3: Season Settings
          await page.click('[data-testid="seasontype-button"]');
          await page.waitForTimeout(200);
          await page.click(
            '[data-testid="seasontype-dropdown"] li:first-child',
          );
          await page.waitForTimeout(200);

          await page.click('[data-testid="frequency-button"]');
          await page.waitForTimeout(200);
          await page.click('[data-testid="frequency-dropdown"] li:first-child');
          await page.waitForTimeout(200);

          await page.fill('input[name="amountOfTables"]', "1");
          await page.click("#next-step-btn");
          await page.waitForTimeout(500);

          // Step 4: Player Selection
          const playerCheckboxes = page.locator(
            'input[type="checkbox"][name="playerIds[]"]',
          );
          const checkboxCount = await playerCheckboxes.count();
          if (checkboxCount >= 2) {
            await playerCheckboxes.nth(0).check();
            await playerCheckboxes.nth(1).check();
          }
          await page.click("#next-step-btn");
          await page.waitForTimeout(500);

          // Step 5: Review & Confirm - NOW we can submit
          await page.click("#create-season-submit");
          await page.waitForTimeout(2000);

          // Check we're still on the form page (indicating failure)
          const currentUrl = page.url();
          expect(currentUrl).toContain("/seasons/new");
        });
      });
    });
  });
});
