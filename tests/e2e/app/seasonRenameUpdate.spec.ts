import { test, expect } from "./seasonFixture";
import { TestDataGenerator } from "../utils";

test.describe("Season Rename Update Consistency", () => {
  test("season title in details view should update when renamed from navigation", async ({
    seasonFixture,
  }) => {
    const { seasons, page } = seasonFixture;
    const season = seasons[0];
    const seasonIdStr = season.id.toString();
    const newSeasonName = TestDataGenerator.string("Updated Season Name");

    // Step 1: Navigate to season details page
    await page.goto(`/app/seasons/${season.id}`);
    await page.waitForSelector('[data-testid="season-matches-title"]');

    // Verify the original season name is displayed in the details view
    await expect(page.locator('[data-testid="season-matches-title"]')).toContainText(season.name);

    // Step 3: Rename the season from the navigation
    const navBar = page.locator('[data-testid="sidebar-nav"]');
    
    // First hover over the season link to make the dropdown visible
    const seasonLink = navBar.locator(`[data-testid="nav-season-link-${seasonIdStr}"]`);
    await seasonLink.hover();
    
    // Now click the dropdown
    const seasonActionDropdown = navBar.locator(
      `[data-testid="season-action-dropdown-${seasonIdStr}"]`
    );
    await seasonActionDropdown.click();

    // Click rename option
    const renameButton = page.locator(`[data-testid="rename-season-${seasonIdStr}"]`);
    await expect(renameButton).toBeVisible();
    await renameButton.click();

    // Fill in new season name in the modal
    const modalContent = page.locator("#renameSeasonModal");
    await expect(modalContent).toBeVisible();
    
    const nameInput = modalContent.locator('input[name="name"]');
    await nameInput.clear();
    await nameInput.fill(newSeasonName);

    // Save the changes
    const saveButton = modalContent.locator('[data-testid="save-season-name"]');
    await saveButton.click();

    // Verify success toast appears
    const successToast = page.locator('[data-testid="rename-season-success-toast"]');
    await expect(successToast).toBeVisible();

    // Step 5: Check if the season title in details view has updated
    // This test will currently FAIL because the season details view doesn't update
    await expect(page.locator('[data-testid="season-matches-title"]')).toContainText(newSeasonName);
  });
});
