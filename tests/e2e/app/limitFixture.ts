// Import our authenticated test instead of the base test
import { test as base } from '../auth.setup';
import { TestDataGenerator } from "../testData";

// Define interfaces for type safety
interface PlayerData {
  name: string;
  email: string;
  preferredMatchGroup: number;
  emailNotificationsEnabled: boolean;
  userId: number;
}

interface Player {
  id: number;
  userId: number;
  teamId?: number;
  name: string;
  email?: string;
  pictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  preferredMatchGroup: number;
  isActive: boolean;
  emailNotificationsEnabled: boolean;
  emailReminderPreferences: string;
}

interface TeamData {
  name: string;
  description: string;
  userId: number;
}

interface Team {
  id: number;
  userId: number;
  name: string;
  description?: string;
  pictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

interface SeasonData {
  name: string;
  description?: string;
  userId: number;
  startDate: string;
  seasonType: string;
  frequency: string;
  playerIds?: number[];
  amountOfTables?: number;
}

interface Season {
  id: number;
  userId: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}



interface Match {
  id: number;
  userId: number;
  seasonId: number;
  title: string;
  description?: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface LimitFixture {
  players: Player[];
  teams: Team[];
  seasons: Season[];
  matches: Match[];
  page: any;
}

// Configuration interface for the limit fixture
interface LimitConfig {
  playerCount?: number;
  teamCount?: number;
  seasonCount?: number;
  matchCount?: number;
}

// Helper functions to generate random data
function generateRandomName(): string {
  return TestDataGenerator.player().name;
}

function generateRandomEmail(): string {
  return TestDataGenerator.player().email;
}

function generateRandomTeamName(): string {
  return TestDataGenerator.team().name;
}

function generateRandomDescription(): string {
  return TestDataGenerator.team().description;
}

function generateRandomSeasonName(): string {
  return TestDataGenerator.season().name;
}

// Extend base test by providing "limitFixture"
export const test = base.extend<{
  playerConfig: LimitConfig;
  teamConfig: LimitConfig;  
  seasonConfig: LimitConfig;
  matchConfig: LimitConfig;
  limitFixture: LimitFixture;
}>({
  // Accept configs with default values
  playerConfig: [{ playerCount: 0 }, { option: true }],
  teamConfig: [{ teamCount: 0 }, { option: true }],
  seasonConfig: [{ seasonCount: 0 }, { option: true }],
  matchConfig: [{ matchCount: 0 }, { option: true }],

  limitFixture: async ({ request, playerConfig, teamConfig, seasonConfig, matchConfig, isolatedUser }, use) => {
    const createdPlayers: Player[] = [];
    const createdTeams: Team[] = [];
    const createdSeasons: Season[] = [];
    const createdMatches: Match[] = [];

    try {
      // Create teams first
      const teamCount = teamConfig.teamCount || 0;
      for (let i = 0; i < teamCount; i++) {
        const teamData: TeamData = {
          name: generateRandomTeamName(),
          description: generateRandomDescription(),
          userId: isolatedUser.userId
        };

        const response = await request.post('/dev/teams', { data: teamData });

        if (!response.ok()) {
          console.log(`Team creation failed: ${await response.text()}`);
          throw new Error('Failed to create team');
        }

        const team: Team = await response.json();
        createdTeams.push(team);
      }

      // Create players
      const playerCount = playerConfig.playerCount || 0;
      for (let i = 0; i < playerCount; i++) {
        const playerData: PlayerData = {
          name: generateRandomName(),
          email: generateRandomEmail(),
          preferredMatchGroup: i + 1,
          emailNotificationsEnabled: i % 2 === 0,
          userId: isolatedUser.userId
        };

        const response = await request.post('/dev/players', { data: playerData });

        if (!response.ok()) {
          console.log(`Player creation failed: ${await response.text()}`);
          throw new Error('Failed to create player');
        }

        const player: Player = await response.json();
        createdPlayers.push(player);
      }

      // Create seasons
      const seasonCount = seasonConfig.seasonCount || 0;
      for (let i = 0; i < seasonCount; i++) {
        const seasonData: SeasonData = {
          name: generateRandomSeasonName(),
          description: generateRandomDescription(),
          userId: isolatedUser.userId,
          startDate: new Date(Date.now() + i * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Weekly intervals
          seasonType: 'pool',
          frequency: 'weekly'
        };

        const response = await request.post('/dev/seasons', { data: seasonData });

        if (!response.ok()) {
          console.log(`Season creation failed: ${await response.text()}`);
          throw new Error('Failed to create season');
        }

        const season: Season = await response.json();
        createdSeasons.push(season);
      }

      // Create matches if needed - matches are created through seasons with players
      const matchCount = matchConfig.matchCount || 0;
      if (matchCount > 0 && createdPlayers.length >= 2) {
        // For match creation, we'll create a single large season with many players
        // to generate the required number of matches efficiently
        // Each season with N players creates N*(N-1)/2 matches

        // Calculate how many players we need for the desired number of matches
        // Using the formula: matches = n*(n-1)/2, solve for n
        // n = (1 + sqrt(1 + 8*matches)) / 2
        const playersNeeded = Math.ceil((1 + Math.sqrt(1 + 8 * matchCount)) / 2);
        const actualPlayersToUse = Math.min(playersNeeded, createdPlayers.length);

        // Select players for the match season
        const seasonPlayers: number[] = [];
        for (let i = 0; i < actualPlayersToUse; i++) {
          seasonPlayers.push(createdPlayers[i].id);
        }

        const seasonData: SeasonData = {
          userId: isolatedUser.userId,
          name: `Match Generation Season`,
          startDate: new Date().toISOString().split('T')[0],
          seasonType: 'other',
          frequency: 'weekly',
          playerIds: seasonPlayers,
          amountOfTables: 1
        };

        const response = await request.post('/dev/seasons', { data: seasonData });

        if (!response.ok()) {
          console.log(`Season creation for matches failed: ${await response.text()}`);
          // Don't throw error, just log it - matches functionality might not be fully implemented
          console.log('Continuing without matches...');
        } else {
          const season: Season = await response.json();
          createdSeasons.push(season);

          // Get the matches created by this season
          const matchesResponse = await request.get(`/dev/seasons/${season.id}/matches`);
          if (matchesResponse.ok()) {
            const seasonMatches: Match[] = await matchesResponse.json();
            createdMatches.push(...seasonMatches);
          }
        }
      }

      // Pass the created data to the test
      await use({ 
        players: createdPlayers, 
        teams: createdTeams, 
        seasons: createdSeasons,
        matches: createdMatches,
        page: isolatedUser.page 
      });

    } finally {
      // Clean up all created resources
      // Note: Matches are automatically deleted when their parent seasons are deleted
      for (const season of createdSeasons) {
        await request.delete(`/dev/seasons/${season.id}`);
      }
      for (const player of createdPlayers) {
        await request.delete(`/dev/players/${player.id}`);
      }
      for (const team of createdTeams) {
        await request.delete(`/dev/teams/${team.id}`);
      }
    }
  },
});

export const expect = base.expect;
