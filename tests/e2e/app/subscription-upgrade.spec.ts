import { test, expect } from "../auth.setup";

test.describe("Subscription Upgrade", () => {
  test.beforeEach(async ({ isolatedUser }) => {
    // Start at the subscription settings page where upgrade functionality is available
    await isolatedUser.page.goto("/app/settings/subscription");
  });

  test("Free user sees subscription page with upgrade option", async ({
    isolatedUser,
  }) => {
    // Verify the subscription page loads correctly
    await expect(
      isolatedUser.page.locator('h1:has-text("Subscription")'),
    ).toBeVisible({ timeout: 10000 });

    // Verify both tier cards are visible
    await expect(
      isolatedUser.page.locator('div:has-text("Free")').first(),
    ).toBeVisible();

    await expect(
      isolatedUser.page.locator('div:has-text("Pro")').first(),
    ).toBeVisible();

    // Verify the Free card shows "Current Plan" badge
    await expect(
      isolatedUser.page.locator(
        'div:has-text("Free") .inline-flex:has-text("Current Plan")',
      ),
    ).toBeVisible();

    // Verify the Pro card shows "Upgrade to Pro" button
    await expect(
      isolatedUser.page.locator('button:has-text("Upgrade to Pro")'),
    ).toBeVisible();

    // Verify features are listed for both tiers
    await expect(
      isolatedUser.page.locator('text="Up to 20 players"'),
    ).toBeVisible();

    await expect(
      isolatedUser.page.locator('text="Unlimited players"'),
    ).toBeVisible();

    // Verify pricing information
    await expect(isolatedUser.page.locator('text="$9.99/month"')).toBeVisible();
  });

  test("User can successfully upgrade from free to pro", async ({
    isolatedUser,
  }) => {
    // Verify user starts with free tier and upgrade button is present
    const upgradeButton = isolatedUser.page.locator(
      'button[hx-get="/app/settings/upgrade"]:has-text("Upgrade to Pro")',
    );
    await expect(upgradeButton).toBeVisible({ timeout: 10000 });
    await expect(upgradeButton).toContainText("Upgrade to Pro");

    // Click the upgrade button to trigger the HTMX request
    await upgradeButton.click();

    // Wait for the payment modal to appear with proper timeout
    const modal = isolatedUser.page.locator(
      '[data-testid="subscriptionPaymentModal_modal"]',
    );
    await expect(modal).toBeVisible({ timeout: 15000 });

    // Wait for modal content to fully load
    await expect(modal.locator('h3:has-text("Upgrade to Pro")')).toBeVisible();

    // Wait for Stripe Elements to load in the modal with extended timeout
    const stripeFrame = isolatedUser.page
      .frameLocator('iframe[name^="__privateStripeFrame"]')
      .first();

    // Wait for card number field to be ready for input
    await expect(
      stripeFrame.locator('[placeholder="Card number"]'),
    ).toBeVisible({ timeout: 20000 });

    // Ensure the form is ready for interaction
    await isolatedUser.page.waitForTimeout(2000);

    // Fill in test credit card information
    // Using Stripe's test card number for successful payment
    await stripeFrame
      .locator('[placeholder="Card number"]')
      .fill("****************");
    await stripeFrame.locator('[placeholder="MM / YY"]').fill("12/30");
    await stripeFrame.locator('[placeholder="CVC"]').fill("123");
    await stripeFrame.locator('[placeholder="ZIP"]').fill("12345");

    // Verify the form accepted the input
    await expect(
      stripeFrame.locator('[placeholder="Card number"]'),
    ).toHaveValue("4242 4242 4242 4242");

    // Submit the payment form (button inside the modal)
    const submitButton = modal.locator(
      'button[type="submit"]:has-text("Upgrade to Pro")',
    );
    await expect(submitButton).toBeVisible();
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    const successMessage = modal.locator(
      'div[x-show="success"]:has-text("Payment successful! Welcome to Pro.")',
    );
    await expect(successMessage).toBeVisible({ timeout: 10000 });

    // Wait for modal to close (either automatically or due to completion)
    await expect(modal).toBeHidden({ timeout: 15000 });

    // Verify subscription status updated to Pro via SSE
    await expect(
      isolatedUser.page.locator('#subscription-cards-container div:has-text("Pro") .inline-flex:has-text("Current Plan")')
    ).toBeVisible();
    
    // Verify upgrade button is no longer visible and downgrade button appears
    await expect(
      isolatedUser.page.locator('button:has-text("Downgrade to Free")')
    ).toBeVisible();
  });

  test("User can open and close upgrade modal", async ({ isolatedUser }) => {
    // Verify the page loads correctly with proper timeout
    await expect(
      isolatedUser.page.locator('h1:has-text("Subscription")'),
    ).toBeVisible({ timeout: 10000 });

    // Verify upgrade button is present and clickable
    const upgradeButton = isolatedUser.page.locator(
      'button[hx-get="/app/settings/upgrade"]:has-text("Upgrade to Pro")',
    );
    await expect(upgradeButton).toBeVisible({ timeout: 10000 });
    await expect(upgradeButton).toBeEnabled();

    // Click button to open modal
    await upgradeButton.click();

    // Wait for the modal to appear with extended timeout
    const modal = isolatedUser.page.locator(
      '[data-testid="subscriptionPaymentModal_modal"]',
    );
    await expect(modal).toBeVisible({ timeout: 15000 });

    // Verify modal title is correct
    await expect(modal.locator('h3:has-text("Upgrade to Pro")')).toBeVisible({
      timeout: 5000,
    });

    // Verify modal content is loaded
    await expect(modal.locator("#card-element")).toBeVisible({
      timeout: 10000,
    });

    // Close the modal by clicking the X button
    const closeButton = modal.locator("button").filter({ hasText: "" }).first(); // The X button has no text
    await closeButton.click();

    // Verify modal is hidden with proper timeout
    await expect(modal).toBeHidden({ timeout: 10000 });

    // Verify the original upgrade button is still visible after closing modal
    await expect(upgradeButton).toBeVisible();
  });

  test("Stripe payment form loads correctly in modal", async ({
    isolatedUser,
  }) => {
    // Open the upgrade modal
    const upgradeButton = isolatedUser.page.locator(
      'button[hx-get="/app/settings/upgrade"]:has-text("Upgrade to Pro")',
    );
    await expect(upgradeButton).toBeVisible({ timeout: 10000 });
    await upgradeButton.click();

    // Wait for the modal to appear with proper timeout
    const modal = isolatedUser.page.locator(
      '[data-testid="subscriptionPaymentModal_modal"]',
    );
    await expect(modal).toBeVisible({ timeout: 15000 });

    // Verify modal content loads
    await expect(modal.locator('h3:has-text("Upgrade to Pro")')).toBeVisible();
    await expect(modal.locator("#card-element")).toBeVisible({
      timeout: 10000,
    });

    // Verify Stripe Elements loads correctly with extended timeout
    const stripeFrame = isolatedUser.page
      .frameLocator('iframe[name^="__privateStripeFrame"]')
      .first();
    await expect(
      stripeFrame.locator('[placeholder="Card number"]'),
    ).toBeVisible({ timeout: 20000 });

    // Verify payment form elements are present and enabled
    const submitButton = modal.locator(
      'button[type="submit"]:has-text("Upgrade to Pro")',
    );
    await expect(submitButton).toBeVisible();
    await expect(submitButton).toBeEnabled();

    // Allow time for Stripe Elements to fully initialize
    await isolatedUser.page.waitForTimeout(2000);

    // Try filling in the form (but don't submit)
    await stripeFrame
      .locator('[placeholder="Card number"]')
      .fill("****************");
    await stripeFrame.locator('[placeholder="MM / YY"]').fill("12/30");
    await stripeFrame.locator('[placeholder="CVC"]').fill("123");
    await stripeFrame.locator('[placeholder="ZIP"]').fill("12345");

    // Verify the form accepted the input with proper formatting
    await expect(
      stripeFrame.locator('[placeholder="Card number"]'),
    ).toHaveValue("4242 4242 4242 4242");

    // Verify that the submit button remains enabled after form input
    await expect(submitButton).toBeEnabled();
  });

  test("Pro user can downgrade to free", async ({ isolatedUser }) => {
    // First upgrade the user to pro (reusing upgrade logic)
    const upgradeButton = isolatedUser.page.locator(
      'button[hx-get="/app/settings/upgrade"]:has-text("Upgrade to Pro")',
    );
    await expect(upgradeButton).toBeVisible({ timeout: 10000 });
    await upgradeButton.click();

    // Complete the upgrade process
    const modal = isolatedUser.page.locator(
      '[data-testid="subscriptionPaymentModal_modal"]',
    );
    await expect(modal).toBeVisible({ timeout: 15000 });

    const stripeFrame = isolatedUser.page
      .frameLocator('iframe[name^="__privateStripeFrame"]')
      .first();
    await expect(
      stripeFrame.locator('[placeholder="Card number"]'),
    ).toBeVisible({ timeout: 20000 });

    await isolatedUser.page.waitForTimeout(2000);
    await stripeFrame
      .locator('[placeholder="Card number"]')
      .fill("****************");
    await stripeFrame.locator('[placeholder="MM / YY"]').fill("12/30");
    await stripeFrame.locator('[placeholder="CVC"]').fill("123");
    await stripeFrame.locator('[placeholder="ZIP"]').fill("12345");

    const submitButton = modal.locator(
      'button[type="submit"]:has-text("Upgrade to Pro")',
    );
    await submitButton.click();

    // Wait for upgrade to complete
    const successMessage = modal.locator(
      'div[x-show="success"]:has-text("Payment successful! Welcome to Pro.")',
    );
    await expect(successMessage).toBeVisible({ timeout: 10000 });
    await expect(modal).toBeHidden({ timeout: 15000 });

    // Verify user is now on Pro tier
    await expect(
      isolatedUser.page.locator('#subscription-cards-container div:has-text("Pro") .inline-flex:has-text("Current Plan")')
    ).toBeVisible();

    // Now test the downgrade functionality
    const downgradeButton = isolatedUser.page.locator(
      'button[hx-post="/app/settings/downgrade"]:has-text("Downgrade to Free")'
    );
    await expect(downgradeButton).toBeVisible({ timeout: 10000 });
    
    // Handle the confirmation dialog that appears when clicking downgrade
    isolatedUser.page.on('dialog', async dialog => {
      await dialog.accept();
    });
    
    await downgradeButton.click();

    // Wait for downgrade to complete and verify user is back on Free tier
    // The SSE should update the subscription cards
    await expect(
      isolatedUser.page.locator('#subscription-cards-container div:has-text("Free") .inline-flex:has-text("Current Plan")')
    ).toBeVisible({ timeout: 15000 });

    // Verify upgrade button is available again  
    await expect(
      isolatedUser.page.locator('button[hx-get="/app/settings/upgrade"]:has-text("Upgrade to Pro")')
    ).toBeVisible({ timeout: 10000 });

    // Verify downgrade button is no longer visible
    await expect(
      isolatedUser.page.locator('button:has-text("Downgrade to Free")')
    ).toBeHidden();
  });
});
