import { test, expect } from '../auth.setup';

test.describe('Navigation State Synchronization', () => {
  test('active sidebar link should reflect the current page after rapid clicking', async ({ page }) => {
    // GIVEN the user is on a page with the main sidebar navigation
    await page.goto('/app/home');
    const sidebar = page.getByTestId('sidebar-nav');
    await expect(sidebar).toBeVisible();

    // WHEN the user clicks on two different navigation links in quick succession
    await sidebar.getByRole('link', { name: 'Players' }).click();
    await sidebar.getByRole('link', { name: 'Teams' }).click();

    // THEN after the navigation settles, the active link should match the current page's URL.
    // Wait for the visually active link to update (aria-current="page")
    await expect(sidebar.locator('a[aria-current="page"]')).toBeVisible();
    const currentPath = new URL(page.url()).pathname;
    expect(['/app/home', '/app/players', '/app/teams']).toContain(currentPath);
    const activeLink = sidebar.locator('a[aria-current="page"]');
    await expect(activeLink).toHaveAttribute('href', currentPath);
  });

  test('active sidebar link is correct after navigating away before the first page loads', async ({ page }) => {
    // GIVEN the user is on the home page
    await page.goto('/app/home');
    const sidebar = page.getByTestId('sidebar-nav');
    await expect(sidebar).toBeVisible();

    // WHEN the user clicks on a link to start a navigation
    await sidebar.getByRole('link', { name: 'Players' }).click();

    // AND immediately navigates to a different page before the first one can complete
    await page.goto('/app/teams');

    // THEN after the navigation settles, the active link should match the current page's URL.
    await expect(sidebar.locator('a[aria-current="page"]')).toBeVisible();
    const currentPath = new URL(page.url()).pathname;
    expect(currentPath).toBe('/app/teams');
    const activeLink = sidebar.locator('a[aria-current="page"]');
    await expect(activeLink).toHaveAttribute('href', '/app/teams');
  });

  test('the correct sidebar link is active after a rapid sequence of clicks', async ({ page }) => {
    // GIVEN the user is on a page with the main sidebar navigation
    await page.goto('/app/home');
    const sidebar = page.getByTestId('sidebar-nav');
    await expect(sidebar).toBeVisible();

    // WHEN the user performs a sequence of rapid navigation clicks
    const clickSequence = [
      { name: 'Players', href: '/app/players' },
      { name: 'Home', href: '/app/home' },
      { name: 'Teams', href: '/app/teams' },
      { name: 'Spending', href: '/app/spending' },
      { name: 'Home', href: '/app/home' },
    ];

    for (const link of clickSequence) {
      await sidebar.getByRole('link', { name: link.name }).click();
      // A very short delay to simulate rapid user interaction without being flaky
      await page.waitForTimeout(25);
    }

    // THEN after the navigation settles, the active link should match the current page's URL.
    await expect(sidebar.locator('a[aria-current="page"]')).toBeVisible();
    const currentPath = new URL(page.url()).pathname;
    const expectedPaths = clickSequence.map(link => link.href);
    expect(expectedPaths).toContain(currentPath);
    const activeLink = sidebar.locator('a[aria-current="page"]');
    await expect(activeLink).toHaveAttribute('href', currentPath);
  });
});
