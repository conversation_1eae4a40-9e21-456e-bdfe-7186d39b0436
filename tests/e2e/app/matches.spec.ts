import { test, expect } from "./seasonFixture";
import { v4 as uuidv4 } from "uuid";
import { TestDataGenerator } from "../utils";

test("can change player 1 on a match", async ({
  seasonFixture,
}) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Find the first match row and extract its match id from the hidden input
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Get the current player 1 name
  const player1NameCell = firstRow.locator(
    `[data-testid="player-1-dropdown-${matchId}"]`,
  );
  const oldPlayerName = (await player1NameCell.textContent())?.trim();

  // Open the player 1 dropdown
  await player1NameCell.click();

  // Click the "Change Player" option in the dropdown
  const changeBtn = page.locator(
    `[data-testid="player-1-dropdown-${matchId}-change"]`,
  );
  await changeBtn.click();

  const modal = page.getByTestId("player1SelectForMatch_modal");
  await expect(modal).toBeVisible();

  // Wait for modal content to fully load
  await page.waitForTimeout(500);

  // Find all player options in the modal
  const options = await modal.locator('[data-testid^="player-select-option-"]').all();
  // Filter out the current player
  const filteredOptions: import('@playwright/test').Locator[] = [];
  for (const option of options) {
    const text = (await option.textContent())?.trim();
    if (text && text !== oldPlayerName) {
      filteredOptions.push(option as import('@playwright/test').Locator);
    }
  }
  expect(filteredOptions.length).toBeGreaterThan(0);

  // Pick a random new player from the available options
  const randomIdx = TestDataGenerator.number(filteredOptions.length);
  const newOption = filteredOptions[randomIdx];
  const newPlayerName = (await newOption.textContent())?.trim();
  await newOption.click();

  // Wait for the HTMX request to complete
  await page.waitForLoadState("networkidle");
  await page.waitForTimeout(1000);

  await expect(modal).not.toBeVisible();

  // Assert that the player 1 name in the table has changed
  await expect(player1NameCell).toContainText(newPlayerName!);
});

test("can change player 2 on a match", async ({
  seasonFixture,
}) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Find the first match row and extract its match id from the hidden input
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Get the current player 2 name
  const player2NameCell = firstRow.locator(
    `[data-testid="player-2-dropdown-${matchId}"]`,
  );
  const oldPlayerName = (await player2NameCell.textContent())?.trim();

  // Open the player 2 dropdown
  await player2NameCell.click();

  // Click the "Change Player" option in the dropdown
  const changeBtn = page.locator(
    `[data-testid="player-2-dropdown-${matchId}-change"]`,
  );
  await changeBtn.click();

  const modal = page.getByTestId("player2SelectForMatch_modal");
  await expect(modal).toBeVisible();

  // Wait for modal content to fully load
  await page.waitForTimeout(500);

  // Find all player options in the modal

  const options = await modal.locator('[data-testid^="player-select-option-"]').all();
  // Filter out the current player
  const filteredOptions: import('@playwright/test').Locator[] = [];
  for (const option of options) {
    const text = (await option.textContent())?.trim();
    if (text && text !== oldPlayerName) {
      filteredOptions.push(option as import('@playwright/test').Locator);
    }
  }
  expect(filteredOptions.length).toBeGreaterThan(0);

  // Pick a random new player from the available options
  const randomIdx = TestDataGenerator.number(filteredOptions.length);
  const newOption = filteredOptions[randomIdx];
  const newPlayerName = (await newOption.textContent())?.trim();
  await newOption.click();

  // Wait for the HTMX request to complete
  await page.waitForLoadState("networkidle");
  await page.waitForTimeout(1000);

  // Modal should close
  await expect(modal).not.toBeVisible();

  await expect(player2NameCell).toBeVisible();
  // Assert that the player 2 name in the table has changed
  await expect(player2NameCell).toContainText(newPlayerName!);
});

test.describe("Custom Matches Column", () => {
  test.use({
    customMatchColumns: [
      {
        name: `Custom Field ${uuidv4()}`,
        fieldType: "text",
      },
    ],
  });

  test("displays custom column in matches table", async ({
    seasonFixture,
    customMatchColumns,
  }) => {
    const { seasons, page } = seasonFixture;

    // Go directly to the current season's matches page
    await page.goto(`/app/seasons/${seasons[0].id}`);

    // Wait for the table to load
    await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

    // Check if a custom column header is present
    const headers = page.locator("table thead tr th");
    const headerCount = await headers.count();
    // Standard columns are 6 (date, player1, points1, player2, points2, group) - winner column was replaced by crown icons
    const hasCustomColumn = headerCount > 6;
    expect(hasCustomColumn).toBe(true); // Assert that a custom column exists due to fixture setup

    // Use the custom column name from the fixture for assertion
    const customColumnName = customMatchColumns[0].name;

    // Check that at least one header contains the custom column name (position-independent)
    const headerTexts = await headers.allTextContents();
    const hasCustomColumnHeader = headerTexts.some(
      (text) => text === customColumnName,
    );
    expect(hasCustomColumnHeader).toBe(true);

    // Check if input fields for the custom column are rendered in match rows
    const rows = page.locator("table tbody tr:visible");
    const rowCount = await rows.count();
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const customCell = row.locator("td").nth(7); // Corresponding cell for first custom column (after 7 standard columns including hidden cell)
      await expect(customCell.locator("input")).toBeVisible();
    }
   });
});

// Tests for the match new player selector modal UI
test.describe('Match New Player Selector Modal', () => {
  test("can see players on page 2", async ({ seasonFixture}) => {
    const { seasons, page } = seasonFixture;
    await page.goto(`/app/seasons/${seasons[0].id}`);
    const firstRow = page.locator('table tbody tr:visible').first();
    const matchIdAttr = await firstRow.locator('[data-testid^="match-id-"]').getAttribute('data-testid');
    const matchId = matchIdAttr!.replace('match-id-', '');

    // Open player 1 modal
    const player1NameCell = firstRow.locator(`[data-testid="player-1-dropdown-${matchId}"]`);
    await player1NameCell.click();
    const changeBtn = page.locator(`[data-testid="player-1-dropdown-${matchId}-change"]`);
    await changeBtn.click();
    const modal = page.getByTestId('player1SelectForMatch_modal');
    await expect(modal).toBeVisible();

    // Get the list of visible players on page 1
    const playerOptionsPage1 = await modal.locator('[data-testid^="player-select-option-"]').allTextContents();

    // Click the next page button (pagination)
    const nextPageBtn = modal.locator('#next-page-btn');
    await expect(nextPageBtn).toBeVisible();
    await nextPageBtn.click();
    // Wait for the modal content to update
    await page.waitForTimeout(800); // Allow for HTMX update

    // Get the list of visible players on page 2
    const playerOptionsPage2 = await modal.locator('[data-testid^="player-select-option-"]').allTextContents();

    // There should be a difference between page 1 and page 2
    expect(playerOptionsPage2.length).toBeGreaterThan(0);
    expect(playerOptionsPage1).not.toEqual(playerOptionsPage2);
  });

  test("can search players using filter bar", async ({ seasonFixture }) => {
    const { seasons, page } = seasonFixture;
    await page.goto(`/app/seasons/${seasons[0].id}`);
    const firstRow = page.locator('table tbody tr:visible').first();
    const matchIdAttr = await firstRow.locator('[data-testid^="match-id-"]').getAttribute('data-testid');
    const matchId = matchIdAttr!.replace('match-id-', '');

    // Open player 1 modal
    const player1NameCell = firstRow.locator(`[data-testid="player-1-dropdown-${matchId}"]`);
    await player1NameCell.click();
    const changeBtn = page.locator(`[data-testid="player-1-dropdown-${matchId}-change"]`);
    await changeBtn.click();
    const modal = page.getByTestId('player1SelectForMatch_modal');
    await expect(modal).toBeVisible();

    // Get all player options currently in the DOM
    const playerOptions = await modal.locator('[data-testid^="player-select-option-"]').all();
    expect(playerOptions.length).toBeGreaterThan(0);

    // Pick a random player
    const randomIdx = TestDataGenerator.number(playerOptions.length);
    const randomOption = playerOptions[randomIdx];
    const playerName = (await randomOption.textContent())?.trim();
    expect(playerName).toBeTruthy();

    // Type the player name in the search input
    const searchInput = modal.locator('[data-testid="player-search-input"]');
    await searchInput.fill(playerName!);
    // Wait for the HTMX update
    await page.waitForTimeout(800);

    // Get all player options after search
    const filteredOptions = await modal.locator('[data-testid^="player-select-option-"]').all();
    expect(filteredOptions.length).toBeGreaterThan(0);
    for (const option of filteredOptions) {
      const filteredName = (await option.textContent())?.trim();
      expect(filteredName).toContain(playerName!);
    }
  });

});

test("can change player 1 score on a match", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Wait for the table to load
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Find the first match row and extract its match id from the hidden input
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Locate the player1 points input field
  const player1PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player1-points"]`,
  );
  await expect(player1PointsInput).toBeVisible();

  // Clear and enter a new score
  const newScore = "15";
  await player1PointsInput.clear();
  await player1PointsInput.fill(newScore);

  // Trigger the update by blurring the input (this should call updateMatch)
  await player1PointsInput.blur();

  // Wait for the HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Verify that the input still has the new value
  await expect(player1PointsInput).toHaveValue(newScore);
});

test("can change player 2 score on a match", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Wait for the table to load
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Find the first match row and extract its match id from the hidden input
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Locate the player2 points input field
  const player2PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player2-points"]`,
  );
  await expect(player2PointsInput).toBeVisible();

  // Clear and enter a new score
  const newScore = "18";
  await player2PointsInput.clear();
  await player2PointsInput.fill(newScore);

  // Trigger the update by blurring the input (this should call updateMatch)
  await player2PointsInput.blur();

  // Wait for the HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Verify that the input still has the new value
  await expect(player2PointsInput).toHaveValue(newScore);
});

test("can change match date", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Wait for the table to load
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Find the first match row and extract its match id from the hidden input
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Locate the match date input field by test ID
  const matchDateInput = firstRow.locator(
    `input[data-testid="match-${matchId}-date-picker"]`,
  );
  await expect(matchDateInput).toBeVisible();

  // Click the date input to open the datepicker
  await matchDateInput.click();

  // Wait for the datepicker portal to contain the datepicker for this specific match
  const datepickerPortal = page.locator("#datepicker-portal");
  const specificDatepicker = datepickerPortal.locator(
    `[data-teleport-id="match-${matchId}"]`,
  );
  await expect(specificDatepicker).toBeVisible();

  // Instead of complex navigation, let's work with the current month/year and pick an available day
  // Get the current year and month displayed in the datepicker
  const currentYear = await specificDatepicker
    .locator('[data-testid="datepicker-year"]')
    .textContent();
  const currentMonth = await specificDatepicker
    .locator('[data-testid="datepicker-month-name"]')
    .textContent();

  // Simply click on day 15 which should be available in any month
  await specificDatepicker.locator('[data-testid="datepicker-day-15"]').click();

  // Wait for the HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Verify that the input has the new value (format: YYYY-MM-dd)
  // We selected day 15, so construct the expected date from the year and month we captured
  const yearNum = parseInt(currentYear!);
  const monthNum =
    currentMonth === "January"
      ? 1
      : currentMonth === "February"
        ? 2
        : currentMonth === "March"
          ? 3
          : currentMonth === "April"
            ? 4
            : currentMonth === "May"
              ? 5
              : currentMonth === "June"
                ? 6
                : currentMonth === "July"
                  ? 7
                  : currentMonth === "August"
                    ? 8
                    : currentMonth === "September"
                      ? 9
                      : currentMonth === "October"
                        ? 10
                        : currentMonth === "November"
                          ? 11
                          : 12;

  const expectedDate = `${yearNum}-${monthNum.toString().padStart(2, "0")}-15`;
  await expect(matchDateInput).toHaveValue(expectedDate);
});

test("crown icon displays for winner", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Wait for the table to load
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Find the first match row and extract its match id
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Set Player 1 score higher than Player 2
  const player1PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player1-points"]`,
  );
  const player2PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player2-points"]`,
  );

  await player1PointsInput.clear();
  await player1PointsInput.fill("15");
  await player1PointsInput.blur();

  await player2PointsInput.clear();
  await player2PointsInput.fill("10");
  await player2PointsInput.blur();

  // Wait for HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Wait for the updateMatch event to trigger a page refresh
  await page.waitForTimeout(2000);

  // Check that crown icon appears next to Player 1 (winner)
  const player1Crown = firstRow.locator(
    `[data-testid="player-1-crown-${matchId}"]`,
  );
  const player2Crown = firstRow.locator(
    `[data-testid="player-2-crown-${matchId}"]`,
  );
  await expect(player1Crown).toBeVisible();

  // Check that no crown appears next to Player 2 (loser)
  await expect(player2Crown).not.toBeVisible();
});

test("no crown for ties or incomplete matches", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Wait for the table to load
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Find the first match row and extract its match id
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Test Case 2a: Tie scenario - set both players to same score
  const player1PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player1-points"]`,
  );
  const player2PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player2-points"]`,
  );

  await player1PointsInput.clear();
  await player1PointsInput.fill("10");
  await player1PointsInput.blur();

  await player2PointsInput.clear();
  await player2PointsInput.fill("10");
  await player2PointsInput.blur();

  // Wait for HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Wait for the updateMatch event to trigger a page refresh (there's a 200ms delay + processing time)
  await page.waitForTimeout(1000);

  // Wait for any additional HTMX request triggered by updateMatch event to complete
  await page.waitForLoadState("networkidle");

  // Re-get the first row after potential page refresh
  const refreshedFirstRow = page.locator("table tbody tr:visible").first();

  // Check that no crown appears for either player in a tie
  const player1Crown = refreshedFirstRow.locator(
    `[data-testid="player-1-crown-${matchId}"]`,
  );
  const player2Crown = refreshedFirstRow.locator(
    `[data-testid="player-2-crown-${matchId}"]`,
  );

  await expect(player1Crown).not.toBeVisible();
  await expect(player2Crown).not.toBeVisible();

  // Test Case 2b: Incomplete match - clear one player's score
  const refreshedPlayer1PointsInput = refreshedFirstRow.locator(
    `input[data-testid="match-${matchId}-player1-points"]`,
  );
  await refreshedPlayer1PointsInput.clear();
  await refreshedPlayer1PointsInput.blur();

  // Wait for HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Wait for the updateMatch event to trigger a page refresh
  await page.waitForTimeout(1000);

  // Wait for any additional HTMX request triggered by updateMatch event to complete
  await page.waitForLoadState("networkidle");

  // Re-get the first row after potential page refresh
  const finalFirstRow = page.locator("table tbody tr:visible").first();
  const finalPlayer1Crown = finalFirstRow.locator(
    `[data-testid="player-1-crown-${matchId}"]`,
  );
  const finalPlayer2Crown = finalFirstRow.locator(
    `[data-testid="player-2-crown-${matchId}"]`,
  );

  // Check that no crown appears for either player when match is incomplete
  await expect(finalPlayer1Crown).not.toBeVisible();
  await expect(finalPlayer2Crown).not.toBeVisible();
});

test("clean layout without winner column", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  // Go directly to the current season's matches page
  await page.goto(`/app/seasons/${seasons[0].id}`);

  // Wait for the table to load
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Check that the winner column header is not visible
  const headers = page.locator("table thead tr th");
  const headerTexts = await headers.allTextContents();

  // The winner column should not be present (no header with "Winner" text)
  const hasWinnerColumn = headerTexts.some(
    (text) =>
      text.toLowerCase().includes("winner") ||
      text.toLowerCase().includes("gewinner"),
  );
  expect(hasWinnerColumn).toBe(false);

  // Verify that player dropdowns are still functional
  const firstRow = page.locator("table tbody tr:visible").first();
  const matchIdAttr = await firstRow
    .locator('[data-testid^="match-id-"]')
    .getAttribute("data-testid");
  const matchId = matchIdAttr!.replace("match-id-", "");

  // Test that player 1 dropdown is functional
  const player1Dropdown = firstRow.locator(
    `[data-testid="player-1-dropdown-${matchId}"]`,
  );
  await expect(player1Dropdown).toBeVisible();

  // Test that player 2 dropdown is functional
  const player2Dropdown = firstRow.locator(
    `[data-testid="player-2-dropdown-${matchId}"]`,
  );
  await expect(player2Dropdown).toBeVisible();

  // Verify that crown icons don't break the layout
  // Set a winner to see crown in context
  const player1PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player1-points"]`,
  );
  const player2PointsInput = firstRow.locator(
    `input[data-testid="match-${matchId}-player2-points"]`,
  );

  await player1PointsInput.clear();
  await player1PointsInput.fill("15");
  await player1PointsInput.blur();

  await player2PointsInput.clear();
  await player2PointsInput.fill("10");
  await player2PointsInput.blur();

  // Wait for HTMX request to complete
  await page.waitForLoadState("networkidle");

  // Wait for the updateMatch event to trigger a page refresh
  await page.waitForTimeout(2000);

  // Verify crown appears and layout is intact
  const player1Crown = firstRow.locator(
    `[data-testid="player-1-crown-${matchId}"]`,
  );
  await expect(player1Crown).toBeVisible();

  // Verify dropdowns are still clickable and functional after crown appears
  await expect(player1Dropdown).toBeVisible();
  await expect(player2Dropdown).toBeVisible();
});

test("can sort matches by player 1 pts", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  await page.goto(`/app/seasons/${seasons[0].id}`);
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Click on player 1 pts header to sort
  const player1PtsHeader = page
    .locator("th button")
    .filter({ hasText: /Pts|Points/ })
    .first();
  await player1PtsHeader.click();
  await page.waitForTimeout(500);

  // Read all displayed rows and extract player 1 points
  const tableRows = page.locator("table tbody tr:visible");
  const rowCount = await tableRows.count();
  const points: number[] = [];

  for (let i = 0; i < rowCount; i++) {
    const row = tableRows.nth(i);
    const matchIdAttr = await row
      .locator('[data-testid^="match-id-"]')
      .getAttribute("data-testid");
    const matchId = matchIdAttr!.replace("match-id-", "");

    const pointsInput = row.locator(
      `input[data-testid="match-${matchId}-player1-points"]`,
    );
    const pointsValue = await pointsInput.inputValue();
    points.push(pointsValue === "" ? 0 : parseInt(pointsValue) || 0);
  }

  // Verify that they are sorted properly (ascending or descending)
  const isAscending = points.every((val, i) => i === 0 || points[i - 1] <= val);
  const isDescending = points.every(
    (val, i) => i === 0 || points[i - 1] >= val,
  );

  expect(isAscending || isDescending).toBe(true);
});

test("can sort matches by player 2 pts", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  await page.goto(`/app/seasons/${seasons[0].id}`);
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Click on player 2 pts header to sort
  const player2PtsHeader = page
    .locator("th button")
    .filter({ hasText: /Pts|Points/ })
    .nth(1);
  await player2PtsHeader.click();
  await page.waitForTimeout(500);

  // Read all displayed rows and extract player 2 points
  const tableRows = page.locator("table tbody tr:visible");
  const rowCount = await tableRows.count();
  const points: number[] = [];

  for (let i = 0; i < rowCount; i++) {
    const row = tableRows.nth(i);
    const matchIdAttr = await row
      .locator('[data-testid^="match-id-"]')
      .getAttribute("data-testid");
    const matchId = matchIdAttr!.replace("match-id-", "");

    const pointsInput = row.locator(
      `input[data-testid="match-${matchId}-player2-points"]`,
    );
    const pointsValue = await pointsInput.inputValue();
    points.push(pointsValue === "" ? 0 : parseInt(pointsValue) || 0);
  }

  // Verify that they are sorted properly (ascending or descending)
  const isAscending = points.every((val, i) => i === 0 || points[i - 1] <= val);
  const isDescending = points.every(
    (val, i) => i === 0 || points[i - 1] >= val,
  );

  expect(isAscending || isDescending).toBe(true);
});

test("can sort matches by date", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  await page.goto(`/app/seasons/${seasons[0].id}`);
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Click on date header to sort
  const dateHeader = page.locator("th button").filter({ hasText: /Date/ });
  await dateHeader.click();
  await page.waitForTimeout(500);

  // Read all displayed rows and extract dates
  const tableRows = page.locator("table tbody tr:visible");
  const rowCount = await tableRows.count();
  const dates: string[] = [];

  for (let i = 0; i < rowCount; i++) {
    const row = tableRows.nth(i);
    const matchIdAttr = await row
      .locator('[data-testid^="match-id-"]')
      .getAttribute("data-testid");
    const matchId = matchIdAttr!.replace("match-id-", "");

    const dateInput = row.locator(`input[data-testid="match-${matchId}-date"]`);
    const dateValue = await dateInput.inputValue();
    dates.push(dateValue || "");
  }

  // Verify that they are sorted properly (ascending or descending)
  const isAscending = dates.every((val, i) => i === 0 || dates[i - 1] <= val);
  const isDescending = dates.every((val, i) => i === 0 || dates[i - 1] >= val);

  expect(isAscending || isDescending).toBe(true);
});

test("can sort matches by player 1 name", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  await page.goto(`/app/seasons/${seasons[0].id}`);
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Click on player 1 name header to sort
  const player1NameHeader = page.getByRole("button", {
    name: /^Player 1( [↑↓])?$/,
  });
  await player1NameHeader.click();
  await page.waitForLoadState("networkidle");
  await page.waitForTimeout(500);

  // Read all displayed rows and extract player 1 names
  const tableRows = page.locator("table tbody tr:visible");
  const rowCount = await tableRows.count();
  const names: string[] = [];

  for (let i = 0; i < rowCount; i++) {
    const row = tableRows.nth(i);
    const matchIdAttr = await row
      .locator('[data-testid^="match-id-"]')
      .getAttribute("data-testid");
    const matchId = matchIdAttr!.replace("match-id-", "");

    const playerDropdown = row.locator(
      `[data-testid="player-1-dropdown-${matchId}"]`,
    );
    const playerName = await playerDropdown.textContent();
    names.push(playerName?.trim() || "");
  }

  // Verify that they are sorted properly (ascending or descending)
  const isAscending = names.every((val, i) => i === 0 || names[i - 1] <= val);
  const isDescending = names.every((val, i) => i === 0 || names[i - 1] >= val);

  expect(isAscending || isDescending).toBe(true);
});

test("can sort matches by player 2 name", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  await page.goto(`/app/seasons/${seasons[0].id}`);
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Click on player 2 name header to sort
  const player2NameHeader = page.getByRole("button", {
    name: /^Player 2( [↑↓])?$/,
  });
  await player2NameHeader.click();
  await page.waitForLoadState("networkidle");
  await page.waitForTimeout(500);

  // Read all displayed rows and extract player 2 names
  const tableRows = page.locator("table tbody tr:visible");
  const rowCount = await tableRows.count();
  const names: string[] = [];

  for (let i = 0; i < rowCount; i++) {
    const row = tableRows.nth(i);
    const matchIdAttr = await row
      .locator('[data-testid^="match-id-"]')
      .getAttribute("data-testid");
    const matchId = matchIdAttr!.replace("match-id-", "");

    const playerDropdown = row.locator(
      `[data-testid="player-2-dropdown-${matchId}"]`,
    );
    const playerName = await playerDropdown.textContent();
    names.push(playerName?.trim() || "");
  }

  // Verify that they are sorted properly (ascending or descending)
  const isAscending = names.every((val, i) => i === 0 || names[i - 1] <= val);
  const isDescending = names.every((val, i) => i === 0 || names[i - 1] >= val);

  expect(isAscending || isDescending).toBe(true);
});

test("can sort matches by group", async ({ seasonFixture }) => {
  const { seasons, page } = seasonFixture;

  await page.goto(`/app/seasons/${seasons[0].id}`);
  await expect(page.locator("table tbody tr:visible").first()).toBeVisible();

  // Click on group header to sort
  const groupHeader = page.locator("th button").filter({ hasText: /Group/ });
  await groupHeader.click();
  await page.waitForTimeout(500);

  // Read all displayed rows and extract group values
  const tableRows = page.locator("table tbody tr:visible");
  const rowCount = await tableRows.count();
  const groups: number[] = [];

  for (let i = 0; i < rowCount; i++) {
    const row = tableRows.nth(i);
    const groupCell = row.locator("td").nth(5); // Assuming group is the 6th column (0-indexed)
    const groupText = await groupCell.textContent();
    const groupValue = parseInt(groupText?.trim() || "0");
    groups.push(groupValue || 0);
  }

  // Verify that they are sorted properly (ascending or descending)
  const isAscending = groups.every((val, i) => i === 0 || groups[i - 1] <= val);
  const isDescending = groups.every(
    (val, i) => i === 0 || groups[i - 1] >= val,
  );

  expect(isAscending || isDescending).toBe(true);
});
