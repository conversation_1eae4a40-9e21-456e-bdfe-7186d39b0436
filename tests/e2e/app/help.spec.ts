import { test } from './seasonFixture';
import { expect } from '@playwright/test';

test.describe('Help Center', () => {
  test('help center main page displays correctly', async ({ seasonFixture }) => {
    const { page } = seasonFixture;

    await page.goto('/app/help');

    // Check main title and subtitle
    await expect(page.locator('h1:has-text("Help Center")')).toBeVisible();
    await expect(page.locator('text=Find answers to your questions')).toBeVisible();

    // Check search input
    const searchInput = page.locator('[data-testid="help-search-input"]');
    await expect(searchInput).toBeVisible();
    await expect(searchInput).toHaveAttribute('placeholder', /Search for help topics/);

    // Check category cards
    await expect(page.locator('[data-testid="help-category-getting-started"]')).toBeVisible();
    await expect(page.locator('[data-testid="help-category-players"]')).toBeVisible();

    // Check quick links
    await expect(page.locator('text=Quick Links')).toBeVisible();
    await expect(page.locator('a[href="/app/help/faq"]')).toBeVisible();
  });

  test('FAQ page displays correctly', async ({ seasonFixture }) => {
    const { page } = seasonFixture;

    await page.goto('/app/help/faq');

    // Check main content
    await expect(page.locator('h1:has-text("Frequently Asked Questions")')).toBeVisible();
    await expect(page.locator('text=Find quick answers to common questions')).toBeVisible();

    // Check category filters
    await expect(page.locator('button:has-text("All")')).toBeVisible();

    // Check FAQ items exist
    const faqItems = page.locator('.faq-item');
    const count = await faqItems.count();
    expect(count).toBeGreaterThan(0);
  });

  test('troubleshooting page displays correctly', async ({ seasonFixture }) => {
    const { page } = seasonFixture;

    await page.goto('/app/help/troubleshooting');

    // Check main content
    await expect(page.locator('h1:has-text("Troubleshooting")')).toBeVisible();
    await expect(page.locator('text=Common problems and their solutions')).toBeVisible();

    // Check troubleshooting items
    await expect(page.locator('text=Can\'t Sign In to Your Account')).toBeVisible();
  });
});
