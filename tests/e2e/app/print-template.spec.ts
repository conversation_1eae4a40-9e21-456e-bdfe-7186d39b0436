import { test, expect } from "../auth.setup";

test.describe("Shared Print Template", () => {
  test("print template renders with correct structure", async ({ page }) => {
    // This would test the shared template by checking any print page
    // We'll use players print as an example
    await page.goto("/app/players?print=true");
    
    // Check basic HTML structure
    const html = await page.content();
    expect(html).toContain("<!DOCTYPE html>");
    expect(html).toContain("<html");
    expect(html).toContain("<head>");
    expect(html).toContain("<body>");
    
    // Check print-specific elements
    expect(html).toContain("window.onload = function() { window.print(); };");
    expect(html).toContain("@media print");
    
    // Check CSS is included
    expect(html).toContain("font-family: Arial, sans-serif");
    expect(html).toContain("border-collapse: collapse");
    
    // Check that the title is set correctly
    const title = await page.title();
    expect(title).toContain("Print");
  });
});
