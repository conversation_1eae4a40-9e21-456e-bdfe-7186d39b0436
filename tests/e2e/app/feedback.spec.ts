import { expect } from '@playwright/test';
import { test } from './seasonFixture';

test.describe('Feedback functionality', () => {
  test('should open feedback modal and submit feedback successfully', async ({ page }) => {

    await page.goto('/app/home');

    // Find the help dropdown button in the desktop sidebar
    const helpDropdown = page.locator('[data-testid="sidebar-nav"] [data-testid="help-dropdown"]');
    await expect(helpDropdown).toBeVisible();

    // Click the dropdown button to open it
    const dropdownButton = helpDropdown.locator('button');
    await dropdownButton.hover();
    await dropdownButton.click();

    // Wait for dropdown positioning (especially important in UI mode)
    await page.waitForTimeout(100);

    // Click the "Give feedback" option using data-testid and wait for the GET request
    const feedbackButton = page.locator('[data-testid="give-feedback-button"]');
    await expect(feedbackButton).toBeVisible();

    // Wait for the feedback modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/feedback') && response.request().method() === 'GET'
    );
    await feedbackButton.click({ force: true });
    await responsePromise;

    // Verify the feedback modal is visible
    const feedbackModal = page.locator('[data-testid="feedbackModal_modal"]');
    await expect(feedbackModal).toBeVisible();

    // Verify modal title
    await expect(page.locator('h3:has-text("Give feedback")')).toBeVisible();

    // Fill in the feedback form using data-testids
    await page.fill('[data-testid="feedback-name-input"]', 'Test User');
    await page.fill('[data-testid="feedback-description-input"]', 'This is a test feedback submission. The app is working great!');

    // Select feedback type (bug report)
    const feedbackTypeButton = page.locator('[data-testid="feedback-type-select"]');
    await feedbackTypeButton.click();
    await page.waitForTimeout(200);
    const bugOption = page.locator('#feedback_type-option-bug');
    await expect(bugOption).toBeVisible();
    await bugOption.click();

    // Submit the form
    const submitButton = page.locator('[data-testid="feedback-submit-button"]');
    await expect(submitButton).toBeVisible();
    await submitButton.click();

    // Wait for submission to complete and modal to close
    await expect(feedbackModal).toBeHidden({ timeout: 10000 });

    // Check for success toast (if toast system is implemented)
    // This might need adjustment based on actual toast implementation
    try {
      const successToast = page.locator('text=Thank you for your feedback!');
      await expect(successToast).toBeVisible({ timeout: 2000 });
    } catch (error) {
      // Toast might not be visible in test environment, that's okay
      console.log('Success toast not found, but that may be expected in test environment');
    }
  });

  test('should show validation errors for empty required fields', async ({ page }) => {

    await page.goto('/app/home');

    // Open the feedback modal
    const helpDropdown = page.locator('[data-testid="sidebar-nav"] [data-testid="help-dropdown"]');
    const dropdownButton = helpDropdown.locator('button');
    await dropdownButton.hover();
    await dropdownButton.click();
    await page.waitForTimeout(200);

    const feedbackButton = page.locator('[data-testid="give-feedback-button"]');
    await feedbackButton.click({ force: true });
    await page.waitForTimeout(500);

    // Interact with fields first to ensure they're tracked by validation
    const nameInput = page.locator('[data-testid="feedback-name-input"]');
    const descriptionInput = page.locator('[data-testid="feedback-description-input"]');

    // Type and then clear to trigger validation
    await nameInput.fill('a');
    await nameInput.fill('');
    await descriptionInput.fill('a');
    await descriptionInput.fill('');

    // Try to submit form without filling required fields
    const submitButton = page.locator('[data-testid="feedback-submit-button"]');
    await submitButton.click();

    // Wait for potential error messages
    await page.waitForTimeout(500);

    // Check that form hasn't submitted successfully (modal should still be visible)
    const feedbackModal = page.locator('[data-testid="feedbackModal_modal"]');
    await expect(feedbackModal).toBeVisible();

    // Check for inline error messages for empty required fields
    const nameErrorMessage = page.locator('div[x-text="errorMessages[\'name\']"]');
    await expect(nameErrorMessage).toBeVisible();
    const nameErrorText = await nameErrorMessage.textContent();
    expect(nameErrorText).toMatch(/required/i);

    const descriptionErrorMessage = page.locator('div[x-text="errorMessages[\'description\']"]');
    await expect(descriptionErrorMessage).toBeVisible();
    const descriptionErrorText = await descriptionErrorMessage.textContent();
    expect(descriptionErrorText).toMatch(/required/i);
  });

  test('should show real-time validation error when name field is emptied', async ({ page }) => {

    await page.goto('/app/home');

    // Open the feedback modal
    const helpDropdown = page.locator('[data-testid="sidebar-nav"] [data-testid="help-dropdown"]');
    const dropdownButton = helpDropdown.locator('button');
    await dropdownButton.hover();
    await dropdownButton.click();
    await page.waitForTimeout(200);

    const feedbackButton = page.locator('[data-testid="give-feedback-button"]');
    await feedbackButton.click({ force: true });
    await page.waitForTimeout(500);

    // Verify modal is open
    const feedbackModal = page.locator('[data-testid="feedbackModal_modal"]');
    await expect(feedbackModal).toBeVisible();

    // Get the name input field
    const nameInput = page.locator('[data-testid="feedback-name-input"]');
    await expect(nameInput).toBeVisible();

    // Initially, there should be no error message
    const nameErrorMessage = page.locator('div[x-text="errorMessages[\'name\']"]');
    await expect(nameErrorMessage).toBeHidden();

    // Type one character in the name field
    await nameInput.fill('a');

    // Clear the field (this should trigger validation)
    await nameInput.fill('');

    // Trigger a blur event to ensure validation runs
    await nameInput.blur();

    // Wait a moment for the validation to process
    await page.waitForTimeout(300);

    // Now the error message should be visible
    await expect(nameErrorMessage).toBeVisible();

    // Verify the error message content indicates the field is required
    const errorText = await nameErrorMessage.textContent();
    expect(errorText).toMatch(/required/i);
  });

  test('should close modal when close button is clicked', async ({ page }) => {

    await page.goto('/app/home');

    // Try to expand sidebar if it's not already expanded (don't fail if already expanded)
    try {
      const sidebarToggle = page.locator('[data-testid="sidebar-toggle"]');
      if (await sidebarToggle.isVisible()) {
        await sidebarToggle.click();
        await page.waitForTimeout(200);
      }
    } catch (error) {
      // Sidebar might already be expanded or toggle not found - continue with test
    }

    // Open the feedback modal
    const helpDropdown = page.locator('[data-testid="sidebar-nav"] [data-testid="help-dropdown"]');
    const dropdownButton = helpDropdown.locator('button');
    await dropdownButton.hover();
    await dropdownButton.click();
    await page.waitForTimeout(200);

    const feedbackButton = page.locator('[data-testid="give-feedback-button"]');
    await feedbackButton.click({ force: true });
    await page.waitForTimeout(500);

    // Verify modal is open
    const feedbackModal = page.locator('[data-testid="feedbackModal_modal"]');
    await expect(feedbackModal).toBeVisible();

    // Click the close button (X button in top right)
    const closeButton = page.locator('[data-testid="feedbackModal_close-button"]');
    await closeButton.click();

    // Wait for modal to close
    await page.waitForTimeout(500);

    // Verify modal is closed
    await expect(feedbackModal).toBeHidden();
  });
});
