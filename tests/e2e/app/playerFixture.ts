// Import our authenticated test instead of the base test
import { test as base } from '../auth.setup';
import { TeamFixture } from './teamFixture';

// Define interfaces for type safety
interface PlayerData {
  name: string;
  email: string;
  preferredMatchGroup: number;
  emailNotificationsEnabled: boolean;
  userId: number;
}

interface Player {
  id: number;
  userId: number;
  teamId?: number;
  name: string;
  email?: string;
  pictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  preferredMatchGroup: number;
  isActive: boolean;
  emailNotificationsEnabled: boolean;
  emailReminderPreferences: string;
}

export interface PlayerFixture {
  players: Player[];
  teams: Team[];
  page: any; // Adjust type based on actual usage if needed
}

// Helper functions to generate random data
import { TestDataGenerator } from "../testData";

function generateRandomName(): string {
  return TestDataGenerator.player().name;
}

function generateRandomEmail(): string {
  return TestDataGenerator.player().email;
}

// Default number of players to create
const defaultPlayerCount: number = 50;

// Add team interfaces
interface TeamData {
  name: string;
  description: string;
  userId: number;
}

interface Team {
  id: number;
  userId: number;
  name: string;
  description?: string;
  pictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

function generateRandomTeamName(): string {
  return TestDataGenerator.team().name;
}

function generateRandomDescription(): string {
  return TestDataGenerator.team().description;
}

// Extend base test by providing "playerFixture"
export const test = base.extend<{
  playerConfig: number;
  teamConfig: number;
  playerFixture: PlayerFixture;
}>({
  // Accept playerConfig as a number with default value
  playerConfig: [defaultPlayerCount, { option: true }],
  teamConfig: [3, { option: true }], // Default 3 teams for player tests

  playerFixture: async ({ request, playerConfig, teamConfig, isolatedUser }, use) => {
    const createdTeams: Team[] = [];
    const createdPlayers: Player[] = [];

    // First create teams
    for (let i = 0; i < teamConfig; i++) {
      const teamData: TeamData = {
        name: generateRandomTeamName(),
        description: generateRandomDescription(),
        userId: isolatedUser.userId
      };

      const response = await request.post('/dev/teams', { data: teamData });

      if (!response.ok()) {
        console.log(`Team creation failed: ${await response.text()}`);
        throw new Error('Failed to create team');
      }

      const team: Team = await response.json();
      createdTeams.push(team);
    }

    // Then create players (some with team assignments)
    for (let i = 0; i < playerConfig; i++) {
      const playerData: PlayerData = {
        name: generateRandomName(),
        email: generateRandomEmail(),
        preferredMatchGroup: i + 1,
        emailNotificationsEnabled: i % 2 === 0, // Alternate between true/false
        userId: isolatedUser.userId
      };

      const response = await request.post('/dev/players', { data: playerData });

      if (!response.ok()) {
        console.log(`Player creation failed: ${await response.text()}`);
        throw new Error('Failed to create player');
      }

      const player: Player = await response.json();
      createdPlayers.push(player);
    }

    // Pass the created players and teams to the test
    await use({ players: createdPlayers, teams: createdTeams, page: isolatedUser.page });

    // Clean up the fixture: delete all created players and teams
    for (const player of createdPlayers) {
      await request.delete(`/dev/players/${player.id}`);
    }
    for (const team of createdTeams) {
      await request.delete(`/dev/teams/${team.id}`);
    }
  },
});

export const expect = base.expect;