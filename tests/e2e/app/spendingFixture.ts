// Import our authenticated test instead of the base test
import { test as base } from '../auth.setup';

// Define interfaces for type safety
interface SpendingData {
  userId: number;
  amount: string;
  description: string;
  date: string;
  category: string;
  teamIds?: number[];
  playerIds?: number[];
  seasonIds?: number[];
  matchIds?: number[];
  fileUrls?: string[];
}

interface Spending {
  id: string;
  amount: string;
  description: string;
  date: string;
  category: string;
}

export interface SpendingFixture {
  spending: Spending[];
  page: any; // Can be either isolatedUser.page or shared page
}

// Helper functions to generate test data
import { generateRandomString } from "../testData";

// Predefined spending records for consistent testing
const defaultSpendingRecords: Omit<SpendingData, 'userId'>[] = [
  {
    amount: "50.00",
    description: "Office supplies purchase",
    date: new Date().toISOString().split('T')[0],
    category: "Equipment"
  },
  {
    amount: "75.25",
    description: "Team lunch expenses",
    date: new Date().toISOString().split('T')[0],
    category: "Food"
  },
  {
    amount: "120.00",
    description: "Travel to tournament",
    date: new Date().toISOString().split('T')[0],
    category: "Travel"
  },
  {
    amount: "200.50",
    description: "New equipment",
    date: new Date().toISOString().split('T')[0],
    category: "Equipment"
  },
  {
    amount: "85.75",
    description: "Bus rental",
    date: new Date().toISOString().split('T')[0],
    category: "Travel"
  },
  {
    amount: "45.00",
    description: "Team dinner",
    date: new Date().toISOString().split('T')[0],
    category: "Food"
  }
];

// Default number of spending records to create
const defaultSpendingCount: number = defaultSpendingRecords.length;

// Extend base test by providing "spendingFixture"
export const test = base.extend<{
  spendingConfig?: number;
  spendingFixture: SpendingFixture;
}>({
  // Accept spendingConfig as a number with default value
  spendingConfig: [defaultSpendingCount, { option: true }],

  spendingFixture: async ({ page, request, spendingConfig, isolatedUser }, use) => {
    const createdSpending: Spending[] = [];

    // Create a basic team for associations
    const teamData = {
      name: `Test Team ${generateRandomString()}`,
      description: "Test team for spending",
      userId: isolatedUser.userId,
    };

    const teamResponse = await request.post('/dev/teams', { data: teamData });
    if (!teamResponse.ok()) {
      throw new Error('Failed to create test team for spending fixture');
    }
    const team = await teamResponse.json();

    // Generate spending records using predefined data
    const recordsToCreate = spendingConfig || defaultSpendingCount;
    for (let i = 0; i < recordsToCreate && i < defaultSpendingRecords.length; i++) {
      const baseRecord = defaultSpendingRecords[i];

      const spendingData: SpendingData = {
        ...baseRecord,
        userId: isolatedUser.userId,
        teamIds: [parseInt(team.id)] // Associate with the test team
      };

      const response = await request.post('/dev/spending', { data: spendingData });

      if (!response.ok()) {
        console.log(`Spending creation failed: ${await response.text()}`);
        throw new Error('Failed to create spending record');
      }

      const spending: Spending = await response.json();
      createdSpending.push(spending);
    }

    // Pass the created spending records to the test with the effective page
    await use({ spending: createdSpending, page: isolatedUser.page });

    // Clean up the fixture: delete all created spending records and team
    for (const spending of createdSpending) {
      await request.delete(`/dev/spending/${spending.id}`);
    }
    await request.delete(`/dev/teams/${team.id}`);
  },
});

export const expect = base.expect;