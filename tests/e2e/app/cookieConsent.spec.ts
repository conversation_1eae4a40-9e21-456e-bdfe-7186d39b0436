// Import the test and expect from the cookie consent fixture
import { test, expect } from './cookieConsentFixture';

test.describe('Cookie Consent Modal', () => {
  test.describe('with rejected cookie consent', () => {
    test.use({consentValue: false})
    test('should display cookie consent modal when not accepted', async ({ page, cookieConsentFixture }) => {
      // Navigate to the home page or any page where the modal should appear
      await page.goto('/signin');

      // Check if the cookie consent modal is visible
      const modal = page.locator('#cookieConsent');
      await expect(modal).toBeVisible();
    });
  });

  test('should not display cookie consent modal when already accepted', async ({ page, cookieConsentFixture, consentValue }) => {
    // Navigate to the home page or any page where the modal should appear
    await page.goto('/signin');

    // Check if the cookie consent modal is not visible
    const modal = page.locator('#cookieConsent');
    await expect(modal).not.toBeVisible();
  });
});
