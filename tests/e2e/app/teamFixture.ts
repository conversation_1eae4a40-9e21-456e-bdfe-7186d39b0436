// Import our authenticated test instead of the base test
import { test as base } from '../auth.setup';

// Define interfaces for type safety
interface TeamData {
  name: string;
  description: string;
  userId: number;
}

interface Team {
  id: string;
  // Add other relevant fields if needed
}

export interface TeamFixture {
  teams: Team[];
  page: any; // Adjust type based on actual usage if needed
}

// Helper functions to generate random data
import { TestDataGenerator } from "../testData";

function generateRandomTeamName(): string {
  return TestDataGenerator.team().name;
}

function generateRandomDescription(): string {
  return TestDataGenerator.team().description;
}

// Default number of teams to create
const defaultTeamCount: number = 5;

// Extend base test by providing "teamFixture"
export const test = base.extend<{
  teamConfig: number;
  teamFixture: TeamFixture;
}>({
  // Accept teamConfig as a number with default value
  teamConfig: [defaultTeamCount, { option: true }],

  teamFixture: async ({ page, request, teamConfig, isolatedUser, userId }, use) => {
    const createdTeams: Team[] = [];
    
    // Determine if we should use isolated user data or shared user data
    // If the test is using the shared page fixture, use shared userId
    // If the test is using isolatedUser.page, use isolatedUser.userId
    const usingSharedPage = page !== isolatedUser?.page;
    const effectiveUserId = usingSharedPage ? userId : isolatedUser.userId;
    
    // Generate configurations for the specified number of teams
    for (let i = 0; i < teamConfig; i++) {
      const teamData: TeamData = {
        name: generateRandomTeamName(),
        description: generateRandomDescription(),
        userId: effectiveUserId 
      };

      const response = await request.post('/dev/teams', { data: teamData });

      if (!response.ok()) {
        console.log(`Team creation failed: ${await response.text()}`);
        throw new Error('Failed to create team');
      }

      const team: Team = await response.json();
      createdTeams.push(team);
    }

    // Pass the created teams to the test
    await use({ teams: createdTeams, page: page });

    // Clean up the fixture: delete all created teams
    for (const team of createdTeams) {
      await request.delete(`/dev/teams/${team.id}`);
    }
  },
});

export const expect = base.expect;