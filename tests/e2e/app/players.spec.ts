import { test, expect } from "./playerFixture";
import {
  generateRandomName,
  generateRandomEmail,
  generateRandomPhoneNumber,
} from "../utils";

test.use({
  playerConfig: 15, // Use 15 players for all tests
});

test.beforeEach(async ({ playerFixture }) => {

  const { page } = playerFixture;
  await page.goto("/app/players");
});

test.describe("Empty state", () => {
  test.use({
    playerConfig: 0, // Use 0 players for empty state tests
  });

  test("shows empty state message when no players exist", async ({
    playerFixture,
  }) => {
    const { page } = playerFixture;

    // Check that the empty state is visible
    const emptyState = page.locator('[data-testid="no-players-empty-state"]');
    await expect(emptyState).toBeVisible();

    // Verify the title and message are present
    await expect(emptyState).toContainText("No players yet");
    await expect(emptyState).toContainText(
      "Get started by adding your first player",
    );

    // Check that the "Add Your First Player" button is present
    const addFirstPlayerButton = page.locator(
      '[data-testid="add-first-player-btn"]',
    );
    await expect(addFirstPlayerButton).toBeVisible();
    await expect(addFirstPlayerButton).toContainText("Add Your First Player");

    // Verify that the table and pagination are not visible
    const table = page.locator("table");
    await expect(table).not.toBeVisible();

    const pagination = page.locator("#pagination-info");
    await expect(pagination).not.toBeVisible();
  });
});

test("default pagination shows 10 players per page", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Check that only 10 rows are displayed initially (plus header row)
  const visibleRows = page.locator("table tbody tr:visible");
  await expect(visibleRows).toHaveCount(10);

  // Verify the pagination info shows the correct default
  const itemsPerPageSelect = page.locator("#items-per-page");
  await expect(itemsPerPageSelect).toHaveValue("10");
});

test("can navigate to second page and see remaining players", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Get initial state
  const initialPaginationText = await page
    .locator("#pagination-info")
    .textContent();
  const initialRowCount = await page.locator("table tbody tr:visible").count();

  // Click next page and wait for navigation to complete
  const nextPageButton = page.locator("#next-page-btn");
  await nextPageButton.click();

  // Wait for the pagination text to change
  await expect(page.locator("#pagination-info")).not.toHaveText(
    initialPaginationText,
  );

  // Verify we have some players on the new page (could be fewer than first page)
  const newRowCount = await page.locator("table tbody tr:visible").count();
  expect(newRowCount).toBeGreaterThan(0);

  // Test going back works
  const prevPageButton = page.locator("#prev-page-btn");
  await prevPageButton.click();

  // Wait for navigation back to complete and verify we're back to the original state
  await expect(page.locator("#pagination-info")).toHaveText(
    initialPaginationText,
  );
  await expect(page.locator("table tbody tr:visible")).toHaveCount(
    initialRowCount,
  );
});

test("can filter players properly", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // First check that all rows are displayed (with pagination)
  const initialRows = page.locator("table tbody tr:visible");
  await expect(initialRows).toHaveCount(10); // First page shows 10 players

  // Get the first player's name to use as a specific search term
  const firstPlayerName = await page
    .locator("table tbody tr")
    .first()
    .locator("input")
    .first()
    .inputValue();

  // Find the search input by checking the data-testid attribute
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await expect(searchInput).toBeVisible();

  // Enter the specific name in the search box
  await searchInput.fill(firstPlayerName);

  // Verify that only matching players are displayed
  const filteredRows = page.locator("table tbody tr:visible");
  await expect(filteredRows).toHaveCount(1); // Should only show the one matching player

  // Verify that the pagination info reflects the filtered view
  const paginationText = page.locator("#pagination-info");
  await expect(paginationText).toContainText("1-1"); // Should show only 1 record

  // Now test with a search term that shouldn't match any players
  await searchInput.fill("XYZ_NO_MATCH_SHOULD_BE_FOUND");

  // Verify no rows are shown when no matches
  const noMatchRows = page.locator("table tbody tr:visible");
  await expect(noMatchRows).toHaveCount(0);

  // Clear the search by filling with empty string
  await searchInput.clear();

  // Verify that all rows are displayed again (with pagination)
  const resetRows = page.locator("table tbody tr:visible");
  await expect(resetRows).toHaveCount(10); // First page shows 10 players again
});

test("can add a new player successfully", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Locate and click the "Add New Player" button by id
  const addNewPlayerButton = page.locator("#add-new-player-btn");
  await addNewPlayerButton.click();

  // Fill out the form fields
  const modal = page.locator("#playersNewForm");
  const randomName = generateRandomName();
  const randomEmail = generateRandomEmail();
  await modal.locator('input[name="name"]').fill(randomName);
  await modal.locator('input[name="email"]').fill(randomEmail);
  await modal.locator('input[name="emailNotificationsEnabled"]').check();

  // Submit the form
  await modal.locator('button[type="submit"]').click();

  // Wait for the modal to close
  await expect(modal).not.toBeVisible();

  // Use the search bar to filter by the new player's email
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await expect(searchInput).toBeVisible();
  await searchInput.fill(randomEmail);

  // Wait for HTMX to process the search
  await page.waitForTimeout(1000);

  // Assert that the new player appears in the filtered table as the first row
  const firstRow = page.locator("table tbody tr").first();
  // First cell: name input
  const nameInput = firstRow.locator("td").nth(0).locator("input");
  await expect(nameInput).toHaveValue(randomName);
  // Second cell: email input
  const emailInput = firstRow.locator("td").nth(1).locator("input");
  await expect(emailInput).toHaveValue(randomEmail);
});

test("user can create new player with a valid phone number", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Locate and click the "Add New Player" button by id
  const addNewPlayerButton = page.locator("#add-new-player-btn");
  await addNewPlayerButton.click();

  // Fill out the form fields
  const modal = page.locator("#playersNewForm");
  const randomName = generateRandomName();
  const randomEmail = generateRandomEmail();
  const randomPhoneNumber = generateRandomPhoneNumber();

  await modal.locator('input[name="name"]').fill(randomName);
  await modal.locator('input[name="email"]').fill(randomEmail);
  await modal.locator('input[name="phone"]').fill(randomPhoneNumber);
  await modal.locator('input[name="emailNotificationsEnabled"]').check();

  // Submit the form
  await modal.locator('button[type="submit"]').click();

  // Wait for the modal to close
  await expect(modal).not.toBeVisible();

  // Use the search bar to filter by the new player's email
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await expect(searchInput).toBeVisible();
  await searchInput.fill(randomEmail);

  // Wait for HTMX to process the search
  await page.waitForTimeout(1000);

  // Assert that the new player appears in the filtered table as the first row
  const firstRow = page.locator("table tbody tr").first();
  // First cell: name input
  const nameInput = firstRow.locator("td").nth(0).locator("input");
  await expect(nameInput).toHaveValue(randomName);
  // Second cell: email input
  const emailInput = firstRow.locator("td").nth(1).locator("input");
  await expect(emailInput).toHaveValue(randomEmail);

  // Check if phone field exists in the table (might be in a different position)
  const phoneInput = firstRow.locator('input[name="phone"]');
  if ((await phoneInput.count()) > 0) {
    await expect(phoneInput).toHaveValue(randomPhoneNumber);
  }
});

test("user can add multiple players with keep adding checkbox enabled", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Locate and click the "Add New Player" button by id
  const addNewPlayerButton = page.locator("#add-new-player-btn");
  await addNewPlayerButton.click();

  const modal = page.locator("#playersNewForm");
  await expect(modal).toBeVisible();

  // Check the "Keep adding players" checkbox
  const keepAddingCheckbox = modal.locator('input[name="keepAddingPlayers"]');
  await expect(keepAddingCheckbox).toBeVisible();
  await keepAddingCheckbox.check();

  // Add first player
  const firstPlayerName = generateRandomName();
  const firstPlayerEmail = generateRandomEmail();
  await modal.locator('input[name="name"]').fill(firstPlayerName);
  await modal.locator('input[name="email"]').fill(firstPlayerEmail);

  // Submit the form
  await modal.locator('button[type="submit"]').click();

  // The modal should stay open because we checked "Keep adding players"
  await expect(modal).toBeVisible();

  // Verify the checkbox is still checked
  await expect(keepAddingCheckbox).toBeChecked();

  // Verify the form was reset (name and email fields should be empty)
  await expect(modal.locator('input[name="name"]')).toHaveValue("");
  await expect(modal.locator('input[name="email"]')).toHaveValue("");

  // Add second player
  const secondPlayerName = generateRandomName();
  const secondPlayerEmail = generateRandomEmail();
  await modal.locator('input[name="name"]').fill(secondPlayerName);
  await modal.locator('input[name="email"]').fill(secondPlayerEmail);

  // This time uncheck the "Keep adding players" checkbox
  await keepAddingCheckbox.uncheck();

  // Submit the form
  await modal.locator('button[type="submit"]').click();

  // Now the modal should close
  await expect(modal).not.toBeVisible();

  // Search for the first player
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await searchInput.fill(firstPlayerEmail);
  await page.waitForTimeout(1000);

  // Assert the first player exists
  const firstRow = page.locator("table tbody tr").first();
  const firstNameInput = firstRow.locator("td").nth(0).locator("input");
  await expect(firstNameInput).toHaveValue(firstPlayerName);

  // Clear search and search for second player
  await searchInput.clear();
  await searchInput.fill(secondPlayerEmail);
  await page.waitForTimeout(1000);

  // Assert the second player exists
  const secondRow = page.locator("table tbody tr").first();
  const secondNameInput = secondRow.locator("td").nth(0).locator("input");
  await expect(secondNameInput).toHaveValue(secondPlayerName);
});

test("user can print players and popup window opens with content", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Wait for the page to load and Alpine.js to initialize
  await page.waitForSelector('h1:has-text("Players")');
  await page.waitForTimeout(2000); // Give Alpine.js more time to initialize

  // Find and click the players actions dropdown button
  const actionsDropdown = page.locator(
    '[data-testid="players-actions-dropdown"]',
  );
  await expect(actionsDropdown).toBeVisible();
  await actionsDropdown.click();

  // Verify the print button is visible in the dropdown
  const printButton = page.locator('[data-testid="print-players-button"]');
  await expect(printButton).toBeVisible();
  await expect(printButton).toContainText("Print Players");

  // Set up listener for new popup window before clicking print
  const popupPromise = page.waitForEvent("popup");

  // Click the print button
  await printButton.click();

  // Wait for the popup window to open
  const popup = await popupPromise;

  // Wait for content to be written to the popup
  await popup.waitForLoadState("networkidle");
  await popup.waitForTimeout(1000); // Additional wait for content to be written

  // Get the popup content
  const popupContent = await popup.content();

  // Verify the popup contains proper HTML structure
  expect(popupContent).toContain("<!DOCTYPE html>");
  expect(popupContent).toContain('<html lang="en">');
  expect(popupContent).toContain("<body>");

  // Check that the popup contains the players title
  expect(popupContent).toContain("Print -");
  expect(popupContent).toContain("Players");

  // Check that the popup contains players table structure
  expect(popupContent).toContain("<table>");
  expect(popupContent).toContain("<th>Name</th>");
  expect(popupContent).toContain("<th>Email</th>");
  expect(popupContent).toContain("<th>Preferred Group</th>");
  expect(popupContent).toContain("<th>Active</th>");
  expect(popupContent).toContain("<th>Email Notifications</th>");

  // Check that print-specific CSS is included
  expect(popupContent).toContain("@media print");

  // Close the popup
  await popup.close();
});

test("print functionality respects players table filters", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Wait for the page to load and Alpine.js to initialize
  await page.waitForSelector('h1:has-text("Players")');
  await page.waitForTimeout(2000);

  // Apply a search filter first
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  if (await searchInput.isVisible()) {
    // Get the first player's name to use as a filter
    const firstPlayerName = await page
      .locator("table tbody tr")
      .first()
      .locator("input")
      .first()
      .inputValue();
    await searchInput.fill(firstPlayerName);
    await page.waitForTimeout(1000); // Wait for HTMX to process
  }

  // Open dropdown and click print
  const actionsDropdown = page.locator(
    '[data-testid="players-actions-dropdown"]',
  );
  await actionsDropdown.click();

  const printButton = page.locator('[data-testid="print-players-button"]');

  // Set up popup listener
  const popupPromise = page.waitForEvent("popup");
  await printButton.click();

  // Get the popup and wait for content
  const popup = await popupPromise;
  await popup.waitForLoadState("networkidle");
  await popup.waitForTimeout(1000);

  const popupContent = await popup.content();

  // Verify basic print content structure
  expect(popupContent).toContain("Print -");
  expect(popupContent).toContain("Players");
  expect(popupContent).toContain("<table>");

  // Close popup
  await popup.close();

  // Clear any search filter
  if (await searchInput.isVisible()) {
    await searchInput.clear();
    await page.waitForTimeout(500); // Wait for HTMX to process
  }
});

test("user can update player name", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Players")');

  // Get the first player row
  const firstRow = page.locator("table tbody tr").first();
  const nameInput = firstRow.locator('input[name="name"]');

  // Get the player ID from the data-testid attribute
  const dataTestId = await nameInput.getAttribute("data-testid");
  const playerId = dataTestId.match(/player-(\d+)-name/)[1];

  // Get the current name and create a new unique name
  const originalName = await nameInput.inputValue();
  const updatedName = `Updated ${originalName} ${Date.now()}`;

  // Clear the input and enter the new name
  await nameInput.clear();
  await nameInput.fill(updatedName);

  // Wait for the HTMX inline update request
  const responsePromise = page.waitForResponse(
    (response) =>
      response.url().includes("/players/") &&
      response.url().includes("/inline") &&
      response.request().method() === "PUT",
  );

  // Trigger blur event to save the changes (inline editing saves on blur)
  await nameInput.blur();

  // Wait for the HTMX PUT request to complete and verify it succeeded
  const response = await responsePromise;
  expect(response.status()).toBe(200);

  // Verify the input still contains the updated value immediately after blur
  await expect(nameInput).toHaveValue(updatedName);

  // Wait a bit for any DOM updates to complete
  await page.waitForTimeout(500);

  // Refresh the page to confirm the change persisted in the database
  await page.reload();
  await page.waitForSelector('h1:has-text("Players")');

  // Search for the updated player to verify it appears in the correct sorted position
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await searchInput.fill(updatedName);
  await page.waitForTimeout(1000); // Wait for HTMX to process search

  // Verify the updated player appears in search results
  const nameInputAfterReload = page.locator(
    `[data-testid="player-${playerId}-name"]`,
  );
  await expect(nameInputAfterReload).toHaveValue(updatedName);
});

test("user can update player email", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Players")');

  // Get the first player row
  const firstRow = page.locator("table tbody tr").first();
  const emailInput = firstRow.locator('input[name="email"]');

  // Get the player ID from the data-testid attribute
  const dataTestId = await emailInput.getAttribute("data-testid");
  const playerId = dataTestId.match(/player-(\d+)-email/)[1];

  // Get the current email and create a new unique email
  const originalEmail = await emailInput.inputValue();
  const updatedEmail = generateRandomEmail();

  // Clear the input and enter the new email
  await emailInput.clear();
  await emailInput.fill(updatedEmail);

  // Wait for the HTMX inline update request
  const responsePromise = page.waitForResponse(
    (response) =>
      response.url().includes("/players/") &&
      response.url().includes("/inline") &&
      response.request().method() === "PUT",
  );

  // Trigger blur event to save the changes (inline editing saves on blur)
  await emailInput.blur();

  // Wait for the HTMX PUT request to complete and verify it succeeded
  const response = await responsePromise;
  expect(response.status()).toBe(200);

  // Verify the input still contains the updated value immediately after blur
  await expect(emailInput).toHaveValue(updatedEmail);

  // Wait a bit for any DOM updates to complete
  await page.waitForTimeout(500);

  // Refresh the page to confirm the change persisted in the database
  await page.reload();
  await page.waitForSelector('h1:has-text("Players")');

  // Search for the player using the updated email
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await searchInput.fill(updatedEmail);
  await page.waitForTimeout(1000); // Wait for HTMX to process search

  // Verify the updated player appears in search results
  const emailInputAfterReload = page.locator(
    `[data-testid="player-${playerId}-email"]`,
  );
  await expect(emailInputAfterReload).toHaveValue(updatedEmail);
});

test("user can update player preferred match group", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Players")');

  // Get the first player row
  const firstRow = page.locator("table tbody tr").first();
  const preferredGroupInput = firstRow.locator(
    'input[name="preferredMatchGroup"]',
  );

  // Get the player ID from the data-testid attribute
  const dataTestId = await preferredGroupInput.getAttribute("data-testid");
  const playerId = dataTestId.match(/player-(\d+)-preferred-group/)[1];

  // Get the current preferred match group and create a new unique value
  const originalGroup = await preferredGroupInput.inputValue();
  const updatedGroup = String(parseInt(originalGroup) + 1);

  // Clear the input and enter the new preferred match group
  await preferredGroupInput.clear();
  await preferredGroupInput.fill(updatedGroup);

  // Wait for the HTMX inline update request
  const responsePromise = page.waitForResponse(
    (response) =>
      response.url().includes("/players/") &&
      response.url().includes("/inline") &&
      response.request().method() === "PUT",
  );

  // Trigger blur event to save the changes (inline editing saves on blur)
  await preferredGroupInput.blur();

  // Wait for the HTMX PUT request to complete and verify it succeeded
  const response = await responsePromise;
  expect(response.status()).toBe(200);

  // Verify the input still contains the updated value immediately after blur
  await expect(preferredGroupInput).toHaveValue(updatedGroup);

  // Wait a bit for any DOM updates to complete
  await page.waitForTimeout(500);

  // Refresh the page to confirm the change persisted in the database
  await page.reload();
  await page.waitForSelector('h1:has-text("Players")');

  // Search for the player by their name to find them after reload
  const nameInput = page.locator(`[data-testid="player-${playerId}-name"]`);
  const playerName = await nameInput.inputValue();
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await searchInput.fill(playerName);
  await page.waitForTimeout(1000); // Wait for HTMX to process search

  // Verify the updated player appears in search results with the new preferred match group
  const preferredGroupInputAfterReload = page.locator(
    `[data-testid="player-${playerId}-preferred-group"]`,
  );
  await expect(preferredGroupInputAfterReload).toHaveValue(updatedGroup);
});

test("user can update player active status", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Navigate to the players page
  await page.goto("/app/players");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Players")');

  // Get the first player row
  const firstRow = page.locator("table tbody tr").first();
  const activeCheckbox = firstRow.locator('input[name="isActive"]');

  // Get the player ID and name before making changes
  const dataTestId = await activeCheckbox.getAttribute("data-testid");
  const playerId = dataTestId.match(/player-(\d+)-active/)[1];
  const nameInput = firstRow.locator('input[name="name"]');
  const playerName = await nameInput.inputValue();

  // Get the current active status
  const originalStatus = await activeCheckbox.isChecked();

  // Wait for the HTMX inline update request
  const responsePromise = page.waitForResponse(
    (response) =>
      response.url().includes("/players/") &&
      response.url().includes("/inline") &&
      response.request().method() === "PUT",
  );

  // Toggle the checkbox (change event triggers HTMX update)
  if (originalStatus) {
    // If player is currently active, deactivate them
    await activeCheckbox.uncheck();

    // Wait for the HTMX PUT request to complete and verify it succeeded
    const response = await responsePromise;
    expect(response.status()).toBe(200);

    // Verify the checkbox is now unchecked
    await expect(activeCheckbox).not.toBeChecked();

    // Wait for any DOM updates
    await page.waitForTimeout(500);

    // Refresh the page to confirm the player is now filtered out (inactive players are hidden)
    await page.reload();
    await page.waitForSelector('h1:has-text("Players")');

    // Search for the player - they should not be found since inactive players are filtered out
    const searchInput = page.locator('[data-testid="players-search-bar"]');
    await searchInput.fill(playerName);
    await page.waitForTimeout(1000); // Wait for HTMX to process search

    // Verify no rows are found (inactive players are hidden)
    const filteredRows = page.locator("table tbody tr:visible");
    await expect(filteredRows).toHaveCount(0);
  } else {
    // If player is currently inactive, activate them
    await activeCheckbox.check();

    // Wait for the HTMX PUT request to complete and verify it succeeded
    const response = await responsePromise;
    expect(response.status()).toBe(200);

    // Verify the checkbox is now checked
    await expect(activeCheckbox).toBeChecked();

    // Wait for any DOM updates
    await page.waitForTimeout(500);

    // Refresh the page to confirm the change persisted
    await page.reload();
    await page.waitForSelector('h1:has-text("Players")');

    // Search for the player to verify they're still visible
    const searchInput = page.locator('[data-testid="players-search-bar"]');
    await searchInput.fill(playerName);
    await page.waitForTimeout(1000); // Wait for HTMX to process search

    // Verify the player appears in search results and is active
    const activeCheckboxAfterReload = page.locator(
      `[data-testid="player-${playerId}-active"]`,
    );
    await expect(activeCheckboxAfterReload).toBeChecked();
  }
});

test("user can update player email notifications", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Players")');

  // Get the first player row
  const firstRow = page.locator("table tbody tr").first();
  const notificationsCheckbox = firstRow.locator(
    'input[name="emailNotificationsEnabled"]',
  );

  // Get the player ID and name before making changes
  const dataTestId = await notificationsCheckbox.getAttribute("data-testid");
  const playerId = dataTestId.match(/player-(\d+)-notifications/)[1];
  const nameInput = firstRow.locator('input[name="name"]');
  const playerName = await nameInput.inputValue();

  // Get the current email notifications status
  const originalStatus = await notificationsCheckbox.isChecked();
  const updatedStatus = !originalStatus;

  // Wait for the HTMX inline update request
  const responsePromise = page.waitForResponse(
    (response) =>
      response.url().includes("/players/") &&
      response.url().includes("/inline") &&
      response.request().method() === "PUT",
  );

  // Toggle the checkbox (change event triggers HTMX update)
  if (updatedStatus) {
    await notificationsCheckbox.check();
  } else {
    await notificationsCheckbox.uncheck();
  }

  // Wait for the HTMX PUT request to complete and verify it succeeded
  const response = await responsePromise;
  expect(response.status()).toBe(200);

  // Verify the checkbox has the updated state immediately after change
  await expect(notificationsCheckbox).toBeChecked({ checked: updatedStatus });

  // Wait a bit for any DOM updates to complete
  await page.waitForTimeout(500);

  // Refresh the page to confirm the change persisted in the database
  await page.reload();
  await page.waitForSelector('h1:has-text("Players")');

  // Search for the player by their name to find them after reload
  const searchInput = page.locator('[data-testid="players-search-bar"]');
  await searchInput.fill(playerName);
  await page.waitForTimeout(1000); // Wait for HTMX to process search

  // Verify the updated player appears in search results with the new email notifications status
  const notificationsCheckboxAfterReload = page.locator(
    `[data-testid="player-${playerId}-notifications"]`,
  );
  await expect(notificationsCheckboxAfterReload).toBeChecked({
    checked: updatedStatus,
  });
});

test("can sort players by name", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Set items per page to show all players to avoid pagination issues
  const itemsPerPageSelect = page.locator("#items-per-page");
  await itemsPerPageSelect.selectOption("50");
  await page.waitForTimeout(1000);

  // Click on name header
  const nameHeader = page
    .locator("th")
    .filter({ hasText: "Name" })
    .locator("button");
  await nameHeader.click();
  await page.waitForTimeout(1000);

  // Read all rows that are displayed
  const getDisplayedNames = async () => {
    const names = [];
    const rows = page.locator("table tbody tr");
    const count = await rows.count();

    for (let i = 0; i < count; i++) {
      const nameInput = rows.nth(i).locator('input[name="name"]');
      const name = await nameInput.inputValue();
      names.push(name);
    }
    return names;
  };

  const displayedNames = await getDisplayedNames();

  // Check that they are properly sorted by name (could be ascending or descending)
  const expectedAscending = [...displayedNames].sort();
  const expectedDescending = [...displayedNames].sort().reverse();

  // The sorting should match either ascending or descending order
  const isValidSort =
    JSON.stringify(displayedNames) === JSON.stringify(expectedAscending) ||
    JSON.stringify(displayedNames) === JSON.stringify(expectedDescending);

  expect(isValidSort).toBe(true);
});

test("can sort players by preferred match group", async ({ playerFixture }) => {
  // Navigate to the players page
  const { page } = playerFixture;

  // Set items per page to show all players to avoid pagination issues
  const itemsPerPageSelect = page.locator("#items-per-page");
  await itemsPerPageSelect.selectOption("50");
  await page.waitForTimeout(1000);

  // Click on preferred group header
  const groupHeader = page
    .locator("th button")
    .filter({ hasText: "Preferred Group" });
  await groupHeader.click();
  await page.waitForTimeout(1000);

  // Read all rows that are displayed
  const getDisplayedGroups = async () => {
    const groups = [];
    const rows = page.locator("table tbody tr");
    const count = await rows.count();

    for (let i = 0; i < count; i++) {
      const groupInput = rows
        .nth(i)
        .locator('input[name="preferredMatchGroup"]');
      const group = parseInt(await groupInput.inputValue());
      groups.push(group);
    }
    return groups;
  };

  const displayedGroups = await getDisplayedGroups();

  // Check that they are properly sorted by preferred group (could be ascending or descending)
  // Sort as strings since the server might be sorting as strings
  const expectedAscendingNumeric = [...displayedGroups].sort((a, b) => a - b);
  const expectedDescendingNumeric = [...displayedGroups].sort((a, b) => b - a);
  const expectedAscendingString = [...displayedGroups].sort((a, b) =>
    String(a).localeCompare(String(b)),
  );
  const expectedDescendingString = [...displayedGroups].sort((a, b) =>
    String(b).localeCompare(String(a)),
  );

  // The sorting should match either ascending or descending order (numeric or string)
  const isValidSort =
    JSON.stringify(displayedGroups) ===
    JSON.stringify(expectedAscendingNumeric) ||
    JSON.stringify(displayedGroups) ===
    JSON.stringify(expectedDescendingNumeric) ||
    JSON.stringify(displayedGroups) ===
    JSON.stringify(expectedAscendingString) ||
    JSON.stringify(displayedGroups) ===
    JSON.stringify(expectedDescendingString);

  expect(isValidSort).toBe(true);
});

test("can sort players by active status", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Navigate to the players page
  await page.goto("/app/players");

  // Set items per page to show all players to avoid pagination issues
  const itemsPerPageSelect = page.locator("#items-per-page");
  await itemsPerPageSelect.selectOption("50");
  await page.waitForTimeout(1000);

  // Click on active header
  const activeHeader = page.locator("th button").filter({ hasText: "Active" });
  await activeHeader.click();
  await page.waitForTimeout(1000);

  // Read all rows that are displayed
  const getDisplayedActiveStatuses = async () => {
    const statuses = [];
    const rows = page.locator("table tbody tr");
    const count = await rows.count();

    for (let i = 0; i < count; i++) {
      const activeCheckbox = rows.nth(i).locator('input[name="isActive"]');
      const isActive = await activeCheckbox.isChecked();
      statuses.push(isActive);
    }
    return statuses;
  };

  const displayedStatuses = await getDisplayedActiveStatuses();

  // Check that they are properly sorted by active status (could be ascending or descending)
  const expectedAscending = [...displayedStatuses].sort((a, b) =>
    a === b ? 0 : a ? 1 : -1,
  );
  const expectedDescending = [...displayedStatuses].sort((a, b) =>
    a === b ? 0 : a ? -1 : 1,
  );

  // The sorting should match either ascending or descending order
  const isValidSort =
    JSON.stringify(displayedStatuses) === JSON.stringify(expectedAscending) ||
    JSON.stringify(displayedStatuses) === JSON.stringify(expectedDescending);

  expect(isValidSort).toBe(true);
});

test("can sort players by email", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Navigate to the players page
  await page.goto("/app/players");

  // Set items per page to show all players to avoid pagination issues
  const itemsPerPageSelect = page.locator("#items-per-page");
  await itemsPerPageSelect.selectOption("50");
  await page.waitForTimeout(1000);

  // Click on email header
  const emailHeader = page
    .locator("th button")
    .filter({ hasText: "Email" })
    .filter({ hasNotText: "Notifications" });
  await emailHeader.click();
  await page.waitForTimeout(1000);

  // Read all rows that are displayed
  const getDisplayedEmails = async () => {
    const emails = [];
    const rows = page.locator("table tbody tr");
    const count = await rows.count();

    for (let i = 0; i < count; i++) {
      const emailInput = rows.nth(i).locator('input[name="email"]');
      const email = await emailInput.inputValue();
      emails.push(email);
    }
    return emails;
  };

  const displayedEmails = await getDisplayedEmails();

  // Check that they are properly sorted by email (could be ascending or descending)
  const expectedAscending = [...displayedEmails].sort();
  const expectedDescending = [...displayedEmails].sort().reverse();

  // The sorting should match either ascending or descending order
  const isValidSort =
    JSON.stringify(displayedEmails) === JSON.stringify(expectedAscending) ||
    JSON.stringify(displayedEmails) === JSON.stringify(expectedDescending);

  expect(isValidSort).toBe(true);
});

test("can sort players by email notifications", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Navigate to the players page
  await page.goto("/app/players");

  // Set items per page to show all players to avoid pagination issues
  const itemsPerPageSelect = page.locator("#items-per-page");
  await itemsPerPageSelect.selectOption("50");
  await page.waitForTimeout(1000);

  // Click on email notifications header
  const notificationsHeader = page
    .locator("th button")
    .filter({ hasText: "Email Notifications" });
  await notificationsHeader.click();
  await page.waitForTimeout(1000);

  // Read all rows that are displayed
  const getDisplayedNotificationStatuses = async () => {
    const statuses = [];
    const rows = page.locator("table tbody tr");
    const count = await rows.count();

    for (let i = 0; i < count; i++) {
      const notificationsCheckbox = rows
        .nth(i)
        .locator('input[name="emailNotificationsEnabled"]');
      const isEnabled = await notificationsCheckbox.isChecked();
      statuses.push(isEnabled);
    }
    return statuses;
  };

  const displayedStatuses = await getDisplayedNotificationStatuses();

  // Check that they are properly sorted by email notifications (could be ascending or descending)
  const expectedAscending = [...displayedStatuses].sort((a, b) =>
    a === b ? 0 : a ? 1 : -1,
  );
  const expectedDescending = [...displayedStatuses].sort((a, b) =>
    a === b ? 0 : a ? -1 : 1,
  );

  // The sorting should match either ascending or descending order
  const isValidSort =
    JSON.stringify(displayedStatuses) === JSON.stringify(expectedAscending) ||
    JSON.stringify(displayedStatuses) === JSON.stringify(expectedDescending);

  expect(isValidSort).toBe(true);
});

test("user can open player column config dialog", async ({ playerFixture }) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Verify the dialog has the correct title
  await expect(dialog).toContainText(
    "Configure which columns are visible in the players table",
  );

  // Check that the standard columns section exists
  await expect(dialog).toContainText("Standard Columns");

  // Check that standard columns are present with checkboxes
  const standardColumns = [
    "Name",
    "Email",
    "Preferred Group",
    "Active",
    "Email Notifications",
    "Picture",
    "Created",
    "Updated",
  ];

  for (const columnName of standardColumns) {
    const checkbox = page.locator(
      `[data-testid="standard-column-${columnName}"]`,
    );
    await expect(checkbox).toBeVisible();
    await expect(checkbox).toHaveAttribute("type", "checkbox");

    // Check that the label is present
    const label = page.locator(`label[for="standard-${columnName}"]`);
    await expect(label).toBeVisible();
    await expect(label).toContainText(columnName);
  }

  // Check that some default columns are checked by default
  const defaultColumns = [
    "Name",
    "Email",
    "Preferred Group",
    "Active",
    "Email Notifications",
  ];
  for (const columnName of defaultColumns) {
    const checkbox = page.locator(
      `[data-testid="standard-column-${columnName}"]`,
    );
    await expect(checkbox).toBeChecked();
  }

  // Check that custom columns section exists
  await expect(dialog).toContainText("Custom Columns");

  // The custom columns section should either show custom columns or an empty state
  const customColumnsSection = dialog
    .locator('h3:has-text("Custom Columns")')
    .locator("..");
  await expect(customColumnsSection).toBeVisible();
});

test("user can open player column config dialog, uncheck name column, save, and name column is hidden in players table", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Find the Name column checkbox and make sure it's initially checked
  const nameCheckbox = page.locator('[data-testid="standard-column-Name"]');
  await expect(nameCheckbox).toBeVisible();
  await expect(nameCheckbox).toBeChecked();

  // Uncheck the Name column checkbox
  await nameCheckbox.uncheck();
  await expect(nameCheckbox).not.toBeChecked();

  // Click the Save button
  const saveButton = page.locator('[data-testid="save-column-config-btn"]');
  await expect(saveButton).toBeVisible();
  await saveButton.click();

  // Wait for the modal to close (the dialog should disappear)
  await expect(dialog).not.toBeVisible();

  // Navigate to the players table
  await page.goto("/app/players");

  // Wait for the players table to load
  await page.waitForSelector('h1:has-text("Players")');

  // Check that the Name column header is NOT present in the table
  const nameColumnHeader = page.locator('th button:has-text("Name")');
  await expect(nameColumnHeader).not.toBeVisible();

  // Verify that other columns are still present
  const emailColumnHeader = page.locator('th button:has-text("Email")').first();
  await expect(emailColumnHeader).toBeVisible();

  const preferredGroupHeader = page.locator(
    'th button:has-text("Preferred Group")',
  );
  await expect(preferredGroupHeader).toBeVisible();

  const activeHeader = page.locator('th button:has-text("Active")');
  await expect(activeHeader).toBeVisible();

  const notificationsHeader = page.locator(
    'th button:has-text("Email Notifications")',
  );
  await expect(notificationsHeader).toBeVisible();
});

test("user can open player column config dialog, uncheck email column, save, and email column is hidden in players table", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Find the Email column checkbox and make sure it's initially checked
  const emailCheckbox = page.locator('[data-testid="standard-column-Email"]');
  await expect(emailCheckbox).toBeVisible();
  await expect(emailCheckbox).toBeChecked();

  // Uncheck the Email column checkbox
  await emailCheckbox.uncheck();
  await expect(emailCheckbox).not.toBeChecked();

  // Click the Save button
  const saveButton = page.locator('[data-testid="save-column-config-btn"]');
  await expect(saveButton).toBeVisible();
  await saveButton.click();

  // Wait for the modal to close (the dialog should disappear)
  await expect(dialog).not.toBeVisible();

  // Navigate to the players table
  await page.goto("/app/players");

  // Wait for the players table to load
  await page.waitForSelector('h1:has-text("Players")');

  // Check that the Email column header is NOT present in the table
  const emailColumnHeader = page
    .locator('th button:has-text("Email")')
    .filter({ hasNotText: "Notifications" });
  await expect(emailColumnHeader).not.toBeVisible();

  // Verify that other columns are still present
  const nameColumnHeader = page.locator('th button:has-text("Name")');
  await expect(nameColumnHeader).toBeVisible();

  const preferredGroupHeader = page.locator(
    'th button:has-text("Preferred Group")',
  );
  await expect(preferredGroupHeader).toBeVisible();

  const activeHeader = page.locator('th button:has-text("Active")');
  await expect(activeHeader).toBeVisible();

  const notificationsHeader = page.locator(
    'th button:has-text("Email Notifications")',
  );
  await expect(notificationsHeader).toBeVisible();
});

test("user can open player column config dialog, uncheck preferred group column, save, and preferred group column is hidden in players table", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Find the Preferred Group column checkbox and make sure it's initially checked
  const preferredGroupCheckbox = page.locator(
    '[data-testid="standard-column-Preferred Group"]',
  );
  await expect(preferredGroupCheckbox).toBeVisible();
  await expect(preferredGroupCheckbox).toBeChecked();

  // Uncheck the Preferred Group column checkbox
  await preferredGroupCheckbox.uncheck();
  await expect(preferredGroupCheckbox).not.toBeChecked();

  // Click the Save button
  const saveButton = page.locator('[data-testid="save-column-config-btn"]');
  await expect(saveButton).toBeVisible();
  await saveButton.click();

  // Wait for the modal to close (the dialog should disappear)
  await expect(dialog).not.toBeVisible();

  // Navigate to the players table
  await page.goto("/app/players");

  // Wait for the players table to load
  await page.waitForSelector('h1:has-text("Players")');

  // Check that the Preferred Group column header is NOT present in the table
  const preferredGroupColumnHeader = page.locator(
    'th button:has-text("Preferred Group")',
  );
  await expect(preferredGroupColumnHeader).not.toBeVisible();

  // Verify that other columns are still present
  const nameColumnHeader = page.locator('th button:has-text("Name")');
  await expect(nameColumnHeader).toBeVisible();

  const emailColumnHeader = page
    .locator('th button:has-text("Email")')
    .filter({ hasNotText: "Notifications" });
  await expect(emailColumnHeader).toBeVisible();

  const activeHeader = page.locator('th button:has-text("Active")');
  await expect(activeHeader).toBeVisible();

  const notificationsHeader = page.locator(
    'th button:has-text("Email Notifications")',
  );
  await expect(notificationsHeader).toBeVisible();
});

test("user can open player column config dialog, uncheck active column, save, and active column is hidden in players table", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Find the Active column checkbox and make sure it's initially checked
  const activeCheckbox = page.locator('[data-testid="standard-column-Active"]');
  await expect(activeCheckbox).toBeVisible();
  await expect(activeCheckbox).toBeChecked();

  // Uncheck the Active column checkbox
  await activeCheckbox.uncheck();
  await expect(activeCheckbox).not.toBeChecked();

  // Click the Save button
  const saveButton = page.locator('[data-testid="save-column-config-btn"]');
  await expect(saveButton).toBeVisible();
  await saveButton.click();

  // Wait for the modal to close (the dialog should disappear)
  await expect(dialog).not.toBeVisible();

  // Navigate to the players table
  await page.goto("/app/players");

  // Wait for the players table to load
  await page.waitForSelector('h1:has-text("Players")');

  // Check that the Active column header is NOT present in the table
  const activeColumnHeader = page.locator('th button:has-text("Active")');
  await expect(activeColumnHeader).not.toBeVisible();

  // Verify that other columns are still present
  const nameColumnHeader = page.locator('th button:has-text("Name")');
  await expect(nameColumnHeader).toBeVisible();

  const emailColumnHeader = page
    .locator('th button:has-text("Email")')
    .filter({ hasNotText: "Notifications" });
  await expect(emailColumnHeader).toBeVisible();

  const preferredGroupHeader = page.locator(
    'th button:has-text("Preferred Group")',
  );
  await expect(preferredGroupHeader).toBeVisible();

  const notificationsHeader = page.locator(
    'th button:has-text("Email Notifications")',
  );
  await expect(notificationsHeader).toBeVisible();
});

test("user can open player column config dialog, uncheck email notifications column, save, and email notifications column is hidden in players table", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Find the Email Notifications column checkbox and make sure it's initially checked
  const emailNotificationsCheckbox = page.locator(
    '[data-testid="standard-column-Email Notifications"]',
  );
  await expect(emailNotificationsCheckbox).toBeVisible();
  await expect(emailNotificationsCheckbox).toBeChecked();

  // Uncheck the Email Notifications column checkbox
  await emailNotificationsCheckbox.uncheck();
  await expect(emailNotificationsCheckbox).not.toBeChecked();

  // Click the Save button
  const saveButton = page.locator('[data-testid="save-column-config-btn"]');
  await expect(saveButton).toBeVisible();
  await saveButton.click();

  // Wait for the modal to close (the dialog should disappear)
  await expect(dialog).not.toBeVisible();

  // Navigate to the players table
  await page.goto("/app/players");

  // Wait for the players table to load
  await page.waitForSelector('h1:has-text("Players")');

  // Check that the Email Notifications column header is NOT present in the table
  const emailNotificationsColumnHeader = page.locator(
    'th button:has-text("Email Notifications")',
  );
  await expect(emailNotificationsColumnHeader).not.toBeVisible();

  // Verify that other columns are still present
  const nameColumnHeader = page.locator('th button:has-text("Name")');
  await expect(nameColumnHeader).toBeVisible();

  const emailColumnHeader = page
    .locator('th button:has-text("Email")')
    .filter({ hasNotText: "Notifications" });
  await expect(emailColumnHeader).toBeVisible();

  const preferredGroupHeader = page.locator(
    'th button:has-text("Preferred Group")',
  );
  await expect(preferredGroupHeader).toBeVisible();

  const activeHeader = page.locator('th button:has-text("Active")');
  await expect(activeHeader).toBeVisible();
});

test("user can open player column config dialog, uncheck picture column, save, and picture column stays hidden in players table", async ({
  playerFixture,
}) => {
  const { page } = playerFixture;

  // Navigate to the app settings page
  await page.goto("/app/settings/app");

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Coachpad - App Settings")');

  // Look for the player column config button
  const playerColumnConfigButton = page.locator(
    '[data-testid="player-column-config-btn"]',
  );
  await expect(playerColumnConfigButton).toBeVisible();

  // Click the button to open the dialog
  await playerColumnConfigButton.click();

  // Wait for the dialog to open
  const dialog = page.locator('[data-testid="player-column-config-dialog"]');
  await expect(dialog).toBeVisible();

  // Find the Picture column checkbox - it should be unchecked by default
  const pictureCheckbox = page.locator(
    '[data-testid="standard-column-Picture"]',
  );
  await expect(pictureCheckbox).toBeVisible();

  // Ensure it's unchecked (should be default state)
  await pictureCheckbox.uncheck();
  await expect(pictureCheckbox).not.toBeChecked();

  // Click the Save button to apply the change
  const saveButton = page.locator('[data-testid="save-column-config-btn"]');
  await expect(saveButton).toBeVisible();
  await saveButton.click();

  // Wait for the modal to close (the dialog should disappear)
  await expect(dialog).not.toBeVisible();

  // Navigate to the players table
  await page.goto("/app/players");
  await page.waitForSelector('h1:has-text("Players")');

  // Check that the Picture column header is NOT present in the table
  const pictureColumnHeader = page.locator('th button:has-text("Picture")');
  await expect(pictureColumnHeader).not.toBeVisible();

  // Verify that other columns are still present
  const nameColumnHeader = page.locator('th button:has-text("Name")');
  await expect(nameColumnHeader).toBeVisible();

  const emailColumnHeader = page
    .locator('th button:has-text("Email")')
    .filter({ hasNotText: "Notifications" });
  await expect(emailColumnHeader).toBeVisible();
});
