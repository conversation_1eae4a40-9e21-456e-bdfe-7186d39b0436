// Import the base test from <PERSON><PERSON>
import { test as base, <PERSON><PERSON><PERSON>, <PERSON> } from '@playwright/test';

// Define the test fixture for cookie consent without inheriting from other fixtures
export const test = base.extend<{
  consentValue: boolean;
  cookieConsentFixture: { page: Page, browser: Browser };
}>({
  // Accept consentValue as an optional parameter with default value 'true'
  consentValue: [true, { option: true }],

  // Define the cookie consent fixture
  cookieConsentFixture: async ({ page, consentValue, browser }, use) => {
    // Use addInitScript to set localStorage state for cookie consent before page load
    if (consentValue) {
      await page.context().addInitScript(() => {
        window.localStorage.setItem('cookieConsent', 'true');
      });
    }

    // Pass control to the test
    await use({ page, browser });

    // Clean up by resetting localStorage state to remove cookieConsent
    await page.context().addInitScript(() => {
      window.localStorage.removeItem('cookieConsent');
    });
  },
});

export const expect = base.expect;