import { test, expect } from '../auth.setup';
import { getBaseURL } from '../config';

test.describe('Delete User Feature', () => {
  test('User can successfully delete their account', async ({ isolatedUser }) => {
    // 1. Navigate to user settings
    await isolatedUser.page.goto('/app/settings/user');

    // 2. Find and click delete account button in danger zone
    const deleteButton = isolatedUser.page.locator('[data-testid="delete-account-btn"]');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();

    // 3. Confirm deletion in modal
    await expect(isolatedUser.page.locator('[data-testid="deleteConfirmation_modal"]')).toBeVisible();
    const confirmButton = isolatedUser.page.locator('[data-testid="confirm-delete-btn"]');
    await confirmButton.click();

    // 4. Wait for deletion process and redirect to homepage
    await expect(isolatedUser.page).toHaveURL(`${getBaseURL()}/?deleted=true`);

    // 5. Verify success message is displayed
    await expect(isolatedUser.page.locator('[data-testid="deletion-success-message"]')).toBeVisible();

    // 6. Test complete! The main delete functionality is working
    // Note: JWT token might still be valid, but user data is deleted from DB
  });
});
