import { test, expect } from "./teamFixture";

test.describe("Teams Export", () => {
  test("user can export teams to CSV", async ({ teamFixture }) => {
    const { page } = teamFixture;

    // Go to teams page
    await page.goto("/app/teams");

    // Wait for teams table to load
    await expect(
      page.locator('[data-testid="teams-table"]')
    ).toBeVisible();

    // Open actions dropdown 
    const actionsDropdown = page.locator('[data-testid="teams-actions-dropdown"]');
    await actionsDropdown.click();

    // Check that export CSV button is visible
    const exportCsvButton = page.locator('[data-testid="export-teams-csv-button"]');
    await expect(exportCsvButton).toBeVisible();
    await expect(exportCsvButton).toContainText("Export CSV");

    // Verify the export CSV button has the correct href attribute
    await expect(exportCsvButton).toHaveAttribute(
      "href",
      "/app/teams/export/csv"
    );

    // Test that export CSV is accessible (but don't actually click it as it causes navigation)
    await expect(exportCsvButton).toBeEnabled();
  });
});
