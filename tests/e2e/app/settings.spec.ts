import { test, expect } from '../auth.setup';
import { generateRandomEmail, generateRandomName, getRandomPastDate, generateRandomPhoneNumber, setDatePicker, TestDataGenerator } from '../utils';

test.describe('User Settings Page', () => {
  const settingsUrl = '/app/settings/user';

  test.beforeEach(async ({ isolatedUser }) => {
    await isolatedUser.page.goto(settingsUrl);
  });

  test('User can update full name', async ({ isolatedUser }) => {
    const fullNameInput = isolatedUser.page.locator('#name');
    const saveButton = isolatedUser.page.locator('[data-testid="save-changes-button"]');

    const randomName = generateRandomName();
    await fullNameInput.fill(randomName);
    await saveButton.click();

    await expect(isolatedUser.page.locator('[data-testid="user-settings-success"]')).toBeVisible();
    await expect(fullNameInput).toHaveValue(randomName);
  });

  test('User can update email address', async ({ isolatedUser }) => {
    const emailInput = isolatedUser.page.locator('#email');
    const saveButton = isolatedUser.page.locator('[data-testid="save-changes-button"]');

    // Generate a random email address
    const randomEmail = generateRandomEmail();
    await emailInput.fill(randomEmail);
    await saveButton.click();

    await expect(isolatedUser.page.locator('[data-testid="user-settings-success"]')).toBeVisible({ timeout: 10000 });
    await expect(emailInput).toHaveValue(randomEmail);
  });

  test('User can update country', async ({ isolatedUser }) => {
    const saveButton = isolatedUser.page.locator('[data-testid="save-changes-button"]');

    // Open the custom country select and choose 'ca' (Canada)
    await isolatedUser.page.locator('#country-button').click();
    await isolatedUser.page.locator('#country-option-ca').click();
    await saveButton.click();

    await expect(isolatedUser.page.locator('[data-testid="user-settings-success"]')).toBeVisible();
    // The hidden input should have the value 'ca'
    const countryInput = isolatedUser.page.locator('#country');
    await expect(countryInput).toHaveValue('ca');
  });

  test('User can update phone number', async ({ isolatedUser }) => {
    const phoneInput = isolatedUser.page.locator('#phone');
    const saveButton = isolatedUser.page.locator('[data-testid="save-changes-button"]');

    const randomPhone = generateRandomPhoneNumber();
    await phoneInput.fill(randomPhone);
    await saveButton.click();

    await expect(isolatedUser.page.locator('[data-testid="user-settings-success"]')).toBeVisible({ timeout: 10000 });
    await expect(phoneInput).toHaveValue(randomPhone);
  });

  test('User can update birth date', async ({ isolatedUser }) => {
    const birthDateInput = isolatedUser.page.locator('input[name="birthday"]');
    const saveButton = isolatedUser.page.locator('[data-testid="save-changes-button"]');

    // Get the initial value
    const initialValue = await birthDateInput.inputValue();

    // Use a date in the past to avoid potential validation issues with future dates
    const targetDateString = "1995-03-15"; // Fixed date for consistent testing

    // Use the utility function to set the date
    await setDatePicker(isolatedUser.page, 'input[name="birthday"]', targetDateString);

    // Get the actual value after date picker interaction
    const actualValue = await birthDateInput.inputValue();

    await saveButton.click();

    await expect(isolatedUser.page.locator('[data-testid="user-settings-success"]')).toBeVisible();
    // Verify that the date changed from the initial value
    expect(actualValue).not.toBe(initialValue);
    // Verify the final value matches what was set by the date picker
    await expect(birthDateInput).toHaveValue(actualValue);
  });

  test('User can update language preference', async ({ isolatedUser }) => {
    const langButton = isolatedUser.page.locator('#lang-button');
    const langOptionFr = isolatedUser.page.locator('#lang-option-fr');
    const saveButton = isolatedUser.page.locator('[data-testid="save-changes-button"]');

    await langButton.click();
    await langOptionFr.click();
    await saveButton.click();

    await expect(isolatedUser.page.locator('[data-testid="user-settings-success"]')).toBeVisible();
    // Check the hidden input value
    const langInput = isolatedUser.page.locator('input[name="lang"]');
    await expect(langInput).toHaveValue('fr');
  });
});

test.describe('App Settings Page', () => {
  const appSettingsUrl = '/app/settings/app';

  test.beforeEach(async ({ isolatedUser }) => {
    await isolatedUser.page.goto(appSettingsUrl);
  });

  test('User can add a new custom match column', async ({ isolatedUser }) => {
    // First, open the custom match columns modal
    const openModalButton = isolatedUser.page.locator('[data-testid="open-custom-match-columns-modal-btn"]');
    await openModalButton.click();

    // Wait for the modal to load
    await isolatedUser.page.waitForSelector('[data-testid="add-custom-match-column-btn"]', { timeout: 10000 });

    // Now click the add button inside the modal
    const addButton = isolatedUser.page.locator('[data-testid="add-custom-match-column-btn"]');
    await addButton.click();

    // Fill out the form
    const columnName = TestDataGenerator.string('Test Match Column', 7);
    await isolatedUser.page.locator('[data-testid="new-match-custom-column-form"] #name').fill(columnName);

    await isolatedUser.page.locator('[data-testid="new-match-custom-column-form"] #fieldType-button').click();
    await isolatedUser.page.locator('[data-testid="new-match-custom-column-form"] #fieldType-option-number').click();

    await isolatedUser.page.locator('[data-testid="new-match-custom-column-form"] [data-testid="save-custom-column-btn"]').click();

    await expect(isolatedUser.page.locator('[data-testid="newMatchCustomColumnForm_modal"]')).toBeHidden();
    await expect(isolatedUser.page.locator(".customMatchColumnsTable tbody tr")).toBeVisible();

    // Verify the new row contains the correct column name (name input is now in the second column due to checkbox column)
    const newRow = isolatedUser.page.locator(".customMatchColumnsTable tbody tr:last-child td:nth-child(2) input");
    await expect(newRow).toHaveValue(columnName);
  });
});
