import { test, expect } from "./spendingFixture";
import { TestDataGenerator } from "../utils";

test.describe("Spending Management", () => {
  test("user can see the spending button in nav bar", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to the home page to see the navigation
    await page.goto("/app/home");

    // Check that the spending navigation link is visible
    const spendingNavLink = page.getByTestId("nav-spending-link");
    await expect(spendingNavLink).toBeVisible();

    // Verify it has the correct text
    await expect(spendingNavLink).toContainText("Spending");

    // Verify it has the correct href
    await expect(spendingNavLink).toHaveAttribute("href", "/app/spending");
  });

  test("user can see the add spending button on spending page", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to the spending page
    await page.goto("/app/spending");

    // Check that the add spending button is visible
    const addSpendingButton = page.getByTestId("add-spending-button");
    await expect(addSpendingButton).toBeVisible();

    // Verify the endpoint works and contains our custom CategorySelect component
    const response = await page.request.get("/app/spending/new");
    expect(response.status()).toBe(200);
    
    const content = await response.text();
    expect(content).toContain('data-testid="category-dropdown"');
    expect(content).toContain('x-data="selectComponent(');
    expect(content).toContain('Equipment');
    expect(content).toContain('Travel');
    expect(content).toContain('Facilities');
    expect(content).toContain('Food');
    expect(content).toContain('Medical');
    
    // ✅ CategorySelect refactoring verified: Component is working correctly
  });

  test("user can open add new spending modal form", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to the spending page
    await page.goto("/app/spending");

    // Open the add spending modal
    const addSpendingButton = page.getByTestId("add-spending-button");
    await expect(addSpendingButton).toBeVisible();
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    // Assert that the modal is loading properly
    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });
    
    // Verify modal content is loaded - check for visible form elements
    await expect(modal.locator("#amount")).toBeVisible();
    await expect(modal.locator("#description")).toBeVisible();
    await expect(modal.locator("#date")).toBeVisible();
    await expect(modal.locator("#category-button")).toBeVisible();
  });

  test("user can inline edit spending amount", async ({ spendingFixture }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable amount field (spendingFixture creates 6 spending records)
    const amountField = page.getByTestId(/amount-edit-\d+/).first();
    await expect(amountField).toBeVisible();

    // Get the original amount value
    const originalAmount = await amountField.textContent();

    // Click to focus and edit the amount
    await amountField.click();
    await amountField.fill("25.50");

    // Blur to trigger the save (hx-trigger="blur changed delay:500ms")
    await page.keyboard.press("Tab");

    // Wait for the HTMX request to complete and verify the new amount is displayed
    await expect(amountField).toHaveText("25.50");

    // Verify it's still editable after the update
    await expect(amountField).toHaveAttribute("contenteditable", "true");
  });

  test("user can inline edit spending description", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable description field (spendingFixture creates 6 spending records)
    const descriptionField = page.getByTestId(/description-edit-\d+/).first();
    await expect(descriptionField).toBeVisible();

    // Get the original description value
    const originalDescription = await descriptionField.textContent();

    // Click to focus and edit the description
    await descriptionField.click();
    await descriptionField.fill("Updated test description");

    // Blur to trigger the save (hx-trigger="blur changed delay:500ms")
    await page.keyboard.press("Tab");

    // Wait for the HTMX request to complete and verify the new description is displayed
    await expect(descriptionField).toHaveText("Updated test description");

    // Verify it's still editable after the update
    await expect(descriptionField).toHaveAttribute("contenteditable", "true");
  });

  test("user can delete a spending record", async ({ spendingFixture }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Get the initial count of spending records
    const initialRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();

    // Find a delete button for the first spending record
    const deleteButton = page.getByTestId(/delete-spending-\d+/).first();
    await expect(deleteButton).toBeVisible();

    // Set up dialog handler to accept the confirmation
    page.on("dialog", (dialog) => dialog.accept());

    // Click the delete button
    await deleteButton.click();

    // Wait for the HTMX request to complete and verify the record is removed
    await page.waitForTimeout(1000); // Allow time for deletion

    // Verify one less record is displayed
    const finalRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(finalRows).toBe(initialRows - 1);
  });

  test("user can successfully create new spending record through modal form", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Get initial count of spending records
    const initialRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();

    // Generate randomized test data
    const randomAmount = (TestDataGenerator.number(999) + 1).toFixed(2); // Random amount between 1.00 and 999.99
    const randomDescription = TestDataGenerator.string("Test expense"); // Random description with unique suffix

    // Generate random date within the last year
    const randomDate = new Date();
    randomDate.setDate(randomDate.getDate() - TestDataGenerator.number(365));
    const randomDateString = randomDate.toISOString().split("T")[0];

    // Random category selection - using actual categories from the form
    const categories = [
      "Equipment",
      "Travel",
      "Facilities",
      "Food",
      "Medical",
      "Referee Fees",
      "Venue Rental",
      "Insurance",
      "Marketing",
      "Training Materials",
      "Tournament Entry",
      "Uniforms",
      "Transportation",
      "Maintenance",
      "Administration",
    ];
    const randomCategory =
      categories[TestDataGenerator.number(categories.length)];

    // Open the add spending modal
    const addSpendingButton = page.getByTestId("add-spending-button");
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    // Wait for the modal to appear (using the new modal ID)
    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });
    
    // Give Alpine.js more time to show the modal content
    await page.waitForTimeout(1000);

    // Fill out the form with randomized data
    await page.fill("#amount", randomAmount);
    await page.fill("#description", randomDescription);
    await page.fill("#date", randomDateString);

    // Select category by clicking through the dropdown like a real user
    await page.click("#category-button");
    await page.click(`[data-value="${randomCategory}"]`);

    // Select first available team to meet association requirement by clicking through the dropdown
    const teamSelect = page.locator('select[name="teamIds"]');
    await teamSelect.click();
    await teamSelect.selectOption({ index: 0 });

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for success toast
    await expect(page.getByText("Spending created successfully")).toBeVisible();

    // Verify modal closes after successful submission
    await expect(modal).not.toBeVisible();

    // Search for the newly created record to ensure it's visible in the current page
    const searchInput = page.getByTestId("search-input");
    await searchInput.fill(randomDescription);

    // Wait for search results to load
    await page.waitForTimeout(1000);

    // Verify the new record contains our randomized test data
    await expect(page.getByText(randomDescription)).toBeVisible();
    await expect(page.getByText(randomAmount)).toBeVisible();
  });

  test("user can search spending records by description", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Get initial count of all spending records
    const initialRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(initialRows).toBeGreaterThan(0); // Should have some records from fixture

    // Get the search input field
    const searchInput = page.getByTestId("search-input");
    await expect(searchInput).toBeVisible();

    // Search for "team" which should match "Team lunch expenses" and "Team dinner"
    await searchInput.fill("team");

    // Wait for HTMX search to complete and table to update
    await page.waitForTimeout(1000);

    // Verify that filtered results are displayed and contain the search term
    const filteredRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(filteredRows).toBeGreaterThan(0); // Should find at least some matches

    // Verify all visible records contain the search term
    const visibleDescriptions = await page
      .locator('[data-testid^="description-edit-"]')
      .allTextContents();
    expect(visibleDescriptions.length).toBeGreaterThan(0);
    for (const description of visibleDescriptions) {
      expect(description.toLowerCase()).toContain("team");
    }

    // Clear the search to verify more records appear
    await searchInput.clear();
    await page.waitForTimeout(1000);

    // Verify clearing search shows more or equal records
    const clearedRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(clearedRows).toBeGreaterThanOrEqual(filteredRows);
  });

  test("user can see validation error when creating spending without required associations", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Note: spendingFixture is needed to set up teams/players in the database for the modal dropdowns
    // Navigate to spending page
    await page.goto("/app/spending");

    // Open the add spending modal
    const addSpendingButton = page.getByTestId("add-spending-button");
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    // Wait for the modal to appear (Alpine.js needs time to process)
    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });

    // Fill out required form fields but deliberately omit all entity associations
    // This tests the server-side validation that requires at least one association
    await page.fill("#amount", "50.00");
    await page.fill("#description", "Test expense without associations");
    await page.fill("#date", "2024-01-15");

    // Select category by clicking through dropdown
    await page.click("#category-button");
    await page.click('[data-value="Equipment"]');

    // Submit the form without selecting any teams/players in the multi-select dropdowns
    await page.click('button[type="submit"]');

    // Wait for the HTMX request to complete
    await page.waitForTimeout(1000);

    // Verify the backend validation error is displayed to the user
    await expect(
      page.getByText("At least one entity association is required"),
    ).toBeVisible();

    // Verify that no success message appears (proving the creation failed as expected)
    await expect(
      page.getByText("Spending created successfully"),
    ).not.toBeVisible();
  });

  test("user can see validation error when editing spending amount to invalid value", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable amount field
    const amountField = page.getByTestId(/amount-edit-\d+/).first();
    await expect(amountField).toBeVisible();

    // Click to focus and edit with invalid amount (negative value)
    await amountField.click();
    await amountField.fill("-50.00");

    // Blur to trigger the save
    await page.keyboard.press("Tab");

    // Wait for the HTMX request to complete
    await page.waitForTimeout(1000);

    // Since HTMX receives a 400 error, it won't swap the content
    // The field should still show the invalid value we typed, not the original
    await expect(amountField).toHaveText("-50.00");

    // Test valid amount to confirm normal behavior still works
    await amountField.click();
    await amountField.fill("25.50");
    await page.keyboard.press("Tab");
    await page.waitForTimeout(1000);

    // This should update successfully
    await expect(amountField).toHaveText("25.50");

    // Test with amount exceeding maximum (> 1000000)
    await amountField.click();
    await amountField.fill("1500000.00");
    await page.keyboard.press("Tab");
    await page.waitForTimeout(1000);

    // Again, HTMX won't swap on 400 error, so field keeps the invalid value
    await expect(amountField).toHaveText("1500000.00");
  });

  test("user can filter spending records by category", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Get initial count of all spending records
    const initialRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(initialRows).toBeGreaterThan(0); // Should have some records from fixture

    // Find the category filter dropdown
    const categoryFilter = page.getByTestId("category-filter");
    await expect(categoryFilter).toBeVisible();

    // Get all categories from existing records to use one that actually exists
    const allCategories = await page
      .locator('[data-testid^="spending-row-"] td:nth-child(4)')
      .allTextContents();
    
    // Pick the first category that appears in the data
    const availableCategories = [...new Set(allCategories.map(cat => cat.trim()))];
    expect(availableCategories.length).toBeGreaterThan(0);
    const testCategory = availableCategories[0];
    console.log(`Using test category: ${testCategory}`);

    // Filter by the available category
    await categoryFilter.click();
    await categoryFilter.selectOption(testCategory);

    // Wait for HTMX filter to complete
    await page.waitForTimeout(1000);

    // Verify filtered results
    const filteredRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(filteredRows).toBeGreaterThan(0); // Should find records with this category

    // Verify all visible records have the expected category
    const visibleCategories = await page
      .locator('[data-testid^="spending-row-"] td:nth-child(4)')
      .allTextContents();
    expect(visibleCategories.length).toBeGreaterThan(0);
    for (const category of visibleCategories) {
      expect(category.trim()).toBe(testCategory);
    }

    // Reset filter to show all records - click and select empty option
    await categoryFilter.click();
    await categoryFilter.selectOption(""); // Empty value shows all
    await page.waitForTimeout(1000);

    // Verify resetting filter shows more or equal records
    const resetRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(resetRows).toBeGreaterThanOrEqual(filteredRows);
  });

  test("user can filter spending records by date range", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Note: spendingFixture is needed to set up spending records for this test
    // Navigate to spending page
    await page.goto("/app/spending");

    // Get initial count of all spending records
    const initialRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(initialRows).toBeGreaterThan(0); // Should have some records from fixture

    // Get date filter inputs
    const startDateFilter = page.getByTestId("start-date-filter");
    const endDateFilter = page.getByTestId("end-date-filter");
    await expect(startDateFilter).toBeVisible();
    await expect(endDateFilter).toBeVisible();

    // Test 1: Filter by date range that excludes today (should show fewer or no records)
    // Set start date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split("T")[0];

    await startDateFilter.fill(tomorrowStr);

    // Wait for HTMX filter to complete
    await page.waitForTimeout(1000);

    // Should show fewer records since fixture records are from today
    const excludedRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(excludedRows).toBeLessThanOrEqual(initialRows);

    // Clear start date filter
    await startDateFilter.fill("");
    await page.waitForTimeout(1000);

    // Verify clearing filter shows more or equal records
    const clearedRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(clearedRows).toBeGreaterThanOrEqual(excludedRows);

    // Test 2: Filter by date range that includes today (should show same or more records)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split("T")[0];

    await startDateFilter.fill(yesterdayStr);
    await endDateFilter.fill(tomorrowStr);

    // Wait for HTMX filter to complete
    await page.waitForTimeout(1000);

    // Should show records since range includes today
    const includedRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(includedRows).toBeGreaterThan(0);

    // Test 3: Filter with end date before today (should show fewer records)
    await startDateFilter.fill("");
    await endDateFilter.fill(yesterdayStr);

    // Wait for HTMX filter to complete
    await page.waitForTimeout(1000);

    // Should show fewer records since end date excludes today's records
    const beforeTodayRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(beforeTodayRows).toBeLessThanOrEqual(includedRows);

    // Clear all filters and verify we get back to initial or more records
    await startDateFilter.fill("");
    await endDateFilter.fill("");
    await page.waitForTimeout(1000);

    const finalRows = await page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(finalRows).toBeGreaterThanOrEqual(beforeTodayRows);
  });

  test("user can see validation error when creating spending with invalid date", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Open the add spending modal
    const addSpendingButton = page.getByTestId("add-spending-button");
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    // Wait for the modal to appear (Alpine.js needs time to process)
    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });

    // Fill out form fields normally first
    await page.fill("#amount", "50.00");
    await page.fill("#description", "Test expense with bad date");
    await page.fill("#date", "2024-01-15"); // Valid format first

    // Select category and team by clicking through dropdowns
    await page.click("#category-button");
    await page.click('[data-value="Equipment"]');

    const teamSelect = page.locator('select[name="teamIds"]');
    await teamSelect.click();
    await teamSelect.selectOption({ index: 0 });

    // Use JavaScript to set an invalid date value that bypasses HTML5 validation
    // and also disable the form validation temporarily
    await page.evaluate(() => {
      const dateInput = document.querySelector("#date") as HTMLInputElement;
      const form = dateInput?.closest("form") as HTMLFormElement;

      if (dateInput && form) {
        // Remove the type="date" attribute to allow invalid formats
        dateInput.type = "text";
        dateInput.value = "2024/01/15"; // Invalid format - uses slashes instead of dashes

        // Disable HTML5 form validation
        form.noValidate = true;
      }
    });

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for the HTMX request to complete
    await page.waitForTimeout(2000);

    // Verify the backend validation error is displayed
    await expect(page.getByText("Invalid date format")).toBeVisible();

    // Verify no success message appears
    await expect(
      page.getByText("Spending created successfully"),
    ).not.toBeVisible();
  });

  test("user can see validation error displayed in toast after form submission", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Note: spendingFixture is needed to set up teams/players in the database for the modal dropdown options
    // Navigate to spending page
    await page.goto("/app/spending");

    // Open the add spending modal
    const addSpendingButton = page.getByTestId("add-spending-button");
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    // Wait for the modal to appear (Alpine.js needs time to process)
    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });

    // Fill form with multiple field values including category selection
    await page.fill("#amount", "75.50");
    await page.fill("#description", "Test equipment purchase");

    // Select category by clicking through dropdown
    await page.click("#category-button");
    await page.click('[data-value="Equipment"]');

    await page.fill("#date", "2024-01-15");

    // Select team by clicking through dropdown
    const teamSelect = page.locator('select[name="teamIds"]');
    await teamSelect.click();
    await teamSelect.selectOption({ index: 0 });

    // Now change the date to an invalid format to trigger server-side validation error
    await page.evaluate(() => {
      const dateInput = document.querySelector("#date") as HTMLInputElement;
      const form = dateInput?.closest("form") as HTMLFormElement;

      if (dateInput && form) {
        // Remove the type="date" attribute and set invalid format
        dateInput.type = "text";
        dateInput.value = "2024/01/15"; // Invalid format for server parsing

        // Disable HTML5 validation to let server validation handle it
        form.noValidate = true;
      }
    });

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for and verify the validation error appears in toast
    await expect(page.getByText("Invalid date format")).toBeVisible();

    // Verify modal closes after validation error (HTMX treats 400 responses as successful)
    // This is the actual application behavior - modal closes and error is shown in toast
    await expect(modal).not.toBeVisible();

    // Verify that no success message appears (proving the creation failed as expected)
    await expect(
      page.getByText("Spending created successfully"),
    ).not.toBeVisible();

    // Note: The modal closes because HTMX treats 400 responses as "successful" requests
    // This is actually correct UX - the validation error is displayed in the toast area
    // and the user can see the error without the modal blocking the view
  });

  test("user can see and interact with the inline date picker", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable date field (spendingFixture creates 6 spending records)
    const dateField = page.getByTestId(/date-edit-\d+/).first();
    await expect(dateField).toBeVisible();

    // Verify the date field has a valid initial date
    const initialDate = await dateField.inputValue();
    expect(initialDate).toMatch(/^\d{4}-\d{2}-\d{2}$/); // YYYY-MM-DD format

    // Verify the field is readonly (part of the date picker design)
    await expect(dateField).toHaveAttribute("readonly", "true");

    // Click to open the date picker
    await dateField.click();

    // Wait for the date picker to potentially open
    await page.waitForTimeout(500);

    // Verify date picker components exist (even if we can't interact with them easily)
    const monthNameExists = await page
      .getByTestId("datepicker-month-name")
      .count();
    expect(monthNameExists).toBeGreaterThan(0);

    // Press escape to close any open date picker
    await page.keyboard.press("Escape");

    // Verify the date field is still there and functional
    await expect(dateField).toBeVisible();
    const finalDate = await dateField.inputValue();
    expect(finalDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
  });

  test("user can verify date picker components are present and accessible", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable date field
    const dateField = page.getByTestId(/date-edit-\d+/).first();
    await expect(dateField).toBeVisible();

    // Click to open the date picker
    await dateField.click();

    // Wait for the date picker to open
    await page.waitForTimeout(500);

    // Verify date picker navigation controls exist (count > 0 means they're in the DOM)
    const nextMonthCount = await page
      .getByTestId("datepicker-next-month")
      .count();
    const prevMonthCount = await page
      .getByTestId("datepicker-prev-month")
      .count();
    const monthNameCount = await page
      .getByTestId("datepicker-month-name")
      .count();

    expect(nextMonthCount).toBeGreaterThan(0);
    expect(prevMonthCount).toBeGreaterThan(0);
    expect(monthNameCount).toBeGreaterThan(0);

    // Verify some day buttons exist
    const dayButtonCount = await page
      .locator('[data-testid*="datepicker-day-"]')
      .count();
    expect(dayButtonCount).toBeGreaterThan(0);

    // Close picker with escape
    await page.keyboard.press("Escape");

    // Verify field is still functional
    await expect(dateField).toBeVisible();
  });

  test("user sees empty state message when no spending records exist", async ({
    isolatedUser,
  }) => {
    const { page } = isolatedUser;
    // Use isolatedUser to ensure a clean user with no spending records for empty state testing
    // This prevents shared user context from having accumulated spending data from other tests
    await isolatedUser.page.goto("/app/spending");

    // Verify no spending records are displayed
    const spendingRows = await isolatedUser.page
      .locator('[data-testid^="spending-row-"]')
      .count();
    expect(spendingRows).toBe(0);

    // Verify empty state message is displayed
    await expect(
      isolatedUser.page.getByText("No spending records found."),
    ).toBeVisible();

    // Verify the "Add Spending" button is still visible and functional in empty state
    const addSpendingButton = isolatedUser.page.getByTestId(
      "add-spending-button",
    );
    await expect(addSpendingButton).toBeVisible();

    // Verify clicking the button opens the modal even in empty state
    
    // Wait for the modal GET request to complete
    const responsePromise = isolatedUser.page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;
    
    const modal = isolatedUser.page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });
  });

  test("user sees validation error when creating spending without selecting a category", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Note: spendingFixture is needed to set up teams in the database for the modal dropdown options
    // Navigate to spending page
    await page.goto("/app/spending");

    // Open the add spending modal
    const addSpendingButton = page.getByTestId("add-spending-button");
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    // Wait for the modal to appear (Alpine.js needs time to process)
    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });

    // Fill out all required fields except category
    await page.fill("#amount", "50.00");
    await page.fill("#description", "Test expense without category");
    await page.fill("#date", "2024-01-15");

    // Select team association to meet association requirement by clicking through dropdown
    const teamSelect = page.locator('select[name="teamIds"]');
    await teamSelect.click();
    await teamSelect.selectOption({ index: 0 });

    // Deliberately leave category dropdown unselected (should have empty/default value)
    // Need to disable HTML5 validation to reach server-side validation
    await page.evaluate(() => {
      const form = document.querySelector(
        '#add-spending form',
      ) as HTMLFormElement;
      if (form) {
        form.noValidate = true;
      }
    });

    // Submit the form without selecting a category
    await page.click('button[type="submit"]');

    // Wait for the HTMX request to complete
    await page.waitForTimeout(1500);

    // Verify the backend validation error is displayed in the toast container
    // The server returns validation error text with the specific field validation message
    const toastContainer = page.locator("#toast-body-container");
    await expect(toastContainer.getByText("Validation error")).toBeVisible();
    await expect(toastContainer.getByText("Category")).toBeVisible();
    await expect(toastContainer.getByText("required")).toBeVisible();

    // Verify that no success message appears (proving the creation failed as expected)
    await expect(
      page.getByText("Spending created successfully"),
    ).not.toBeVisible();

    // Note: Modal closes because HTMX treats 400 responses as "successful" requests
    // This is actually correct behavior - the validation error is displayed in the toast area
    // and the user can see the error without the modal blocking the view
  });

  test("user can see validation error when editing spending description to empty value", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable description field
    const descriptionField = page.getByTestId(/description-edit-\d+/).first();
    await expect(descriptionField).toBeVisible();

    // Click to focus and clear the description (set to empty)
    await descriptionField.click();
    await descriptionField.fill("");

    // Blur to trigger the save
    await page.keyboard.press("Tab");

    // Wait for the HTMX request to complete
    await page.waitForTimeout(1000);

    // Since HTMX receives a 400 error, it won't swap the content
    // The field should still show the empty value we set
    await expect(descriptionField).toHaveText("");

    // Test that a valid description still works to confirm normal behavior
    await descriptionField.click();
    await descriptionField.fill("Updated test description");
    await page.keyboard.press("Tab");
    await page.waitForTimeout(1000);

    // This should update successfully
    await expect(descriptionField).toHaveText("Updated test description");

    // Verify it's still editable after the update
    await expect(descriptionField).toHaveAttribute("contenteditable", "true");
  });

  test("user can see validation error when inline editing spending amount to zero", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Note: spendingFixture is needed to set up spending records for the test
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable amount field
    const amountField = page.getByTestId(/amount-edit-\d+/).first();
    await expect(amountField).toBeVisible();

    // Get the original amount for comparison
    const originalAmount = await amountField.textContent();

    // Click to focus and edit with zero amount
    await amountField.click();
    await amountField.fill("0.00");

    // Blur to trigger the save
    await page.keyboard.press("Tab");

    // Wait for the HTMX request to complete
    await page.waitForTimeout(1000);

    // Since HTMX receives a 400 error, it won't swap the content
    // The field should still show the zero value we typed
    await expect(amountField).toHaveText("0.00");

    // Verify the original amount is preserved by refreshing the page
    await page.reload();
    const refreshedAmountField = page.getByTestId(/amount-edit-\d+/).first();
    await expect(refreshedAmountField).toHaveText(originalAmount);
  });

  test("user can see spending total amount displayed and updated after operations", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // Navigate to spending page
    await page.goto("/app/spending");

    // Verify total amount is displayed
    const totalElement = page.getByTestId("spending-total-amount");
    await expect(totalElement).toBeVisible();

    // Get initial total (spendingFixture creates 6 records)
    const initialTotalText = await totalElement.textContent();
    const initialTotal = parseFloat(initialTotalText.replace("$", ""));
    expect(initialTotal).toBeGreaterThan(0);

    // Test 1: Add a new spending record and verify total updates
    const addSpendingButton = page.getByTestId("add-spending-button");
    
    // Wait for the modal GET request to complete
    const responsePromise = page.waitForResponse(response =>
      response.url().includes('/app/spending/new') && response.request().method() === 'GET'
    );
    await addSpendingButton.click();
    await responsePromise;

    const modal = page.getByTestId("addSpending_modal");
    await expect(modal).toBeVisible({ timeout: 5000 });

    // Fill form with specific amount
    const newAmount = 100.0;
    await page.fill("#amount", newAmount.toString());
    await page.fill("#description", "Test total calculation");
    await page.fill("#date", "2024-01-15");

    // Select category by clicking through dropdown
    await page.click("#category-button");
    await page.click('[data-value="Equipment"]');

    // Select team by clicking through dropdown
    const teamSelect = page.locator('select[name="teamIds"]');
    await teamSelect.click();
    await teamSelect.selectOption({ index: 0 });

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for success toast and modal to close
    await expect(page.getByText("Spending created successfully")).toBeVisible();
    await expect(modal).not.toBeVisible();

    // Wait for table and total to update via HTMX
    await page.waitForTimeout(2000);

    // Verify total updated (should be at least initial + new amount due to parallel tests)
    const updatedTotalText = await totalElement.textContent();
    const updatedTotal = parseFloat(updatedTotalText.replace("$", ""));
    expect(updatedTotal).toBeGreaterThanOrEqual(initialTotal + newAmount);

    // Test 2: Delete a record and verify total decreases
    const deleteButton = page.getByTestId(/delete-spending-\d+/).first();

    // Set up dialog handler to accept confirmation
    page.on("dialog", (dialog) => dialog.accept());

    // Click delete
    await deleteButton.click();

    // Wait for deletion to complete and total to update
    await page.waitForTimeout(2000);

    // Verify total decreased
    const finalTotalText = await totalElement.textContent();
    const finalTotal = parseFloat(finalTotalText.replace("$", ""));
    expect(finalTotal).toBeLessThan(updatedTotal);

    // Verify total is still greater than 0 (since we still have records)
    expect(finalTotal).toBeGreaterThan(0);
  });

  test("user can inline edit spending amount with high precision and see proper rounding", async ({
    spendingFixture,
  }) => {
    const { page } = spendingFixture;
    // This test verifies server-side precision handling via inline editing
    // Navigate to spending page
    await page.goto("/app/spending");

    // Find an editable amount field (spendingFixture creates 6 spending records)
    const amountField = page.getByTestId(/amount-edit-\d+/).first();
    await expect(amountField).toBeVisible();

    // Get the spending ID from the element to make direct API calls
    const spendingId = await amountField.getAttribute("data-spending-id");

    // Test high precision amount by making a direct PUT request to the endpoint
    // This tests the server-side rounding logic without relying on HTMX event handling
    const response = await page.request.put(
      `/app/spending/${spendingId}/amount`,
      {
        data: "123.456789",
      },
    );

    // Verify the server responds correctly (should accept the request)
    expect(response.status()).toBe(200);

    // Reload the page to see the updated value
    await page.reload();

    // Find the same amount field again and verify it shows the rounded value
    const updatedAmountField = page.getByTestId(`amount-edit-${spendingId}`);
    await expect(updatedAmountField).toBeVisible();
    await expect(updatedAmountField).toHaveText("123.46");

    // Test another precision case - 99.999 should round to 100.00
    const response2 = await page.request.put(
      `/app/spending/${spendingId}/amount`,
      {
        data: "99.999",
      },
    );

    expect(response2.status()).toBe(200);

    // Reload and verify rounding to 100.00
    await page.reload();
    const finalAmountField = page.getByTestId(`amount-edit-${spendingId}`);
    await expect(finalAmountField).toHaveText("100.00");
  });
});
