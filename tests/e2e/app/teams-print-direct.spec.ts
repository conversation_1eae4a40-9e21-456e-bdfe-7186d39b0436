import { test, expect } from "./teamFixture";

test.describe("Teams Print Direct Test", () => {
  test("teams print URL works directly", async ({ teamFixture, page }) => {
    // Navigate directly to the teams print URL
    await page.goto("/app/teams?print=true");
    
    // Get the page content
    const html = await page.content();
    console.log("Direct print page content:", html.substring(0, 1000));
    
    // Check if our print template is being used
    expect(html).toContain("<!DOCTYPE html>");
    expect(html).toContain("Teams Report");
    expect(html).toContain("Team Name");
    expect(html).toContain("Description");
  });
});
