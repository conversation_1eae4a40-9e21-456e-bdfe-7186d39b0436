import { config } from "dotenv";
import * as path from "path";

// Load environment variables from .env.development file
config({ path: path.resolve(__dirname, '../../.env.development') });

/**
 * Get the backend port from environment variable, matching the backend logic
 * For E2E tests, we expect the backend to be running on the development port (9000)
 * This should match the port in .env.development
 */
export function getBackendPort(): string {
  const port = process.env.COACHPAD_BACKEND_PORT;
  if (!port) {
    throw new Error("COACHPAD_BACKEND_PORT environment variable is required");
  }
  return port;
}

/**
 * Get the base URL for the application
 */
export function getBaseURL(): string {
  return `http://localhost:${getBackendPort()}`;
}
