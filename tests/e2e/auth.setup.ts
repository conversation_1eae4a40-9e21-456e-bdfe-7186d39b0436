import { test as baseTest, expect, Page, BrowserContext } from '@playwright/test';
import { test as cookieConsentTest } from './app/cookieConsentFixture';
import * as fs from 'fs';
import * as path from 'path';
import { getBaseURL } from './config';
import { generateRandomString } from './testData';

export * from '@playwright/test';

// Define a type for the isolated user fixture's return value
interface IsolatedUserContext {
  page: Page;
  userId: number;
  email: string;
}

// Add userId to the test fixtures
// Extend the baseTest to include your existing fixtures and the new isolatedUser fixture
export const test = baseTest.extend<{ userId: number, isolatedUser: IsolatedUserContext, consentValue: boolean, customMatchColumns: Array<{ name?: string, fieldType?: string, description?: string, isRequired?: boolean, displayOrder?: number }>, autoConsent: boolean, page: Page }, { workerStorageState: string, workerUserId: number, workerUserCredentials: { email: string, password: string } }>({
  // Use the same storage state for all tests in this worker.
  storageState: ({ workerStorageState }, use) => use(workerStorageState),

  // Expose userId as a test fixture that uses the worker-level userId
  userId: ({ workerUserId }, use) => use(workerUserId),

  // Store userId at worker level
  workerUserId: [async ({ }, use) => {
    // Use parallelIndex as a unique identifier for each worker.
    const id = baseTest.info().parallelIndex;
    const userIdFileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}-userId.txt`);

    // If we've already stored the userId, use it
    if (fs.existsSync(userIdFileName)) {
      const userId = parseInt(fs.readFileSync(userIdFileName, 'utf8'), 10);
      await use(userId);
      return;
    }

    // This should not happen in normal flows, but we need a fallback
    console.warn('UserId not found! Authentication may need to be run first.');
    await use(0); // Provide a default/invalid ID that will cause tests to fail meaningfully
  }, { scope: 'worker' }],

  // Store email and password at worker level
  workerUserCredentials: [async ({ }, use) => {
    const id = baseTest.info().parallelIndex;
    const emailFileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}-email.txt`);
    const passwordFileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}-password.txt`);

    // If we've already stored the email and password, use them
    if (fs.existsSync(emailFileName) && fs.existsSync(passwordFileName)) {
      const email = fs.readFileSync(emailFileName, 'utf8');
      const password = fs.readFileSync(passwordFileName, 'utf8');
      await use({ email, password });
      return;
    }

    // This should not happen in normal flows, but we need a fallback
    console.warn('User credentials not found! Authentication may need to be run first.');
    await use({ email: '', password: '' }); // Provide default/invalid credentials
  }, { scope: 'worker' }],

  // Authenticate once per worker with a worker-scoped fixture.
  workerStorageState: [async ({ browser }, use) => {
    // Use parallelIndex as a unique identifier for each worker.
    const id = baseTest.info().parallelIndex;
    const fileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}.json`);
    const userIdFileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}-userId.txt`);
    const emailFileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}-email.txt`);
    const passwordFileName = path.resolve(baseTest.info().project.outputDir, `.auth/${id}-password.txt`);

    // Ensure directory exists
    const dir = path.dirname(fileName);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    if (fs.existsSync(fileName)) {
      // Reuse existing authentication state if any.
      await use(fileName);
      return;
    }

    // Important: make sure we authenticate in a clean environment by unsetting storage state.
    const page = await browser.newPage({ storageState: undefined });

    try {
      // First create a test user using our development endpoint
      console.log(`Creating test user for worker ${id}...`);
      // Generate unique email per worker to avoid conflicts
      const timestamp = Date.now(); // Add timestamp for additional uniqueness
      const testEmail = `testuser-${id}-${timestamp}@example.com`;
      const testPassword = 'testpassword123';

      const createUserResponse = await page.request.post(`${getBaseURL()}/dev/create-test-user`, {
        data: {
          email: testEmail,
          password: testPassword,
          name: `Test User ${id}`
        }
      });

      let userId;

      if (!createUserResponse.ok()) {
        // If creation fails, the user might already exist - we can try signing in directly
        console.log(`Note: Failed to create test user: ${await createUserResponse.text()}`);
      } else {
        console.log('Test user created successfully');
        // Extract the user ID from the response if available
        const userData = await createUserResponse.json().catch(() => ({}));
        userId = userData.id; // Store the user ID if available
      }

      // Use the test signin endpoint for authentication
      console.log('Signing in test user via API...');
      const signinResponse = await page.request.post(`${getBaseURL()}/dev/test-signin`, {
        data: {
          email: testEmail,
          password: testPassword
        }
      });

      if (!signinResponse.ok()) {
        throw new Error(`Failed to sign in test user: ${await signinResponse.text()}`);
      }
      console.log('API signin successful');
      // Always get userId from the signin response
      const signinData = await signinResponse.json().catch(() => ({}));
      userId = signinData.userId;

      console.log('Login successful');

      // Save the authentication state to the worker-specific file
      await page.context().storageState({ path: fileName });
      console.log(`Authentication state saved to: ${fileName}`);

      // Save the userId for future use
      if (userId) {
        fs.writeFileSync(userIdFileName, userId.toString(), 'utf8');
      } else {
        console.warn('Could not determine user ID during authentication!');
      }

      // Save email and password
      fs.writeFileSync(emailFileName, testEmail, 'utf8');
      fs.writeFileSync(passwordFileName, testPassword, 'utf8');

      await use(fileName);
    } catch (error) {
      console.error('Authentication setup failed:', error);
      throw error; // Re-throw to fail the setup
    } finally {
      await page.close();
    }
  }, { scope: 'worker' }],

  // Accept consentValue as an optional parameter with default value 'true'
  consentValue: true,
  // Accept customMatchColumn as an optional parameter for creating a custom match column
  customMatchColumns: undefined,

  // Add autoConsent fixture parameter - default enabled, configurable per test
  autoConsent: [true, { option: true }],

  // Override the page fixture to handle automatic cookie consent
  page: async ({ page: basePage, autoConsent, storageState }, use) => {
    // If using shared auth storage state AND autoConsent is enabled
    if (storageState && autoConsent) {
      await basePage.context().addInitScript(() => {
        window.localStorage.setItem('cookieConsent', 'true');
      });
    }

    await use(basePage);
  },

  // New fixture for creating an isolated user for a single test
  isolatedUser: async ({ browser, consentValue, customMatchColumns }, use) => {
    // Create a new page for the test with isolated context
    const isolatedId = `isolated-${Date.now()}-${generateRandomString(10)}`;
    const isolatedAuthDir = path.resolve(baseTest.info().project.outputDir, '.auth/isolated');
    const isolatedAuthFile = path.join(isolatedAuthDir, `${isolatedId}.json`);
    const isolatedUserDataFile = path.join(isolatedAuthDir, `${isolatedId}-userdata.json`);

    // Ensure directory exists
    if (!fs.existsSync(isolatedAuthDir)) {
      fs.mkdirSync(isolatedAuthDir, { recursive: true });
    }

    let authContext: BrowserContext | undefined;
    let pageForAuthOperations: Page | undefined; // Temporary page for auth operations
    const testEmail = `isolated-${isolatedId}@example.com`;
    const testPassword = 'testpassword123'; // Or generate a random one
    let isolatedUserId: number | undefined;

    try {
      // Create a new page without any existing storage state for auth operations
      pageForAuthOperations = await browser.newPage({ storageState: undefined });

      // 1. Create a test user
      const createUserResponse = await pageForAuthOperations.request.post(`${getBaseURL()}/dev/create-test-user`, {
        data: {
          email: testEmail,
          password: testPassword,
          name: `Isolated User ${isolatedId}`
        }
      });

      if (createUserResponse.ok()) {
        const userData = await createUserResponse.json().catch(() => ({}));
        isolatedUserId = userData.id;
      } else {
      }

      // 2. Sign in the user
      const signinResponse = await pageForAuthOperations.request.post(`${getBaseURL()}/dev/test-signin`, {
        data: {
          email: testEmail,
          password: testPassword
        }
      });

      if (!signinResponse.ok()) {
        throw new Error(`Isolated User [${isolatedId}]: Failed to sign in ${testEmail}: ${await signinResponse.text()}`);
      }
      const signinData = await signinResponse.json().catch(() => ({}));
      if (!isolatedUserId && signinData.userId) { // If createUser didn't return ID, get from signin
        isolatedUserId = signinData.userId;
      }
      if (!isolatedUserId) {
      }

      // 3. Save authentication state to a temporary file from the auth page's context
      await pageForAuthOperations.context().storageState({ path: isolatedAuthFile });
      // Removed log statement for auth state saved as per user request

      // Save user ID and email for reference
      if (isolatedUserId !== undefined) {
        fs.writeFileSync(isolatedUserDataFile, JSON.stringify({ userId: isolatedUserId, email: testEmail }), 'utf8');
      }

      // Close the temporary page used for auth operations
      await pageForAuthOperations.close();
      pageForAuthOperations = undefined;

      // Create a new page for the test with the isolated storage state
      const testContext = await browser.newContext({ storageState: isolatedAuthFile });
      const testPage = await testContext.newPage();

      // Set cookie consent in local storage if consentValue is true
      if (consentValue) {
        await testPage.context().addInitScript(() => {
          window.localStorage.setItem('cookieConsent', 'true');
        });
      }

      // If customMatchColumns is provided, create each custom match column for the user
      if (customMatchColumns && isolatedUserId !== undefined) {
        console.log(`Isolated User [${isolatedId}]: Creating custom match columns for user ${testEmail}...`);
        for (const column of customMatchColumns) {
          const columnResponse = await testPage.request.post(`${getBaseURL()}/dev/custom-match-columns`, {
            data: {
              userId: isolatedUserId,
              name: column.name || 'Custom Column',
              fieldType: column.fieldType || 'text',
              description: column.description || '',
              isRequired: column.isRequired || false,
              displayOrder: column.displayOrder || 1,
              isActive: true
            }
          });
          if (columnResponse.ok()) {
            console.log(`Isolated User [${isolatedId}]: Custom match column '${column.name || 'Custom Column'}' created successfully for user ${testEmail}.`);
          } else {
            console.warn(`Isolated User [${isolatedId}]: Failed to create custom match column '${column.name || 'Custom Column'}' for user ${testEmail}: ${await columnResponse.text()}`);
          }
        }
      }

      // Provide the authenticated page and user details to the test
      await use({ page: testPage, userId: isolatedUserId!, email: testEmail });

      // Cleanup: Reset local storage for cookie consent after the test
      await testPage.context().addInitScript(() => {
        window.localStorage.removeItem('cookieConsent');
      });

      // Close the test page and context
      await testPage.close();
      await testContext.close();

    } finally {
      // 5. Teardown: Close context and page, delete auth files, and delete user from backend
      if (pageForAuthOperations) { // If an error occurred before it was closed
        await pageForAuthOperations.close();
      }
      // The page instance provided to \`use()\` is managed by Playwright and closed automatically.
      // The context, however, needs to be closed manually.
      if (authContext) {
        await authContext.close();
      }

      if (fs.existsSync(isolatedAuthFile)) {
        fs.unlinkSync(isolatedAuthFile);
        // Removed log statement for cleaning up auth file as per user request
      }
      if (fs.existsSync(isolatedUserDataFile)) {
        fs.unlinkSync(isolatedUserDataFile);
      }

      // Delete the user from the backend if userId is available
      if (isolatedUserId !== undefined) {
        try {
          const cleanupPage = await browser.newPage({ storageState: undefined });
          const deleteResponse = await cleanupPage.request.delete(`${getBaseURL()}/dev/users/${isolatedUserId}`);
          if (deleteResponse.ok()) {
          } else {
          }
          await cleanupPage.close();
        } catch (error) {
        }
      } else {
      }
      // Removed log statement for teardown complete as per user request
    }
  },
});
