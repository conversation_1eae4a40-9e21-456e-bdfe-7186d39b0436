// Utility functions for E2E tests
import type { Page } from "@playwright/test";

// Re-export all test data generation functions from the centralized module
export {
  TestDataGenerator,
  generateRandomString,
  generateRandomEmail,
  generateRandomName,
  generateRandomPhoneNumber,
  getRandomPastDate,
  generateRandomSeasonName,
  generateRandomTeamName,
  generateRandomDescription,
} from "./testData";

/**
 * Sets a date in a date picker component by clicking through the UI
 * @param page - The Playwright page object
 * @param inputSelector - The selector for the date input field
 * @param dateString - The date to set in YYYY-MM-DD format
 */
export async function setDatePicker(
  page: Page,
  inputSelector: string,
  dateString: string,
): Promise<void> {
  const targetDate = new Date(dateString);
  const targetYear = targetDate.getFullYear();
  const targetMonth = targetDate.getMonth(); // 0-based
  const targetDay = targetDate.getDate();

  // Click the date input to open the picker
  await page.click(inputSelector);

  // Wait for any date picker to become visible
  await page.waitForSelector('[data-testid="datepicker-month-name"]:visible');

  // Navigate to the correct year and month
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  let currentMonthName = await page.locator('[data-testid="datepicker-month-name"]:visible').first().textContent();
  let currentMonth = monthNames.indexOf((currentMonthName ?? "").trim());
  let currentYear = parseInt(
    (await page.locator('[data-testid="datepicker-year"]:visible').first().textContent()) ?? "0",
  );

  // Navigate to target year and month
  while (currentYear !== targetYear || currentMonth !== targetMonth) {
    if (
      currentYear > targetYear ||
      (currentYear === targetYear && currentMonth > targetMonth)
    ) {
      // Go back
      await page.locator('[data-testid="datepicker-prev-month"]:visible').first().click();
    } else {
      // Go forward
      await page.locator('[data-testid="datepicker-next-month"]:visible').first().click();
    }

    // Update current month and year
    currentMonthName = await page.locator('[data-testid="datepicker-month-name"]:visible').first().textContent();
    currentMonth = monthNames.indexOf((currentMonthName ?? "").trim());
    currentYear = parseInt(
      (await page.locator('[data-testid="datepicker-year"]:visible').first().textContent()) ?? "0",
    );
  }

  // Click the target day
  await page.locator(`[data-testid="datepicker-day-${targetDay}"]:visible`).first().click();
}
