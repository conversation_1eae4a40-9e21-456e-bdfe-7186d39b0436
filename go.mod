module github.com/j-em/coachpad

go 1.24.2

require (
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/jackc/pgx/v5 v5.7.4
	github.com/labstack/echo/v4 v4.13.3
	github.com/stytchauth/stytch-go/v16 v16.14.0
)

require (
	github.com/go-playground/validator/v10 v10.26.0
	github.com/joho/godotenv v1.5.1
	github.com/mailgun/mailgun-go/v4 v4.23.0
	github.com/olivere/vite v0.1.0
	github.com/r3labs/sse/v2 v2.10.0
	github.com/stretchr/testify v1.10.0
	github.com/stripe/stripe-go/v82 v82.0.0
	golang.org/x/crypto v0.37.0
	golang.org/x/net v0.39.0
	maragu.dev/gomponents v1.1.0
	maragu.dev/gomponents-heroicons/v3 v3.0.0
	maragu.dev/gomponents-htmx v0.6.1
)

require (
	github.com/MicahParks/keyfunc/v2 v2.1.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/go-chi/chi/v5 v5.2.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761 // indirect
	github.com/jackc/puddle/v2 v2.2.2 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mailgun/errors v0.4.0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180228061459-e0a39a4cb421 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rogpeppe/go-internal v1.14.1 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	gopkg.in/cenkalti/backoff.v1 v1.1.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
