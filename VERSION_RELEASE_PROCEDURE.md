# Version Release Procedure

This document defines **when** and **why** to cut new versions of CoachPad. For the technical **how** of deployment, see [DEPLOYMENT.md](./DEPLOYMENT.md).

## Version Numbering

We use [Semantic Versioning (SemVer)](https://semver.org/): `MAJOR.MINOR.PATCH`

- **MAJOR**: Breaking changes or significant architecture changes
- **MINOR**: New features, backwards-compatible functionality additions
- **PATCH**: Bug fixes, security patches, minor improvements

Examples: `1.0.0`, `1.2.3`, `2.0.0`

## When to Cut a New Version

### 1. Scheduled Releases (Recommended)

**Minor Releases**: Every 2-4 weeks
- Collect completed features from the development cycle
- Ensure stability through testing period
- Coordinate with user communication needs

**Patch Releases**: As needed (typically within 1-3 days of critical issues)

### 2. Trigger-Based Releases

#### Immediate Release Required (PATCH)
- **Security vulnerabilities** discovered in dependencies or application code
- **Critical bugs** affecting core functionality (user registration, payments, data loss)
- **Data integrity issues** that could corrupt user data
- **Performance issues** causing significant user experience degradation
- **Third-party service breaking changes** (Stytch, Stripe, Mailgun API changes)

#### Planned Release (MINOR)
- **Feature completeness**: When a significant user-facing feature is complete and tested
- **Integration milestones**: New third-party integrations are fully implemented
- **User experience improvements**: Substantial UI/UX enhancements
- **Performance optimizations**: Significant improvements to application speed or efficiency

#### Major Release (MAJOR)
- **Breaking API changes**: Changes that require user action or break existing workflows
- **Architecture overhauls**: Significant changes to database schema or application structure
- **Authentication changes**: Changes to user authentication or authorization systems
- **Payment model changes**: Modifications to subscription or billing logic

## Release Criteria Checklist

Before cutting any version, ensure ALL criteria are met:

### Development Readiness
- [ ] All planned features for the release are complete
- [ ] All tests pass: `npm test`
- [ ] Code quality checks pass: `go fmt ./...`, `goimports -w .`
- [ ] Database schema is stable (no pending migrations)
- [ ] SQLC code is regenerated and committed
- [ ] No known critical or high-priority bugs

### Testing and Quality Assurance
- [ ] Manual testing completed for new features
- [ ] Regression testing completed for existing functionality
- [ ] Cross-browser testing (if frontend changes)
- [ ] Mobile responsiveness verified (if UI changes)
- [ ] Third-party integrations tested (Stytch, Stripe, Mailgun)
- [ ] Performance testing completed (if performance-related changes)

### Documentation and Communication
- [ ] User-facing changes documented
- [ ] Breaking changes clearly documented (for MAJOR releases)
- [ ] Migration instructions prepared (if needed)
- [ ] Release notes drafted

### Production Readiness
- [ ] Production database backup strategy confirmed
- [ ] Rollback plan prepared and tested
- [ ] Environment variables and configuration reviewed
- [ ] Monitoring and alerting systems ready

## Release Process

### 1. Pre-Release (1-3 days before)
1. **Code Freeze**: No new features, only critical bug fixes
2. **Final Testing**: Complete testing checklist above
3. **Documentation**: Finalize release notes and user documentation
4. **Stakeholder Communication**: Notify team and users of upcoming release

### 2. Release Day
1. **Version Tagging**: Create git tag with version number
2. **Release Notes**: Publish release notes
3. **Deployment**: Follow [DEPLOYMENT.md](./DEPLOYMENT.md) procedure
4. **Verification**: Complete post-deployment verification checklist
5. **Communication**: Announce release to users

### 3. Post-Release (24-48 hours after)
1. **Monitor**: Watch application logs and error rates
2. **User Feedback**: Monitor support channels for issues
3. **Performance**: Verify system performance metrics
4. **Hotfix Readiness**: Be prepared for emergency patches if needed

## Emergency Release Process

For critical security or data integrity issues:

1. **Immediate Assessment** (within 1 hour)
   - Confirm severity and impact
   - Determine if temporary mitigation is possible
   - Estimate fix development time

2. **Expedited Development** (within 4-24 hours depending on severity)
   - Develop minimal fix addressing the critical issue
   - Essential testing only (automated tests + critical path manual testing)
   - Skip non-essential documentation

3. **Emergency Deployment**
   - Follow abbreviated deployment checklist
   - Deploy during low-traffic hours if possible
   - Monitor closely for 2-4 hours post-deployment

4. **Post-Emergency Cleanup**
   - Complete full testing suite
   - Update documentation
   - Conduct post-mortem if appropriate

## Version History Tracking

Maintain clear records in:
- **Git Tags**: Each version gets a git tag
- **Release Notes**: User-facing changes and improvements
- **CHANGELOG.md**: Technical changes for developers
- **Database Schema Versions**: Track schema changes with migration scripts

## Communication Strategy

### Internal Team
- **Pre-release**: Team notification 2-3 days before
- **Release**: Deploy during agreed maintenance window
- **Post-release**: Summary of changes and any follow-up items

### Users
- **Minor Releases**: Email newsletter with new features
- **Patch Releases**: In-app notification or support documentation update
- **Major Releases**: Advance notice (1-2 weeks), migration guides, and comprehensive communication

## Rollback Criteria

Initiate rollback if:
- Critical functionality is broken for >25% of users
- Data corruption or loss is detected
- Security vulnerability is introduced
- Third-party integration failures affecting core features
- Performance degradation >50% compared to previous version

Follow rollback procedure in [DEPLOYMENT.md](./DEPLOYMENT.md#rollback-procedure).

## Version Release Calendar

Consider avoiding releases during:
- Major holidays or vacation periods
- End of billing cycles (month/quarter end)
- Known high-traffic periods for your users
- Fridays (unless emergency) - prefer Tuesday-Thursday releases

## Success Metrics

Track these metrics for each release:
- **Deployment Success Rate**: Clean deployments without rollbacks
- **Post-Release Bug Rate**: Critical bugs discovered within 48 hours
- **User Adoption**: Feature usage metrics for new functionality
- **Performance Impact**: Response time and error rate changes
- **Time to Deploy**: Efficiency of release process