-- Insert test data into players table
INSERT INTO players (user_id, "name") VALUES
(1, 'Player1'),
(1, '<PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON>'),
(1, '<PERSON><PERSON><PERSON>');

INSERT INTO seasons (user_id, name, start_date, frequency, season_type) VALUES
(1, 'Season 1', '2023-01-01', 'weekly', 'other'),
(1, 'Season 2', '2023-04-01', 'biweekly', 'other'),
(1, 'Season 3', '2023-07-01', 'weekly', 'other'),
(1, 'Season 4', '2023-10-01', 'biweekly', 'other'),
(1, 'Season 5', '2024-01-01', 'weekly', 'other'),
(1, 'Season 6', '2024-04-01', 'biweekly', 'other'),
(1, 'Season 7', '2024-07-01', 'weekly', 'other'),
(1, 'Season 8', '2024-10-01', 'biweekly', 'other'),
(1, 'Season 9', '2025-01-01', 'weekly', 'other'),
(1, 'Season 10', '2025-04-01', 'biweekly', 'other'),
(1, 'Season 11', '2025-07-01', 'weekly', 'other'),
(1, 'Season 12', '2025-10-01', 'biweekly', 'other');

-- Insert test data into matches table
INSERT INTO matches (season_id, player_id_1, player_id_1_points, player_id_2, player_id_2_points, match_date, winner_id, "group") VALUES
(1, 1, 10, 2, 8, '2023-01-15', 1, 1),
(1, 3, 7, 4, 9, '2023-01-22', 4, 2),
(2, 1, 5, 3, 6, '2023-04-05', 3, 1),
(2, 2, 8, 5, 7, '2023-04-12', 2, 3),
-- Additional matches for Season 1
(1, 1, 6, 3, 5, '2023-01-29', 3, 1),
(1, 2, 7, 4, 6, '2023-02-05', 4, 2),
(1, 1, 8, 5, 7, '2023-02-12', 1, 3),
(1, 2, 9, 3, 8, '2023-02-19', 2, 1),
(1, 4, 6, 5, 5, '2023-02-26', 4, 2),
-- ... (add more matches to reach 50 for Season 1) ...
(1, 3, 7, 1, 6, '2023-03-05', 3, 3),
(1, 5, 8, 2, 7, '2023-03-12', 5, 1),
(1, 4, 9, 1, 8, '2023-03-19', 4, 2),
(1, 3, 6, 2, 5, '2023-03-26', 3, 3),
(1, 5, 7, 4, 6, '2023-03-31', 5, 1),
-- Additional matches for Season 2
(2, 1, 6, 2, 5, '2023-04-19', 1, 1),
(2, 3, 7, 4, 6, '2023-04-26', 4, 2),
(2, 1, 8, 5, 7, '2023-05-03', 1, 3),
(2, 2, 9, 3, 8, '2023-05-10', 2, 1),
(2, 4, 6, 5, 5, '2023-05-17', 4, 2),
-- ... (add more matches to reach 50 for Season 2) ...
(2, 3, 7, 1, 6, '2023-05-24', 3, 3),
(2, 5, 8, 2, 7, '2023-05-31', 5, 1),
(2, 4, 9, 1, 8, '2023-06-07', 4, 2),
(2, 3, 6, 2, 5, '2023-06-14', 3, 3),
(2, 5, 7, 4, 6, '2023-06-21', 5, 1),
(2, 1, 8, 3, 7, '2023-06-28', 1, 3),
-- Matches scheduled for today
(1, 1, 0, 2, 0, CURRENT_DATE, NULL, 1),
(1, 3, 0, 4, 0, CURRENT_DATE, NULL, 2),
(1, 5, 0, 1, 0, CURRENT_DATE, NULL, 3),
(1, 2, 0, 3, 0, CURRENT_DATE, NULL, 1),
(1, 4, 0, 5, 0, CURRENT_DATE, NULL, 2),
(2, 1, 0, 2, 0, CURRENT_DATE, NULL, 3),
(2, 3, 0, 4, 0, CURRENT_DATE, NULL, 1),
(2, 5, 0, 1, 0, CURRENT_DATE, NULL, 2),
(2, 2, 0, 3, 0, CURRENT_DATE, NULL, 3),
(2, 4, 0, 5, 0, CURRENT_DATE, NULL, 1),

-- Matches scheduled for next week
(1, 1, 0, 2, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 1),
(1, 3, 0, 4, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 2),
(1, 5, 0, 1, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 3),
(1, 2, 0, 3, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 1),
(1, 4, 0, 5, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 2),
(2, 1, 0, 2, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 3),
(2, 3, 0, 4, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 1),
(2, 5, 0, 1, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 2),
(2, 2, 0, 3, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 3),
(2, 4, 0, 5, 0, CURRENT_DATE + INTERVAL '7 days', NULL, 1),

-- Matches scheduled for the week after next
(1, 1, 0, 2, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 1),
(1, 3, 0, 4, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 2),
(1, 5, 0, 1, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 3),
(1, 2, 0, 3, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 1),
(1, 4, 0, 5, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 2),
(2, 1, 0, 2, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 3),
(2, 3, 0, 4, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 1),
(2, 5, 0, 1, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 2),
(2, 2, 0, 3, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 3),
(2, 4, 0, 5, 0, CURRENT_DATE + INTERVAL '14 days', NULL, 1);
