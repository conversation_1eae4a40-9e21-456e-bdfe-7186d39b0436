package signin

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	html "maragu.dev/gomponents/html"
)

// ForgotPasswordSuccess renders a simple success message after requesting a reset link
func ForgotPasswordSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signin/forgotPasswordSuccess.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("p-4 bg-green-100 text-green-800 rounded text-center mt-4"),
		gomponents.Text(locales["reset_link_sent"]),
	)
}
