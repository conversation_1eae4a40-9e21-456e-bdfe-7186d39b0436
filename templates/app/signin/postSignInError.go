package signin

import (
	"fmt"
	"time"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/toast"
	"maragu.dev/gomponents"
)

// PostSignInError renders an error message fragment that can be inserted after a failed sign in attempt.
func PostSignInError(errorMessage, lang string) gomponents.Node {
	// Load localized strings
	locales, err := i18n.LoadTemplateLocales("./templates/app/signin/postSignInError.locales.json", lang)
	if err != nil {
		locales = map[string]string{}
	}

	return toast.Toast(toast.ToastConfig{
		ID:         fmt.Sprintf("signin-error-toast-%d", time.Now().UnixNano()),
		Message:    locales["errorMessagePrefix"] + " " + errorMessage,
		Style:      "error",
		DataTestID: "signin-error",
		AutoClose:  true,
	})
}
