package signin

import (
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

// ResetPasswordPageConfig holds the data needed to render the reset password page
type ResetPasswordPageConfig struct {
	Token string
	Lang  string
}

// ResetPasswordPage renders the reset password page using the config
func ResetPasswordPage(cfg ResetPasswordPageConfig) gomponents.Node {
	// This page uses HTMLLayout instead of AppLayout, so we use LoadLocalesOnly
	locales := pagebuilder.NewPage("/app/signin/reset-password").
		WithLocaleBase("app/signin/resetPasswordPage").
		LoadLocalesOnly(cfg.Lang)

	return layouts.HTMLLayout(locales["page_title"], ResetPasswordForm(cfg.<PERSON>, cfg.Token))
}
