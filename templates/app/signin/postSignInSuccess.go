package signin

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PostSignInSuccess renders a success message fragment that can be inserted after successful sign in.
func PostSignInSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signin/postSignInSuccess.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = make(map[string]string)
	}

	return html.Div(
		gomponents.Attr("data-testid", "post-signin-success"),
		html.Class("bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4"),
		html.Role("alert"),
		html.Div(
			html.Class("flex flex-col space-y-2"),
			html.Div(
				html.Class("flex items-center"),
				html.Span(
					html.Class("h-5 w-5"),
					icons.CheckCircle(),
				),
				html.Span(
					html.Class("block sm:inline font-medium"),
					gomponents.Text(locales["successMessage"]),
				),
			),
			html.Div(
				html.Class("text-sm"),
				html.P(
					gomponents.Text(locales["welcomeMessage"]),
				),
				button.PrimaryIconButtonLink(button.IconButtonLinkConfig{
					ID:         "continue-button",
					DataTestID: "continue-button",
					Href:       "/app/home",
					Text:       locales["continue"],
					Icon:       icons.ArrowRight(),
				}),
			),
		),
	)
}
