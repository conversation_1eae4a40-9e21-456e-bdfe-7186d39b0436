package signin

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	html "maragu.dev/gomponents/html"
)

func ForgotPasswordDialog(lang string) gomponents.Node {
	// Load localized strings
	locales, err := i18n.LoadTemplateLocales("./templates/app/signin/forgotPassword.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback to empty map
	}

	formContent := html.Form(
		html.Method("POST"),
		htmx.Boost("true"),
		html.Action("/auth/password-reset/initiate"),
		html.ID("forgot-password-form"),
		html.Class("space-y-4"),
		form.FormInput(form.FormInputProps{
			Label:       locales["email_label"],
			Type:        "email",
			ID:          "reset-email",
			Name:        "email",
			Required:    true,
			Placeholder: "<EMAIL>",
		}),
		html.Div(
			html.Class("flex justify-end gap-2"),
			button.PrimaryIconButton(button.IconButtonConfig{
				ID:         "submit-forgot-password-btn",
				ButtonType: "submit",
				Text:       locales["send_reset_link_button"],
				Icon:       icons.PaperAirplane(),
			}),
			button.SecondaryButton(button.BaseButtonConfig{
				ID:   "cancel-forgot-password-btn",
				Text: locales["cancel_button"],
				Attrs: []gomponents.Node{
					html.Type("button"),
					gomponents.Attr("@click", "forgotPassword_modalOpen = false"),
				},
			}),
		),
	)

	// Using the raw Modal component since the Alpine.js state
	// is managed by the parent signin component
	return modal.Modal(modal.ModalConfig{
		ModalID:      "forgotPassword",
		Title:        locales["reset_password_title"],
		Content:      formContent,
		IsUnclosable: false,
	})
}
