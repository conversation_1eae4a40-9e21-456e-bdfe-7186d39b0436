package signin

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatescomponents "github.com/j-em/coachpad/templates/components"
	"github.com/j-em/coachpad/templates/components/applogo"
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/infobox"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents-heroicons/v3/solid"
	"maragu.dev/gomponents/html"
)

func SignIn(lang string, user *db.User) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signin/signin.locales.json", lang)
	if err != nil {
		// Handle error, perhaps log it or use default values
		locales = make(map[string]string)
	}

	// Create user info box if user is provided
	var userInfoBox gomponents.Node
	if user != nil {
		userInfoBox = infobox.InfoBox(infobox.InfoBoxConfig{
			ID:          "signin-user-info",
			UserName:    user.Name,
			Style:       "info",
			Lang:        lang,
			DataTestID:  "signin-user-info-box",
			Dismissible: true,
		})
	}

	return layouts.HTMLLayout(locales["title"], html.Div(
		html.Class("flex min-h-screen justify-center overflow-y-auto"),
		html.Div(
			html.Class("w-full max-w-sm p-8 pt-[70px] space-y-8 rounded-lg"),
			// Add Alpine.js state for modal open/close
			gomponents.Attr("x-data", "{errors: {}, errorMessages: {}, fields: {}, forgotPassword_modalOpen: false}"),
			html.Div(
				html.Class("flex items-center mb-2 justify-start w-full relative"),
				// Add the language switcher to the top left of the logo
				html.Div(
					html.Class("absolute top-[-40px] right-[-25px]"),
					templatescomponents.PublicLangSwitcher(lang),
				),
				// Center the logo horizontally in the available space
				applogo.AppLogo(lang),
			),
			html.H2(
				html.Class("mt-6 text-center text-2xl font-bold"),
				gomponents.Text(locales["form_title"]),
			),
			// Display user info box if provided
			gomponents.If(userInfoBox != nil, userInfoBox),
			html.Form(
				html.Method("POST"),
				html.Action("/signin"),
				html.Class("mt-8 space-y-6"),
				gomponents.Attr("x-validate-form"),
				gomponents.Attr("hx-boost", "true"),
				gomponents.Attr("hx-target", "#toast-body-container"),
				gomponents.Attr("hx-swap", "afterbegin"),
				gomponents.Attr("hx-indicator", "#signin-submit-btn"),
				html.Div(
					html.Class("rounded-md space-y-4"),
					form.FormInput(form.FormInputProps{
						Label:       locales["email"],
						Type:        "email",
						ID:          "email",
						Name:        "email",
						Required:    true,
						Placeholder: locales["email_placeholder"],
					}),
					form.FormInput(form.FormInputProps{
						Label:       locales["password"],
						Type:        "password",
						ID:          "password",
						Name:        "password",
						Required:    true,
						MinLength:   3,
						Placeholder: locales["password_placeholder"],
					}),
				),
				html.Div(
					button.PrimaryIconButton(button.IconButtonConfig{
						ID:         "signin-submit-btn",
						DataTestID: "signin-submit-btn",
						ButtonType: "submit",
						Text:       locales["submit"],
						Icon:       solid.ArrowDown(),
					}),
				),
			),
			html.Div(
				html.Class("flex justify-end mt-2"),
				button.SecondaryButton(button.BaseButtonConfig{
					ID:         "forgot-password-btn",
					ButtonType: "button",
					Text:       locales["forgot_password"],
					Class:      "text-sm text-blue-600 hover:underline border-none bg-transparent shadow-none px-0 py-0 ml-auto",
					Attrs: []gomponents.Node{
						gomponents.Attr("@click", "forgotPassword_modalOpen = true"),
					},
				}),
			),
			ForgotPasswordDialog(lang),
		),
	))
}
