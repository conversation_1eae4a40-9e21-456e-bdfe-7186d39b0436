package signin

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents-heroicons/v3/solid"
	html "maragu.dev/gomponents/html"
)

// ResetPasswordForm renders the form for setting a new password
func ResetPasswordForm(lang string, token string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signin/resetPassword.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		html.Class("flex min-h-screen justify-center items-center bg-gray-50"),
		html.Div(
			html.Class("w-full max-w-sm p-8 bg-white rounded-lg shadow-md"),
			html.H2(
				html.Class("text-xl font-bold mb-4 text-center"),
				gomponents.Text(locales["set_new_password"]),
			),
			html.Form(
				html.Method("POST"),
				html.Action("/auth/password-reset/complete"),
				html.Class("space-y-4"),
				html.ID("reset-password-form"),
				gomponents.Attr("x-validate-form", ""),
				gomponents.Attr("x-data", "{ fields: {}, errors: {}, errorMessages: {} }"),
				html.Input(
					html.Type("hidden"),
					html.Name("token"),
					html.Value(token),
				),
				form.FormInput(form.FormInputProps{
					Label:          locales["new_password"],
					Type:           "password",
					ID:             "new-password",
					Name:           "new_password",
					Required:       true,
					MinLength:      8,
					Pattern:        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$",
					PatternMessage: locales["password_requirements"],
				}),
				form.FormInput(form.FormInputProps{
					Label:               locales["confirm_password"],
					Type:                "password",
					ID:                  "confirm-password",
					Name:                "confirm_password",
					Required:            true,
					MinLength:           8,
					ValidateWith:        "new-password",
					ValidateType:        "match",
					RelatedErrorMessage: locales["passwords_no_match"],
				}),
				html.Div(
					html.Class("flex justify-end gap-2"),
					button.PrimaryIconButton(button.IconButtonConfig{
						ID:         "submit-reset-password-btn",
						ButtonType: "submit",
						Text:       locales["reset_password"],
						Icon:       solid.Check(),
						Attrs: []gomponents.Node{
							gomponents.Attr("x-bind:disabled", "Object.values(errors).some(error => error)"),
						},
					}),
				),
			),
		),
	)
}
