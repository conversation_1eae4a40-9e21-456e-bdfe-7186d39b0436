package templatesappmatches

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/toast"
	"maragu.dev/gomponents"
)

type CreateMatchSuccessToastProps struct {
	Lang string
}

func CreateMatchSuccessToast(props CreateMatchSuccessToastProps) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/matches/createMatchSuccessToast.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{
			"success_message": "Match created successfully",
		}
	}

	return toast.Toast(toast.ToastConfig{
		ID:         "match-created-toast",
		Message:    locales["success_message"],
		Style:      "success",
		DataTestID: "match-created-toast",
		AutoClose:  true,
		Lang:       props.Lang,
	})
}
