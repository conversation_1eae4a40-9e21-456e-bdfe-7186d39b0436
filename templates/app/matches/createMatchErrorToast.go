package templatesappmatches

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/toast"
	"maragu.dev/gomponents"
)

type CreateMatchErrorToastProps struct {
	ErrorMessage string
	Lang         string
}

func CreateMatchErrorToast(props CreateMatchErrorToastProps) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/matches/createMatchErrorToast.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{
			"error_prefix": "Error",
		}
	}

	message := locales["error_prefix"] + ": " + props.ErrorMessage

	return toast.Toast(toast.ToastConfig{
		ID:         "match-error-toast",
		Message:    message,
		Style:      "error",
		DataTestID: "match-error-toast",
		AutoClose:  false,
		Lang:       props.<PERSON>,
	})
}
