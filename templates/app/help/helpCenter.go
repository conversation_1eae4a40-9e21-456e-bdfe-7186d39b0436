package templatesapphelp

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
	htmx "maragu.dev/gomponents-htmx"
	html "maragu.dev/gomponents/html"
)

type HelpCenterPageProps struct {
	Lang       string
	ActiveLink string
	UserID     int32
}

type HelpCenterContentProps struct {
	Lang   string
	UserID int32
}

type HelpSearchResultsProps struct {
	Query string
	Lang  string
}

type HelpSectionPageProps struct {
	Section    string
	Lang       string
	ActiveLink string
	UserID     int32
}

type HelpSectionContentProps struct {
	Section string
	Lang    string
	UserID  int32
}

type HelpTopicPageProps struct {
	Section    string
	Topic      string
	Lang       string
	ActiveLink string
	UserID     int32
}

type HelpTopicContentProps struct {
	Section string
	Topic   string
	Lang    string
	UserID  int32
}

// HelpCenterPage renders the full help center page
func HelpCenterPage(props HelpCenterPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/help").
		WithTitle("Help Center").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			IsSidebarOpen: false,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return HelpCenterContent(props.Lang, HelpCenterContentProps{
				Lang:   props.Lang,
				UserID: props.UserID,
			})
		})
}

// HelpCenterContent renders the main help center content
func HelpCenterContent(lang string, props HelpCenterContentProps) gomponents.Node {
	// Load locales
	locales := i18n.MustLoadTemplateLocales("./templates/app/help/help.locales.json", lang)

	return html.Div(
		html.Class("max-w-7xl mx-auto px-4 py-8"),

		// Header
		html.Div(
			html.Class("text-center mb-12"),
			html.H1(
				html.Class("text-4xl font-bold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(locales["help_center_title"]),
			),
			html.P(
				html.Class("text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto"),
				gomponents.Text(locales["help_center_subtitle"]),
			),
		),

		// Search Section
		html.Div(
			html.Class("max-w-2xl mx-auto mb-12"),
			html.Div(
				html.Class("relative"),
				html.Input(
					html.Type("search"),
					html.Name("search"),
					html.ID("help-search"),
					html.Placeholder(locales["search_placeholder"]),
					html.Class("w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"),
					htmx.Get("/app/help/search"),
					htmx.Target("#search-results"),
					htmx.Trigger("input changed delay:300ms"),
					htmx.Include("this"),
					gomponents.Attr("data-testid", "help-search-input"),
				),
				html.Div(
					html.Class("absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"),
					heroicons.MagnifyingGlass(html.Class("w-6 h-6")),
				),
			),
			// Search results container
			html.Div(
				html.ID("search-results"),
				html.Class("mt-4"),
			),
		),

		// Help Categories Grid
		html.Div(
			html.Class("grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12"),
			HelpCategoryCard(locales, "getting-started", "rocket", locales["getting_started_title"], locales["getting_started_desc"]),
			HelpCategoryCard(locales, "players", "user-group", locales["players_title"], locales["players_desc"]),
			HelpCategoryCard(locales, "teams", "users", locales["teams_title"], locales["teams_desc"]),
			HelpCategoryCard(locales, "seasons", "calendar", locales["seasons_title"], locales["seasons_desc"]),
			HelpCategoryCard(locales, "matches", "trophy", locales["matches_title"], locales["matches_desc"]),
			HelpCategoryCard(locales, "spending", "credit-card", locales["spending_title"], locales["spending_desc"]),
		),

		// Quick Links Section
		html.Div(
			html.Class("bg-gray-50 dark:bg-gray-800 rounded-xl p-8"),
			html.H2(
				html.Class("text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center"),
				gomponents.Text(locales["quick_links_title"]),
			),
			html.Div(
				html.Class("grid gap-4 md:grid-cols-2 lg:grid-cols-4"),
				QuickLinkItem(locales["faq_title"], "/app/help/faq", "question-mark-circle"),
				QuickLinkItem(locales["troubleshooting_title"], "/app/help/troubleshooting", "wrench-screwdriver"),
				QuickLinkItem(locales["contact_support"], "/app/feedback", "chat-bubble-left-right"),
				QuickLinkItem(locales["video_tutorials"], "/app/help/tutorials", "play-circle"),
			),
		),
	)
}

// HelpCategoryCard renders a category card in the help center
func HelpCategoryCard(locales map[string]string, section, iconName, title, description string) gomponents.Node {
	var icon gomponents.Node
	switch iconName {
	case "rocket":
		icon = heroicons.RocketLaunch()
	case "user-group":
		icon = heroicons.UserGroup()
	case "users":
		icon = heroicons.Users()
	case "calendar":
		icon = heroicons.Calendar()
	case "trophy":
		icon = heroicons.Trophy()
	case "credit-card":
		icon = heroicons.CreditCard()
	default:
		icon = heroicons.DocumentText()
	}

	return html.Div(
		html.Class("bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300 cursor-pointer h-full"),
		htmx.Get("/app/help/"+section),
		htmx.Boost("true"),
		gomponents.Attr("data-testid", "help-category-"+section),
		html.Div(
			html.Class("p-6 text-center"),
			html.Div(
				html.Class("w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"),
				html.Div(
					html.Class("w-8 h-8 text-blue-600 dark:text-blue-400"),
					icon,
				),
			),
			html.H3(
				html.Class("text-xl font-semibold text-gray-900 dark:text-white mb-2"),
				gomponents.Text(title),
			),
			html.P(
				html.Class("text-gray-600 dark:text-gray-300"),
				gomponents.Text(description),
			),
		),
	)
}

// QuickLinkItem renders a quick link item
func QuickLinkItem(title, href, iconName string) gomponents.Node {
	var icon gomponents.Node
	switch iconName {
	case "question-mark-circle":
		icon = heroicons.QuestionMarkCircle()
	case "wrench-screwdriver":
		icon = heroicons.WrenchScrewdriver()
	case "chat-bubble-left-right":
		icon = heroicons.ChatBubbleLeftRight()
	case "play-circle":
		icon = heroicons.PlayCircle()
	default:
		icon = heroicons.DocumentText()
	}

	return html.A(
		html.Href(href),
		html.Class("flex items-center gap-3 p-4 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"),
		htmx.Boost("true"),
		html.Div(
			html.Class("w-6 h-6 text-blue-600 dark:text-blue-400"),
			icon,
		),
		html.Span(
			html.Class("font-medium text-gray-900 dark:text-white"),
			gomponents.Text(title),
		),
	)
}

// HelpSearchResults renders search results
func HelpSearchResults(lang string, props HelpSearchResultsProps) gomponents.Node {
	if props.Query == "" {
		return html.Div()
	}

	// Load locales
	locales := i18n.MustLoadTemplateLocales("./templates/app/help/help.locales.json", lang)

	// Mock search results for demonstration
	// In a real implementation, you'd search through your help content
	results := []struct {
		Title       string
		Description string
		Section     string
		Topic       string
		Href        string
	}{
		{
			Title:       "Adding Your First Player",
			Description: "Learn how to add players to your team and manage their information.",
			Section:     "players",
			Topic:       "adding-players",
			Href:        "/app/help/players/adding-players",
		},
		{
			Title:       "Creating a New Season",
			Description: "Step-by-step guide to creating and configuring a new season.",
			Section:     "seasons",
			Topic:       "creating-season",
			Href:        "/app/help/seasons/creating-season",
		},
		{
			Title:       "Recording Match Results",
			Description: "How to enter and update match results in the system.",
			Section:     "matches",
			Topic:       "recording-results",
			Href:        "/app/help/matches/recording-results",
		},
	}

	return html.Div(
		html.Class("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm max-h-96 overflow-y-auto"),
		html.Div(
			html.Class("p-4 border-b border-gray-200 dark:border-gray-700"),
			html.P(
				html.Class("text-sm text-gray-600 dark:text-gray-300"),
				gomponents.Textf(locales["search_results_for"], props.Query),
			),
		),
		html.Div(
			html.Class("divide-y divide-gray-200 dark:divide-gray-700"),
			gomponents.Group(func() []gomponents.Node {
				var nodes []gomponents.Node
				for _, result := range results {
					nodes = append(nodes, SearchResultItem(result.Title, result.Description, result.Href))
				}
				return nodes
			}()),
		),
	)
}

// SearchResultItem renders a single search result
func SearchResultItem(title, description, href string) gomponents.Node {
	return html.A(
		html.Href(href),
		html.Class("block p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"),
		htmx.Boost("true"),
		html.H4(
			html.Class("font-medium text-gray-900 dark:text-white mb-1"),
			gomponents.Text(title),
		),
		html.P(
			html.Class("text-sm text-gray-600 dark:text-gray-300"),
			gomponents.Text(description),
		),
	)
}
