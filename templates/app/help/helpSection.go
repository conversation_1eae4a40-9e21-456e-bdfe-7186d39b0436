package templatesapphelp

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
	htmx "maragu.dev/gomponents-htmx"
	html "maragu.dev/gomponents/html"
)

// HelpSectionPage renders the full help section page
func HelpSectionPage(props HelpSectionPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/help/"+props.Section).
		WithTitle("Help - "+props.Section).
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			IsSidebarOpen: false,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return HelpSectionContent(props.Lang, HelpSectionContentProps{
				Section: props.Section,
				Lang:    props.Lang,
				UserID:  props.UserID,
			})
		})
}

// HelpSectionContent renders help section content
func HelpSectionContent(lang string, props HelpSectionContentProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/help/helpSection.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	sectionData := getSectionData(props.Section, locales)

	return html.Div(
		html.Class("max-w-7xl mx-auto px-4 py-8"),

		// Breadcrumbs
		html.Nav(
			html.Class("flex mb-8"),
			html.Ol(
				html.Class("inline-flex items-center space-x-1 md:space-x-3"),
				html.Li(
					html.Class("inline-flex items-center"),
					html.A(
						html.Href("/app/help"),
						html.Class("inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"),
						htmx.Boost("true"),
						heroicons.Home(html.Class("w-4 h-4 mr-2")),
						gomponents.Text(locales["help_center"]),
					),
				),
				html.Li(
					html.Div(
						html.Class("flex items-center"),
						heroicons.ChevronRight(html.Class("w-6 h-6 text-gray-400")),
						html.Span(
							html.Class("ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-300"),
							gomponents.Text(sectionData.Title),
						),
					),
				),
			),
		),

		// Header
		html.Div(
			html.Class("mb-8"),
			html.H1(
				html.Class("text-3xl font-bold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(sectionData.Title),
			),
			html.P(
				html.Class("text-lg text-gray-600 dark:text-gray-300"),
				gomponents.Text(sectionData.Description),
			),
		),

		// Content Grid
		html.Div(
			html.Class("grid gap-6 md:grid-cols-2 lg:grid-cols-3"),
			gomponents.Group(func() []gomponents.Node {
				var nodes []gomponents.Node
				for _, topic := range sectionData.Topics {
					nodes = append(nodes, HelpTopicCard(props.Section, topic))
				}
				return nodes
			}()),
		),
	)
}

// HelpTopicPage renders the full help topic page
func HelpTopicPage(props HelpTopicPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/help/"+props.Section+"/"+props.Topic).
		WithTitle("Help - "+props.Topic).
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			IsSidebarOpen: false,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return HelpTopicContent(props.Lang, HelpTopicContentProps{
				Section: props.Section,
				Topic:   props.Topic,
				Lang:    props.Lang,
				UserID:  props.UserID,
			})
		})
}

// HelpTopicContent renders help topic content
func HelpTopicContent(lang string, props HelpTopicContentProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/help/helpSection.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	topicData := getTopicData(props.Section, props.Topic, locales)
	sectionData := getSectionData(props.Section, locales)

	return html.Div(
		html.Class("max-w-4xl mx-auto px-4 py-8"),

		// Breadcrumbs
		html.Nav(
			html.Class("flex mb-8"),
			html.Ol(
				html.Class("inline-flex items-center space-x-1 md:space-x-3"),
				html.Li(
					html.Class("inline-flex items-center"),
					html.A(
						html.Href("/app/help"),
						html.Class("inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"),
						htmx.Boost("true"),
						heroicons.Home(html.Class("w-4 h-4 mr-2")),
						gomponents.Text(locales["help_center"]),
					),
				),
				html.Li(
					html.Div(
						html.Class("flex items-center"),
						heroicons.ChevronRight(html.Class("w-6 h-6 text-gray-400")),
						html.A(
							html.Href("/app/help/"+props.Section),
							html.Class("ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white"),
							htmx.Boost("true"),
							gomponents.Text(sectionData.Title),
						),
					),
				),
				html.Li(
					html.Div(
						html.Class("flex items-center"),
						heroicons.ChevronRight(html.Class("w-6 h-6 text-gray-400")),
						html.Span(
							html.Class("ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-300"),
							gomponents.Text(topicData.Title),
						),
					),
				),
			),
		),

		// Content
		html.Article(
			html.Class("prose prose-lg dark:prose-invert max-w-none"),
			html.H1(
				html.Class("text-3xl font-bold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(topicData.Title),
			),
			gomponents.Raw(topicData.Content),
		),

		// Related Topics
		html.Div(
			html.Class("mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg"),
			html.H2(
				html.Class("text-xl font-semibold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(locales["related_topics"]),
			),
			html.Div(
				html.Class("grid gap-3 md:grid-cols-2"),
				gomponents.Group(func() []gomponents.Node {
					var nodes []gomponents.Node
					relatedTopics := getRelatedTopics(props.Section, props.Topic, locales)
					for _, topic := range relatedTopics {
						nodes = append(nodes, RelatedTopicLink(props.Section, topic))
					}
					return nodes
				}()),
			),
		),
	)
}

// HelpTopicCard renders a topic card in a section
func HelpTopicCard(section string, topic HelpTopic) gomponents.Node {
	return html.Div(
		html.Class("bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300 cursor-pointer"),
		htmx.Get("/app/help/"+section+"/"+topic.Slug),
		htmx.Boost("true"),
		gomponents.Attr("data-testid", "help-topic-"+topic.Slug),
		html.Div(
			html.Class("p-6"),
			html.H3(
				html.Class("text-lg font-semibold text-gray-900 dark:text-white mb-2"),
				gomponents.Text(topic.Title),
			),
			html.P(
				html.Class("text-gray-600 dark:text-gray-300 text-sm"),
				gomponents.Text(topic.Description),
			),
			html.Div(
				html.Class("mt-4 flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium"),
				gomponents.Text("Read more"),
				heroicons.ArrowRight(html.Class("w-4 h-4 ml-1")),
			),
		),
	)
}

// RelatedTopicLink renders a related topic link
func RelatedTopicLink(section string, topic HelpTopic) gomponents.Node {
	return html.A(
		html.Href("/app/help/"+section+"/"+topic.Slug),
		html.Class("block p-3 bg-white dark:bg-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"),
		htmx.Boost("true"),
		html.Span(
			html.Class("text-sm font-medium text-gray-900 dark:text-white"),
			gomponents.Text(topic.Title),
		),
	)
}

// Data structures for help content
type HelpSection struct {
	Title       string
	Description string
	Topics      []HelpTopic
}

type HelpTopic struct {
	Slug        string
	Title       string
	Description string
	Content     string
}

// getSectionData returns data for a specific help section
func getSectionData(section string, locales map[string]string) HelpSection {
	switch section {
	case "getting-started":
		return HelpSection{
			Title:       locales["getting_started_title"],
			Description: locales["getting_started_desc"],
			Topics: []HelpTopic{
				{Slug: "first-steps", Title: locales["first_steps_title"], Description: locales["first_steps_desc"]},
				{Slug: "account-setup", Title: locales["account_setup_title"], Description: locales["account_setup_desc"]},
				{Slug: "navigation", Title: locales["navigation_title"], Description: locales["navigation_desc"]},
			},
		}
	case "players":
		return HelpSection{
			Title:       locales["players_title"],
			Description: locales["players_desc"],
			Topics: []HelpTopic{
				{Slug: "adding-players", Title: locales["adding_players_title"], Description: locales["adding_players_desc"]},
				{Slug: "editing-players", Title: locales["editing_players_title"], Description: locales["editing_players_desc"]},
				{Slug: "player-stats", Title: locales["player_stats_title"], Description: locales["player_stats_desc"]},
			},
		}
	case "teams":
		return HelpSection{
			Title:       locales["teams_title"],
			Description: locales["teams_desc"],
			Topics: []HelpTopic{
				{Slug: "creating-teams", Title: locales["creating_teams_title"], Description: locales["creating_teams_desc"]},
				{Slug: "team-management", Title: locales["team_management_title"], Description: locales["team_management_desc"]},
			},
		}
	case "seasons":
		return HelpSection{
			Title:       locales["seasons_title"],
			Description: locales["seasons_desc"],
			Topics: []HelpTopic{
				{Slug: "creating-season", Title: locales["creating_season_title"], Description: locales["creating_season_desc"]},
				{Slug: "season-settings", Title: locales["season_settings_title"], Description: locales["season_settings_desc"]},
			},
		}
	case "matches":
		return HelpSection{
			Title:       locales["matches_title"],
			Description: locales["matches_desc"],
			Topics: []HelpTopic{
				{Slug: "recording-results", Title: locales["recording_results_title"], Description: locales["recording_results_desc"]},
				{Slug: "match-history", Title: locales["match_history_title"], Description: locales["match_history_desc"]},
			},
		}
	case "spending":
		return HelpSection{
			Title:       locales["spending_title"],
			Description: locales["spending_desc"],
			Topics: []HelpTopic{
				{Slug: "tracking-expenses", Title: locales["tracking_expenses_title"], Description: locales["tracking_expenses_desc"]},
				{Slug: "budget-management", Title: locales["budget_management_title"], Description: locales["budget_management_desc"]},
			},
		}
	default:
		return HelpSection{
			Title:       "Help Section",
			Description: "Help section content",
			Topics:      []HelpTopic{},
		}
	}
}

// getTopicData returns data for a specific help topic
func getTopicData(section, topic string, locales map[string]string) HelpTopic {
	// This would typically fetch from a database or CMS
	// For now, return mock data based on section and topic
	content := generateTopicContent(section, topic, locales)

	allSections := getSectionData(section, locales)
	for _, t := range allSections.Topics {
		if t.Slug == topic {
			return HelpTopic{
				Slug:        t.Slug,
				Title:       t.Title,
				Description: t.Description,
				Content:     content,
			}
		}
	}

	return HelpTopic{
		Slug:        topic,
		Title:       "Help Topic",
		Description: "Help topic content",
		Content:     content,
	}
}

// getRelatedTopics returns related topics for a specific topic
func getRelatedTopics(section, topic string, locales map[string]string) []HelpTopic {
	sectionData := getSectionData(section, locales)
	var related []HelpTopic

	for _, t := range sectionData.Topics {
		if t.Slug != topic {
			related = append(related, t)
		}
	}

	// Limit to first 4 related topics
	if len(related) > 4 {
		related = related[:4]
	}

	return related
}

// generateTopicContent generates HTML content for a topic
func generateTopicContent(section, topic string, locales map[string]string) string {
	// This would typically fetch from a database or file system
	// For now, return mock content
	switch section + "/" + topic {
	case "getting-started/first-steps":
		return `
			<h2>Welcome to Coachpad!</h2>
			<p>Getting started with Coachpad is easy. Follow these steps to set up your account and start managing your sports activities.</p>
			
			<h3>Step 1: Complete Your Profile</h3>
			<p>First, make sure your profile is complete with your personal information, including your name, email, and preferences.</p>
			
			<h3>Step 2: Add Your First Players</h3>
			<p>Navigate to the Players section and start adding the members of your team or group.</p>
			
			<h3>Step 3: Create a Season</h3>
			<p>Set up your first season to organize your activities and matches.</p>
			
			<h3>Next Steps</h3>
			<ul>
				<li>Explore the different sections of the application</li>
				<li>Set up your teams and organize players</li>
				<li>Start recording match results</li>
				<li>Track your expenses and budget</li>
			</ul>
		`
	case "players/adding-players":
		return `
			<h2>Adding Players to Your Team</h2>
			<p>Learn how to add and manage players in your Coachpad account.</p>
			
			<h3>Adding a New Player</h3>
			<ol>
				<li>Navigate to the <strong>Players</strong> section in the main menu</li>
				<li>Click the <strong>"Add Player"</strong> button</li>
				<li>Fill in the player's information:
					<ul>
						<li>First and last name (required)</li>
						<li>Email address (optional)</li>
						<li>Phone number (optional)</li>
						<li>Position or role</li>
					</ul>
				</li>
				<li>Click <strong>"Save"</strong> to add the player</li>
			</ol>
			
			<h3>Player Information</h3>
			<p>You can add various details about each player:</p>
			<ul>
				<li><strong>Contact Information:</strong> Email and phone for communication</li>
				<li><strong>Playing Position:</strong> Their preferred or assigned position</li>
				<li><strong>Notes:</strong> Any additional information about the player</li>
			</ul>
			
			<h3>Tips</h3>
			<ul>
				<li>Add players before creating teams for easier organization</li>
				<li>Keep contact information up to date for important notifications</li>
				<li>Use the search feature to quickly find players in larger rosters</li>
			</ul>
		`
	default:
		return `
			<h2>Help Topic</h2>
			<p>This help topic content is currently being developed. Please check back later for detailed information.</p>
			
			<h3>Need Immediate Help?</h3>
			<p>If you need assistance right away, please:</p>
			<ul>
				<li>Check our FAQ section for common questions</li>
				<li>Use the search feature to find related topics</li>
				<li>Contact our support team through the feedback form</li>
			</ul>
		`
	}
}
