package templatesapphelp

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
	htmx "maragu.dev/gomponents-htmx"
	html "maragu.dev/gomponents/html"
)

// FAQPageProps represents props for the FAQ page
type FAQPageProps struct {
	Lang       string
	ActiveLink string
	UserID     int32
}

// FAQContentProps represents props for the FAQ content
type FAQContentProps struct {
	Lang   string
	UserID int32
}

// TroubleshootingPageProps represents props for the troubleshooting page
type TroubleshootingPageProps struct {
	Lang       string
	ActiveLink string
	UserID     int32
}

// TroubleshootingContentProps represents props for the troubleshooting content
type TroubleshootingContentProps struct {
	Lang   string
	UserID int32
}

// FAQ represents a frequently asked question
type FAQ struct {
	Question string
	Answer   string
	Category string
}

// TroubleshootingItem represents a troubleshooting guide item
type TroubleshootingItem struct {
	Title       string
	Description string
	Steps       []string
	Category    string
}

// FAQPage renders the full FAQ page
func FAQPage(props FAQPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/help/faq").
		WithTitle("Frequently Asked Questions").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			IsSidebarOpen: false,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return FAQContent(props.Lang, FAQContentProps{
				Lang:   props.Lang,
				UserID: props.UserID,
			})
		})
}

// FAQContent renders the FAQ content
func FAQContent(lang string, props FAQContentProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/help/faq.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	faqs := getFAQs(locales)
	categories := getFAQCategories(faqs)

	return html.Div(
		html.Class("max-w-7xl mx-auto px-4 py-8"),

		// Breadcrumbs
		html.Nav(
			html.Class("flex mb-8"),
			html.Ol(
				html.Class("inline-flex items-center space-x-1 md:space-x-3"),
				html.Li(
					html.Class("inline-flex items-center"),
					html.A(
						html.Href("/app/help"),
						html.Class("inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"),
						htmx.Boost("true"),
						heroicons.Home(html.Class("w-4 h-4 mr-2")),
						gomponents.Text(locales["help_center"]),
					),
				),
				html.Li(
					html.Div(
						html.Class("flex items-center"),
						heroicons.ChevronRight(html.Class("w-6 h-6 text-gray-400")),
						html.Span(
							html.Class("ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-300"),
							gomponents.Text(locales["faq_title"]),
						),
					),
				),
			),
		),

		// Header
		html.Div(
			html.Class("text-center mb-12"),
			html.H1(
				html.Class("text-4xl font-bold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(locales["faq_title"]),
			),
			html.P(
				html.Class("text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto"),
				gomponents.Text(locales["faq_subtitle"]),
			),
		),

		// Category Filter
		html.Div(
			html.Class("mb-8"),
			html.Div(
				html.Class("flex flex-wrap gap-2 justify-center"),
				html.Button(
					html.Class("px-4 py-2 bg-blue-600 text-white rounded-full text-sm font-medium hover:bg-blue-700 transition-colors"),
					gomponents.Attr("onclick", "showAllFAQs()"),
					gomponents.Text(locales["all_categories"]),
				),
				gomponents.Group(func() []gomponents.Node {
					var nodes []gomponents.Node
					for _, category := range categories {
						nodes = append(nodes, html.Button(
							html.Class("px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"),
							gomponents.Attr("onclick", "filterFAQs('"+category+"')"),
							gomponents.Text(category),
						))
					}
					return nodes
				}()),
			),
		),

		// FAQ List
		html.Div(
			html.Class("max-w-4xl mx-auto"),
			html.Div(
				html.ID("faq-container"),
				html.Class("space-y-4"),
				gomponents.Group(func() []gomponents.Node {
					var nodes []gomponents.Node
					for i, faq := range faqs {
						nodes = append(nodes, FAQItem(faq, i))
					}
					return nodes
				}()),
			),
		),

		// Contact Support Section
		html.Div(
			html.Class("mt-16 text-center p-8 bg-gray-50 dark:bg-gray-800 rounded-xl"),
			html.H2(
				html.Class("text-2xl font-bold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(locales["still_need_help"]),
			),
			html.P(
				html.Class("text-gray-600 dark:text-gray-300 mb-6"),
				gomponents.Text(locales["contact_support_desc"]),
			),
			html.A(
				html.Href("/app/feedback"),
				html.Class("inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"),
				htmx.Boost("true"),
				heroicons.ChatBubbleLeftRight(html.Class("w-5 h-5 mr-2")),
				gomponents.Text(locales["contact_support"]),
			),
		),

		// JavaScript for FAQ filtering and accordion
		html.Script(
			gomponents.Text(`
				function showAllFAQs() {
					const items = document.querySelectorAll('.faq-item');
					items.forEach(item => item.style.display = 'block');
					updateActiveButton(event.target);
				}
				
				function filterFAQs(category) {
					const items = document.querySelectorAll('.faq-item');
					items.forEach(item => {
						if (item.dataset.category === category) {
							item.style.display = 'block';
						} else {
							item.style.display = 'none';
						}
					});
					updateActiveButton(event.target);
				}
				
				function updateActiveButton(activeBtn) {
					const buttons = document.querySelectorAll('.category-filter button');
					buttons.forEach(btn => {
						btn.className = btn.className.replace('bg-blue-600 text-white', 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300');
					});
					activeBtn.className = activeBtn.className.replace('bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300', 'bg-blue-600 text-white');
				}
				
				function toggleFAQ(id) {
					const content = document.getElementById('faq-content-' + id);
					const icon = document.getElementById('faq-icon-' + id);
					
					if (content.style.display === 'none' || content.style.display === '') {
						content.style.display = 'block';
						icon.style.transform = 'rotate(180deg)';
					} else {
						content.style.display = 'none';
						icon.style.transform = 'rotate(0deg)';
					}
				}
			`),
		),
	)
}

// FAQItem renders a single FAQ item with accordion functionality
func FAQItem(faq FAQ, index int) gomponents.Node {
	return html.Div(
		html.Class("faq-item bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"),
		gomponents.Attr("data-category", faq.Category),
		html.Button(
			html.Class("w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"),
			gomponents.Attr("onclick", fmt.Sprintf("toggleFAQ(%d)", index)),
			html.H3(
				html.Class("text-lg font-medium text-gray-900 dark:text-white"),
				gomponents.Text(faq.Question),
			),
			heroicons.ChevronDown(
				html.ID(fmt.Sprintf("faq-icon-%d", index)),
				html.Class("w-5 h-5 text-gray-500 transition-transform duration-200"),
			),
		),
		html.Div(
			html.ID(fmt.Sprintf("faq-content-%d", index)),
			html.Class("px-6 pb-4 text-gray-600 dark:text-gray-300"),
			html.Style("display: none;"),
			gomponents.Raw(faq.Answer),
		),
	)
}

// TroubleshootingPage renders the full troubleshooting page
func TroubleshootingPage(props TroubleshootingPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/help/troubleshooting").
		WithTitle("Troubleshooting").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			IsSidebarOpen: false,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return TroubleshootingContent(props.Lang, TroubleshootingContentProps{
				Lang:   props.Lang,
				UserID: props.UserID,
			})
		})
}

// TroubleshootingContent renders the troubleshooting content
func TroubleshootingContent(lang string, props TroubleshootingContentProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/help/troubleshooting.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	troubleshootingItems := getTroubleshootingItems(locales)

	return html.Div(
		html.Class("max-w-7xl mx-auto px-4 py-8"),

		// Breadcrumbs
		html.Nav(
			html.Class("flex mb-8"),
			html.Ol(
				html.Class("inline-flex items-center space-x-1 md:space-x-3"),
				html.Li(
					html.Class("inline-flex items-center"),
					html.A(
						html.Href("/app/help"),
						html.Class("inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"),
						htmx.Boost("true"),
						heroicons.Home(html.Class("w-4 h-4 mr-2")),
						gomponents.Text(locales["help_center"]),
					),
				),
				html.Li(
					html.Div(
						html.Class("flex items-center"),
						heroicons.ChevronRight(html.Class("w-6 h-6 text-gray-400")),
						html.Span(
							html.Class("ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-300"),
							gomponents.Text(locales["troubleshooting_title"]),
						),
					),
				),
			),
		),

		// Header
		html.Div(
			html.Class("text-center mb-12"),
			html.H1(
				html.Class("text-4xl font-bold text-gray-900 dark:text-white mb-4"),
				gomponents.Text(locales["troubleshooting_title"]),
			),
			html.P(
				html.Class("text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto"),
				gomponents.Text(locales["troubleshooting_subtitle"]),
			),
		),

		// Troubleshooting Items Grid
		html.Div(
			html.Class("grid gap-8 md:grid-cols-1 lg:grid-cols-2"),
			gomponents.Group(func() []gomponents.Node {
				var nodes []gomponents.Node
				for _, item := range troubleshootingItems {
					nodes = append(nodes, TroubleshootingItemCard(item))
				}
				return nodes
			}()),
		),
	)
}

// TroubleshootingItemCard renders a troubleshooting guide card
func TroubleshootingItemCard(item TroubleshootingItem) gomponents.Node {
	return html.Div(
		html.Class("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"),
		html.H3(
			html.Class("text-xl font-semibold text-gray-900 dark:text-white mb-3"),
			gomponents.Text(item.Title),
		),
		html.P(
			html.Class("text-gray-600 dark:text-gray-300 mb-4"),
			gomponents.Text(item.Description),
		),
		html.Div(
			html.Class("space-y-3"),
			html.H4(
				html.Class("text-lg font-medium text-gray-900 dark:text-white"),
				gomponents.Text("Steps:"),
			),
			html.Ol(
				html.Class("list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300"),
				gomponents.Group(func() []gomponents.Node {
					var nodes []gomponents.Node
					for _, step := range item.Steps {
						nodes = append(nodes, html.Li(gomponents.Text(step)))
					}
					return nodes
				}()),
			),
		),
	)
}

// Helper functions to get FAQ and troubleshooting data
func getFAQs(locales map[string]string) []FAQ {
	return []FAQ{
		{
			Question: locales["faq_add_player_q"],
			Answer:   locales["faq_add_player_a"],
			Category: locales["category_players"],
		},
		{
			Question: locales["faq_create_season_q"],
			Answer:   locales["faq_create_season_a"],
			Category: locales["category_seasons"],
		},
		{
			Question: locales["faq_record_match_q"],
			Answer:   locales["faq_record_match_a"],
			Category: locales["category_matches"],
		},
		{
			Question: locales["faq_track_spending_q"],
			Answer:   locales["faq_track_spending_a"],
			Category: locales["category_spending"],
		},
		{
			Question: locales["faq_forgot_password_q"],
			Answer:   locales["faq_forgot_password_a"],
			Category: locales["category_account"],
		},
		{
			Question: locales["faq_subscription_q"],
			Answer:   locales["faq_subscription_a"],
			Category: locales["category_account"],
		},
	}
}

func getFAQCategories(faqs []FAQ) []string {
	categoryMap := make(map[string]bool)
	for _, faq := range faqs {
		categoryMap[faq.Category] = true
	}

	var categories []string
	for category := range categoryMap {
		categories = append(categories, category)
	}

	return categories
}

func getTroubleshootingItems(locales map[string]string) []TroubleshootingItem {
	return []TroubleshootingItem{
		{
			Title:       locales["trouble_login_title"],
			Description: locales["trouble_login_desc"],
			Steps: []string{
				locales["trouble_login_step1"],
				locales["trouble_login_step2"],
				locales["trouble_login_step3"],
				locales["trouble_login_step4"],
			},
			Category: locales["category_account"],
		},
		{
			Title:       locales["trouble_data_sync_title"],
			Description: locales["trouble_data_sync_desc"],
			Steps: []string{
				locales["trouble_data_sync_step1"],
				locales["trouble_data_sync_step2"],
				locales["trouble_data_sync_step3"],
			},
			Category: locales["category_technical"],
		},
		{
			Title:       locales["trouble_performance_title"],
			Description: locales["trouble_performance_desc"],
			Steps: []string{
				locales["trouble_performance_step1"],
				locales["trouble_performance_step2"],
				locales["trouble_performance_step3"],
				locales["trouble_performance_step4"],
			},
			Category: locales["category_technical"],
		},
		{
			Title:       locales["trouble_mobile_title"],
			Description: locales["trouble_mobile_desc"],
			Steps: []string{
				locales["trouble_mobile_step1"],
				locales["trouble_mobile_step2"],
				locales["trouble_mobile_step3"],
			},
			Category: locales["category_technical"],
		},
	}
}
