package templatesappchangelog

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/modal"
	. "maragu.dev/gomponents"
	. "maragu.dev/gomponents/html"
)

func ChangelogContent(lang string) Node {
	locales := i18n.MustLoadTemplateLocales("./templates/app/changelog/changelogContent.locales.json", lang)

	// Create the changelog content
	changelogContent := Div(
		Class("space-y-4"),
		H4(
			Class("text-lg font-semibold text-gray-900 dark:text-white"),
			Text(locales["recent_updates"]),
		),
		Div(
			Class("space-y-3"),
			// Version entry
			Div(
				Class("border-l-4 border-blue-500 pl-4"),
				Div(
					Class("flex items-center gap-2"),
					H5(
						Class("font-medium text-gray-900 dark:text-white"),
						Text("v1.0.0"),
					),
					Span(
						Class("text-sm text-gray-500 dark:text-gray-400"),
						Text("2024-01-15"),
					),
				),
				Ul(
					Class("mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300"),
					Li(Text(locales["feature_players_management"])),
					Li(Text(locales["feature_seasons_creation"])),
					Li(Text(locales["feature_match_scheduling"])),
				),
			),
			// Future version placeholder
			Div(
				Class("border-l-4 border-gray-300 pl-4"),
				Div(
					Class("flex items-center gap-2"),
					H5(
						Class("font-medium text-gray-900 dark:text-white"),
						Text(locales["coming_soon"]),
					),
				),
				Ul(
					Class("mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-300"),
					Li(Text(locales["feature_statistics"])),
					Li(Text(locales["feature_team_management"])),
					Li(Text(locales["feature_notifications"])),
				),
			),
		),
	)

	// Create modal configuration
	modalConfig := modal.ModalConfig{
		ModalID: "changelog",
		Title:   locales["changelog_title"],
		Content: changelogContent,
		IsOpen:  true,
	}

	// Wrap with div that sets the proper x-data state for the modal to be opened
	return Div(
		Attr("x-data", "{ changelog_modalOpen: false }"),
		Attr("x-init", "setTimeout(() => { changelog_modalOpen = true }, 0)"),
		modal.Modal(modalConfig),
	)
}
