package templatesappdev

import (
	"github.com/j-em/coachpad/templates/pagebuilder"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/card"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type DevPageContentProps struct {
	Lang string
}

func renderSSETestCardWithSSE(locales map[string]string) gomponents.Node {
	content := html.Div(
		html.Class("p-4"),
		html.P(
			html.Class("mb-4 text-gray-600"),
			gomponents.Text(locales["sse_test_description"]),
		),
		button.PrimaryButton(button.BaseButtonConfig{
			Text:       locales["send_sse_event"],
			ID:         "test-sse-btn",
			ButtonType: "button",
			Attrs: []gomponents.Node{
				gomponents.Attr("hx-post", "/app/test-sse"),
				gomponents.Attr("hx-trigger", "click"),
				gomponents.Attr("hx-swap", "none"),
			},
		}),
		html.Div(
			html.ID("sse-messages"),
			html.Class("mt-4 p-3 bg-gray-50 rounded border min-h-[100px]"),
			gomponents.Attr("sse-connect", "/app/sse"),
			gomponents.Attr("sse-swap", "test-message"),
			gomponents.Attr("hx-target", "#sse-messages"),
			html.P(
				html.ID("sse-placeholder"),
				html.Class("text-sm text-gray-500"),
				gomponents.Text(locales["sse_messages_placeholder"]),
			),
		),
	)

	return card.Card(card.CardProps{
		Title:   locales["sse_test"],
		Content: content,
	})
}

// DevPageContent renders the main content for the development page
func DevPageContent(lang string, props DevPageContentProps) gomponents.Node {
	return pagebuilder.NewPage("/app/dev").
		WithLocaleBase("app/dev/devPageContent").
		RenderContent(lang, func(ctx pagebuilder.PageContext) gomponents.Node {
			return html.Div(
				html.Class("h-full overflow-y-auto"),
				html.Div(
					html.Class("mx-auto px-4 py-6"),
					html.H1(
						html.Class("text-3xl font-bold mb-6"),
						gomponents.Text(ctx.Locales["development_tools"]),
					),
					html.P(
						html.Class("text-gray-600 mb-6"),
						gomponents.Text(ctx.Locales["development_description"]),
					),
					renderSSETestCardWithSSE(ctx.Locales),
				),
			)
		})
}
