package templatesappdev

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type DevPageProps struct {
	Lang          string
	UserID        int32
	ActiveLink    string
	Seasons       []db.Season
	IsSidebarOpen bool
}

func DevPage(props DevPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/dev").
		WithLocaleBase("app/dev/devPage").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return DevPageContent(props.Lang, DevPageContentProps{
				Lang: props.Lang,
			})
		})
}
