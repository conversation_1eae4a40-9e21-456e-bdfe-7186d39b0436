package templatesappverification

import (
	"strings"

	"github.com/j-em/coachpad/i18n"
	templatesuibutton "github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// VerificationSection renders the email verification section for user settings
func VerificationSection(lang string, isVerified bool, userEmail string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/verification/verification.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback to empty map
	}

	if isVerified {
		return html.Div(
			html.Class("bg-green-50 border border-green-200 rounded-lg p-4"),
			html.H3(
				html.Class("text-lg font-medium text-green-800 mb-2"),
				gomponents.Text(locales["verification_section_title"]),
			),
			html.P(
				html.Class("text-green-700"),
				gomponents.Text(locales["verification_status_verified"]),
			),
		)
	}

	return html.Div(
		html.Class("bg-yellow-50 border border-yellow-200 rounded-lg p-4"),
		html.H3(
			html.Class("text-lg font-medium text-yellow-800 mb-2"),
			gomponents.Text(locales["verification_section_title"]),
		),
		html.P(
			html.Class("text-yellow-700 mb-3"),
			gomponents.Text(locales["verification_status_unverified"]),
		),
		html.P(
			html.Class("text-yellow-600 text-sm mb-4"),
			gomponents.Text(locales["verification_description"]),
		),
		html.Form(
			htmx.Post("/app/verification/send"),
			htmx.Target("#verification-section"),
			htmx.Swap("outerHTML"),
			html.Input(
				html.Type("hidden"),
				html.Name("email"),
				html.Value(userEmail),
			),
			templatesuibutton.PrimaryButton(templatesuibutton.BaseButtonConfig{
				Text:       locales["send_verification_email"],
				ButtonType: "submit",
			}),
		),
	)
}

// VerificationEmailSent renders the success message after sending verification email
func VerificationEmailSent(lang string, email string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/verification/verification.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback to empty map
	}

	message := strings.ReplaceAll(locales["verification_email_sent_message"], "{email}", email)

	return html.Div(
		html.Class("bg-blue-50 border border-blue-200 rounded-lg p-4"),
		html.H3(
			html.Class("text-lg font-medium text-blue-800 mb-2"),
			gomponents.Text(locales["verification_email_sent_title"]),
		),
		html.P(
			html.Class("text-blue-700"),
			gomponents.Text(message),
		),
	)
}

// VerificationError renders error messages for verification failures
func VerificationError(lang string, errorMessage string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/verification/verification.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback to empty map
	}

	return html.Div(
		html.Class("bg-red-50 border border-red-200 rounded-lg p-4"),
		html.H3(
			html.Class("text-lg font-medium text-red-800 mb-2"),
			gomponents.Text(locales["verification_error"]),
		),
		html.P(
			html.Class("text-red-700"),
			gomponents.Text(errorMessage),
		),
	)
}

// AlreadyVerified renders message when user is already verified
func AlreadyVerified(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/verification/verification.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback to empty map
	}

	return html.Div(
		html.Class("bg-green-50 border border-green-200 rounded-lg p-4"),
		html.H3(
			html.Class("text-lg font-medium text-green-800 mb-2"),
			gomponents.Text(locales["already_verified_title"]),
		),
		html.P(
			html.Class("text-green-700"),
			gomponents.Text(locales["already_verified_message"]),
		),
	)
}

// VerificationSuccess renders the success page after email verification
func VerificationSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/verification/verification.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback to empty map
	}

	return html.Div(
		html.Class("min-h-screen flex items-center justify-center bg-gray-50"),
		html.Div(
			html.Class("max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md"),
			html.Div(
				html.Class("text-center"),
				html.H2(
					html.Class("text-3xl font-bold text-green-600 mb-4"),
					gomponents.Text(locales["verification_success_title"]),
				),
				html.P(
					html.Class("text-gray-700 mb-6"),
					gomponents.Text(locales["verification_success_message"]),
				),
				html.A(
					html.Href("/app/settings/user"),
					html.Class("inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"),
					gomponents.Text(locales["return_to_settings"]),
				),
			),
		),
	)
}
