package templatesappfeedback

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// FeedbackForm renders the feedback form with name, description, and type fields.
func FeedbackForm(lang string) gomponents.Node {
	locales := i18n.MustLoadTemplateLocales("./templates/app/feedback/feedbackModal.locales.json", lang)

	feedbackTypeOptions := []uiselect.SelectItem{
		{Value: "bug", Title: locales["feedback_type_bug"]},
		{Value: "feature_request", Title: locales["feedback_type_feature"]},
		{Value: "other", Title: locales["feedback_type_other"]},
	}

	return html.Form(
		gomponents.Attr("x-data", "{ fields: {}, errors: {}, errorMessages: {} }"),
		gomponents.Attr("x-validate-form", ""),
		htmx.Post("/app/feedback"),
		htmx.Target("#toast-body-container"),
		htmx.Swap("afterbegin"),
		gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { feedbackModal_modalOpen = false; }"),
		html.Class("space-y-4"),

		// Name field
		form.FormInput(form.FormInputProps{
			Label:       locales["feedback_name_label"],
			Type:        "text",
			ID:          "name",
			Name:        "name",
			Required:    true,
			Placeholder: locales["feedback_name_placeholder"],
			DataTestID:  "feedback-name-input",
		}),

		// Description field
		form.FormTextArea(form.FormTextAreaProps{
			Label:       locales["feedback_description_label"],
			ID:          "description",
			Name:        "description",
			Required:    true,
			Placeholder: locales["feedback_description_placeholder"],
			Rows:        4,
			DataTestID:  "feedback-description-input",
		}),

		// Feedback type select
		uiselect.Select(uiselect.SelectProps{
			Items:       feedbackTypeOptions,
			Name:        "feedback_type",
			ID:          "feedback_type",
			XID:         "feedback_type",
			Label:       locales["feedback_type_label"],
			Required:    true,
			Placeholder: locales["feedback_type_placeholder"],
			Class:       "w-full",
			DataTestID:  "feedback-type-select",
		}),

		// Submit button
		html.Div(
			html.Class("flex justify-end pt-4"),
			button.PrimaryButton(button.BaseButtonConfig{
				Text:       locales["feedback_submit"],
				ButtonType: "submit",
				DataTestID: "feedback-submit-button",
			}),
		),
	)
}
