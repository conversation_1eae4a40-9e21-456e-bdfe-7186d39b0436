package templatesappfeedback

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
)

// FeedbackModal renders the feedback form in a modal dialog.
// Refactored to use the new ClientModalWithAutoOpen wrapper for clarity.
func FeedbackModal(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/feedback/feedbackModal.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "feedbackModal",
		Title:        locales["feedback_title"],
		Content:      FeedbackForm(lang),
		IsUnclosable: false,
		DataTestID:   "feedback-modal",
	})
}
