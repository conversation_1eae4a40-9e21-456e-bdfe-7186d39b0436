package templatesappteams

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/utils/testid"
	"maragu.dev/gomponents"
)

// TeamRowDropdown creates a dropdown for team row actions
func TeamRowDropdown(team db.Team, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamsTable.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return dropdown.Dropdown(dropdown.DropdownConfig{
		ID:         fmt.Sprintf("team-actions-%d", team.ID),
		DataTestID: testid.Generate("team", team.ID, "actions", "dropdown"),
		ButtonIcon: icons.EllipsisVertical(),
		Items: []dropdown.DropdownItem{
			{
				Text:       locales["delete_team"],
				Icon:       icons.Trash(),
				DataTestID: testid.Generate("team", team.ID, "delete"),
				OnClick:    fmt.Sprintf("if (confirm('%s')) { htmx.ajax('DELETE', '/app/teams/%d', {target: '#team-row-%d', swap: 'outerHTML'}); }", locales["confirm_delete_team"], team.ID, team.ID),
			},
		},
	})
}
