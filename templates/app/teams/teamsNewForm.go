package templatesappteams

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/limitbanner"
	modalwrappers "github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type TeamsNewFormConfig struct {
	Lang       string
	TeamsUsage *limits.LimitCheckResult
}

func TeamsNewForm(config TeamsNewFormConfig) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamsNewForm.locales.json", config.Lang)
	if err != nil {
		// Handle error, perhaps log it or use default values
		locales = make(map[string]string)
	}
	return modalwrappers.ClientModalWithAutoOpen(modalwrappers.ClientModalConfig{
		ModalID: "teamsNewForm",
		Title:   locales["formTitle"],
		Content: html.Form(
			html.Method("POST"),
			htmx.Boost("true"),
			htmx.Post("/app/teams"),
			gomponents.Attr("x-effect", "htmx.process($el)"),
			html.ID("teamsNewForm_form"),
			gomponents.Attr("x-data", "{fields: {}, errors: {}, errorMessages: {}, groupErrors: {}}"),
			gomponents.Attr("x-validate-form", ""),
			html.Div(
				html.Class("dark:bg-gray-800 dark:text-white"),

				// Usage indicator for teams
				gomponents.If(config.TeamsUsage != nil,
					html.Div(
						html.Class("mb-4"),
						limitbanner.UsageIndicator(limitbanner.LimitBannerConfig{
							LimitResult: config.TeamsUsage,
							Lang:        config.Lang,
							Class:       "mb-4",
						}),
					),
				),

				form.FormInput(form.FormInputProps{
					Label:        locales["formNameLabel"],
					Type:         "text",
					ID:           "name",
					Name:         "name",
					Required:     true,
					ExtraClasses: "dark:bg-gray-700 dark:border-gray-600 dark:text-white",
				}),
				form.FormTextArea(form.FormTextAreaProps{
					Label: locales["formDescriptionLabel"],
					ID:    "description",
					Name:  "description",
					Rows:  3,
				}),
			),
			html.Div(
				html.Class("flex items-center justify-end mt-4"),
				button.PrimaryButton(button.BaseButtonConfig{
					ButtonType: "submit",
					Text:       locales["formSubmit"],
				}),
			),
		),
	})
}
