package templatesappteams

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// TeamSelectProps defines the properties for a team select component
type TeamSelectProps struct {
	Lang      string
	TeamID    *int32
	TeamName  string
	Required  bool
	FieldName string // Field name for validation (defaults to "teamId")
	Label     string // Custom label (optional)
}

// TeamSelect creates a component for selecting teams via a modal
// This component will show selected team and open a modal when the button is clicked
func TeamSelect(lang string) gomponents.Node {
	return TeamSelectWithProps(TeamSelectProps{
		Lang:      lang,
		FieldName: "teamId",
	})
}

// TeamSelectWithValue creates a team select component with a pre-selected value
func TeamSelectWithValue(lang string, teamID *int32, teamName string) gomponents.Node {
	return TeamSelectWithProps(TeamSelectProps{
		Lang:      lang,
		TeamID:    teamID,
		TeamName:  teamName,
		FieldName: "teamId",
	})
}

// TeamSelectWithProps creates a team select component with full configuration
func TeamSelectWithProps(props TeamSelectProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamSelect.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Use custom label or default from locales
	labelText := props.Label
	if labelText == "" {
		labelText = locales["select_team"]
	}

	// Set field name, default to "teamId"
	fieldName := props.FieldName
	if fieldName == "" {
		fieldName = "teamId"
	}

	// Set initial values based on parameters
	var initialTeamID string
	if props.TeamID != nil {
		initialTeamID = fmt.Sprintf("%d", *props.TeamID)
	}

	var selectedTeamValue string
	if props.TeamID != nil {
		selectedTeamValue = "'" + initialTeamID + "'"
	} else {
		selectedTeamValue = "null"
	}

	// Alpine.js data with watcher to update hidden input and trigger validation
	initialDataWithWatcher := fmt.Sprintf("{ teamSelectModal_modalOpen: false, selectedTeam: %s, selectedTeamName: '%s', init() { this.$watch('selectedTeam', value => { const input = this.$el.querySelector('#%s'); if (input) { if (value && value !== 'null') { input.value = value; input.name = '%s'; } else { input.value = ''; input.removeAttribute('name'); } input.dispatchEvent(new Event('input', { bubbles: true })); input.dispatchEvent(new Event('change', { bubbles: true })); } }); } }",
		selectedTeamValue, props.TeamName, fieldName, fieldName)

	return html.Div(
		gomponents.Attr("x-data", initialDataWithWatcher),
		html.Label(
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
			gomponents.Attr("for", fieldName),
			gomponents.Text(labelText),
			gomponents.If(props.Required, html.Span(
				html.Class("text-red-500 ml-1"),
				gomponents.Text("*"),
			)),
		),
		// Button to trigger modal
		button.SecondaryButton(button.BaseButtonConfig{
			ID:         "select-team-button",
			ButtonType: "button",
			Text:       locales["select_team"],
			Class:      "mb-2 w-full justify-start text-left appearance-none relative px-3 py-2 border border-gray-300 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300",
			Attrs: []gomponents.Node{
				gomponents.Attr("hx-get", "/app/teams/select-modal"),
				gomponents.Attr("hx-target", "#team-select-modal-container"),
				gomponents.Attr("hx-swap", "innerHTML"),
			},
		}),
		html.Div(html.ID("team-select-modal-container")),
		// Display selected team
		html.Div(
			html.ID("selected-team-display"),
			html.Class("mt-2"),
			gomponents.Attr("x-show", "selectedTeam"),
			html.P(
				html.Class("text-sm text-gray-600 dark:text-gray-400 mb-1"),
				gomponents.Text(locales["selected_team"]),
			),
			html.Div(
				html.Class("flex gap-2"),
				html.Span(
					html.Class("inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded"),
					gomponents.Attr("x-text", "selectedTeamName"),
				),
			),
		),
		// Hidden input for selected team ID (name attribute added dynamically when team is selected)
		html.Input(
			html.Type("hidden"),
			html.ID(fieldName),
			gomponents.Attr("x-bind:value", "selectedTeam"),
			gomponents.Attr("data-field-name", fieldName),
			gomponents.If(props.Required, html.Required()),
		),
		// Error message container
		html.Div(
			html.Class("h-5"), // Fixed height to prevent layout shift
			html.Div(
				html.Class("text-red-500 text-sm"),
				gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", fieldName)),
				gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", fieldName)),
			),
		),
	)
}

// TeamSelectModal renders a modal with team selection radio buttons
func TeamSelectModal(teams []db.Team, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamSelect.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	modalContent := html.Div(
		gomponents.Attr("x-data", "{ modalSelectedTeam: null, modalSelectedTeamName: '', searchQuery: '' }"),
		gomponents.Attr("x-init", "modalSelectedTeam = selectedTeam; modalSelectedTeamName = selectedTeamName"),
		html.Div(
			html.Class("space-y-4"),
			gomponents.If(len(teams) == 0,
				html.Div(
					html.Class("text-center text-gray-500 py-4"),
					gomponents.Text(locales["no_teams_available"]),
				),
			),
			gomponents.If(len(teams) > 0,
				html.Div(
					html.Class("mb-4"),
					html.Label(
						html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"),
						gomponents.Text(locales["search_teams"]),
					),
					html.Input(
						html.Type("text"),
						html.ID("team-search-input"),
						html.Placeholder(locales["search_placeholder"]),
						html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
						gomponents.Attr("x-model", "searchQuery"),
					),
				),
			),
			// None option
			html.Div(
				html.Class("flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded"),
				html.Label(
					html.Class("flex items-center space-x-3 cursor-pointer flex-1"),
					html.Input(
						html.Type("radio"),
						html.Name("team_selection"),
						html.Value(""),
						gomponents.Attr("x-model", "modalSelectedTeam"),
						gomponents.Attr("@change", "modalSelectedTeamName = ''"),
						html.Class("form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"),
					),
					html.Span(
						html.Class("text-gray-900 dark:text-gray-100 font-medium"),
						gomponents.Text(locales["no_team"]),
					),
				),
			),
			gomponents.If(len(teams) > 0,
				html.Div(
					html.Class("max-h-64 overflow-y-auto"),
					html.Ul(
						html.ID("teams-selection-list"),
						html.Class("space-y-2"),
						gomponents.Map(teams, func(t db.Team) gomponents.Node {
							return html.Li(
								html.Class("flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded"),
								gomponents.Attr("x-show", fmt.Sprintf("searchQuery === '' || '%s'.toLowerCase().includes(searchQuery.toLowerCase())", t.Name)),
								html.Label(
									html.Class("flex items-center space-x-3 cursor-pointer flex-1"),
									html.Input(
										html.Type("radio"),
										html.Name("team_selection"),
										html.Value(fmt.Sprintf("%d", t.ID)),
										gomponents.Attr("x-model", "modalSelectedTeam"),
										gomponents.Attr("@change", fmt.Sprintf("modalSelectedTeamName = '%s'", t.Name)),
										html.Class("form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"),
									),
									html.Span(
										html.Class("text-gray-900 dark:text-gray-100 font-medium"),
										gomponents.Text(t.Name),
									),
								),
							)
						}),
					),
				),
			),
			html.Div(
				html.Class("flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"),
				button.SecondaryButton(button.BaseButtonConfig{
					Text:       locales["cancel"],
					ButtonType: "button",
					Attrs: []gomponents.Node{
						gomponents.Attr("@click", "teamSelectModal_modalOpen = false"),
					},
				}),
				button.PrimaryButton(button.BaseButtonConfig{
					Text:       locales["confirm_selection"],
					ButtonType: "button",
					Attrs: []gomponents.Node{
						gomponents.Attr("@click", `
								selectedTeam = modalSelectedTeam;
								selectedTeamName = modalSelectedTeamName;
								teamSelectModal_modalOpen = false;
							`),
					},
				}),
			),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "teamSelectModal",
		Title:        locales["select_team"],
		Content:      modalContent,
		IsUnclosable: false,
		DataTestID:   "team-select-modal",
	})
}
