package templatesappteams

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type TeamsDashboardPageProps struct {
	ActiveLink     string
	Teams          []db.Team
	AllTeams       []db.Team
	Lang           string
	Seasons        []db.Season
	IsSidebarOpen  bool
	TotalItems     int
	TotalPages     int
	Params         TeamsTableParams
	IsSearchActive bool
}

func TeamsDashboardPage(props TeamsDashboardPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/teams").
		WithLocaleBase("app/teams/teamsDashboardPage").
		WithTitle("title").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return TeamsDashboardPageContent(props.Teams, props.AllTeams, props.Lang, &props.Params, props.TotalItems, props.TotalPages, props.IsSearchActive)
		})
}
