package templatesappteams

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatesprint "github.com/j-em/coachpad/templates/ui/print"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PrintTeamsProps struct {
	Teams []db.Team
	Lang  string
}

func PrintTeams(props PrintTeamsProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/printTeams.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	content := html.Div(
		html.Div(
			html.Class("print-header"),
			html.H1(
				html.Class("print-title"),
				gomponents.Text(locales["print_title"]),
			),
			html.P(
				html.Class("print-subtitle"),
				gomponents.Text(locales["print_subtitle"]),
			),
		),
		html.Table(
			html.Class("print-table"),
			html.THead(
				html.Tr(
					html.Th(gomponents.Text(locales["table_header_name"])),
					html.Th(gomponents.Text(locales["table_header_description"])),
					html.Th(gomponents.Text(locales["table_header_status"])),
				),
			),
			html.TBody(
				gomponents.Group(
					gomponents.Map(props.Teams, func(team db.Team) gomponents.Node {
						status := locales["status_inactive"]
						if team.IsActive {
							status = locales["status_active"]
						}
						return html.Tr(
							html.Td(gomponents.Text(team.Name)),
							html.Td(gomponents.Text(team.Description.String)),
							html.Td(gomponents.Text(status)),
						)
					}),
				),
			),
		),
		html.Div(
			html.Class("print-footer"),
			gomponents.Text(locales["print_footer"]),
		),
	)

	return templatesprint.PrintPage(templatesprint.PrintPageProps{
		Title:   locales["print_title"],
		Lang:    props.Lang,
		Content: content,
	})
}
