package templatesappteams

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	"github.com/j-em/coachpad/templates/ui/icons"
	tablepagination "github.com/j-em/coachpad/templates/ui/tablePagination"
	tablesearchbar "github.com/j-em/coachpad/templates/ui/tableSearchBar"
	"github.com/j-em/coachpad/templates/ui/tableactions"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/pagination"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// TeamsTableParams contains parameters for table filtering/sorting/pagination
type TeamsTableParams struct {
	pagination.SortablePaginationParams
}

// TeamsTableProps contains props for the HTMX-enabled teams table
type TeamsTableProps struct {
	Teams          []db.Team // The paginated/filtered teams to display
	AllTeams       []db.Team // All teams in the database (to determine if DB is empty)
	Lang           string
	Params         TeamsTableParams
	TotalItems     int
	TotalPages     int
	IsSearchActive bool // Whether a search is currently being performed
}

// TeamsSearchBar is deprecated - use the unified tablesearchbar.TableSearchBar instead
// This function is kept for backwards compatibility but will be removed in a future version
func TeamsSearchBar(params TeamsTableParams, lang string) gomponents.Node {
	return tablesearchbar.TableSearchBar(tablesearchbar.TableSearchBarProps{
		SearchValue:      params.Search,
		SearchDataTestID: "teams-search-bar",
		BaseURL:          "/app/teams-table",
		TargetID:         "teams-table-content",
		HtmxInclude:      "[name='sort'], [name='dir'], [name='page'], [name='per_page']",
		HiddenInputs: map[string]string{
			"page":     strconv.Itoa(params.Page),
			"per_page": strconv.Itoa(params.ItemsPerPage),
			"sort":     params.Sort,
			"dir":      params.Direction,
		},
		Lang:        lang,
		LocalesPath: "./templates/app/teams/teamsTable.locales.json",
	})
}

// TeamsDashboardHeader is now merged here
func TeamsDashboardHeader(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamsTable.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		html.Div(
			html.Class("flex items-center justify-between gap-3"),

			html.H1(
				html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white"),
				gomponents.Text(locales["teams"]),
			),
		),
		html.Div(
			gomponents.Attr("id", "newTeamFormContainer"),
		),
	)
}

// TeamsDashboardPageContent renders the main dashboard view for the Teams page.
func TeamsDashboardPageContent(teams []db.Team, allTeams []db.Team, lang string, params *TeamsTableParams, totalItems, totalPages int, isSearchActive bool) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamsTable.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}
	// Use default parameters if not provided
	if params == nil {
		params = &TeamsTableParams{
			SortablePaginationParams: pagination.SortablePaginationParams{
				BasePaginationParams: pagination.BasePaginationParams{
					Page:         1,
					ItemsPerPage: 10,
					Search:       "",
				},
				Sort:      "name",
				Direction: "asc",
			},
		}
	}

	// Use len(teams) if totalItems not provided
	if totalItems == 0 {
		totalItems = len(teams)
	}

	// Calculate totalPages if not provided
	if totalPages == 0 {
		totalPages = pagination.CalculateTotalPages(totalItems, params.ItemsPerPage)
	}

	return html.Div(
		html.Class("h-full flex flex-col gap-3 p-4"),
		html.Head(
			html.TitleEl(
				gomponents.Text(
					locales["page_title"]),
			),
		),
		TeamsDashboardHeader(lang),
		// Actions dropdown - always visible
		html.Div(
			html.Class("flex justify-between items-center mb-6"),
			dropdown.Dropdown(dropdown.DropdownConfig{
				ID:         "teams-actions",
				DataTestID: "teams-actions-dropdown",
				ButtonText: locales["actions"],
				ButtonIcon: icons.EllipsisVertical(),
				Items: []dropdown.DropdownItem{
					{
						Text:       locales["print_teams"],
						Icon:       icons.Printer(),
						DataTestID: "print-teams-button",
						OnClick:    buildTeamsPrintURL("/app/teams", *params),
					},
					{
						Text:         locales["export_csv"],
						Icon:         icons.ArrowDownTray(),
						DataTestID:   "export-teams-csv-button",
						Href:         "/app/teams/export/csv",
						DisableBoost: true,
					},
					{
						Text:       locales["add_team"],
						Icon:       icons.Plus(),
						DataTestID: "add-new-team-btn",
						XHxGet:     "'/app/teams/new'",
						HxTarget:   "#newTeamFormContainer",
					},
				},
			}),
		),
		// Search bar stays outside the refreshable content
		gomponents.If(len(allTeams) > 0, TeamsSearchBar(*params, lang)),
		// When no search bar is visible, add hidden fields to preserve state
		gomponents.If(len(allTeams) == 0, html.Div(
			html.Input(html.Type("hidden"), html.Name("search"), html.Value(params.Search)),
			html.Input(html.Type("hidden"), html.Name("page"), html.Value(strconv.Itoa(params.Page))),
			html.Input(html.Type("hidden"), html.Name("per_page"), html.Value(strconv.Itoa(params.ItemsPerPage))),
			html.Input(html.Type("hidden"), html.Name("sort"), html.Value(params.Sort)),
			html.Input(html.Type("hidden"), html.Name("dir"), html.Value(params.Direction)),
		)),
		// Teams content container that gets refreshed
		html.Div(
			html.ID("teams-table-content"),
			html.Class("flex flex-col min-h-0"),
			htmx.Trigger("teamsUpdated from:body"),
			htmx.Get("/app/teams-table"),
			htmx.Target("this"),
			htmx.Include("[name='search'], [name='sort'], [name='dir'], [name='page'], [name='per_page']"),
			TeamsTableContent(TeamsTableProps{
				Teams:          teams,
				AllTeams:       allTeams,
				Lang:           lang,
				Params:         *params,
				TotalItems:     totalItems,
				TotalPages:     totalPages,
				IsSearchActive: isSearchActive,
			}),
		),
	)
}

// TeamsTableContent renders the refreshable table content for HTMX updates
func TeamsTableContent(props TeamsTableProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/teams/teamsTable.locales.json", props.Lang)
	if err != nil {
		// Handle error, use default values
		locales = map[string]string{}
	}

	baseURL := "/app/teams-table"

	return html.Div(
		html.Class("flex flex-col min-h-0 teamsTable"),
		gomponents.Attr("data-testid", "teams-table"),

		// Global saving indicator
		html.Div(
			html.Class("mb-4 text-sm text-gray-600 dark:text-gray-300 items-center htmx-indicator"),
			html.ID("global-saving-indicator"),
			gomponents.Attr("data-testid", "global-saving-indicator"),
			gomponents.El("svg",
				html.Class("animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500 dark:text-blue-400"),
				gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
				gomponents.Attr("fill", "none"),
				gomponents.Attr("viewBox", "0 0 24 24"),
				gomponents.El("circle",
					html.Class("opacity-25"),
					gomponents.Attr("cx", "12"),
					gomponents.Attr("cy", "12"),
					gomponents.Attr("r", "10"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.Attr("stroke-width", "4"),
				),
			),
			gomponents.Text(locales["saving"]),
		),

		// Empty state or table content
		func() gomponents.Node {
			if props.TotalItems == 0 {
				// Check if this is a search result with no matches or truly empty database
				if props.IsSearchActive && len(props.AllTeams) > 0 {
					// No search results (but teams exist in DB)
					return html.Div(
						html.Class("flex flex-col items-center justify-center py-12 px-4 text-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600"),
						gomponents.Attr("data-testid", "no-search-results-state"),
						gomponents.El("svg",
							html.Class("w-16 h-16 text-gray-400 dark:text-gray-500 mb-4"),
							gomponents.Attr("fill", "none"),
							gomponents.Attr("stroke", "currentColor"),
							gomponents.Attr("viewBox", "0 0 24 24"),
							gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
							gomponents.El("path",
								gomponents.Attr("stroke-linecap", "round"),
								gomponents.Attr("stroke-linejoin", "round"),
								gomponents.Attr("stroke-width", "2"),
								gomponents.Attr("d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"),
							),
						),
						html.H3(
							html.Class("text-lg font-medium text-gray-900 dark:text-white mb-2"),
							gomponents.Text(locales["no_search_results_title"]),
						),
						html.P(
							html.Class("text-gray-500 dark:text-gray-400 mb-6 max-w-md"),
							gomponents.Text(locales["no_search_results_message"]),
						),
						button.PrimaryIconButton(button.IconButtonConfig{
							ID:         "clear-search-btn",
							DataTestID: "clear-search-btn",
							ButtonType: "button",
							Text:       locales["clear_search"],
							Class:      "",
							Icon:       icons.XCircle(),
							Attrs: []gomponents.Node{
								htmx.Get("/app/teams-table"),
								htmx.Target("#teams-table-content"),
								htmx.PushURL("true"),
							},
						}),
					)
				} else {
					// No teams in database at all
					return html.Div(
						html.Class("flex flex-col items-center justify-center py-12 px-4 text-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600"),
						gomponents.Attr("data-testid", "no-teams-empty-state"),
						gomponents.El("svg",
							html.Class("w-16 h-16 text-gray-400 dark:text-gray-500 mb-4"),
							gomponents.Attr("fill", "none"),
							gomponents.Attr("stroke", "currentColor"),
							gomponents.Attr("viewBox", "0 0 24 24"),
							gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
							gomponents.El("path",
								gomponents.Attr("stroke-linecap", "round"),
								gomponents.Attr("stroke-linejoin", "round"),
								gomponents.Attr("stroke-width", "2"),
								gomponents.Attr("d", "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"),
							),
						),
						html.H3(
							html.Class("text-lg font-medium text-gray-900 dark:text-white mb-2"),
							gomponents.Text(locales["no_teams_title"]),
						),
						html.P(
							html.Class("text-gray-500 dark:text-gray-400 mb-6 max-w-md"),
							gomponents.Text(locales["no_teams_message"]),
						),
						button.PrimaryIconButton(button.IconButtonConfig{
							ID:         "add-first-team-btn",
							DataTestID: "add-first-team-btn",
							ButtonType: "button",
							Text:       locales["add_first_team"],
							Class:      "",
							Icon:       icons.Plus(),
							Attrs: []gomponents.Node{
								gomponents.Attr("hx-get", "/app/teams/new"),
								gomponents.Attr("hx-target", "#newTeamFormContainer"),
							},
						}),
					)
				}
			}
			// Table content when teams exist
			return html.Div(
				html.Class("shadow border-b border-gray-200 dark:border-gray-700 sm:rounded-lg overflow-y-auto overflow-x-auto flex-1 min-h-0 bg-white dark:bg-gray-900"),
				html.Table(
					html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed"),
					// Table Header with sticky positioning and sorting
					html.THead(
						html.Class("bg-gray-50 dark:bg-gray-800 sticky top-0 z-10"),
						html.Tr(
							buildSortableTeamHeader("name", locales["table_header_name"], props, baseURL),
							buildSortableTeamHeader("description", locales["table_header_description"], props, baseURL),
							buildSortableTeamHeader("isActive", locales["table_header_active"], props, baseURL),
							html.Th(
								gomponents.Attr("scope", "col"),
								html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20"),
								gomponents.Text(locales["table_header_actions"]),
							),
						),
					),
					// Table Body
					html.TBody(
						html.Class("bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700"),
						gomponents.Group(
							gomponents.Map(props.Teams, func(team db.Team) gomponents.Node {
								return buildTeamRow(team, props.Lang)
							}),
						),
					),
				),
			)
		}(),

		// Pagination Controls - only show when there are actual results to paginate
		gomponents.If(props.TotalItems > 0, tablepagination.TablePagination(tablepagination.TablePaginationProps{
			Params:      props.Params.SortablePaginationParams,
			TotalItems:  props.TotalItems,
			TotalPages:  props.TotalPages,
			Lang:        props.Lang,
			BaseURL:     baseURL,
			DataTestID:  "teams-pagination",
			TargetID:    "teams-table-content",
			HtmxInclude: "[name='search'], [name='sort'], [name='dir']",
		})),
	)
}

// buildSortableTeamHeader creates a sortable table header
func buildSortableTeamHeader(field, title string, props TeamsTableProps, baseURL string) gomponents.Node {
	isCurrentSort := props.Params.Sort == field
	direction := "asc"
	if isCurrentSort && props.Params.Direction == "asc" {
		direction = "desc"
	}

	queryParams := fmt.Sprintf("?sort=%s&dir=%s", field, direction)
	if props.Params.Search != "" {
		queryParams += "&search=" + props.Params.Search
	}
	queryParams += fmt.Sprintf("&per_page=%d", props.Params.ItemsPerPage)

	sortIcon := ""
	if isCurrentSort {
		if props.Params.Direction == "asc" {
			sortIcon = " ↑"
		} else {
			sortIcon = " ↓"
		}
	}

	return html.Th(
		gomponents.Attr("scope", "col"),
		html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
		html.Button(
			html.Class("hover:text-gray-700 dark:hover:text-gray-100"),
			htmx.Get(baseURL+queryParams),
			htmx.Target("#teams-table-content"),
			htmx.PushURL("true"),
			gomponents.Text(title+sortIcon),
		),
	)
}

// buildTeamRow creates a table row for a team (placeholder - needs real Team model)
func buildTeamRow(team db.Team, lang string) gomponents.Node {
	return html.Tr(
		html.ID(fmt.Sprintf("team-row-%d", team.ID)),
		// Name field
		html.Td(
			html.Class("px-6 py-4"),
			html.Input(
				html.Type("text"),
				html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
				html.Value(team.Name),
				html.Name("name"),
				gomponents.Attr("data-testid", fmt.Sprintf("team-%d-name", team.ID)),
				htmx.Put(fmt.Sprintf("/app/teams/%d/inline", team.ID)),
				htmx.Trigger("input changed delay:900ms"),
				htmx.Indicator("#global-saving-indicator"),
				htmx.Swap("none"),
			),
		),
		// Description field
		html.Td(
			html.Class("px-6 py-4"),
			html.Input(
				html.Type("text"),
				html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
				html.Value(team.Description.String),
				html.Name("description"),
				gomponents.Attr("data-testid", fmt.Sprintf("team-%d-description", team.ID)),
				htmx.Put(fmt.Sprintf("/app/teams/%d/inline", team.ID)),
				htmx.Trigger("input changed delay:900ms"),
				htmx.Indicator("#global-saving-indicator"),
				htmx.Swap("none"),
			),
		),
		// Is active checkbox
		html.Td(
			html.Class("px-6 py-4"),
			html.Div(
				html.Class("flex items-center"),
				html.Input(
					html.Type("checkbox"),
					html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
					gomponents.If(team.IsActive, html.Checked()),
					html.Name("isActive"),
					html.Value("true"),
					gomponents.Attr("data-testid", fmt.Sprintf("team-%d-active", team.ID)),
					htmx.Put(fmt.Sprintf("/app/teams/%d/inline", team.ID)),
					htmx.Trigger("change"),
					htmx.Indicator("#global-saving-indicator"),
					htmx.Swap("none"),
				),
			),
		),
		// Actions column with dropdown
		html.Td(
			html.Class("px-6 py-4 w-20"),
			tableactions.MultiActionDropdown(tableactions.MultiActionConfig{
				ID:         fmt.Sprintf("team-%d-actions", team.ID),
				DataTestID: fmt.Sprintf("team-%d-actions-dropdown", team.ID),
				EntityID:   team.ID,
				EntityName: team.Name,
				Size:       tableactions.SizeDefault,
				Lang:       lang,
				Actions: []tableactions.ActionItem{
					{
						Type:       tableactions.ActionDelete,
						HxEndpoint: fmt.Sprintf("/app/teams/%d", team.ID),
						HxTarget:   fmt.Sprintf("#team-row-%d", team.ID),
						HxSwap:     "outerHTML",
						DataTestID: fmt.Sprintf("team-%d-delete", team.ID),
					},
				},
			}),
		),
		// Hidden fields for the PUT request
		html.Input(html.Type("hidden"), html.Name("id"), html.Value(strconv.Itoa(int(team.ID)))),
	)
}

// TeamsTable renders the teams table wrapper (used for backward compatibility)
func TeamsTable(props TeamsTableProps) gomponents.Node {
	return html.Div(
		html.ID("teams-table-content"),
		TeamsTableContent(props),
	)
}

// buildTeamsPrintURL creates a JavaScript window.open call with current filter state
func buildTeamsPrintURL(baseURL string, params TeamsTableParams) string {
	printParams := utils.PrintURLParams{
		BaseURL:      baseURL,
		Page:         params.Page,
		ItemsPerPage: params.ItemsPerPage,
		Search:       params.Search,
		Sort:         params.Sort,
		Direction:    params.Direction,
		ExtraParams:  make(map[string]string),
	}

	return utils.BuildPrintURL(printParams)
}
