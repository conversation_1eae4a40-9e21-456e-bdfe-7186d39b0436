package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	templatesuibadge "github.com/j-em/coachpad/templates/ui/badge"
	templatesuibutton "github.com/j-em/coachpad/templates/ui/button"

	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type SubscriptionSectionProps struct {
	SubscriptionTier string
	Lang             string
}

func SubscriptionSection(props SubscriptionSectionProps) gomponents.Node {
	return subscriptionSectionInternal(props, true)
}

// SubscriptionSectionContent returns just the content without SSE attributes
func SubscriptionSectionContent(props SubscriptionSectionProps) gomponents.Node {
	return subscriptionSectionInternal(props, false)
}

// SubscriptionSectionInner returns just the inner content for SSE updates
func SubscriptionSectionInner(props SubscriptionSectionProps) gomponents.Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/app/settings/usersettingsPageContent.locales.json", props.<PERSON>)

	if props.SubscriptionTier == "pro" {
		return html.Div(
			html.Class("flex items-center gap-2"),
			html.Span(
				html.Class("text-sm font-medium text-gray-700 dark:text-gray-300"),
				gomponents.Text(locales["subscription_status"]),
			),
			templatesuibadge.Badge(templatesuibadge.BadgeProps{
				Text:    locales["pro_account_badge"],
				Variant: "success",
				Size:    "md",
			}),
		)
	} else {
		return templatesuibutton.PrimaryButton(templatesuibutton.BaseButtonConfig{
			Text:       locales["upgrade_to_pro_button"],
			ButtonType: "button",
			Attrs: []gomponents.Node{
				gomponents.Attr("hx-get", "/app/settings/upgrade"),
				gomponents.Attr("hx-target", "#modal-body-container"),
				gomponents.Attr("hx-swap", "innerHTML"),
			},
		})
	}
}

func subscriptionSectionInternal(props SubscriptionSectionProps, includeSSE bool) gomponents.Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/app/settings/usersettingsPageContent.locales.json", props.Lang)

	var attributes []gomponents.Node
	attributes = append(attributes, html.Class("mt-6"))
	attributes = append(attributes, html.ID("subscription-section"))

	if includeSSE {
		attributes = append(attributes, gomponents.Attr("sse-connect", "/app/settings/sse"))
		attributes = append(attributes, gomponents.Attr("hx-trigger", "sse:SubscriptionUpdated"))
		attributes = append(attributes, gomponents.Attr("hx-get", "/app/settings/subscription-section"))
		attributes = append(attributes, gomponents.Attr("hx-target", "#subscription-section"))
	}

	// Create div with dynamic attributes
	divArgs := append(attributes,
		gomponents.If(props.SubscriptionTier == "pro",
			html.Div(
				html.Class("flex items-center gap-2"),
				html.Span(
					html.Class("text-sm font-medium text-gray-700 dark:text-gray-300"),
					gomponents.Text(locales["subscription_status"]),
				),
				templatesuibadge.Badge(templatesuibadge.BadgeProps{
					Text:    locales["pro_account_badge"],
					Variant: "success",
					Size:    "md",
				}),
			),
		),
		gomponents.If(props.SubscriptionTier != "pro",
			// Button to open the modal using hx-get
			templatesuibutton.PrimaryButton(templatesuibutton.BaseButtonConfig{
				Text:       locales["upgrade_to_pro_button"],
				ButtonType: "button",
				Attrs: []gomponents.Node{
					gomponents.Attr("hx-get", "/app/settings/upgrade"),
					gomponents.Attr("hx-target", "#modal-body-container"),
					gomponents.Attr("hx-swap", "innerHTML"),
				},
			}),
		),
	)

	return html.Div(divArgs...)
}
