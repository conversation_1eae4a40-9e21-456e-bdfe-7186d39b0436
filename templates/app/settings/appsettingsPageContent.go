package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type AppSettingsPageContentConfig struct {
	Lang                    string
	PlayerCustomColumns     []db.PlayerCustomColumn
	MatchCustomColumns      []db.MatchCustomColumn
	NotificationPreferences *db.NotificationPreference
	UsageLimits             map[limits.ResourceType]*limits.LimitCheckResult
}

func AppSettingsPageContent(config AppSettingsPageContentConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/app").
		WithLocaleBase("app/settings/appsettings").
		WithClass("overflow-auto h-full").
		RenderContent(config.Lang, func(ctx pagebuilder.PageContext) gomponents.Node {
			return html.Div(
				html.Class("p-4 space-y-6 h-full overflow-y-auto"),

				// App Settings Header
				html.H1(
					html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white"),
					gomponents.Text(ctx.Locales["page_title"]),
				),

				// App Settings Description
				html.P(
					html.Class("text-gray-700 dark:text-gray-300"),
					gomponents.Text(ctx.Locales["description"]),
				),

				// Custom Player Columns Section (now a modal trigger)
				html.Div(
					html.Class("mt-8"),
					html.H2(
						html.Class("text-2xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
						gomponents.Text(ctx.Locales["custom_player_columns_title"]),
					),
					html.P(
						html.Class("text-gray-700 dark:text-gray-300 mb-4"),
						gomponents.Text(ctx.Locales["custom_player_columns_description"]),
					),
					html.Div(
						html.Class("flex gap-4"),
						button.PrimaryButton(button.BaseButtonConfig{
							ButtonType: "button",
							Text:       ctx.Locales["custom_player_column_add"] + " / " + ctx.Locales["custom_player_columns_title"],
							Attrs: []gomponents.Node{
								gomponents.Attr("hx-get", "/app/settings/custom-player-columns/dashboard"),
								gomponents.Attr("hx-target", "#playerColumnsModalContainer"),
								gomponents.Attr("hx-swap", "innerHTML"),
							},
						}),
						button.SecondaryButton(button.BaseButtonConfig{
							ButtonType: "button",
							Text:       ctx.Locales["player_column_config"],
							DataTestID: "player-column-config-btn",
							Attrs: []gomponents.Node{
								gomponents.Attr("hx-get", "/app/settings/player-column-config"),
								gomponents.Attr("hx-target", "#playerColumnConfigModalContainer"),
								gomponents.Attr("hx-swap", "innerHTML"),
							},
						}),
					),
					html.Div(
						gomponents.Attr("id", "playerColumnsModalContainer"),
					),
					html.Div(
						gomponents.Attr("id", "playerColumnConfigModalContainer"),
					),
				),

				// Custom Match Columns Section (now a modal trigger)
				html.Div(
					html.Class("mt-8"),
					html.H2(
						html.Class("text-2xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
						gomponents.Text(ctx.Locales["custom_match_columns_title"]),
					),
					html.P(
						html.Class("text-gray-700 dark:text-gray-300 mb-4"),
						gomponents.Text(ctx.Locales["custom_match_columns_description"]),
					),
					button.PrimaryButton(button.BaseButtonConfig{
						ButtonType: "button",
						Text:       ctx.Locales["custom_match_column_add"] + " / " + ctx.Locales["custom_match_columns_title"],
						DataTestID: "open-custom-match-columns-modal-btn",
						Attrs: []gomponents.Node{
							gomponents.Attr("hx-get", "/app/settings/custom-match-columns/dashboard"),
							gomponents.Attr("hx-target", "#matchColumnsModalContainer"),
							gomponents.Attr("hx-swap", "innerHTML"),
						},
					}),
					html.Div(
						gomponents.Attr("id", "matchColumnsModalContainer"),
					),
				),

				// Usage Limits Section
				UsageLimitsSection(config.UsageLimits, config.Lang),

				// Notification Preferences Section
				NotificationPreferences(NotificationPreferencesConfig{
					Lang:        config.Lang,
					Preferences: config.NotificationPreferences,
				}),
			)
		})
}
