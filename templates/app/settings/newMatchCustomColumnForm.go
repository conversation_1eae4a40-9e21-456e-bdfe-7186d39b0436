package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	templatesuiform "github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/modal"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

func NewMatchCustomColumnForm(lang string) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/newMatchCustomColumnForm.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = map[string]string{}
	}

	formContent := html.Form(
		// add datatestid for testing
		gomponents.Attr("data-testid", "new-match-custom-column-form"),
		html.Class("space-y-4"),
		gomponents.Attr("x-data", "{errors: {}, errorMessages: {}, fields: {}}"),
		gomponents.Attr("x-validate-form", "true"),
		htmx.Post("/app/settings/custom-match-columns"),
		htmx.Target(".customMatchColumnsTable"),
		htmx.Boost("true"),
		htmx.Swap("outerHTML"),
		gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { newMatchCustomColumn_modalOpen = false }"),

		// Name field
		form.FormInput(templatesuiform.FormInputProps{
			Label:       locales["custom_match_column_name"],
			Type:        "text",
			ID:          "name",
			Name:        "name",
			Required:    true,
			Placeholder: "e.g. Match Note",
		}),

		// Field type selector
		uiselect.Select(uiselect.SelectProps{
			Label:        locales["custom_match_column_type"],
			ID:           "fieldType",
			Name:         "fieldType",
			Required:     true,
			DefaultValue: "text", // Set default value to ensure validation passes
			Items: []uiselect.SelectItem{
				{Value: "text", Title: locales["field_type_text"]},
				{Value: "number", Title: locales["field_type_number"]},
				{Value: "date", Title: locales["field_type_date"]},
				{Value: "boolean", Title: locales["field_type_boolean"]},
				{Value: "email", Title: locales["field_type_email"]},
				{Value: "url", Title: locales["field_type_url"]},
				{Value: "select", Title: locales["field_type_select"]},
			},
		}),

		// Description field
		form.FormInput(templatesuiform.FormInputProps{
			Label:       locales["custom_match_column_description"],
			Type:        "text",
			ID:          "description",
			Name:        "description",
			Required:    false,
			Placeholder: "e.g. Additional notes about the match",
		}),

		// Required checkbox
		form.FormCheckbox(form.FormCheckboxProps{
			Label:    locales["custom_match_column_required"],
			ID:       "isRequired",
			Name:     "isRequired",
			Required: false,
		}),

		// Active checkbox
		form.FormCheckbox(form.FormCheckboxProps{
			Label:    locales["custom_match_column_active"],
			ID:       "isActive",
			Name:     "isActive",
			Required: false,
			Checked:  true,
		}),

		// Submit and Cancel buttons
		html.Div(
			html.Class("flex space-x-4"),
			button.PrimaryIconButton(button.IconButtonConfig{
				ID:         "save-btn",
				DataTestID: "save-custom-column-btn",
				ButtonType: "submit",
				Text:       locales["save"],
				Icon:       icons.Check(),
			}),
			button.SecondaryIconButton(button.IconButtonConfig{
				ID:         "cancel-btn",
				DataTestID: "cancel-btn",
				ButtonType: "button",
				Text:       locales["cancel"],
				Icon:       icons.XMark(),
				Attrs: []gomponents.Node{
					gomponents.Attr("@click", "newMatchCustomColumn_modalOpen = false"),
				},
			}),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "newMatchCustomColumn",
		Title:        locales["custom_match_column_add"],
		Content:      formContent,
		IsUnclosable: false,
		DataTestID:   "new-match-custom-column-modal",
	})
}
