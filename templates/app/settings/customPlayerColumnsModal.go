package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	modalwrappers "github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
)

// CustomPlayerColumnsModal renders the custom player columns dashboard in a modal dialog.
func CustomPlayerColumnsModal(columns []db.PlayerCustomColumn, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/appsettings.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return modalwrappers.ClientModalWithAutoOpen(modalwrappers.ClientModalConfig{
		ModalID: "playerColumnsModal",
		Title:   locales["custom_player_columns_title"],
		Content: CustomPlayerColumnsTable(columns, lang),
	})
}
