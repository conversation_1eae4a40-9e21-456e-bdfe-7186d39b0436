package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	templatesuiform "github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/modal"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

func NewCustomPlayerColumnForm(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/appsettings.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	formContent := html.Form(
		html.Class("space-y-4"),
		gomponents.Attr("x-data", "{errors: {}, errorMessages: {}, fields: {}}"),
		gomponents.Attr("x-validate-form", "true"),
		htmx.Post("/app/settings/custom-player-columns"),
		htmx.Target("#newCustomColumnFormContainer"),
		htmx.Swap("outerHTML"),
		gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { newCustomColumn_modalOpen = false }"),

		// Name field
		form.FormInput(templatesuiform.FormInputProps{
			Label:       locales["custom_player_column_name"],
			Type:        "text",
			ID:          "name",
			Name:        "name",
			Required:    true,
			Placeholder: locales["name_placeholder"],
		}),

		// Field type selector
		uiselect.Select(uiselect.SelectProps{
			Label:    locales["custom_player_column_type"],
			ID:       "fieldType",
			Name:     "fieldType",
			Required: true,
			Items: []uiselect.SelectItem{
				{Value: "text", Title: locales["field_type_text"]},
				{Value: "number", Title: locales["field_type_number"]},
				{Value: "date", Title: locales["field_type_date"]},
				{Value: "boolean", Title: locales["field_type_boolean"]},
				{Value: "email", Title: locales["field_type_email"]},
				{Value: "url", Title: locales["field_type_url"]},
				{Value: "select", Title: locales["field_type_select"]},
			},
		}),

		// Description field
		form.FormInput(templatesuiform.FormInputProps{
			Label:       locales["custom_player_column_description"],
			Type:        "text",
			ID:          "description",
			Name:        "description",
			Required:    false,
			Placeholder: locales["description_placeholder"],
		}),

		// Required checkbox
		form.FormCheckbox(form.FormCheckboxProps{
			Label:    locales["custom_player_column_required"],
			ID:       "isRequired",
			Name:     "isRequired",
			Required: false,
		}),

		// Display order field
		form.FormInput(templatesuiform.FormInputProps{
			Label:       locales["custom_player_column_order"],
			Type:        "number",
			ID:          "displayOrder",
			Name:        "displayOrder",
			Required:    true,
			Placeholder: locales["order_placeholder"],
		}),

		// Submit and Cancel buttons
		html.Div(
			html.Class("flex space-x-4"),
			button.PrimaryIconButton(button.IconButtonConfig{
				ID:         "create-column-btn",
				DataTestID: "save-custom-column-btn",
				ButtonType: "submit",
				Text:       locales["custom_player_column_save"],
				Icon:       icons.Check(),
			}),
			button.SecondaryIconButton(button.IconButtonConfig{
				ID:         "cancel-column-btn",
				DataTestID: "cancel-column-btn",
				ButtonType: "button",
				Text:       locales["custom_player_column_cancel"],
				Icon:       icons.XMark(),
				Attrs: []gomponents.Node{
					gomponents.Attr("@click", "newCustomColumn_modalOpen = false"),
				},
			}),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "newCustomColumn",
		Title:        locales["custom_player_column_add"],
		Content:      formContent,
		IsUnclosable: false,
		DataTestID:   "new-custom-column-modal",
	})
}
