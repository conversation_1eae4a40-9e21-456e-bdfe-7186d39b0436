package templatesappsettings

import (
	"fmt"

	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type APIKeyFormConfig struct {
	Lang   string
	KeyID  int32 // 0 for new key, >0 for editing existing key
	Name   string
	APIKey string // Only populated when creating a new key
}

func NewAPIKeyForm(config APIKeyFormConfig) gomponents.Node {
	return html.Div(
		html.Class("bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-4"),
		html.H3(
			html.Class("text-lg font-medium text-gray-900 dark:text-white mb-4"),
			gomponents.Text("Generate New API Key"),
		),

		html.Form(
			gomponents.Attr("hx-post", "/app/settings/api-keys"),
			gomponents.Attr("hx-target", "#apiKeyFormContainer"),
			gomponents.Attr("hx-swap", "innerHTML"),

			html.Div(
				html.Class("mb-4"),
				html.Label(
					html.For("name"),
					html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"),
					gomponents.Text("API Key Name"),
				),
				html.Input(
					html.Type("text"),
					html.Name("name"),
					html.ID("name"),
					html.Required(),
					html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
					html.Placeholder("e.g., Production Bot, Mobile App, etc."),
					html.MaxLength("255"),
				),
				html.P(
					html.Class("mt-1 text-sm text-gray-500 dark:text-gray-400"),
					gomponents.Text("Choose a descriptive name to help you identify this API key."),
				),
			),

			html.Div(
				html.Class("mb-4"),
				html.Label(
					html.For("expires_at"),
					html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"),
					gomponents.Text("Expiration Date (Optional)"),
				),
				html.Input(
					html.Type("date"),
					html.Name("expires_at"),
					html.ID("expires_at"),
					html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
				),
				html.P(
					html.Class("mt-1 text-sm text-gray-500 dark:text-gray-400"),
					gomponents.Text("Leave empty for a key that never expires."),
				),
			),

			html.Div(
				html.Class("flex space-x-3"),
				button.PrimaryButton(button.BaseButtonConfig{
					ButtonType: "submit",
					Text:       "Generate API Key",
				}),
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       "Cancel",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-get", "/app/settings/api-keys/cancel"),
						gomponents.Attr("hx-target", "#apiKeyFormContainer"),
						gomponents.Attr("hx-swap", "innerHTML"),
					},
				}),
			),
		),
	)
}

func EditAPIKeyForm(config APIKeyFormConfig) gomponents.Node {
	return html.Div(
		html.Class("bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-4"),
		html.H3(
			html.Class("text-lg font-medium text-gray-900 dark:text-white mb-4"),
			gomponents.Text("Edit API Key"),
		),

		html.Form(
			gomponents.Attr("hx-put", fmt.Sprintf("/app/settings/api-keys/%d", config.KeyID)),
			gomponents.Attr("hx-target", "#apiKeyFormContainer"),
			gomponents.Attr("hx-swap", "innerHTML"),

			html.Div(
				html.Class("mb-4"),
				html.Label(
					html.For("name"),
					html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"),
					gomponents.Text("API Key Name"),
				),
				html.Input(
					html.Type("text"),
					html.Name("name"),
					html.ID("name"),
					html.Value(config.Name),
					html.Required(),
					html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
					html.MaxLength("255"),
				),
			),

			html.Div(
				html.Class("flex space-x-3"),
				button.PrimaryButton(button.BaseButtonConfig{
					ButtonType: "submit",
					Text:       "Update API Key",
				}),
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       "Cancel",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-get", "/app/settings/api-keys/cancel"),
						gomponents.Attr("hx-target", "#apiKeyFormContainer"),
						gomponents.Attr("hx-swap", "innerHTML"),
					},
				}),
			),
		),
	)
}

func APIKeyCreatedSuccess(config APIKeyFormConfig) gomponents.Node {
	return html.Div(
		html.Class("bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 p-6 rounded-lg mb-4"),
		html.H3(
			html.Class("text-lg font-medium text-green-800 dark:text-green-200 mb-4"),
			gomponents.Text("API Key Created Successfully!"),
		),

		html.Div(
			html.Class("mb-4"),
			html.P(
				html.Class("text-green-700 dark:text-green-300 mb-2"),
				gomponents.Text("Your new API key has been generated. Copy it now - you won't be able to see it again!"),
			),
			html.Div(
				html.Class("bg-gray-100 dark:bg-gray-800 p-3 rounded border font-mono text-sm break-all"),
				html.Div(
					html.Class("flex justify-between items-center"),
					html.Span(
						html.Class("text-gray-900 dark:text-white"),
						gomponents.Text(config.APIKey),
					),
					button.SecondaryButton(button.BaseButtonConfig{
						ButtonType: "button",
						Text:       "Copy",
						Class:      "ml-2 text-xs",
						Attrs: []gomponents.Node{
							gomponents.Attr("onclick", fmt.Sprintf("navigator.clipboard.writeText('%s'); this.textContent='Copied!'; setTimeout(() => this.textContent='Copy', 2000)", config.APIKey)),
						},
					}),
				),
			),
		),

		html.Div(
			html.Class("mb-4"),
			html.H4(
				html.Class("font-medium text-green-800 dark:text-green-200 mb-2"),
				gomponents.Text("Usage Instructions:"),
			),
			html.Ul(
				html.Class("text-sm text-green-700 dark:text-green-300 space-y-1 list-disc list-inside"),
				html.Li(gomponents.Text("Include the API key in the Authorization header: Authorization: Bearer YOUR_API_KEY")),
				html.Li(gomponents.Text("Or use the X-API-Key header: X-API-Key: YOUR_API_KEY")),
				html.Li(gomponents.Text("API endpoint: /api/v1/")),
				html.Li(gomponents.Text("Rate limit: 1000 requests per hour, 100 per minute")),
			),
		),

		button.PrimaryButton(button.BaseButtonConfig{
			ButtonType: "button",
			Text:       "Done",
			Attrs: []gomponents.Node{
				gomponents.Attr("hx-get", "/app/settings/api-keys/cancel"),
				gomponents.Attr("hx-target", "#apiKeyFormContainer"),
				gomponents.Attr("hx-swap", "innerHTML"),
				gomponents.Attr("hx-trigger", "click"),
			},
		}),
	)
}
