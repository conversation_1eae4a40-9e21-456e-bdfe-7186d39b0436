package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	templatesuibutton "github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/card"
	datepicker "github.com/j-em/coachpad/templates/ui/datepicker"
	"github.com/j-em/coachpad/templates/ui/form"
	templatesuilangselect "github.com/j-em/coachpad/templates/ui/langselect"
	"github.com/j-em/coachpad/templates/ui/modal"
	"github.com/j-em/coachpad/templates/ui/uiselect"

	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type UserSettingsPageConfig struct {
	Lang                 string
	Name                 string
	Email                string
	Country              string
	Phone                string
	Birthday             string
	IsSidebarOpen        bool
	ActiveLink           string
	SubscriptionTier     string
	StripeClientSecret   string
	StripePublishableKey string
	VerificationSent     bool
	EmailVerified        bool
	VerificationError    bool
	UserIsVerified       bool
	ActiveSubscription   db.GetUserSubscriptionRow
}

type UserSettingsPageContentConfig struct {
	Email                string
	Name                 string
	Country              string
	Phone                string
	Birthday             string
	IsSidebarOpen        bool
	Lang                 string
	VerificationSent     bool
	EmailVerified        bool
	VerificationError    bool
	UserIsVerified       bool
	SubscriptionTier     string
	ActiveSubscription   db.GetUserSubscriptionRow
	StripeClientSecret   string
	StripePublishableKey string
}

func UserSettingsPage(config UserSettingsPageConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/user").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          config.Lang,
			IsSidebarOpen: config.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return UserSettingsPageContent(UserSettingsPageContentConfig{
				Lang:                 config.Lang,
				Name:                 config.Name,
				Email:                config.Email,
				Country:              config.Country,
				Phone:                config.Phone,
				Birthday:             config.Birthday,
				VerificationSent:     config.VerificationSent,
				EmailVerified:        config.EmailVerified,
				VerificationError:    config.VerificationError,
				UserIsVerified:       config.UserIsVerified,
				SubscriptionTier:     config.SubscriptionTier,
				ActiveSubscription:   config.ActiveSubscription,
				StripeClientSecret:   config.StripeClientSecret,
				StripePublishableKey: config.StripePublishableKey,
			})
		})
}

// You'll need to implement SubscriptionSection or import it
type SubscriptionSectionProps struct {
	SubscriptionTier string
	Lang             string
}

func SubscriptionSection(props SubscriptionSectionProps) gomponents.Node {
	// Placeholder - you'll need to move the actual implementation here
	return html.Div()
}

func UserSettingsPageContent(props UserSettingsPageContentConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/user").
		WithTrigger("settingsUpdated from:body").
		WithClass("flex flex-col h-full").
		RenderContent(props.Lang, func(ctx pagebuilder.PageContext) gomponents.Node {
			return html.Div(
				html.Class("p-4 space-y-6 h-full overflow-y-auto"),
				gomponents.Attr("x-validate-form", "true"),
				gomponents.Attr("x-data", "{errors: {}, errorMessages: {}, fields: {}}"),

				html.H1(
					html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-6"),
					gomponents.Text(ctx.Locales["user_settings_header"]),
				),

				html.Form(
					htmx.Boost("true"),
					htmx.Patch("/app/settings/user"),
					htmx.Target("#toast-body-container"),
					htmx.Swap("afterbegin"),
					html.Div(
						html.Class("grid grid-cols-1 lg:grid-cols-2 gap-6"),

						// Personal Information Card
						card.Card(card.CardProps{
							Title: ctx.Locales["personal_information"],
							Content: gomponents.Group([]gomponents.Node{
								form.FormInput(form.FormInputProps{
									Name:        "name",
									ID:          "name",
									Type:        "text",
									Label:       ctx.Locales["full_name"],
									Placeholder: ctx.Locales["full_name"],
									Value:       props.Name,
								}),
								form.FormInput(form.FormInputProps{
									Name:        "email",
									ID:          "email",
									Type:        "email",
									Label:       ctx.Locales["email_address"],
									Placeholder: ctx.Locales["email_address"],
									Value:       props.Email,
								}),
								// Email verification section
								html.Div(
									html.Class("p-4 bg-gray-100 dark:bg-gray-600 rounded-lg"),
									gomponents.If(props.UserIsVerified,
										html.Div(
											html.Class("text-green-600 dark:text-green-400 font-semibold"),
											html.Label(
												html.For("email-verification-status"),
												gomponents.Text(ctx.Locales["email_verified_successfully"]),
											),
										),
									),
									gomponents.If(!props.UserIsVerified,
										html.Div(
											html.Div(gomponents.Attr("id", "emailVerificationMessage")),
											gomponents.If(props.VerificationError,
												html.Div(
													html.Class("text-red-600 dark:text-red-400 font-semibold"),
													html.Label(
														html.For("email-verification-status"),
														gomponents.Text(ctx.Locales["verification_email_error"]),
													),
												),
											),
											gomponents.If(props.VerificationSent && !props.VerificationError,
												html.Div(
													html.Class("text-blue-600 dark:text-blue-400 font-semibold"),
													html.Label(
														html.For("email-verification-status"),
														gomponents.Text(ctx.Locales["verification_email_sent"]),
													),
												),
											),
											gomponents.If(!props.VerificationSent && !props.VerificationError,
												html.P(gomponents.Text(ctx.Locales["email_not_verified_message"])),
											),
											templatesuibutton.PrimaryButton(templatesuibutton.BaseButtonConfig{
												Text:       ctx.Locales["resend_verification_email"],
												ButtonType: "button",
												Attrs: []gomponents.Node{
													gomponents.Attr("hx-get", "/app/send-verification-email"),
													gomponents.Attr("hx-target", "#emailVerificationMessage"),
													gomponents.Attr("hx-swap", "innerHTML"),
												},
											}),
										),
									),
								),
							}),
						}),

						// Contact & Preferences Card
						card.Card(card.CardProps{
							Title: ctx.Locales["contact_preferences"],
							Content: gomponents.Group([]gomponents.Node{
								uiselect.CountrySelect(uiselect.CountrySelectProps{
									Lang: props.Lang,
									SelectProps: uiselect.SelectProps{
										Name:         "country",
										ID:           "country",
										DefaultValue: props.Country,
									},
								}),
								form.FormInput(form.FormInputProps{
									Name:        "phone",
									ID:          "phone",
									Type:        "tel",
									Label:       ctx.Locales["phone"],
									Placeholder: ctx.Locales["phone"],
									Value:       props.Phone,
								}),
								datepicker.TeleportDatePickerComponent(datepicker.TeleportDatePickerProps{
									Name:        "birthday",
									UniqueId:    "user-settings-birthday",
									Label:       ctx.Locales["birth_date"],
									Placeholder: ctx.Locales["birth_date"],
									Value:       props.Birthday,
									Lang:        ctx.Lang,
									Class:       "w-full",
								}),
								templatesuilangselect.LanguageSelect(templatesuilangselect.LanguageSelectConfig{
									Name:         "lang",
									DefaultValue: props.Lang,
									ID:           "lang",
								}),
							}),
						}),
					),

					html.Div(
						html.Class("flex justify-end gap-4 mt-6"),
						templatesuibutton.PrimaryButton(templatesuibutton.BaseButtonConfig{
							Text:       ctx.Locales["save_changes"],
							ButtonType: "submit",
							Attrs: []gomponents.Node{
								gomponents.Attr("data-testid", "save-changes-button"),
							},
						}),
					),
				),
				// Subscription section with SSE live updates
				SubscriptionSection(SubscriptionSectionProps{
					SubscriptionTier: props.SubscriptionTier,
					Lang:             props.Lang,
				}),

				// Danger Zone section with shared Alpine.js data
				html.Div(
					html.Class("mt-8"),
					gomponents.Attr("x-data", "{ deleteConfirmation_modalOpen: false }"),
					card.Card(card.CardProps{
						Title: ctx.Locales["danger_zone"],
						Content: gomponents.Group([]gomponents.Node{
							html.P(
								html.Class("text-sm text-red-600 dark:text-red-400 mb-4"),
								gomponents.Text(ctx.Locales["delete_account_warning"]),
							),
							templatesuibutton.BaseButton(templatesuibutton.BaseButtonConfig{
								Text:       ctx.Locales["delete_account"],
								ButtonType: "button",
								Class:      "cursor-pointer flex items-center gap-2 justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 border-transparent text-white bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600",
								DataTestID: "delete-account-btn",
								Attrs: []gomponents.Node{
									gomponents.Attr("@click", "deleteConfirmation_modalOpen = true"),
								},
							}),
						}),
					}),

					// Delete Confirmation Modal - now expects parent to manage Alpine.js state
					modal.ConfirmationModal(
						"deleteConfirmation",
						ctx.Locales["confirm_deletion"],
						ctx.Locales["delete_confirmation_message"],
						ctx.Locales["confirm_delete"],
						ctx.Locales["cancel"],
						templatesuibutton.BaseButton(templatesuibutton.BaseButtonConfig{
							Text:       ctx.Locales["confirm_delete"],
							ButtonType: "button",
							Class:      "cursor-pointer flex items-center gap-2 justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 border-transparent text-white bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600",
							DataTestID: "confirm-delete-btn",
							Attrs: []gomponents.Node{
								htmx.Delete("/app/user/delete"),
								gomponents.Attr("hx-target", "body"),
								gomponents.Attr("hx-swap", "outerHTML"),
							},
						}),
					),
				),
			)
		})
}
