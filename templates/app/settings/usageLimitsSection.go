package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/limitbanner"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// UsageLimitsSection renders the usage limits section for the settings page
func UsageLimitsSection(usage map[limits.ResourceType]*limits.LimitCheckResult, lang string) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/usageLimitsSection.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	if len(usage) == 0 {
		return gomponents.Text("")
	}

	// Check if user has any unlimited resources (Pro tier)
	isPro := false
	for _, result := range usage {
		if result.IsUnlimited {
			isPro = true
			break
		}
	}

	return html.Div(
		html.Class("mt-8"),
		html.H2(
			html.Class("text-2xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
			gomponents.Text(locales["usage_limits_title"]),
		),
		html.P(
			html.Class("text-gray-700 dark:text-gray-300 mb-4"),
			gomponents.Text(locales["usage_limits_description"]),
		),

		// Usage summary card
		limitbanner.QuickUsageCard(usage, lang),

		// Show individual banners for resources approaching limits
		html.Div(
			html.Class("mt-6 space-y-4"),
			gomponents.Group(
				gomponents.Map([]limits.ResourceType{
					limits.ResourcePlayers,
					limits.ResourceSeasons,
					limits.ResourceMatches,
					limits.ResourceTeams,
				}, func(resourceType limits.ResourceType) gomponents.Node {
					result, exists := usage[resourceType]
					if !exists {
						return gomponents.Text("")
					}

					return limitbanner.LimitBanner(limitbanner.LimitBannerConfig{
						LimitResult: result,
						Lang:        lang,
						ShowUpgrade: !isPro,
						UpgradeURL:  "/app/settings/subscription",
						DataTestID:  "limit-banner-" + string(resourceType),
						ID:          "limit-banner-" + string(resourceType),
						Class:       "",
					})
				}),
			),
		),

		// Upgrade CTA for free users
		gomponents.If(!isPro,
			html.Div(
				html.Class("mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"),
				html.Div(
					html.Class("flex items-center justify-between"),
					html.Div(
						html.H3(
							html.Class("text-lg font-medium text-blue-900 dark:text-blue-100"),
							gomponents.Text(locales["upgrade_cta_title"]),
						),
						html.P(
							html.Class("text-blue-700 dark:text-blue-300 mt-1"),
							gomponents.Text(locales["upgrade_cta_description"]),
						),
					),
					html.Div(
						button.PrimaryButton(button.BaseButtonConfig{
							ButtonType: "button",
							Text:       locales["upgrade_to_pro"],
							Class:      "bg-blue-600 hover:bg-blue-700",
							Attrs: []gomponents.Node{
								gomponents.Attr("onclick", "window.location.href='/app/settings/subscription'"),
							},
						}),
					),
				),
			),
		),
	)
}
