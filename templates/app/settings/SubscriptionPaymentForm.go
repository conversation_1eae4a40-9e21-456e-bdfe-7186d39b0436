package templatesappsettings

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// SubscriptionPaymentFormProps holds configuration for SubscriptionPaymentForm
type SubscriptionPaymentFormProps struct {
	ClientSecret   string
	PublishableKey string
	Lang           string // Language for localization
}

// SubscriptionPaymentForm renders a payment form for upgrading to Pro using Stripe Elements wrapped in a modal
func SubscriptionPaymentForm(props SubscriptionPaymentFormProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/SubscriptionPaymentForm.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	formContent := html.Div(
		html.Form(
			gomponents.Attr("x-data", fmt.Sprintf("stripePayment('%s','%s')", props.ClientSecret, props.PublishableKey)),
			gomponents.Attr("@submit.prevent", "$data.submitPayment()"),
			html.Div(
				html.ID("card-element"),
				html.Class("mb-4 border p-2 rounded"),
			),
			button.PrimaryButton(button.BaseButtonConfig{
				ButtonType: "submit",
				Text:       locales["upgrade_to_pro"],
			}),
			html.Div(
				html.Class("text-red-600 mt-2"),
				gomponents.Attr("x-text", "error"),
			),
			html.Div(
				html.Class("text-green-600 mt-2"),
				gomponents.Attr("x-show", "success"),
				gomponents.Text(locales["payment_successful"]),
			),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "subscriptionPaymentModal",
		Title:        locales["upgrade_to_pro"],
		Content:      formContent,
		IsUnclosable: false,
		DataTestID:   "subscription-payment-modal",
	})
}
