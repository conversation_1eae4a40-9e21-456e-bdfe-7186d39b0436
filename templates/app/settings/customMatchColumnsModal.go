package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	modalwrappers "github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
)

// CustomMatchColumnsModal renders the custom match columns dashboard in a modal dialog.
func CustomMatchColumnsModal(columns []db.MatchCustomColumn, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/customMatchColumnsModal.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return modalwrappers.ClientModalWithAutoOpen(modalwrappers.ClientModalConfig{
		ModalID: "matchColumnsModal",
		Title:   locales["custom_match_columns_title"],
		Content: CustomMatchColumnsTable(columns, lang),
	})
}
