package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	modalwrappers "github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PlayerColumnConfigModal renders a modal for player column configuration
func PlayerColumnConfigModal(customColumns []db.PlayerCustomColumn, columnVisibilities []db.GetPlayerColumnVisibilityRow, lang string) gomponents.Node {
	// Define standard player columns that are always available
	standardColumns := []struct {
		name        string
		description string
		isDefault   bool
	}{
		{"Name", "Player's full name", true},
		{"Email", "Player's email address", true},
		{"Preferred Group", "Player's preferred match group", true},
		{"Active", "Whether the player is active", true},
		{"Email Notifications", "Whether email notifications are enabled", true},
		{"Picture", "Player's profile picture", false},
		{"Created", "When the player was created", false},
		{"Updated", "When the player was last updated", false},
	}

	// Create a map of column visibility preferences
	visibilityMap := make(map[string]bool)
	for _, visibility := range columnVisibilities {
		visibilityMap[visibility.ColumnName] = visibility.IsVisible
	}

	return modalwrappers.ClientModalWithAutoOpen(modalwrappers.ClientModalConfig{
		ModalID: "playerColumnConfigModal",
		Title:   "Player Column Configuration",
		Content: html.Form(
			gomponents.Attr("data-testid", "player-column-config-dialog"),
			gomponents.Attr("hx-post", "/app/settings/player-column-config"),
			gomponents.Attr("hx-target", "#playerColumnConfigModalContainer"),
			gomponents.Attr("hx-swap", "innerHTML"),
			html.P(
				html.Class("text-gray-600 dark:text-gray-400 mb-4"),
				gomponents.Text("Configure which columns are visible in the players table."),
			),

			// Standard columns section
			html.Div(
				html.Class("mb-6"),
				html.H3(
					html.Class("text-lg font-semibold text-gray-900 dark:text-white mb-3"),
					gomponents.Text("Standard Columns"),
				),
				html.Div(
					html.Class("space-y-2"),
					gomponents.Group(func() []gomponents.Node {
						var nodes []gomponents.Node
						for _, column := range standardColumns {
							// Check if column is visible (use default if not found in preferences)
							isVisible := column.isDefault // default value
							if vis, exists := visibilityMap[column.name]; exists {
								isVisible = vis
							}

							nodes = append(nodes, html.Div(
								html.Class("flex items-center space-x-3"),
								html.Input(
									gomponents.Attr("type", "checkbox"),
									gomponents.Attr("id", "standard-"+column.name),
									gomponents.Attr("name", "standardColumns"),
									gomponents.Attr("value", column.name),
									gomponents.Attr("data-testid", "standard-column-"+column.name),
									html.Class("rounded border-gray-300 text-blue-600 focus:ring-blue-500"),
									gomponents.If(isVisible, gomponents.Attr("checked", "checked")),
								),
								html.Label(
									gomponents.Attr("for", "standard-"+column.name),
									html.Class("text-sm font-medium text-gray-700 dark:text-gray-300"),
									gomponents.Text(column.name),
								),
								html.Span(
									html.Class("text-xs text-gray-500 dark:text-gray-400"),
									gomponents.Text(" - "+column.description),
								),
							))
						}
						return nodes
					}()),
				),
			),

			// Custom columns section
			gomponents.If(len(customColumns) > 0, html.Div(
				html.Class("mb-6"),
				html.H3(
					html.Class("text-lg font-semibold text-gray-900 dark:text-white mb-3"),
					gomponents.Text("Custom Columns"),
				),
				html.Div(
					html.Class("space-y-2"),
					gomponents.Group(func() []gomponents.Node {
						var nodes []gomponents.Node
						for _, column := range customColumns {
							description := ""
							if column.Description.Valid {
								description = column.Description.String
							}

							// Check if custom column is visible
							isVisible := column.IsActive.Valid && column.IsActive.Bool // default to isActive
							if vis, exists := visibilityMap[column.Name]; exists {
								isVisible = vis
							}

							nodes = append(nodes, html.Div(
								html.Class("flex items-center space-x-3"),
								html.Input(
									gomponents.Attr("type", "checkbox"),
									gomponents.Attr("id", "custom-"+column.Name),
									gomponents.Attr("name", "customColumns"),
									gomponents.Attr("value", column.Name),
									gomponents.Attr("data-testid", "custom-column-"+column.Name),
									html.Class("rounded border-gray-300 text-blue-600 focus:ring-blue-500"),
									gomponents.If(isVisible, gomponents.Attr("checked", "checked")),
								),
								html.Label(
									gomponents.Attr("for", "custom-"+column.Name),
									html.Class("text-sm font-medium text-gray-700 dark:text-gray-300"),
									gomponents.Text(column.Name),
								),
								gomponents.If(description != "", html.Span(
									html.Class("text-xs text-gray-500 dark:text-gray-400"),
									gomponents.Text(" - "+description),
								)),
							))
						}
						return nodes
					}()),
				),
			)),

			// Empty state for custom columns
			gomponents.If(len(customColumns) == 0, html.Div(
				html.Class("mb-6"),
				html.H3(
					html.Class("text-lg font-semibold text-gray-900 dark:text-white mb-3"),
					gomponents.Text("Custom Columns"),
				),
				html.P(
					html.Class("text-sm text-gray-500 dark:text-gray-400"),
					gomponents.Text("No custom columns have been created yet."),
				),
			)),

			// Save button
			html.Div(
				html.Class("flex justify-end pt-4 mt-6 border-t border-gray-200 dark:border-gray-600"),
				html.Button(
					html.Type("submit"),
					html.Class("px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"),
					gomponents.Attr("data-testid", "save-column-config-btn"),
					gomponents.Text("Save Configuration"),
				),
			),
		),
	})
}
