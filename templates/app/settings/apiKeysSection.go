package templatesappsettings

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type APIKeysSectionConfig struct {
	Lang    string
	APIKeys []db.GetAPIKeysByUserIDRow
}

func APIKeysSection(config APIKeysSectionConfig) gomponents.Node {
	return html.Div(
		html.Class("mt-8"),
		html.H2(
			html.Class("text-2xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
			gomponents.Text("API Keys"),
		),
		html.P(
			html.Class("text-gray-700 dark:text-gray-300 mb-4"),
			gomponents.Text("Generate API keys to access the CoachPad API programmatically. Keep your API keys secure and never share them publicly."),
		),

		// Create API Key Button
		button.PrimaryButton(button.BaseButtonConfig{
			ButtonType: "button",
			Text:       "Generate New API Key",
			Class:      "mb-4",
			Attrs: []gomponents.Node{
				gomponents.Attr("hx-get", "/app/settings/api-keys/new"),
				gomponents.Attr("hx-target", "#apiKeyFormContainer"),
				gomponents.Attr("hx-swap", "innerHTML"),
			},
		}),

		// Form Container
		html.Div(
			gomponents.Attr("id", "apiKeyFormContainer"),
		),

		// API Keys Table - render directly instead of HTMX refresh
		html.Div(
			gomponents.Attr("id", "apiKeysTable"),
			gomponents.Attr("hx-trigger", "refreshApiKeysTable from:body"),
			gomponents.Attr("hx-get", "/app/settings/api-keys"),
			gomponents.Attr("hx-swap", "outerHTML"),
			APIKeysTable(config),
		),
	)
}

func APIKeysTable(config APIKeysSectionConfig) gomponents.Node {
	if len(config.APIKeys) == 0 {
		return html.Div(
			html.Class("text-center py-8 text-gray-500"),
			html.P(gomponents.Text("No API keys created yet.")),
			html.P(
				html.Class("text-sm mt-2"),
				gomponents.Text("Generate your first API key to start using the CoachPad API."),
			),
		)
	}

	return html.Div(
		html.Class("bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"),
		html.Table(
			html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-700"),
			html.THead(
				html.Class("bg-gray-50 dark:bg-gray-700"),
				html.Tr(
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text("Name"),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text("Key"),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text("Last Used"),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text("Expires"),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text("Actions"),
					),
				),
			),
			html.TBody(
				html.Class("bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"),
				gomponents.Group(renderAPIKeyRows(config.APIKeys)),
			),
		),
	)
}

func renderAPIKeyRows(apiKeys []db.GetAPIKeysByUserIDRow) []gomponents.Node {
	rows := make([]gomponents.Node, len(apiKeys))
	for i, key := range apiKeys {
		rows[i] = html.Tr(
			html.Td(
				html.Class("px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"),
				gomponents.Text(key.Name),
			),
			html.Td(
				html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 font-mono"),
				gomponents.Text(key.Prefix+"..."),
			),
			html.Td(
				html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"),
				formatLastUsed(key.LastUsedAt),
			),
			html.Td(
				html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"),
				formatExpiration(key.ExpiresAt),
			),
			html.Td(
				html.Class("px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2"),
				// Edit button
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       "Edit",
					Class:      "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-get", fmt.Sprintf("/app/settings/api-keys/%d/edit", key.ID)),
						gomponents.Attr("hx-target", "#apiKeyFormContainer"),
						gomponents.Attr("hx-swap", "innerHTML"),
					},
				}),
				// Delete button
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       "Delete",
					Class:      "text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 ml-2",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-delete", fmt.Sprintf("/app/settings/api-keys/%d", key.ID)),
						gomponents.Attr("hx-target", "#apiKeysTable"),
						gomponents.Attr("hx-swap", "outerHTML"),
						gomponents.Attr("hx-confirm", "Are you sure you want to delete this API key? This action cannot be undone."),
					},
				}),
			),
		)
	}
	return rows
}

func formatLastUsed(lastUsed any) gomponents.Node {
	if lastUsed == nil {
		return gomponents.Text("Never")
	}
	return gomponents.Text("Recently") // You can format the actual timestamp here
}

func formatExpiration(expiresAt any) gomponents.Node {
	if expiresAt == nil {
		return gomponents.Text("Never")
	}
	return gomponents.Text("Set") // You can format the actual timestamp here
}
