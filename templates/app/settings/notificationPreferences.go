package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type NotificationPreferencesConfig struct {
	Lang        string
	Preferences *db.NotificationPreference
}

func NotificationPreferences(config NotificationPreferencesConfig) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/notificationPreferences.locales.json", config.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Default values if preferences is nil
	inAppEnabled := true
	matchUpdates := true
	scheduleChanges := true
	results := true
	announcements := true

	if config.Preferences != nil {
		inAppEnabled = config.Preferences.InAppEnabled.Valid && config.Preferences.InAppEnabled.Bool
		matchUpdates = config.Preferences.MatchUpdates.Valid && config.Preferences.MatchUpdates.Bool
		scheduleChanges = config.Preferences.ScheduleChanges.Valid && config.Preferences.ScheduleChanges.Bool
		results = config.Preferences.Results.Valid && config.Preferences.Results.Bool
		announcements = config.Preferences.Announcements.Valid && config.Preferences.Announcements.Bool
	}

	return html.Div(
		html.Class("mt-8"),
		html.H2(
			html.Class("text-2xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
			gomponents.Text(locales["title"]),
		),
		html.P(
			html.Class("text-gray-700 dark:text-gray-300 mb-6"),
			gomponents.Text(locales["description"]),
		),

		// Notification preferences form
		html.Form(
			html.ID("notification-preferences-form"),
			gomponents.Attr("hx-put", "/app/notifications/preferences"),
			gomponents.Attr("hx-target", "#toast-body-container"),
			gomponents.Attr("hx-swap", "afterbegin"),
			html.Class("space-y-4"),

			// In-app notifications toggle
			html.Div(
				html.Class("flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"),
				html.Div(
					html.Class("flex-1"),
					html.H3(
						html.Class("text-lg font-medium text-gray-900 dark:text-white"),
						gomponents.Text(locales["in_app_enabled_title"]),
					),
					html.P(
						html.Class("text-sm text-gray-600 dark:text-gray-400"),
						gomponents.Text(locales["in_app_enabled_description"]),
					),
				),
				html.Div(
					html.Class("ml-4"),
					html.Label(
						html.Class("relative inline-flex items-center cursor-pointer"),
						html.Input(
							html.Type("checkbox"),
							html.Name("in_app_enabled"),
							html.Value("true"),
							gomponents.If(inAppEnabled, html.Checked()),
							html.Class("sr-only peer"),
						),
						html.Div(
							html.Class("w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"),
						),
					),
				),
			),

			// Match updates toggle
			html.Div(
				html.Class("flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"),
				html.Div(
					html.Class("flex-1"),
					html.H3(
						html.Class("text-lg font-medium text-gray-900 dark:text-white"),
						gomponents.Text(locales["match_updates_title"]),
					),
					html.P(
						html.Class("text-sm text-gray-600 dark:text-gray-400"),
						gomponents.Text(locales["match_updates_description"]),
					),
				),
				html.Div(
					html.Class("ml-4"),
					html.Label(
						html.Class("relative inline-flex items-center cursor-pointer"),
						html.Input(
							html.Type("checkbox"),
							html.Name("match_updates"),
							html.Value("true"),
							gomponents.If(matchUpdates, html.Checked()),
							html.Class("sr-only peer"),
						),
						html.Div(
							html.Class("w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"),
						),
					),
				),
			),

			// Schedule changes toggle
			html.Div(
				html.Class("flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"),
				html.Div(
					html.Class("flex-1"),
					html.H3(
						html.Class("text-lg font-medium text-gray-900 dark:text-white"),
						gomponents.Text(locales["schedule_changes_title"]),
					),
					html.P(
						html.Class("text-sm text-gray-600 dark:text-gray-400"),
						gomponents.Text(locales["schedule_changes_description"]),
					),
				),
				html.Div(
					html.Class("ml-4"),
					html.Label(
						html.Class("relative inline-flex items-center cursor-pointer"),
						html.Input(
							html.Type("checkbox"),
							html.Name("schedule_changes"),
							html.Value("true"),
							gomponents.If(scheduleChanges, html.Checked()),
							html.Class("sr-only peer"),
						),
						html.Div(
							html.Class("w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"),
						),
					),
				),
			),

			// Results toggle
			html.Div(
				html.Class("flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"),
				html.Div(
					html.Class("flex-1"),
					html.H3(
						html.Class("text-lg font-medium text-gray-900 dark:text-white"),
						gomponents.Text(locales["results_title"]),
					),
					html.P(
						html.Class("text-sm text-gray-600 dark:text-gray-400"),
						gomponents.Text(locales["results_description"]),
					),
				),
				html.Div(
					html.Class("ml-4"),
					html.Label(
						html.Class("relative inline-flex items-center cursor-pointer"),
						html.Input(
							html.Type("checkbox"),
							html.Name("results"),
							html.Value("true"),
							gomponents.If(results, html.Checked()),
							html.Class("sr-only peer"),
						),
						html.Div(
							html.Class("w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"),
						),
					),
				),
			),

			// Announcements toggle
			html.Div(
				html.Class("flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"),
				html.Div(
					html.Class("flex-1"),
					html.H3(
						html.Class("text-lg font-medium text-gray-900 dark:text-white"),
						gomponents.Text(locales["announcements_title"]),
					),
					html.P(
						html.Class("text-sm text-gray-600 dark:text-gray-400"),
						gomponents.Text(locales["announcements_description"]),
					),
				),
				html.Div(
					html.Class("ml-4"),
					html.Label(
						html.Class("relative inline-flex items-center cursor-pointer"),
						html.Input(
							html.Type("checkbox"),
							html.Name("announcements"),
							html.Value("true"),
							gomponents.If(announcements, html.Checked()),
							html.Class("sr-only peer"),
						),
						html.Div(
							html.Class("w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"),
						),
					),
				),
			),

			// Save button
			html.Div(
				html.Class("pt-6"),
				button.PrimaryButton(button.BaseButtonConfig{
					ButtonType: "submit",
					Text:       locales["save_button"],
					Class:      "w-full",
				}),
			),
		),
	)
}
