package templatesappsettings

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/utils/jsonutils"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func CustomMatchColumnsTable(columns []db.MatchCustomColumn, lang string) gomponents.Node {
	// Convert columns to JSON for Alpine.js data
	columnsJSON, _ := jsonutils.Marshal(columns)

	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/appsettings.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = map[string]string{}
	}

	// Create error message with localized text
	saveErrorMessage := locales["save_error"]

	return html.Div(
		html.Class("flex flex-col min-h-0 customMatchColumnsTable"),
		gomponents.Attr("x-data", fmt.Sprintf("customColumnsTable(%s)", string(columnsJSON))),

		// Saving indicator
		html.Div(
			html.Class("mb-4 text-sm text-gray-600 dark:text-gray-300 flex items-center"),
			gomponents.Attr("x-cloak", ""),
			gomponents.Attr("x-show", "columns.some(c => c.saving)"),
			gomponents.El("svg",
				html.Class("animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500 dark:text-blue-400"),
				gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
				gomponents.Attr("fill", "none"),
				gomponents.Attr("viewBox", "0 0 24 24"),
				gomponents.El("circle",
					html.Class("opacity-25"),
					gomponents.Attr("cx", "12"),
					gomponents.Attr("cy", "12"),
					gomponents.Attr("r", "10"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.Attr("stroke-width", "4"),
				),
			),
			gomponents.Text(locales["saving"]),
		),

		// Error message
		html.Div(
			html.Class("mb-4 text-sm text-red-600 dark:text-red-400 flex items-center gap-2"),
			gomponents.Attr("x-show", "error"),
			icons.ErrorIcon(),
			html.Span(
				gomponents.Attr("x-text", "error"),
			),
			// Set a default localized error message in a hidden span for JavaScript to use
			html.Span(
				html.ID("save-error-message"),
				html.Class("hidden"),
				gomponents.Text(saveErrorMessage),
			),
		),

		// Top bar with Add button and Action menu
		html.Div(
			html.Class("mb-4 flex justify-between items-center"),
			// Add button
			button.PrimaryIconButton(button.IconButtonConfig{
				ID:         "add-custom-match-column-btn",
				DataTestID: "add-custom-match-column-btn",
				ButtonType: "button",
				Text:       locales["custom_match_column_add"],
				Icon:       icons.Plus(),
				Attrs: []gomponents.Node{
					gomponents.Attr("hx-get", "/app/settings/custom-match-columns/new"),
					gomponents.Attr("hx-target", "#newCustomMatchColumnFormContainer"),
				},
			}),
			// Action menu (shown when items are selected)
			html.Div(
				html.Class("relative"),
				gomponents.Attr("x-show", "hasSelectedColumns"),
				gomponents.Attr("x-data", "{ open: false }"),
				html.Button(
					html.Type("button"),
					html.Class("flex items-center p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none"),
					gomponents.Attr("@click", "open = !open"),
					gomponents.Attr("@click.away", "open = false"),
					icons.EllipsisVertical(gomponents.Attr("class", "h-5 w-5")),
				),
				// Dropdown menu
				html.Div(
					html.Class("absolute right-0 z-10 mt-2 w-48 origin-top-right bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg"),
					gomponents.Attr("x-show", "open"),
					gomponents.Attr("x-transition", ""),
					html.Div(
						html.Class("py-1"),
						html.Button(
							html.Type("button"),
							html.Class("block w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700"),
							gomponents.Attr("@click", "deleteSelectedColumns(); open = false"),
							gomponents.Text("Delete"),
						),
					),
				),
			),
		),

		// Container for new custom column form
		html.Div(
			gomponents.Attr("id", "newCustomMatchColumnFormContainer"),
		),

		// Search bar
		html.Div(
			html.Class("relative mb-4"),
			html.Div(
				html.Class("absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"),
				gomponents.El("svg",
					html.Class("h-5 w-5 text-gray-400 dark:text-gray-300"),
					gomponents.Attr("fill", "none"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.Attr("viewBox", "0 0 24 24"),
					gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
					gomponents.El("path",
						gomponents.Attr("stroke-linecap", "round"),
						gomponents.Attr("stroke-linejoin", "round"),
						gomponents.Attr("stroke-width", "2"),
						gomponents.Attr("d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"),
					),
				),
			),
			html.Input(
				html.Type("text"),
				html.Class("shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500"),
				html.Placeholder(locales["search_columns"]),
				gomponents.Attr("x-model", "searchTerm"),
				gomponents.Attr("x-ref", "searchInput"),
				// Reset to first page when search term changes
				gomponents.Attr("@input", "currentPage = 1"),
				gomponents.Attr("data-testid", "custom-match-columns-search-bar"),
			),
			// Clear search button that appears when there's text
			html.Button(
				html.Type("button"),
				html.Class("absolute inset-y-0 right-0 pr-3 flex items-center"),
				gomponents.Attr("x-show", "searchTerm.length > 0"),
				gomponents.Attr("@click", "searchTerm = ''; $refs.searchInput.focus()"),
				gomponents.El("svg",
					html.Class("h-5 w-5 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-400"),
					gomponents.Attr("fill", "none"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.Attr("viewBox", "0 0 24 24"),
					gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
					gomponents.El("path",
						gomponents.Attr("stroke-linecap", "round"),
						gomponents.Attr("stroke-linejoin", "round"),
						gomponents.Attr("stroke-width", "2"),
						gomponents.Attr("d", "M6 18L18 6M6 6l12 12"),
					),
				),
			),
		),

		// Table
		html.Div(
			html.Class("shadow border-b border-gray-200 dark:border-gray-600 sm:rounded-lg flex-1 overflow-y-auto"),
			html.Table(
				html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-600"),
				// Table Header
				html.THead(
					html.Class("bg-gray-50 dark:bg-gray-700 sticky top-0 z-10"),
					html.Tr(
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left"),
							html.Div(
								html.Class("flex items-center"),
								html.Input(
									html.Type("checkbox"),
									html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
									gomponents.Attr("x-model", "isAllSelected"),
									gomponents.Attr("@change", "toggleAllSelection()"),
								),
							),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
							gomponents.Attr("@click", "sortBy('name')"),
							gomponents.Text(locales["custom_match_column_name"]),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
							gomponents.Attr("@click", "sortBy('fieldType')"),
							gomponents.Text(locales["custom_match_column_type"]),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
							gomponents.Attr("@click", "sortBy('description')"),
							gomponents.Text(locales["custom_match_column_description"]),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
							gomponents.Attr("@click", "sortBy('isRequired')"),
							gomponents.Text(locales["custom_match_column_required"]),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
							gomponents.Attr("@click", "sortBy('isActive')"),
							gomponents.Text(locales["custom_match_column_active"]),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
							gomponents.Attr("@click", "sortBy('displayOrder')"),
							gomponents.Text(locales["custom_match_column_order"]),
						),
						html.Th(
							gomponents.Attr("scope", "col"),
							html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
							gomponents.Text(locales["custom_match_column_actions"]),
						),
					),
				),
				// Table Body
				html.TBody(
					html.Class("bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600"),
					gomponents.Attr("x-ref", "tbody"),
					gomponents.Attr("x-data", "{ edit: {} }"),
					html.Template(
						gomponents.Attr("x-for", "(column, index) in paginatedColumns"),
						gomponents.Attr(":key", "column.id"),
						html.Tr(
							html.Td(
								html.Class("px-6 py-4"),
								html.Div(
									html.Class("flex items-center"),
									html.Input(
										html.Type("checkbox"),
										html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
										gomponents.Attr("x-model", "selectedColumns"),
										gomponents.Attr(":value", "column.id"),
										gomponents.Attr("@change", "toggleColumnSelection(column.id)"),
									),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Input(
									html.Type("text"),
									html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-500 focus:border-blue-500 focus:outline-none w-full px-2 py-1"),
									gomponents.Attr("x-model", "column.name"),
									gomponents.Attr("@change", "updateColumn(column, 'name')"),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Select(
									html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-500 focus:border-blue-500 focus:outline-none w-full px-2 py-1"),
									gomponents.Attr("x-model", "column.fieldType"),
									gomponents.Attr("@change", "updateColumn(column, 'fieldType')"),
									html.Option(gomponents.Attr("value", "text"), gomponents.Text("Text")),
									html.Option(gomponents.Attr("value", "number"), gomponents.Text("Number")),
									html.Option(gomponents.Attr("value", "date"), gomponents.Text("Date")),
									html.Option(gomponents.Attr("value", "boolean"), gomponents.Text("Boolean")),
									html.Option(gomponents.Attr("value", "email"), gomponents.Text("Email")),
									html.Option(gomponents.Attr("value", "url"), gomponents.Text("URL")),
									html.Option(gomponents.Attr("value", "select"), gomponents.Text("Select")),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Input(
									html.Type("text"),
									html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-500 focus:border-blue-500 focus:outline-none w-full px-2 py-1"),
									gomponents.Attr("x-model", "column.description"),
									gomponents.Attr("@change", "updateColumn(column, 'description')"),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Div(
									html.Class("flex items-center"),
									html.Input(
										html.Type("checkbox"),
										html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
										gomponents.Attr("x-model", "column.isRequired"),
										gomponents.Attr("@change", "updateColumn(column, 'isRequired')"),
									),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Div(
									html.Class("flex items-center"),
									html.Input(
										html.Type("checkbox"),
										html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
										gomponents.Attr("x-model", "column.isActive"),
										gomponents.Attr("@change", "updateColumn(column, 'isActive')"),
									),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Input(
									html.Type("number"),
									html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-500 focus:border-blue-500 focus:outline-none w-full px-2 py-1"),
									gomponents.Attr("x-model", "column.displayOrder"),
									gomponents.Attr("@change", "updateColumn(column, 'displayOrder')"),
								),
							),
							html.Td(
								html.Class("px-6 py-4"),
								html.Button(
									html.Type("button"),
									html.Class("text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-600"),
									gomponents.Attr("@click", "deleteColumn(column)"),
									gomponents.Text(locales["custom_match_column_delete"]),
								),
							),
						),
					),
				),
			),
		),

		// Pagination Controls with Items Per Page Selector
		html.Div(
			html.Class("mt-4 flex justify-between items-center"),
			// Left side: Pagination controls
			html.Div(
				html.Class("flex space-x-2"),
				html.Button(
					html.ID("prev-page-btn"),
					html.Class("px-4 py-2 border rounded text-sm text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-500"),
					gomponents.Attr("@click", "previousPage()"),
					gomponents.Attr("x-bind:disabled", "currentPage === 1"),
					gomponents.Attr("x-bind:class", "currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''"),
					gomponents.Text(locales["prev_page"]),
				),
				html.Button(
					html.ID("next-page-btn"),
					html.Class("px-4 py-2 border rounded text-sm text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-500"),
					gomponents.Attr("@click", "nextPage()"),
					gomponents.Attr("x-bind:disabled", "currentPage === totalPages"),
					gomponents.Attr("x-bind:class", "currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''"),
					gomponents.Text(locales["next_page"]),
				),
			),
			// Middle: Pagination information
			html.Div(
				html.ID("pagination-info"),
				html.Class("text-sm text-gray-700 dark:text-gray-300 flex items-center space-x-1"),
				html.Span(gomponents.Text(locales["showing"])),
				html.Span(gomponents.Text(" ")), // space
				html.Span(gomponents.Attr("x-text", "((currentPage - 1) * Number(itemsPerPage)) + 1")),
				html.Span(gomponents.Text("-")),
				html.Span(gomponents.Attr("x-text", "Math.min(currentPage * Number(itemsPerPage), columns.length)")),
				html.Span(gomponents.Text(" ")), // space
				html.Span(gomponents.Text(locales["of"])),
				html.Span(gomponents.Text(" ")), // space
				html.Span(gomponents.Attr("x-text", "columns.length")),
			),
			// Right side: Items per page selector
			html.Div(
				html.Class("flex items-center space-x-2"),
				html.Label(
					html.Class("text-sm text-gray-700 dark:text-gray-300"),
					html.For("items-per-page"),
					gomponents.Text(locales["items_per_page"]),
				),
				html.Select(
					html.ID("items-per-page"),
					html.Class("text-sm border rounded px-2 py-1 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-800"),
					gomponents.Attr("x-model", "itemsPerPage"),
					gomponents.Attr("@change", "currentPage = 1"), // Reset to first page when changing items per page
					html.Option(gomponents.Attr("value", "5"), gomponents.Text("5")),
					html.Option(gomponents.Attr("value", "10"), gomponents.Text("10")),
					html.Option(gomponents.Attr("value", "25"), gomponents.Text("25")),
					html.Option(gomponents.Attr("value", "50"), gomponents.Text("50")),
				),
			),
		),
	)
}
