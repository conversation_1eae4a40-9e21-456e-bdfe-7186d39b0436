package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// DowngradeSuccess renders a success message after downgrading subscription
func DowngradeSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/downgradeResponse.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("mt-4 p-3 bg-green-100 text-green-800 rounded"),
		gomponents.Text(locales["downgrade_success"]),
	)
}
