package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/toast"
	"maragu.dev/gomponents"
)

// UserSettingsPatchSuccess renders a success message fragment that can be inserted into the user settings page.
func UserSettingsPatchSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/usersettingsPatchSuccess.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = make(map[string]string)
	}

	return toast.Toast(toast.ToastConfig{
		ID:         "user-settings-toast",
		Message:    locales["successMessage"],
		Style:      "success",
		DataTestID: "user-settings-success",
		AutoClose:  true,
	})
}
