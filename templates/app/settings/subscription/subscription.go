package templatesappsubscription

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/pagebuilder"
	templatesuibutton "github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	gomponents "maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type SubscriptionPageConfig struct {
	Lang             string
	ActiveLink       string
	IsSidebarOpen    bool
	SubscriptionTier string
}

type SubscriptionPageContentConfig struct {
	Lang             string
	SubscriptionTier string
}

type SubscriptionCardsContentConfig struct {
	Lang             string
	SubscriptionTier string
}

func SubscriptionPage(config SubscriptionPageConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/subscription").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          config.Lang,
			IsSidebarOpen: config.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return SubscriptionPageContent(SubscriptionPageContentConfig{
				Lang:             config.Lang,
				SubscriptionTier: config.SubscriptionTier,
			})
		})
}

func SubscriptionPageContent(config SubscriptionPageContentConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/subscription").
		WithClass("flex flex-col h-full").
		RenderContent(config.Lang, func(ctx pagebuilder.PageContext) gomponents.Node {
			return html.Div(
				html.Class("min-h-screen px-4 py-8 overflow-auto"),
				html.Div(
					html.Class("max-w-4xl mx-auto"),
					html.H1(
						html.Class("text-2xl font-bold text-gray-900 dark:text-white mb-8"),
						gomponents.Text(ctx.Locales["subscription"]),
					),
					html.Div(
						html.ID("subscription-cards-container"),
						html.Class("grid grid-cols-1 md:grid-cols-2 gap-6"),
						gomponents.Attr("sse-connect", "/app/settings/sse"),
						htmx.Trigger("sse:SubscriptionUpdated"),
						htmx.Get("/app/settings/subscription-content"),
						htmx.Target("#subscription-cards-container"),
						htmx.Swap("innerHTML"),
						SubscriptionCard(SubscriptionCardConfig{
							Lang:         config.Lang,
							Tier:         "free",
							Title:        ctx.Locales["free_tier"],
							Price:        ctx.Locales["free_tier"],
							Features:     getFreeFeatures(ctx.Locales),
							IsCurrent:    config.SubscriptionTier == "free",
							ButtonText:   "",
							ButtonAction: "",
							Locales:      ctx.Locales,
						}),
						SubscriptionCard(SubscriptionCardConfig{
							Lang:         config.Lang,
							Tier:         "pro",
							Title:        ctx.Locales["pro_tier"],
							Price:        ctx.Locales["monthly_price"],
							Features:     getProFeatures(ctx.Locales),
							IsCurrent:    config.SubscriptionTier == "pro",
							ButtonText:   getButtonText(config.SubscriptionTier, ctx.Locales),
							ButtonAction: getButtonAction(config.SubscriptionTier),
							Locales:      ctx.Locales,
						}),
					),
				),
			)
		})
}

type SubscriptionCardConfig struct {
	Lang         string
	Tier         string
	Title        string
	Price        string
	Features     []string
	IsCurrent    bool
	ButtonText   string
	ButtonAction string
	Locales      map[string]string
}

func SubscriptionCard(config SubscriptionCardConfig) gomponents.Node {
	cardClass := "bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-2"
	if config.IsCurrent {
		cardClass += " border-blue-500 dark:border-blue-400"
	} else {
		cardClass += " border-gray-200 dark:border-gray-600"
	}

	return html.Div(
		html.Class(cardClass),
		html.Div(
			html.Class("text-center mb-6"),
			html.H2(
				html.Class("text-xl font-semibold text-gray-900 dark:text-white mb-2"),
				gomponents.Text(config.Title),
			),
			gomponents.If(config.IsCurrent, html.Div(
				html.Class("inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 mb-2"),
				gomponents.Text(config.Locales["current_plan"]),
			)),
			html.P(
				html.Class("text-2xl font-bold text-gray-900 dark:text-white"),
				gomponents.Text(config.Price),
			),
		),
		html.Ul(
			html.Class("space-y-3 mb-6"),
			gomponents.Group(renderFeatures(config.Features)),
		),
		gomponents.If(config.ButtonText != "", html.Div(
			html.Class("text-center"),
			gomponents.If(config.ButtonAction == "upgrade",
				templatesuibutton.PrimaryButton(templatesuibutton.BaseButtonConfig{
					Text:       config.ButtonText,
					ButtonType: "button",
					Class:      "w-full",
					Attrs: []gomponents.Node{
						htmx.Get("/app/settings/upgrade"),
						htmx.Target("#modal-body-container"),
					},
				}),
			),
			gomponents.If(config.ButtonAction == "downgrade",
				templatesuibutton.SecondaryButton(templatesuibutton.BaseButtonConfig{
					Text:       config.ButtonText,
					ButtonType: "button",
					Class:      "w-full",
					Attrs: []gomponents.Node{
						htmx.Post("/app/settings/downgrade"),
						htmx.Confirm(config.Locales["confirm_downgrade"]),
						htmx.Indicator("#downgrade-spinner"),
						htmx.Target("#subscription-cards-container"),
						htmx.Swap("innerHTML"),
						gomponents.Attr("id", "downgrade-btn"),
					},
				}),
			),
		)),
	)
}

func renderFeatures(features []string) []gomponents.Node {
	var nodes []gomponents.Node
	for _, feature := range features {
		nodes = append(nodes, html.Li(
			html.Class("flex items-center text-gray-700 dark:text-gray-300"),
			html.Span(
				html.Class("w-5 h-5 text-green-500 dark:text-green-400 mr-2"),
				icons.Check(),
			),
			gomponents.Text(feature),
		))
	}
	return nodes
}

func getFreeFeatures(locales map[string]string) []string {
	return []string{
		locales["free_players"],
		locales["free_seasons"],
		locales["free_matches"],
		locales["free_stats"],
	}
}

func getProFeatures(locales map[string]string) []string {
	return []string{
		locales["pro_players"],
		locales["pro_seasons"],
		locales["pro_matches"],
		locales["pro_stats"],
		locales["pro_custom_columns"],
		locales["pro_export"],
		locales["pro_priority_support"],
	}
}

func getButtonText(subscriptionTier string, locales map[string]string) string {
	if subscriptionTier == "free" {
		return locales["upgrade_to_pro"]
	}
	return locales["downgrade_to_free"]
}

func getButtonAction(subscriptionTier string) string {
	if subscriptionTier == "free" {
		return "upgrade"
	}
	return "downgrade"
}

func SubscriptionCardsContent(config SubscriptionCardsContentConfig) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/subscription/subscription.locales.json", config.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return gomponents.Group([]gomponents.Node{
		SubscriptionCard(SubscriptionCardConfig{
			Lang:         config.Lang,
			Tier:         "free",
			Title:        locales["free_tier"],
			Price:        locales["free_tier"],
			Features:     getFreeFeatures(locales),
			IsCurrent:    config.SubscriptionTier == "free",
			ButtonText:   "",
			ButtonAction: "",
			Locales:      locales,
		}),
		SubscriptionCard(SubscriptionCardConfig{
			Lang:         config.Lang,
			Tier:         "pro",
			Title:        locales["pro_tier"],
			Price:        locales["monthly_price"],
			Features:     getProFeatures(locales),
			IsCurrent:    config.SubscriptionTier == "pro",
			ButtonText:   getButtonText(config.SubscriptionTier, locales),
			ButtonAction: getButtonAction(config.SubscriptionTier),
			Locales:      locales,
		}),
	})
}
