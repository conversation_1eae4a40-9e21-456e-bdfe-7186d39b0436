package templatesappsettings

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type AppSettingsPageConfig struct {
	Lang                    string
	IsSidebarOpen           bool
	ActiveLink              string
	PlayerCustomColumns     []db.PlayerCustomColumn
	MatchCustomColumns      []db.MatchCustomColumn
	NotificationPreferences *db.NotificationPreference
	UsageLimits             map[limits.ResourceType]*limits.LimitCheckResult
}

func AppSettingsPage(config AppSettingsPageConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/app").
		WithLocaleBase("app/settings/appsettings").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          config.Lang,
			Seasons:       []db.Season{},
			IsSidebarOpen: config.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return AppSettingsPageContent(AppSettingsPageContentConfig{
				Lang:                    config.Lang,
				PlayerCustomColumns:     config.PlayerCustomColumns,
				MatchCustomColumns:      config.MatchCustomColumns,
				NotificationPreferences: config.NotificationPreferences,
				UsageLimits:             config.UsageLimits,
			})
		})
}
