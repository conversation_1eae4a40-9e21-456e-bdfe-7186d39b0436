{"en": {"page_title": "Coachpad - A<PERSON> Settings", "description": "Manage application-wide settings here, including custom fields for your players and matches.", "custom_player_columns_title": "Custom Player Fields", "custom_player_columns_description": "Define additional information that you want to track for each player.", "player_column_config": "Column Configuration", "save_success": "Setting<PERSON> saved successfully", "save_error": "Error saving settings. Please try again.", "custom_player_column_name": "Name", "custom_player_column_type": "Field Type", "custom_player_column_description": "Description", "custom_player_column_required": "Required", "custom_player_column_active": "Active", "custom_player_column_order": "Display Order", "custom_player_column_add": "Add Custom Field", "custom_player_column_delete": "Delete", "custom_player_column_edit": "Edit", "custom_player_column_save": "Save", "custom_player_column_cancel": "Cancel", "custom_player_column_actions": "Actions", "search_columns": "Search custom fields...", "items_per_page": "Items per page:", "showing": "Showing", "of": "of", "prev_page": "Previous", "next_page": "Next", "saving": "Saving changes...", "custom_match_column_add": "Add Custom Field", "custom_match_columns_title": "Custom Match Fields", "name_placeholder": "e.g. Skill Level", "description_placeholder": "e.g. Player's skill level from 1-10", "order_placeholder": "1", "field_type_text": "Text", "field_type_number": "Number", "field_type_date": "Date", "field_type_boolean": "<PERSON><PERSON><PERSON> (Yes/No)", "field_type_email": "Email", "field_type_url": "URL", "field_type_select": "Select (Dropdown)"}, "fr": {"page_title": "Coachpad - Paramètres de l'Application", "description": "<PERSON><PERSON><PERSON> les paramètres de l'application ici, y compris les champs personnalisés pour vos joueurs et matchs.", "custom_player_columns_title": "Champs Personnalisés des Joueurs", "custom_player_columns_description": "Définissez des informations supplémentaires que vous souhaitez suivre pour chaque joueur.", "player_column_config": "Configuration des Colonnes", "save_success": "Paramètres enregistrés avec succès", "save_error": "Erreur lors de l'enregistrement des paramètres. Veuillez réessayer.", "custom_player_column_name": "Nom", "custom_player_column_type": "Type de Champ", "custom_player_column_description": "Description", "custom_player_column_required": "Obligatoire", "custom_player_column_active": "Actif", "custom_player_column_order": "Ordre d'Affichage", "custom_player_column_add": "Ajouter un Champ", "custom_player_column_delete": "<PERSON><PERSON><PERSON><PERSON>", "custom_player_column_edit": "Modifier", "custom_player_column_save": "Enregistrer", "custom_player_column_cancel": "Annuler", "custom_player_column_actions": "Actions", "search_columns": "Rechercher des champs personnalisés...", "items_per_page": "Éléments par page:", "showing": "Affichage de", "of": "sur", "prev_page": "Précédent", "next_page": "Suivant", "saving": "Enregistrement des modifications...", "custom_match_column_add": "Ajouter un Champ", "custom_match_columns_title": "Champs Personnalisés des Matchs", "name_placeholder": "ex. Niveau de compétence", "description_placeholder": "ex. Niveau de compétence du joueur de 1-10", "order_placeholder": "1", "field_type_text": "Texte", "field_type_number": "Nombre", "field_type_date": "Date", "field_type_boolean": "<PERSON><PERSON><PERSON><PERSON> (Oui/Non)", "field_type_email": "<PERSON><PERSON><PERSON>", "field_type_url": "URL", "field_type_select": "Sélection (Liste déroulante)"}}