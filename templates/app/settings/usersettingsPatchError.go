package templatesappsettings

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// UserSettingsPatchError renders an error message fragment that can be inserted into the user settings page.
func UserSettingsPatchError(errorMessage, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/usersettingsPatchError.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"),
		html.Role("alert"),
		html.Div(
			html.Class("flex flex-col space-y-2"),
			html.Span(
				html.Class("block sm:inline"),
				gomponents.Text(locales["errorMessagePrefix"]+errorMessage),
			),
		),
	)
}
