package templatesappapiacccess

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// APIDocumentationModal renders the API documentation modal
func APIDocumentationModal(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/settings/apiAccess/apiDocumentationModal.locales.json", lang)
	if err != nil {
		// Fallback to default English text if locales can't be loaded
		locales = map[string]string{
			"title":       "API Documentation",
			"description": "Learn how to use the Coachpad API to integrate with your applications.",
			"coming_soon": "API documentation is coming soon. Stay tuned for updates!",
			"close":       "Close",
		}
	}

	content := html.Div(
		html.Class("space-y-4"),
		html.P(
			html.Class("text-gray-600 dark:text-gray-400"),
			gomponents.Text(locales["description"]),
		),
		html.Div(
			html.Class("bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"),
			html.P(
				html.Class("text-blue-800 dark:text-blue-200"),
				gomponents.Text(locales["coming_soon"]),
			),
		),
		html.Div(
			html.Class("flex justify-end pt-4"),
			html.Button(
				html.Type("button"),
				html.Class("px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"),
				gomponents.Attr("@click", "apiDocumentationModal_modalOpen = false"),
				gomponents.Text(locales["close"]),
			),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "apiDocumentationModal",
		Title:        locales["title"],
		Content:      content,
		IsUnclosable: false,
		DataTestID:   "api-documentation-modal",
	})
}
