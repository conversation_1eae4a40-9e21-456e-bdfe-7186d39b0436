{"en": {"modal_title": "API Documentation", "documentation_description": "Use these API keys to access your Coachpad data programmatically. All API requests must be authenticated using your API key.", "authentication_title": "Authentication", "authentication_description": "Include your API key as a Bear<PERSON> token in the Authorization header:", "base_url_title": "Base URL", "base_url_description": "All API endpoints are available at the following base URL:"}, "fr": {"modal_title": "Documentation API", "documentation_description": "Utilisez ces clés API pour accéder à vos données Coachpad de manière programmatique. Toutes les requêtes API doivent être authentifiées avec votre clé API.", "authentication_title": "Authentification", "authentication_description": "Incluez votre clé API comme jeton Bearer dans l'en-tête Authorization :", "base_url_title": "URL de Base", "base_url_description": "Tous les points de terminaison API sont disponibles à l'URL de base suivante :"}}