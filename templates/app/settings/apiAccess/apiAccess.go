package templatesappapiacccess

import (
	"github.com/j-em/coachpad/db"
	templatesappsettings "github.com/j-em/coachpad/templates/app/settings"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type APIAccessPageConfig struct {
	Lang          string
	IsSidebarOpen bool
	ActiveLink    string
	APIKeys       []db.GetAPIKeysByUserIDRow
}

type APIAccessPageContentConfig struct {
	Lang    string
	APIKeys []db.GetAPIKeysByUserIDRow
}

func APIAccessPage(config APIAccessPageConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/api-access").
		WithLocaleBase("app/settings/apiAccess/apiAccess").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          config.Lang,
			Seasons:       []db.Season{},
			IsSidebarOpen: config.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return APIAccessPageContent(APIAccessPageContentConfig{
				Lang:    config.Lang,
				APIKeys: config.APIKeys,
			})
		})
}

func APIAccessPageContent(config APIAccessPageContentConfig) gomponents.Node {
	return pagebuilder.NewPage("/app/settings/api-access").
		WithLocaleBase("app/settings/apiAccess/apiAccess").
		WithClass("flex flex-col h-full").
		RenderContent(config.Lang, func(ctx pagebuilder.PageContext) gomponents.Node {
			return html.Div(
				html.Class("p-4 space-y-6 h-full overflow-y-auto"),

				// API Access Header
				html.H1(
					html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white"),
					gomponents.Text(ctx.Locales["page_title"]),
				),

				// API Access Description
				html.P(
					html.Class("text-gray-700 dark:text-gray-300"),
					gomponents.Text(ctx.Locales["description"]),
				),

				// API Keys Section
				templatesappsettings.APIKeysSection(templatesappsettings.APIKeysSectionConfig{
					Lang:    config.Lang,
					APIKeys: config.APIKeys,
				}),

				// API Documentation Section
				html.Div(
					html.Class("mt-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg"),
					html.H2(
						html.Class("text-2xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
						gomponents.Text(ctx.Locales["documentation_title"]),
					),
					html.P(
						html.Class("text-gray-700 dark:text-gray-300 mb-4"),
						gomponents.Text(ctx.Locales["documentation_description"]),
					),
					button.SecondaryButton(button.BaseButtonConfig{
						Text:       ctx.Locales["see_api_documentation"],
						DataTestID: "api-documentation-button",
						Attrs: []gomponents.Node{
							gomponents.Attr("hx-get", "/app/settings/api-access/documentation"),
							gomponents.Attr("hx-target", "#modal-body-container"),
							gomponents.Attr("hx-swap", "innerHTML"),
						},
					}),
				),
			)
		})
}
