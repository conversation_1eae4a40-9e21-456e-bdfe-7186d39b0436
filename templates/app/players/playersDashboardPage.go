package templatesappplayers

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type PlayersDashboardPageProps struct {
	ActiveLink       string
	Players          []db.Player
	AllPlayers       []db.<PERSON>
	Lang             string
	Seasons          []db.Season
	IsSidebarOpen    bool
	TotalItems       int
	TotalPages       int
	Params           PlayersTableParams
	IsSearchActive   bool
	ColumnVisibility map[string]bool
}

func PlayersDashboardPage(props PlayersDashboardPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/players").
		WithLocaleBase("app/players/playersDashboardPage").
		WithTitle("title").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return PlayersDashboardPageContent(PlayersDashboardProps{
				Players:          props.Players,
				AllPlayers:       props.AllPlayers,
				Lang:             props.Lang,
				Params:           &props.Params,
				TotalItems:       props.TotalItems,
				TotalPages:       props.TotalPages,
				IsSearchActive:   props.IsSearchActive,
				ColumnVisibility: props.ColumnVisibility,
			})
		})
}
