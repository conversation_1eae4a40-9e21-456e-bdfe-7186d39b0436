package templatesappplayers

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func PlayersPostSuccess(player db.Player, lang string) gomponents.Node {
	// Load localized strings
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersPostSuccess.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback
	}

	return html.Div(
		html.H1(gomponents.Text(locales["player_created_successfully"])),
		html.P(gomponents.Text(locales["player_name"]+": "+player.Name)),
		html.P(gomponents.Text(locales["player_email"]+": "+player.Email.String)),
	)
}
