package templatesappplayers

import (
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	tablesearchbar "github.com/j-em/coachpad/templates/ui/tableSearchBar"
	"github.com/j-em/coachpad/utils/pagination"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// PlayersDashboardProps contains props for the players dashboard page component
type PlayersDashboardProps struct {
	Players          []db.Player
	AllPlayers       []db.Player
	Lang             string
	Params           *PlayersTableParams
	TotalItems       int
	TotalPages       int
	IsSearchActive   bool
	DataTestID       string
	ColumnVisibility map[string]bool
}

// PlayersDashboardPageContent renders the main dashboard view for the Players page.
//
// It includes:
//   - The dashboard header (title and add player button)
//   - The players table (with search, sorting, and pagination)
//   - htmx attributes to automatically refresh the dashboard when the
//     `playersUpdated` event is triggered anywhere in the document body (e.g., after a player is added/edited/deleted)
//   - htmx attributes to fetch the latest players list from `/app/players` and update this component
//
// Args:
//
//	props: PlayersDashboardProps containing all necessary data and configuration
//
// Returns:
//
//	A gomponents.Node representing the full dashboard UI for players management
func PlayersDashboardPageContent(props PlayersDashboardProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersTable.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Use default parameters if not provided
	if props.Params == nil {
		props.Params = &PlayersTableParams{
			SortablePaginationParams: pagination.SortablePaginationParams{
				BasePaginationParams: pagination.BasePaginationParams{
					Page:         1,
					ItemsPerPage: 10,
					Search:       "",
				},
				Sort:      "name",
				Direction: "asc",
			},
		}
	}

	// Use len(players) if totalItems not provided
	if props.TotalItems == 0 {
		props.TotalItems = len(props.Players)
	}

	// Calculate totalPages if not provided
	if props.TotalPages == 0 {
		props.TotalPages = pagination.CalculateTotalPages(props.TotalItems, props.Params.ItemsPerPage)
	}

	return html.Div(
		html.Class("h-full flex flex-col gap-3 p-4"),
		gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),
		html.Head(
			html.TitleEl(
				gomponents.Text(
					locales["page_title"]),
			),
		),
		PlayersDashboardHeader(PlayersDashboardHeaderProps{
			Lang: props.Lang,
		}),
		// Search bar stays outside the refreshable area
		gomponents.If(len(props.AllPlayers) > 0, tablesearchbar.TableSearchBar(tablesearchbar.TableSearchBarProps{
			SearchValue:         props.Params.Search,
			SearchDataTestID:    "players-search-bar",
			BaseURL:             "/app/players",
			TargetID:            "players-table-content",
			ShowSavingIndicator: true, // Players table has saving indicator
			HiddenInputs: map[string]string{
				"page":     strconv.Itoa(props.Params.Page),
				"per_page": strconv.Itoa(props.Params.ItemsPerPage),
				"sort":     props.Params.Sort,
				"dir":      props.Params.Direction,
			},
			Lang:        props.Lang,
			LocalesPath: "./templates/app/players/playersTable.locales.json",
		})),
		// Table content container that gets refreshed
		html.Div(
			html.ID("players-table-content"),
			html.Class("min-h-0 flex flex-col"),
			htmx.Trigger("playersUpdated from:body"),
			htmx.Get("/app/players"),
			htmx.Target("this"),
			PlayersTable(PlayersTableProps{
				Players:          props.Players,
				AllPlayers:       props.AllPlayers,
				Lang:             props.Lang,
				Params:           *props.Params,
				TotalItems:       props.TotalItems,
				TotalPages:       props.TotalPages,
				IsSearchActive:   props.IsSearchActive,
				ColumnVisibility: props.ColumnVisibility,
			}),
		),
	)
}
