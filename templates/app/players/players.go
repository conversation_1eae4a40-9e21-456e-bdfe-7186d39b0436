package templatesappplayers

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n" // Import the i18n package
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PlayersSelectionList renders a list of players optimized for selection in forms
// with proper HTMX attributes for integration with the season creation form.
// The list has built-in validation that enforces an even number of player selections.
func PlayersSelectionList(players []db.Player, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/players.locales.json", lang)
	if err != nil {
		locales = make(map[string]string) // Fallback
	}

	if len(players) == 0 {
		return html.Div(
			html.Class("no-players-message text-center text-gray-500"),
			gomponents.Text(locales["noPlayersMessage"]),
		)
	}

	return html.Div(
		html.Class("players-selection"),
		html.ID("players-selection-container"), // Added ID for test targeting
		html.Ul(
			// Add validation attributes directly
			html.Class("player-selection-list divide-y divide-gray-200"),
			html.ID("players-selection-list"), // Added ID for test targeting
			gomponents.Attr("data-group-name", "players"),
			gomponents.Attr("data-group-even", "true"),
			gomponents.Attr("data-group-error-message", "Please select an even number of players for balanced teams"),
			// Use Map to transform the players to list items directly
			gomponents.Map(players, playerListItem),
		),
		html.Div(
			html.ID("players-error"),
			html.Class("mt-2 text-sm text-red-600"),
			gomponents.Attr("x-show", "formValidation.groupErrors.players"),
			gomponents.Attr("x-text", "formValidation.groupErrors.players"),
		),
	)
}

// playerListItem creates a list item for a player in the selection list
func playerListItem(p db.Player) gomponents.Node {
	return html.Li(
		html.Class("player-selection-item py-2"),
		html.Label(
			html.Class("flex items-center space-x-3"),
			html.Input(
				html.Type("checkbox"),
				html.Name("playerIds[]"),
				html.Value(strconv.Itoa(int(p.ID))),
				html.ID(fmt.Sprintf("player_%d", p.ID)),
				html.Class("form-checkbox h-5 w-5 text-blue-600"),
			),
			html.Span(
				html.Class("text-gray-700"),
				gomponents.Text(p.Name),
			),
		),
	)
}
