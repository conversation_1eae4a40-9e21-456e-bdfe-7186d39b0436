package templatesappplayers

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/pkg/limits"
	templatesappteams "github.com/j-em/coachpad/templates/app/teams"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/limitbanner"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type PlayersNewFormConfig struct {
	Lang         string
	PlayersUsage *limits.LimitCheckResult
}

func PlayersNewForm(config PlayersNewFormConfig) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersNewForm.locales.json", config.Lang)
	if err != nil {
		// Handle error, perhaps log it or use default values
		locales = make(map[string]string)
	}

	formContent := html.Form(
		html.Method("POST"),
		htmx.Boost("true"),
		htmx.Post("/app/players"),
		htmx.Target("#toast-body-container"),
		htmx.Swap("innerHTML"),
		gomponents.Attr("x-effect", "htmx.process($el)"),
		html.ID("playersNewForm_form"),
		gomponents.Attr("x-data", "{fields: {}, errors: {}, errorMessages: {}, groupErrors: {}}"),
		gomponents.Attr("x-validate-form", ""),
		gomponents.Attr("hx-on:htmx:after-request", `
			if (event.detail.successful && event.detail.xhr.status === 200) {
				const keepAdding = document.querySelector('#keepAddingPlayers').checked;
				if (keepAdding) {
					// Reset form but keep the checkbox state
					document.querySelector('#playersNewForm_form').reset();
					document.querySelector('#keepAddingPlayers').checked = true;
					// Focus on name field for next player
					document.querySelector('#name').focus();
				}
					}
				`),
		html.Div(
			html.Class("dark:bg-gray-800 dark:text-white"),

			// Usage indicator for players
			gomponents.If(config.PlayersUsage != nil,
				html.Div(
					html.Class("mb-4"),
					limitbanner.UsageIndicator(limitbanner.LimitBannerConfig{
						LimitResult: config.PlayersUsage,
						Lang:        config.Lang,
						Class:       "mb-4",
					}),
				),
			),

			form.FormInput(form.FormInputProps{
				Label:        locales["formNameLabel"],
				Type:         "text",
				ID:           "name",
				Name:         "name",
				Required:     true,
				ExtraClasses: "dark:bg-gray-700 dark:border-gray-600 dark:text-white",
			}),
			form.FormInput(form.FormInputProps{
				Label: locales["formEmailLabel"],
				Type:  "email",
				ID:    "email",
				Name:  "email",
			}),
			form.FormInput(form.FormInputProps{
				Label: locales["formPhoneLabel"],
				Type:  "tel",
				ID:    "phone",
				Name:  "phone",
			}),
			templatesappteams.TeamSelectWithProps(templatesappteams.TeamSelectProps{
				Lang:      config.Lang,
				Required:  false, // Team selection is optional for players
				FieldName: "teamId",
			}),
			// Add default preferred match group
			html.Input(
				html.Type("hidden"),
				html.Name("preferredMatchGroup"),
				html.Value("1"),
			),
		),
		form.FormCheckbox(form.FormCheckboxProps{
			Label: locales["formEmailNotificationsEnabledLabel"],
			ID:    "emailNotificationsEnabled",
			Name:  "emailNotificationsEnabled",
		}),
		form.FormCheckbox(form.FormCheckboxProps{
			Label: locales["keepAddingPlayersLabel"],
			ID:    "keepAddingPlayers",
			Name:  "keepAddingPlayers",
		}),
		html.Div(
			html.Class("flex items-center justify-end mt-4"),
			button.PrimaryButton(button.BaseButtonConfig{
				ButtonType: "submit",
				Text:       locales["formSubmit"],
			}),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "playersNewForm",
		Title:        locales["formTitle"],
		Content:      formContent,
		IsUnclosable: false,
		DataTestID:   "players-new-form-modal",
	})
}
