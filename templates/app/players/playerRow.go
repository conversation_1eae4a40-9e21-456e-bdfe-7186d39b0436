package templatesappplayers

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/db"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// PlayerRowProps contains props for a single player table row component
type PlayerRowProps struct {
	Player           db.Player
	DataTestID       string
	ColumnVisibility map[string]bool // Column visibility preferences
}

// PlayerRow renders a single table row for a player with inline editing
func PlayerRow(props PlayerRowProps) gomponents.Node {
	player := props.Player

	return html.Tr(
		html.ID(fmt.Sprintf("player-row-%d", player.ID)),
		gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),

		// Name field
		gomponents.If(props.ColumnVisibility["Name"], html.Td(
			html.Class("px-6 py-4"),
			html.Input(
				html.Type("text"),
				html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
				html.Value(player.Name),
				html.Name("name"),
				gomponents.Attr("data-testid", fmt.Sprintf("player-%d-name", player.ID)),
				htmx.Put(fmt.Sprintf("/app/players/%d/inline", player.ID)),
				htmx.Trigger("input changed delay:900ms"),
				htmx.Include("closest tr"),
				htmx.Indicator("#global-saving-indicator"),
				htmx.Swap("none"),
			),
		)),
		// Email field
		gomponents.If(props.ColumnVisibility["Email"], html.Td(
			html.Class("px-6 py-4"),
			html.Input(
				html.Type("email"),
				html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
				html.Value(player.Email.String),
				html.Name("email"),
				gomponents.Attr("data-testid", fmt.Sprintf("player-%d-email", player.ID)),
				htmx.Put(fmt.Sprintf("/app/players/%d/inline", player.ID)),
				htmx.Trigger("input changed delay:900ms"),
				htmx.Include("closest tr"),
				htmx.Indicator("#global-saving-indicator"),
				htmx.Swap("none"),
			),
		)),
		// Preferred match group field
		gomponents.If(props.ColumnVisibility["Preferred Group"], html.Td(
			html.Class("px-6 py-4"),
			html.Input(
				html.Type("number"),
				html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
				html.Value(strconv.Itoa(int(player.PreferredMatchGroup))),
				html.Name("preferredMatchGroup"),
				gomponents.Attr("data-testid", fmt.Sprintf("player-%d-preferred-group", player.ID)),
				htmx.Put(fmt.Sprintf("/app/players/%d/inline", player.ID)),
				htmx.Trigger("input changed delay:900ms"),
				htmx.Include("closest tr"),
				htmx.Indicator("#global-saving-indicator"),
				htmx.Swap("none"),
			),
		)),
		// Is active checkbox
		gomponents.If(props.ColumnVisibility["Active"], html.Td(
			html.Class("px-6 py-4"),
			html.Div(
				html.Class("flex items-center"),
				html.Input(
					html.Type("checkbox"),
					html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
					gomponents.If(player.IsActive, html.Checked()),
					html.Name("isActive"),
					html.Value("true"),
					gomponents.Attr("data-testid", fmt.Sprintf("player-%d-active", player.ID)),
					htmx.Put(fmt.Sprintf("/app/players/%d/inline", player.ID)),
					htmx.Trigger("change"),
					htmx.Include("closest tr"),
					htmx.Indicator("#global-saving-indicator"),
					htmx.Swap("none"),
				),
			),
		)),
		// Email notifications checkbox
		gomponents.If(props.ColumnVisibility["Email Notifications"], html.Td(
			html.Class("px-6 py-4"),
			html.Div(
				html.Class("flex items-center"),
				html.Input(
					html.Type("checkbox"),
					html.Class("h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-500 rounded"),
					gomponents.If(player.EmailNotificationsEnabled, html.Checked()),
					html.Name("emailNotificationsEnabled"),
					html.Value("true"),
					gomponents.Attr("data-testid", fmt.Sprintf("player-%d-notifications", player.ID)),
					htmx.Put(fmt.Sprintf("/app/players/%d/inline", player.ID)),
					htmx.Trigger("change"),
					htmx.Include("closest tr"),
					htmx.Indicator("#global-saving-indicator"),
					htmx.Swap("none"),
				),
			),
		)),
		// Hidden fields for the PUT request
		html.Input(html.Type("hidden"), html.Name("id"), html.Value(strconv.Itoa(int(player.ID)))),
	)
}
