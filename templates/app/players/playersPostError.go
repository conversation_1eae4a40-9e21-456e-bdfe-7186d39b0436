package templatesappplayers

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func PlayersPostError(errorMessage, lang string) gomponents.Node {
	// Load localized strings
	locales, err := i18n.LoadTemplateLocales("templates/app/players/playersPostError.locales.json", lang)
	if err != nil {
		locales = map[string]string{}
	}

	return html.Div(
		html.H1(gomponents.Text(locales["errorTitle"])),
		html.P(gomponents.Text(locales["errorMessagePrefix"]+errorMessage)),
	)
}
