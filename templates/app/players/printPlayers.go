package templatesappplayers

import (
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatesprint "github.com/j-em/coachpad/templates/ui/print"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PrintPlayersProps struct {
	Players []db.Player
	Lang    string
}

func PrintPlayers(props PrintPlayersProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/printPlayers.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	content := html.Div(
		html.H1(gomponents.Text(locales["print_title"])),
		html.Table(
			html.THead(
				html.Tr(
					html.Th(gomponents.Text(locales["table_header_name"])),
					html.Th(gomponents.Text(locales["table_header_email"])),
					html.Th(gomponents.Text(locales["table_header_preferred_group"])),
					html.Th(gomponents.Text(locales["table_header_active"])),
					html.Th(gomponents.Text(locales["table_header_notifications"])),
				),
			),
			html.TBody(
				gomponents.Group(
					gomponents.Map(props.Players, func(player db.Player) gomponents.Node {
						return html.Tr(
							html.Td(gomponents.Text(player.Name)),
							html.Td(gomponents.Text(player.Email.String)),
							html.Td(gomponents.Text(strconv.Itoa(int(player.PreferredMatchGroup)))),
							html.Td(gomponents.Text(formatPlayerActive(player.IsActive, locales))),
							html.Td(gomponents.Text(formatPlayerNotifications(player.EmailNotificationsEnabled, locales))),
						)
					}),
				),
			),
		),
	)

	return templatesprint.PrintPage(templatesprint.PrintPageProps{
		Title:   locales["print_title"],
		Lang:    props.Lang,
		Content: content,
	})
}

// Helper functions for formatting
func formatPlayerActive(isActive bool, locales map[string]string) string {
	if isActive {
		return locales["yes"]
	}
	return locales["no"]
}

func formatPlayerNotifications(enabled bool, locales map[string]string) string {
	if enabled {
		return locales["yes"]
	}
	return locales["no"]
}
