package templatesappplayers

import (
	"strconv"

	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// PlayersSearchBarProps contains props for the players search bar component
type PlayersSearchBarProps struct {
	Params     PlayersTableParams
	Lang       string
	BaseURL    string
	DataTestID string
}

// PlayersSearchBar renders the search bar that stays outside the refreshable table area
func PlayersSearchBar(props PlayersSearchBarProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersTable.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("mb-4 flex flex-wrap justify-between items-center gap-4"),
		// Global saving indicator
		html.Div(
			html.Class("mb-4 text-sm text-gray-600 dark:text-gray-300 items-center htmx-indicator"),
			html.ID("global-saving-indicator"),
			gomponents.Attr("data-testid", "global-saving-indicator"),
			gomponents.El("svg",
				html.Class("animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500 dark:text-blue-400"),
				gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
				gomponents.Attr("fill", "none"),
				gomponents.Attr("viewBox", "0 0 24 24"),
				gomponents.El("circle",
					html.Class("opacity-25"),
					gomponents.Attr("cx", "12"),
					gomponents.Attr("cy", "12"),
					gomponents.Attr("r", "10"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.Attr("stroke-width", "4"),
				),
			),
			gomponents.Text(locales["saving"]),
		),

		// Search input with icon
		html.Div(
			html.Class("relative"),
			html.Input(
				html.Type("text"),
				html.Class("border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-md px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"),
				html.Value(props.Params.Search),
				gomponents.Attr("placeholder", locales["search_players"]),
				gomponents.Attr("data-testid", props.DataTestID),
				html.Name("search"),
				htmx.Get(props.BaseURL),
				htmx.Target("#players-table-content"),
				htmx.Include("[name='sort'], [name='dir'], [name='page'], [name='per_page']"),
				htmx.Trigger("input changed delay:500ms"),
			),
			html.Div(
				html.Class("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"),
				gomponents.El("svg",
					html.Class("h-5 w-5"),
					gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
					gomponents.Attr("fill", "none"),
					gomponents.Attr("viewBox", "0 0 24 24"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.El("path",
						gomponents.Attr("stroke-linecap", "round"),
						gomponents.Attr("stroke-linejoin", "round"),
						gomponents.Attr("stroke-width", "2"),
						gomponents.Attr("d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"),
					),
				),
			),
		),
		// Hidden inputs to preserve current state
		html.Input(html.Type("hidden"), html.Name("page"), html.Value(strconv.Itoa(props.Params.Page))),
		html.Input(html.Type("hidden"), html.Name("per_page"), html.Value(strconv.Itoa(props.Params.ItemsPerPage))),
		html.Input(html.Type("hidden"), html.Name("sort"), html.Value(props.Params.Sort)),
		html.Input(html.Type("hidden"), html.Name("dir"), html.Value(props.Params.Direction)),
	)
}
