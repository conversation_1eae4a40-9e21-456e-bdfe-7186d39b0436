package templatesappplayers

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PlayersEditError renders an error message fragment for edit player errors.
func PlayersEditError(errorMessage, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersEditError.locales.json", lang)
	if err != nil {
		locales = map[string]string{}
	}

	return html.Div(
		html.Class("bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"),
		html.Role("alert"),
		html.Div(
			html.Class("flex flex-col space-y-2"),
			html.Div(
				html.Class("flex items-center gap-2"),
				html.Span(
					html.Class("h-5 w-5 text-red-500"),
					icons.ExclamationCircle(),
				),
				html.Span(
					html.Class("block sm:inline font-medium"),
					gomponents.Text(locales["errorTitle"]),
				),
			),
			html.P(
				html.Class("text-sm"),
				gomponents.Text(locales["errorMessagePrefix"]+errorMessage),
			),
		),
	)
}
