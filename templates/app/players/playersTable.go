package templatesappplayers

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	"github.com/j-em/coachpad/templates/ui/icons"
	tablepagination "github.com/j-em/coachpad/templates/ui/tablePagination"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/pagination"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// PlayersTableParams contains parameters for table filtering/sorting/pagination
type PlayersTableParams struct {
	pagination.SortablePaginationParams
}

// PlayersTableProps contains props for the HTMX-enabled players table
type PlayersTableProps struct {
	Players          []db.Player // The paginated/filtered players to display
	AllPlayers       []db.Player // All players in the database (to determine if <PERSON> is empty)
	Lang             string
	Params           PlayersTableParams
	TotalItems       int
	TotalPages       int
	IsSearchActive   bool            // Whether a search is currently being performed
	ColumnVisibility map[string]bool // Column visibility preferences
}

// PlayersTable renders the players table with HTMX functionality instead of Alpine.js
func PlayersTable(props PlayersTableProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersTable.locales.json", props.Lang)
	if err != nil {
		// Handle error, use default values
		locales = map[string]string{}
	}

	baseURL := "/app/players"

	return html.Div(
		html.Class("flex flex-col min-h-0 playersTable"),
		gomponents.Attr("data-testid", "players-table"),

		// Title and actions header
		html.Div(
			html.Class("flex justify-between items-center mb-6"),
			gomponents.If(len(props.AllPlayers) > 0,
				dropdown.Dropdown(dropdown.DropdownConfig{
					ID:         "players-actions",
					DataTestID: "players-actions-dropdown",
					ButtonText: locales["actions"],
					ButtonIcon: icons.EllipsisVertical(),
					Items: []dropdown.DropdownItem{
						{
							Text:       locales["print_players"],
							Icon:       icons.Printer(),
							DataTestID: "print-players-button",
							OnClick:    "window.open(window.location.href + (window.location.href.includes('?') ? '&' : '?') + 'print=true', '_blank', 'width=800,height=600')",
						},
						{
							Text:         locales["export_csv"],
							Icon:         icons.ArrowDownTray(),
							DataTestID:   "export-players-csv-button",
							Href:         "/app/players/export/csv",
							DisableBoost: true,
						},
					},
				}),
			),
		),

		// Empty state or table content
		func() gomponents.Node {
			if props.TotalItems == 0 {
				// Check if this is a search result with no matches or truly empty database
				if props.IsSearchActive && len(props.AllPlayers) > 0 {
					// No search results (but players exist in DB)
					return html.Div(
						html.Class("flex flex-col items-center justify-center py-12 px-4 text-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600"),
						gomponents.Attr("data-testid", "no-search-results-state"),
						gomponents.El("svg",
							html.Class("w-16 h-16 text-gray-400 dark:text-gray-500 mb-4"),
							gomponents.Attr("fill", "none"),
							gomponents.Attr("stroke", "currentColor"),
							gomponents.Attr("viewBox", "0 0 24 24"),
							gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
							gomponents.El("path",
								gomponents.Attr("stroke-linecap", "round"),
								gomponents.Attr("stroke-linejoin", "round"),
								gomponents.Attr("stroke-width", "2"),
								gomponents.Attr("d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"),
							),
						),
						html.H3(
							html.Class("text-lg font-medium text-gray-900 dark:text-white mb-2"),
							gomponents.Text(locales["no_search_results_title"]),
						),
						html.P(
							html.Class("text-gray-500 dark:text-gray-400 mb-6 max-w-md"),
							gomponents.Text(locales["no_search_results_message"]),
						),
						button.PrimaryIconButton(button.IconButtonConfig{
							ID:         "clear-search-btn",
							DataTestID: "clear-search-btn",
							ButtonType: "button",
							Text:       locales["clear_search"],
							Class:      "",
							Icon:       icons.XCircle(),
							Attrs: []gomponents.Node{
								htmx.Get("/app/players"),
								htmx.Target("#players-table-content"),
								htmx.PushURL("true"),
							},
						}),
					)
				} else {
					// No players in database at all
					return html.Div(
						html.Class("flex flex-col items-center justify-center py-12 px-4 text-center bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600"),
						gomponents.Attr("data-testid", "no-players-empty-state"),
						gomponents.El("svg",
							html.Class("w-16 h-16 text-gray-400 dark:text-gray-500 mb-4"),
							gomponents.Attr("fill", "none"),
							gomponents.Attr("stroke", "currentColor"),
							gomponents.Attr("viewBox", "0 0 24 24"),
							gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
							gomponents.El("path",
								gomponents.Attr("stroke-linecap", "round"),
								gomponents.Attr("stroke-linejoin", "round"),
								gomponents.Attr("stroke-width", "2"),
								gomponents.Attr("d", "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"),
							),
						),
						html.H3(
							html.Class("text-lg font-medium text-gray-900 dark:text-white mb-2"),
							gomponents.Text(locales["no_players_title"]),
						),
						html.P(
							html.Class("text-gray-500 dark:text-gray-400 mb-6 max-w-md"),
							gomponents.Text(locales["no_players_message"]),
						),
						button.PrimaryIconButton(button.IconButtonConfig{
							ID:         "add-first-player-btn",
							DataTestID: "add-first-player-btn",
							ButtonType: "button",
							Text:       locales["add_first_player"],
							Class:      "",
							Icon:       icons.Plus(),
							Attrs: []gomponents.Node{
								gomponents.Attr("hx-get", "/app/players/new"),
								gomponents.Attr("hx-target", "#newPlayerFormContainer"),
							},
						}),
					)
				}
			}
			// Table content when players exist
			return html.Div(
				html.Class("shadow overflow-hidden border-b border-gray-200 dark:border-gray-700 sm:rounded-lg overflow-y-auto overflow-x-auto flex-1 min-h-0 bg-white dark:bg-gray-900"),
				html.Table(
					html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-700"),
					// Table Header with sticky positioning and sorting
					html.THead(
						html.Class("bg-gray-50 dark:bg-gray-800 sticky top-0 z-10"),
						html.Tr(
							gomponents.If(props.ColumnVisibility["Name"], buildSortablePlayerHeader("name", locales["table_header_name"], props, baseURL)),
							gomponents.If(props.ColumnVisibility["Email"], buildSortablePlayerHeader("email", locales["table_header_email"], props, baseURL)),
							gomponents.If(props.ColumnVisibility["Preferred Group"], buildSortablePlayerHeader("preferredMatchGroup", locales["table_header_preferred_group"], props, baseURL)),
							gomponents.If(props.ColumnVisibility["Active"], buildSortablePlayerHeader("isActive", locales["table_header_active"], props, baseURL)),
							gomponents.If(props.ColumnVisibility["Email Notifications"], buildSortablePlayerHeader("emailNotificationsEnabled", locales["table_header_notifications"], props, baseURL)),
						),
					),
					// Table Body
					html.TBody(
						html.Class("bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700"),
						gomponents.Group(
							gomponents.Map(props.Players, func(player db.Player) gomponents.Node {
								return PlayerRow(PlayerRowProps{
									Player:           player,
									ColumnVisibility: props.ColumnVisibility,
								})
							}),
						),
					),
				),
			)
		}(),

		// Pagination Controls - only show when there are actual results to paginate
		gomponents.If(props.TotalItems > 0, tablepagination.TablePagination(tablepagination.TablePaginationProps{
			Params:      props.Params.SortablePaginationParams,
			TotalItems:  props.TotalItems,
			TotalPages:  props.TotalPages,
			Lang:        props.Lang,
			BaseURL:     baseURL,
			DataTestID:  "players-pagination",
			TargetID:    "players-table-content",
			HtmxInclude: "[name='search'], [name='sort'], [name='dir']",
		})),
	)
}

// buildSortablePlayerHeader creates a sortable table header
func buildSortablePlayerHeader(field, title string, props PlayersTableProps, baseURL string) gomponents.Node {
	isCurrentSort := props.Params.Sort == field
	direction := "asc"
	if isCurrentSort && props.Params.Direction == "asc" {
		direction = "desc"
	}

	queryParams := fmt.Sprintf("?sort=%s&dir=%s", field, direction)
	if props.Params.Search != "" {
		queryParams += "&search=" + props.Params.Search
	}
	queryParams += fmt.Sprintf("&per_page=%d", props.Params.ItemsPerPage)

	sortIcon := ""
	if isCurrentSort {
		if props.Params.Direction == "asc" {
			sortIcon = " ↑"
		} else {
			sortIcon = " ↓"
		}
	}

	return html.Th(
		gomponents.Attr("scope", "col"),
		html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
		button.SecondaryButton(button.BaseButtonConfig{
			ButtonType: "button",
			Text:       title + sortIcon,
			Class:      "hover:text-gray-700 dark:hover:text-gray-100 border-none bg-transparent shadow-none p-0",
			Attrs: []gomponents.Node{
				htmx.Get(baseURL + queryParams),
				htmx.Target("#players-table-content"),
				htmx.PushURL("true"),
			},
		}),
	)
}

// Helper functions

// buildPlayersPrintURL creates a JavaScript window.open call with current filter state
func buildPlayersPrintURL(baseURL string, params PlayersTableParams) string {
	printParams := utils.PrintURLParams{
		BaseURL:      baseURL,
		Page:         params.Page,
		ItemsPerPage: params.ItemsPerPage,
		Search:       params.Search,
		Sort:         params.Sort,
		Direction:    params.Direction,
		ExtraParams:  make(map[string]string),
	}

	return utils.BuildPrintURL(printParams)
}
