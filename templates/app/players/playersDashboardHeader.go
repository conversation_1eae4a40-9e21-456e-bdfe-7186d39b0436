package templatesappplayers

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PlayersDashboardHeaderProps contains props for the players dashboard header component
type PlayersDashboardHeaderProps struct {
	Lang           string
	AddButtonAttrs []gomponents.Node
	DataTestID     string
}

// PlayersDashboardHeader renders the dashboard header with title and add player button
func PlayersDashboardHeader(props PlayersDashboardHeaderProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersTable.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Default attributes for add button if none provided
	addButtonAttrs := props.AddButtonAttrs
	if addButtonAttrs == nil {
		addButtonAttrs = []gomponents.Node{
			gomponents.Attr("hx-get", "/app/players/new"),
			gomponents.Attr("hx-target", "#newPlayerFormContainer"),
		}
	}

	return html.Div(
		html.Div(
			html.Class("flex items-center justify-between gap-3"),

			html.H1(
				html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white"),
				gomponents.Text(locales["players"]),
			),
			button.PrimaryIconButton(button.IconButtonConfig{
				ID:         "add-new-player-btn",
				DataTestID: "add-new-player-btn",
				ButtonType: "button",
				Text:       locales["add_player"],
				Class:      "",
				Icon:       icons.Plus(),
				Attrs:      addButtonAttrs,
			}),
		),
		html.Div(
			gomponents.Attr("id", "newPlayerFormContainer"),
			gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),
		),
	)
}
