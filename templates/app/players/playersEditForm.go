package templatesappplayers

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	templatesappteams "github.com/j-em/coachpad/templates/app/teams"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type EmailReminderPreferences struct {
	Match24h        bool `json:"match_24h"`
	Match2h         bool `json:"match_2h"`
	ScheduleUpdates bool `json:"schedule_updates"`
	Results         bool `json:"results"`
}

// PlayersEditForm renders a modal form for editing an existing player.
func PlayersEditForm(lang string, playerID int32, name, email, phone string, teamID *int32, emailNotificationsEnabled bool, emailPrefs EmailReminderPreferences) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersEditForm.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	formContent := html.Form(
		htmx.Boost("true"),
		htmx.Put(fmt.Sprintf("/app/players/%d", playerID)),
		html.ID("playersEditForm"),
		gomponents.Attr("data-testid", "playersEditForm"),
		htmx.Target("#playersEditFormResult"),
		gomponents.Attr("x-data", "{fields: {}, errors: {}, errorMessages: {}, groupErrors: {}}"),
		gomponents.Attr("x-validate-form", ""),
		html.Div(
			html.Class("dark:bg-gray-800 dark:text-white flex flex-col gap-2"),
			form.FormInput(form.FormInputProps{
				Label:        locales["formNameLabel"],
				Type:         "text",
				ID:           "name",
				Name:         "name",
				Required:     true,
				ExtraClasses: "dark:bg-gray-700 dark:border-gray-600 dark:text-white",
				Value:        name,
			}),
			form.FormInput(form.FormInputProps{
				Label: locales["formEmailLabel"],
				Type:  "email",
				ID:    "email",
				Name:  "email",
				Value: email,
			}),
			form.FormInput(form.FormInputProps{
				Label: locales["formPhoneLabel"],
				Type:  "tel",
				ID:    "phone",
				Name:  "phone",
				Value: phone,
			}),
			templatesappteams.TeamSelectWithValue(lang, teamID, ""),
			form.FormCheckbox(form.FormCheckboxProps{
				Label:   locales["formEmailNotificationsEnabledLabel"],
				ID:      "emailNotificationsEnabled",
				Name:    "emailNotificationsEnabled",
				Checked: emailNotificationsEnabled,
			}),

			// Email Reminder Preferences Section
			gomponents.If(emailNotificationsEnabled,
				html.Div(
					html.Class("mt-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg"),
					html.H4(
						html.Class("text-sm font-medium text-gray-900 dark:text-white mb-3"),
						gomponents.Text(locales["emailReminderPreferencesLabel"]),
					),
					html.Div(
						html.Class("space-y-2"),
						form.FormCheckbox(form.FormCheckboxProps{
							Label:   locales["match24hLabel"],
							ID:      "emailPrefs.match24h",
							Name:    "emailPrefs.match24h",
							Checked: emailPrefs.Match24h,
						}),
						form.FormCheckbox(form.FormCheckboxProps{
							Label:   locales["match2hLabel"],
							ID:      "emailPrefs.match2h",
							Name:    "emailPrefs.match2h",
							Checked: emailPrefs.Match2h,
						}),
						form.FormCheckbox(form.FormCheckboxProps{
							Label:   locales["scheduleUpdatesLabel"],
							ID:      "emailPrefs.scheduleUpdates",
							Name:    "emailPrefs.scheduleUpdates",
							Checked: emailPrefs.ScheduleUpdates,
						}),
						form.FormCheckbox(form.FormCheckboxProps{
							Label:   locales["resultsLabel"],
							ID:      "emailPrefs.results",
							Name:    "emailPrefs.results",
							Checked: emailPrefs.Results,
						}),
					),
				),
			),
		),
		html.Div(
			html.ID("playersEditFormResult"),
		),
		html.Div(
			html.Class("flex items-center justify-end mt-4"),
			button.PrimaryButton(button.BaseButtonConfig{
				ButtonType: "submit",
				Text:       locales["formSubmit"],
				Class:      "",
			}),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered, with pre-rendered static content
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "playersEditForm",
		Title:        locales["formTitle"],
		Content:      formContent,
		IsUnclosable: false,
		DataTestID:   "players-edit-form-modal",
	})
}
