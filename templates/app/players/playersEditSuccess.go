package templatesappplayers

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PlayersEditSuccess renders a success message for editing a player.
func PlayersEditSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/players/playersEditSuccess.locales.json", lang)
	if err != nil {
		locales = map[string]string{}
	}
	return html.Div(
		html.Class("flex justify-end"),
		html.Div(
			html.Class("bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded text-sm font-medium"),
			gomponents.Text(locales["successMessage"]),
		),
	)
}
