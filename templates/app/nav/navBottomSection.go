package templatesnav

import (
	"github.com/j-em/coachpad/templates/components/notificationbell"
	"github.com/j-em/coachpad/templates/ui/button"
	gomponents "maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// NavBottomSectionProps contains the properties for the nav bottom section
type NavBottomSectionProps struct {
	Lang           string
	UserID         int32
	UnreadCount    int64
	DisconnectText string
}

// NavBottomSection renders the bottom section with notification bell and logout
func NavBottomSection(props NavBottomSectionProps) gomponents.Node {
	return html.Div(
		html.Class("mt-auto"),

		// Notification bell
		html.Div(
			html.Class("mb-2 flex justify-center"),
			notificationbell.NotificationBell(notificationbell.NotificationBellProps{
				Lang:        props.Lang,
				UserID:      props.UserID,
				UnreadCount: props.UnreadCount,
				ID:          "notification-bell-sidebar",
				HxTarget:    "#notification-bell-sidebar",
			}),
		),

		// Logout button
		button.PrimaryButton(button.BaseButtonConfig{
			ButtonType: "submit",
			Text:       props.DisconnectText,
			Class:      "bg-red-600 hover:bg-red-700 border-red-600 focus:ring-red-500 text-sm font-bold shadow",
			Attrs: []gomponents.Node{
				htmx.Post("/app/logout"),
				gomponents.Attr("@click", "$store.appStore.closeAllModals()"),
			},
		}),
	)
}
