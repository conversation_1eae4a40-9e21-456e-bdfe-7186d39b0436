package templatesnav

import (
	"github.com/j-em/coachpad/templates/ui/button"
	gomponents "maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// NavToggleButton creates either an expand or collapse button based on the isExpand parameter
func NavToggleButton(isExpand bool, attrs ...gomponents.Node) gomponents.Node {
	icon := heroicons.Bars4()
	action := "true"
	testId := "nav-expand-button"
	class := "mr-4"

	if !isExpand {
		icon = heroicons.ArrowLeft()
		action = "false"
		testId = "nav-collapse-button"
		class = "ml-4"
	}

	finalAttrs := []gomponents.Node{
		html.ID(testId),
		gomponents.Attr("data-testid", testId),
		htmx.Put("/app/settings/isSidebarOpen"),
		htmx.Swap("none"),
		htmx.Vals(`{"isSidebarOpen": ` + action + `}`),
		gomponents.Attr("@click", "isSidebarOpen = "+action+"; $store.appStore.closeAllModals()"),
	}

	finalAttrs = append(finalAttrs, attrs...)

	return button.SecondaryIconButton(button.IconButtonConfig{
		ID:         testId,
		DataTestID: testId,
		ButtonType: "button",
		Text:       "",
		Icon:       icon,
		Size:       "small",
		Attrs:      finalAttrs,
		Class:      class,
	})
}
