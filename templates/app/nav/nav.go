package templatesnav

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	gomponents "maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// NavProps contains all properties needed for the Nav component
type NavProps struct {
	ActiveLink    string
	Seasons       []db.Season
	DataTestID    string
	Lang          string
	IsDevelopment bool
	UserID        int32
	UnreadCount   int64
}

// Nav renders the main navigation component
func Nav(props NavProps) gomponents.Node {
	locales := i18n.MustLoadTemplateLocales("./templates/app/nav/nav.locales.json", props.Lang)

	return html.Nav(
		htmx.Boost("true"),
		html.Class("flex overflow-hidden border-r border-gray-200 pb-4 h-full w-full md:w-[300px] overflow-y-auto"),
		gomponents.Attr("x-cloak"),
		gomponents.Attr("data-testid", props.DataTestID),
		gomponents.Attr("@scroll", "$dispatch('close-all-dropdowns')"),
		html.Div(
			html.Class("max-w-7xl mx-auto px-4 pt-2 flex flex-col flex-1"),

			// Header with logo and toggle
			NavHeader(props.Lang),

			// Main navigation items
			NavMainContent(NavMainContentProps{
				ActiveLink:    props.ActiveLink,
				Locales:       locales,
				IsDevelopment: props.IsDevelopment,
				Lang:          props.Lang,
				Seasons:       props.Seasons,
			}),

			// Help dropdown
			NavHelpDropdown(locales),

			// Bottom section with notification bell and logout
			NavBottomSection(NavBottomSectionProps{
				Lang:           props.Lang,
				UserID:         props.UserID,
				UnreadCount:    props.UnreadCount,
				DisconnectText: locales["disconnect"],
			}),
		),
	)
}
