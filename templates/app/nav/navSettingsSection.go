package templatesnav

import (
	gomponents "maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
)

// NavSettingsSectionProps contains the properties for the settings section
type NavSettingsSectionProps struct {
	ActiveLink string
	Locales    map[string]string
}

// NavSettingsSection renders the settings navigation section
func NavSettingsSection(props NavSettingsSectionProps) gomponents.Node {
	return NavSection(NavSectionConfig{
		Title:     props.Locales["settings"],
		TitleIcon: heroicons.Cog(),
		Items: []NavSectionItem{
			{
				Href:   "/app/settings/user",
				Text:   props.Locales["user_settings"],
				Icon:   heroicons.UserCircle(),
				Active: "/app/settings/user" == props.ActiveLink,
			},
			{
				Href:   "/app/settings/app",
				Text:   props.Locales["app_settings"],
				Icon:   heroicons.Cog(),
				Active: "/app/settings/app" == props.ActiveLink,
			},
			{
				Href:   "/app/settings/subscription",
				Text:   props.Locales["subscription"],
				Icon:   heroicons.CreditCard(),
				Active: "/app/settings/subscription" == props.ActiveLink,
			},
			{
				Href:   "/app/settings/api-access",
				Text:   props.Locales["api_access"],
				Icon:   heroicons.Key(),
				Active: "/app/settings/api-access" == props.ActiveLink,
			},
		},
	})
}
