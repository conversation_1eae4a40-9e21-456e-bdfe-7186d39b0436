package templatesnav

import (
	gomponents "maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// NavSectionItem represents a single menu item within a section
type NavSectionItem = NavLinkConfig

// NavSectionConfig represents the configuration for a navigation section
type NavSectionConfig struct {
	Title     string           // Section title (optional)
	TitleHref string           // URL for section title if clickable (optional)
	TitleIcon gomponents.Node  // Icon for section title (optional)
	Items     []NavSectionItem // Items within the section
}

// NavSection renders a navigation section with optional title and nested items
func NavSection(config NavSectionConfig) gomponents.Node {
	// Create title node if provided
	var titleNode gomponents.Node
	if config.Title != "" {
		if config.TitleHref != "" {
			// Clickable section title - reuse NavLink
			titleNode = NavLink(config.TitleHref, config.Title, config.TitleIcon, false)
		} else {
			// Non-clickable section title
			titleNode = html.Div(
				html.Class("px-3 py-2 text-sm font-semibold text-gray-600 flex gap-2 items-center"),
				gomponents.If(config.TitleIcon != nil, html.Span(
					html.Class("*:w-7 h-7"),
					config.TitleIcon,
				)),
				gomponents.Text(config.Title),
			)
		}
	}

	// Create item nodes
	itemNodes := make([]gomponents.Node, len(config.Items))
	for i, item := range config.Items {
		item.Nested = true
		itemNodes[i] = NavLinkWithConfig(item)
	}

	return html.Div(
		html.Class("flex flex-col space-y-1"),
		titleNode,
		gomponents.Group(itemNodes),
	)
}
