package templatesnav

import (
	"github.com/j-em/coachpad/templates/components/applogo"
	gomponents "maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// NavHeader renders the navigation header with logo and toggle button
func NavHeader(lang string) gomponents.Node {
	return html.Div(
		html.Class("flex justify-center items-center w-full pb-4 pt-4 pl-4"),
		html.Div(
			html.Class("w-[175px] flex items-center mr-auto"),
			applogo.AppLogo(lang),
		),
		NavToggleButton(false, gomponents.Attr("x-show", "isSidebarOpen")),
	)
}
