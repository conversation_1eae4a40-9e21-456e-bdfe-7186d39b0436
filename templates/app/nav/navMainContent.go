package templatesnav

import (
	"github.com/j-em/coachpad/db"
	gomponents "maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
)

// NavMainContentProps contains the properties for the main navigation content
type NavMainContentProps struct {
	ActiveLink    string
	Locales       map[string]string
	IsDevelopment bool
	Lang          string
	Seasons       []db.Season
}

// NavMainContent renders the main navigation items
func NavMainContent(props NavMainContentProps) gomponents.Node {
	return NavItems(
		NavLink("/app/home", props.Locales["home"], heroicons.Home(), "/app/home" == props.ActiveLink),
		NavLink("/app/players", props.Locales["players"], heroicons.UserGroup(), "/app/players" == props.ActiveLink),
		NavLink("/app/teams", props.Locales["teams"], heroicons.UserGroup(), "/app/teams" == props.ActiveLink),
		NavLink("/app/spending", props.Locales["spending"], heroicons.CreditCard(), "/app/spending" == props.ActiveLink),
		NavLink("/app/seasons/new", props.Locales["new_season"], heroicons.PlusCircle(), "/app/seasons/new" == props.ActiveLink),
		gomponents.If(props.IsDevelopment, NavLink("/app/dev", props.Locales["development"], heroicons.WrenchScrewdriver(), "/app/dev" == props.ActiveLink)),
		NavSeasonsList(props.Lang, props.Seasons),
	)
}
