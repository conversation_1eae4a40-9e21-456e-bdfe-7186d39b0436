package templatesnav

import (
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/link"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// NavSeasonLink renders a single clickable season link for the sidebar with hover action menu
func NavSeasonLink(lang string, season db.Season) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/nav/navSeasonLink.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	seasonPath := "/app/seasons/" + strconv.Itoa(int(season.ID))
	seasonIdStr := strconv.Itoa(int(season.ID))

	return html.Div(
		html.Class("relative group min-h-[45px] px-2 flex items-center justify-between rounded-md text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white transition-colors duration-200"),
		gomponents.Attr("x-data", "{ isMenuVisible: false, isDropdownOpen: false, showMenu() { this.isMenuVisible = true; }, hideMenu() { if (!this.isDropdownOpen) this.isMenuVisible = false; } }"),
		gomponents.Attr("@mouseenter", "showMenu()"),
		gomponents.Attr("@mouseleave", "hideMenu()"),
		gomponents.Attr("@dropdown-opened.window", "if ($event.detail.id === 'season-action-"+seasonIdStr+"') { isDropdownOpen = true; }"),
		gomponents.Attr("@dropdown-closed.window", "if ($event.detail.id === 'season-action-"+seasonIdStr+"') { isDropdownOpen = false; hideMenu(); }"),
		gomponents.Attr(":class", "{ 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600': $store.appStore.activeLink === '"+seasonPath+"' }"),
		gomponents.Attr("hx-target", "#app-layout-content"),
		gomponents.Attr("hx-swap", "innerHTML"),

		// Season link
		link.Link(link.LinkProps{
			Href:        seasonPath,
			Text:        season.Name,
			Class:       "text-sm font-medium block flex-1 h-full flex items-center",
			AriaLabel:   season.Name,
			DataTestID:  "nav-season-link-" + seasonIdStr,
			XOnClick:    "$store.appStore.setActiveLinkAndClose('" + seasonPath + "')",
			XDataActive: "$store.appStore.activeLink === '" + seasonPath + "'",
		}),

		// Action dropdown trigger (only visible on hover)
		html.Div(
			gomponents.Attr("x-show", "isMenuVisible"),
			gomponents.Attr("x-transition:enter", "transition ease-out duration-200"),
			gomponents.Attr("x-transition:enter-start", "opacity-0"),
			gomponents.Attr("x-transition:enter-end", "opacity-100"),
			gomponents.Attr("x-transition:leave", "transition ease-in duration-200"),
			gomponents.Attr("x-transition:leave-start", "opacity-100"),
			gomponents.Attr("x-transition:leave-end", "opacity-0"),
			gomponents.Attr("@click.stop"),

			dropdown.Dropdown(dropdown.DropdownConfig{
				ID:         "season-action-" + seasonIdStr,
				DataTestID: "season-action-dropdown-" + seasonIdStr,
				ButtonIcon: icons.EllipsisVertical(),
				Class:      "relative",
				Items: []dropdown.DropdownItem{
					{
						Text:       locales["rename"],
						Icon:       icons.PencilSquare(),
						XHxGet:     "'/app/seasons/" + seasonIdStr + "/rename-modal'",
						HxTarget:   "#modal-body-container",
						DataTestID: "rename-season-" + seasonIdStr,
					},
					{
						Text:       locales["delete"],
						Icon:       icons.Trash(),
						XHxGet:     "'/app/seasons/" + seasonIdStr + "/delete-modal'",
						HxTarget:   "#modal-body-container",
						DataTestID: "delete-season-" + seasonIdStr,
					},
				},
			}),
		),
	)
}
