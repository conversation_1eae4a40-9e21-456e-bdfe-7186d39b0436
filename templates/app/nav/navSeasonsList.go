package templatesnav

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/utils"
	gomponents "maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// List of seasons in the navigation bar
func NavSeasonsList(lang string, seasons []db.Season) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/nav/navSeasonsList.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		html.Class("container mx-auto px-4 py-8 flex flex-col min-h-[230px]"),
		htmx.Get("/app/nav/seasons"),
		htmx.Target("this"),
		htmx.Swap("outerHTML"),
		htmx.Trigger(utils.HXEventUpdateSeason+" from:body"),
		html.Div(
			html.Class("flex justify-between items-center mb-6"),
			html.H1(
				html.Class("text-xl font-bold"),
				gomponents.Text(locales["seasons"]),
			),
		),
		gomponents.If(len(seasons) > 0,
			html.Div(
				html.Class("flex flex-col max-h-96 overflow-y-auto"),
				gomponents.Attr("data-testid", "sidebar-seasons-list"),
				gomponents.Map(seasons, func(season db.Season) gomponents.Node {
					return NavSeasonLink(lang, season)
				}),
			),
		),
		gomponents.If(
			len(seasons) == 0,
			html.Div(
				html.Class("text-center py-12"),
				html.P(
					html.Class("text-gray-600 mb-4"),
					gomponents.Text(locales["no_seasons_yet"]),
				),
			),
		),
	)
}
