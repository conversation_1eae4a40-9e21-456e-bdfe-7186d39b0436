package templatesnav

import (
	"strings"

	gomponents "maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// NavLinkConfig contains configuration for a navigation link
type NavLinkConfig struct {
	Href   string
	Text   string
	Icon   gomponents.Node
	Active bool
	Nested bool // If true, uses nested styling (smaller icon, left padding)
}

// NavLink renders a navigation link with icon and active state styling
func NavLink(href, text string, icon gomponents.Node, active bool) gomponents.Node {
	return NavLinkWithConfig(NavLinkConfig{
		Href:   href,
		Text:   text,
		Icon:   icon,
		Active: active,
		Nested: false,
	})
}

// NavLinkWithConfig renders a navigation link using configuration struct
func NavLinkWithConfig(config NavLinkConfig) gomponents.Node {
	var className, iconClass string

	if config.Nested {
		className = "pl-6 pr-3 py-2 rounded-md text-sm font-medium block inline-flex gap-2 items-center loading-pulse text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white transition-colors duration-200"
		iconClass = "*:w-5 h-5"
	} else {
		className = "px-3 py-2 rounded-md text-sm font-medium block inline-flex gap-2 items-center loading-pulse text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white transition-colors duration-200"
		iconClass = "*:w-7 h-7"
	}

	if config.Active {
		className += " bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
		// Add aria-current="page" for accessibility and Playwright tests
		return html.A(
			html.Href(config.Href),
			html.Class(className),
			gomponents.Attr("data-testid", generateTestID(config.Href)),
			gomponents.Attr("hx-sync", "closest nav:replace"),
			gomponents.Attr(":class", "{ 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600': $store.appStore.activeLink === '"+config.Href+"' }"),
			gomponents.Attr(":data-active", "$store.appStore.activeLink === '"+config.Href+"'"),
			gomponents.Attr("hx-target", "#app-layout-content"),
			gomponents.Attr(":aria-current", "$store.appStore.activeLink === '"+config.Href+"' ? 'page' : null"),
			html.Span(
				html.Class(iconClass),
				config.Icon,
			),
			gomponents.Text(config.Text),
		)
	}

	// Generate test ID from href path
	testID := generateTestID(config.Href)

	return html.A(
		html.Href(config.Href),
		html.Class(className),
		gomponents.Attr("data-testid", testID),
		gomponents.Attr("hx-sync", "closest nav:replace"),
		gomponents.Attr("@click", "$store.appStore.closeAllMenus()"),
		gomponents.Attr(":class", "{ 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600': $store.appStore.activeLink === '"+config.Href+"' }"),
		gomponents.Attr(":data-active", "$store.appStore.activeLink === '"+config.Href+"'"),
		gomponents.Attr("hx-target", "#app-layout-content"),
		gomponents.Attr(":aria-current", "$store.appStore.activeLink === '"+config.Href+"' ? 'page' : null"),

		html.Span(
			html.Class(iconClass),
			config.Icon,
		),
		gomponents.Text(config.Text),
	)
}

// generateTestID creates a test ID from the href path
func generateTestID(href string) string {
	// Remove leading slash and /app prefix
	path := strings.TrimPrefix(href, "/app/")
	path = strings.TrimPrefix(path, "/")

	// Replace slashes with dashes and create test ID
	testID := strings.ReplaceAll(path, "/", "-")

	// Handle special cases
	if testID == "" || testID == "home" {
		testID = "home"
	}

	return "nav-" + testID + "-link"
}
