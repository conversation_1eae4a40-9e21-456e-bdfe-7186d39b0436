package signup

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatescomponents "github.com/j-em/coachpad/templates/components"
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"github.com/j-em/coachpad/templates/ui/datepicker"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/infobox"
	templatesuilangselect "github.com/j-em/coachpad/templates/ui/langselect"
	uiselect "github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

func SignUp(lang string, user *db.User) gomponents.Node {
	locales := pagebuilder.NewPage("/signup").
		WithLocaleBase("app/signup/signup").
		LoadLocalesOnly(lang)

	// Load header locales for signin/signup buttons
	headerLocales, err := i18n.LoadTemplateLocales("./templates/components/public_header.locales.json", lang)
	if err != nil {
		headerLocales = make(map[string]string)
	}

	// Create user info box if user is provided
	var userInfoBox gomponents.Node
	if user != nil {
		userInfoBox = infobox.InfoBox(infobox.InfoBoxConfig{
			ID:          "signup-user-info",
			UserName:    user.Name,
			Style:       "info",
			Lang:        lang,
			DataTestID:  "signup-user-info-box",
			Dismissible: true,
		})
	}

	return layouts.HTMLLayout(locales["page_title"], html.Div(
		html.Class("flex flex-col h-svh"),
		templatescomponents.PublicHeader(lang, html.Div(
			html.Class("flex items-center gap-3 flex-col md:flex-row w-full md:w-auto"),
			// Display user info box if provided (first)
			gomponents.If(userInfoBox != nil,
				html.Div(
					html.Class("w-full md:w-auto"),
					userInfoBox,
				),
			),
			// Then the buttons
			templatescomponents.PublicHeaderButton(headerLocales["sign_in"], "/signin"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_up"], "/signup"),
		)),
		html.Form(
			html.Method("POST"),
			html.Action("/signup"),
			htmx.Boost("true"),
			htmx.Target("#signup-form-container"),
			html.Class("flex flex-col min-h-0 flex-1 overflow-y-auto"),
			gomponents.Attr("x-data", "multiStepSignup()"),
			// Add data attributes for JS to read localized text
			html.DataAttr("step-title1", locales["step_title_1"]),
			html.DataAttr("step-title2", locales["step_title_2"]),
			html.DataAttr("step-title3", locales["step_title_3"]),
			html.DataAttr("step-title4", locales["step_title_4"]),
			html.DataAttr("step-of", locales["step_of"]),
			html.Div(
				html.ID("signup-form-container"),
				html.Div(
					html.ID("form-error"),
					html.Class("text-red-500 p-4 text-center hidden"),
				),
				// Progress Indicator
				html.Div(
					html.Class("px-5 pt-4 pb-2"),
					html.Div(
						html.Class("flex items-center justify-between mb-4"),
						html.Template(
							gomponents.Attr("x-for", "step in [1, 2, 3, 4]"),
							gomponents.Attr("x-key", "step"),
							html.Div(
								html.Class("flex items-center"),
								gomponents.Attr(":class", "step < 4 ? 'flex-1' : ''"),
								html.Div(
									html.Class("flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium step-indicator"),
									gomponents.Attr(":class", `{
										'bg-blue-600 text-white': step < currentStep,
										'bg-blue-600 text-white border-2 border-blue-200': step === currentStep,
										'bg-gray-200 text-gray-500': step > currentStep
									}`),
									gomponents.Attr("x-text", "step"),
								),
								html.Template(
									gomponents.Attr("x-if", "step < 4"),
									html.Div(
										html.Class("flex-1 h-0.5 mx-2 step-transition"),
										gomponents.Attr(":class", "step < currentStep ? 'bg-blue-600' : 'bg-gray-200'"),
									),
								),
							),
						),
					),
					html.Div(
						html.Class("text-center"),
						html.H2(
							html.Class("text-lg font-semibold"),
							gomponents.Attr("x-text", "getStepTitle()"),
						),
						html.P(
							html.Class("text-sm text-gray-600 mt-1"),
							gomponents.Attr("x-text", "getStepOfText()"),
						),
					),
				),
				// Step Content
				html.Div(
					html.Class("flex-1 px-5 pt-2 pb-[100px] overflow-y-auto"),
					// Step 1: Basic Information
					html.Div(
						gomponents.Attr("x-show", "currentStep === 1"),
						html.Class("max-w-md mx-auto space-y-4 step-content"),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:       locales["username"],
								Type:        "text",
								ID:          "name",
								Name:        "name",
								Required:    true,
								Placeholder: "Enter your username",
								XModel:      "formData.name",
							}),
						),
						html.Div(
							form.UniqueEmailInput(form.UniqueEmailInputProps{
								Label:    locales["email"],
								ID:       "email",
								Name:     "email",
								Required: true,
								XModel:   "formData.email",
							}),
						),
						html.Div(
							html.Label(
								html.For("lang"),
								html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"),
								gomponents.Text(locales["language"]),
							),
							templatesuilangselect.LanguageSelect(templatesuilangselect.LanguageSelectConfig{
								ID:           "lang",
								Name:         "lang",
								DefaultValue: lang,
								XModel:       "formData.lang",
							}),
							html.Div(
								html.Class("text-red-500 text-sm mt-1"),
								gomponents.Attr("x-show", "errors['lang']"),
								gomponents.Attr("x-text", "errorMessages['lang']"),
							),
						),
					),
					// Step 2: Personal Details
					html.Div(
						gomponents.Attr("x-show", "currentStep === 2"),
						html.Class("max-w-md mx-auto space-y-4 step-content"),
						html.Div(
							uiselect.CountrySelect(uiselect.CountrySelectProps{
								Lang: lang,
								SelectProps: uiselect.SelectProps{
									ID:       "country",
									Name:     "country",
									Required: true,
									XModel:   "formData.country",
								},
							}),
							html.Div(
								html.Class("text-red-500 text-sm mt-1"),
								gomponents.Attr("x-show", "errors['country']"),
								gomponents.Attr("x-text", "errorMessages['country']"),
							),
						),
						html.Div(
							datepicker.TeleportDatePickerComponent(datepicker.TeleportDatePickerProps{
								UniqueId:    "signup-birthday",
								Class:       "w-full",
								Label:       locales["birthday"],
								Placeholder: "DD/MM/YYYY",
								Name:        "birthday",
								Lang:        lang,
							}),
						),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:  locales["phone"],
								Type:   "tel",
								ID:     "phone",
								Name:   "phone",
								XModel: "formData.phone",
							}),
						),
					),
					// Step 3: Security Setup
					html.Div(
						gomponents.Attr("x-show", "currentStep === 3"),
						html.Class("max-w-md mx-auto space-y-4 step-content"),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:            locales["password"],
								Type:             "password",
								ID:               "password",
								Name:             "password",
								Required:         true,
								MinLength:        8,
								MinLengthMessage: "Must be at least 8 characters",
								Placeholder:      locales["password_placeholder"],
								XModel:           "formData.password",
							}),
						),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:               locales["confirm_password"],
								Type:                "password",
								ID:                  "confirm-password",
								Name:                "confirm-password",
								Required:            true,
								MinLength:           8,
								Placeholder:         locales["confirm_password_placeholder"],
								ValidateWith:        "password",
								ValidateType:        "match",
								RelatedErrorMessage: locales["passwords_do_not_match"],
								XModel:              "formData['confirm-password']",
							}),
						),
					),
					// Step 4: Confirmation/Review
					html.Div(
						gomponents.Attr("x-show", "currentStep === 4"),
						html.Class("max-w-md mx-auto space-y-6 step-content"),
						html.Div(
							html.Class("text-center mb-6"),
							html.H3(
								html.Class("text-xl font-semibold mb-2"),
								gomponents.Text(locales["review_title"]),
							),
							html.P(
								html.Class("text-gray-600 dark:text-gray-400"),
								gomponents.Text(locales["review_subtitle"]),
							),
						),
						// Basic Information Section
						html.Div(
							html.Class("bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700"),
							html.H4(
								html.Class("font-semibold mb-3 flex items-center justify-between"),
								gomponents.Text(locales["basic_info"]),
								html.Button(
									html.Type("button"),
									html.Class("text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"),
									gomponents.Attr("@click", "goToStep(1)"),
									gomponents.Text(locales["edit"]),
								),
							),
							html.Div(
								html.Class("space-y-2 text-sm"),
								html.Div(
									html.Class("flex justify-between"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["username"]+":")),
									html.Span(html.Class("dark:text-gray-200"), gomponents.Attr("x-text", "formData.name")),
								),
								html.Div(
									html.Class("flex justify-between"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["email"]+":")),
									html.Span(html.Class("dark:text-gray-200"), gomponents.Attr("x-text", "formData.email")),
								),
								html.Div(
									html.Class("flex justify-between"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["language"]+":")),
									html.Span(html.Class("dark:text-gray-200"), gomponents.Attr("x-text", "typeof formData.lang === 'string' ? formData.lang : (formData.lang && formData.lang.title) || ''")),
								),
							),
						),
						// Personal Details Section
						html.Div(
							html.Class("bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700"),
							html.H4(
								html.Class("font-semibold mb-3 flex items-center justify-between"),
								gomponents.Text(locales["personal_details"]),
								html.Button(
									html.Type("button"),
									html.Class("text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"),
									gomponents.Attr("@click", "goToStep(2)"),
									gomponents.Text(locales["edit"]),
								),
							),
							html.Div(
								html.Class("space-y-2 text-sm"),
								html.Div(
									html.Class("flex justify-between"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["country"]+":")),
									html.Span(html.Class("dark:text-gray-200"), gomponents.Attr("x-text", "typeof formData.country === 'string' ? formData.country : (formData.country && formData.country.title) || ''")),
								),
								html.Div(
									html.Class("flex justify-between"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["birthday"]+":")),
									html.Span(html.Class("dark:text-gray-200"), gomponents.Attr("x-text", "formData.birthday || 'Not provided'")),
								),
								html.Div(
									html.Class("flex justify-between"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["phone"]+":")),
									html.Span(html.Class("dark:text-gray-200"), gomponents.Attr("x-text", "formData.phone || 'Not provided'")),
								),
							),
						),
						// Security Information Section
						html.Div(
							html.Class("bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700"),
							html.H4(
								html.Class("font-semibold mb-3 flex items-center justify-between"),
								gomponents.Text(locales["security_info"]),
								html.Button(
									html.Type("button"),
									html.Class("text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"),
									gomponents.Attr("@click", "goToStep(3)"),
									gomponents.Text(locales["edit"]),
								),
							),
							html.Div(
								html.Class("text-sm"),
								html.Div(
									html.Class("flex justify-between items-center"),
									html.Span(html.Class("text-gray-600 dark:text-gray-400"), gomponents.Text(locales["password"]+":")),
									html.Span(
										html.Class("flex items-center text-green-600 dark:text-green-400"),
										html.Span(gomponents.Text(locales["password_configured"])),
										html.Span(html.Class("ml-1"), gomponents.Text("✓")),
									),
								),
							),
						),
					),
				),
				// Navigation Buttons
				html.Div(
					html.Class("fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-5 py-4"),
					html.Div(
						html.Class("flex justify-between max-w-lg mx-auto"),
						html.Button(
							html.Type("button"),
							html.Class("px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"),
							gomponents.Attr("@click", "prevStep()"),
							gomponents.Attr(":disabled", "currentStep === 1"),
							gomponents.Text(locales["previous"]),
						),
						html.Template(
							gomponents.Attr("x-if", "currentStep < totalSteps"),
							html.Button(
								html.Type("button"),
								html.Class("px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"),
								gomponents.Attr("@click", "nextStep()"),
								gomponents.Attr(":disabled", "!canProceedToNextStep()"),
								gomponents.Text(locales["next"]),
							),
						),
						html.Template(
							gomponents.Attr("x-if", "currentStep === totalSteps"),
							html.Button(
								html.Type("submit"),
								html.Class("px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 relative"),
								html.Span(
									gomponents.Text(locales["confirm_signup"]),
								),
								html.Span(
									html.Class("htmx-indicator absolute"),
								),
							),
						),
					),
				),
			),
		),
	))
}
