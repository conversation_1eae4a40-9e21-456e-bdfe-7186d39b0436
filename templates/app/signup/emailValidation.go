package signup

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// EmailValidationRequired renders error message when email is required
func EmailValidationRequired(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signup/emailValidation.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Span(
		html.Class("text-red-500"),
		gomponents.Text(locales["email_required"]),
	)
}

// EmailValidationAvailable renders success message when email is available
func EmailValidationAvailable(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signup/emailValidation.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Span(
		html.Class("text-green-600"),
		gomponents.Text(locales["email_available"]),
	)
}

// EmailValidationTaken renders error message when email is already registered
func EmailValidationTaken(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signup/emailValidation.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Span(
		html.Class("text-red-500"),
		gomponents.Text(locales["email_taken"]),
	)
}

// EmailValidationInvalid renders error message when email format is invalid
func EmailValidationInvalid(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signup/emailValidation.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Span(
		html.Class("text-red-500"),
		gomponents.Text(locales["email_invalid"]),
	)
}
