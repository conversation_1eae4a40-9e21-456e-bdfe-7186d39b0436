package signup

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func SignupError(lang string, errorMessage string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/signup/signup.locales.json", lang)
	if err != nil {
		// Handle error, perhaps log it or use default values
		locales = make(map[string]string)
	}
	return html.Div(
		html.Class("w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md mt-10 mx-auto"),
		html.H2(
			html.Class("mt-6 text-center text-2xl font-bold text-gray-900"),
			gomponents.Text(locales["signup_error"]),
		),
		html.P(
			html.Class("mt-2 text-center text-gray-600"),
			gomponents.Text(errorMessage),
		),
		html.Div(
			html.Class("mt-6 flex justify-center"),
			html.A(
				html.Href("/signup"),
				html.Class("px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"),
				gomponents.Text(locales["try_again"]),
			),
		),
	)
}
