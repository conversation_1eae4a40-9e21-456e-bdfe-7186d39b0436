package templatesapphome

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/card"
	"github.com/j-em/coachpad/templates/ui/matchDisplay"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// UserUpcomingMatches renders a card displaying upcoming matches
func UserUpcomingMatches(lang string, matches []db.Match) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/home/<USER>", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	var content gomponents.Node

	if matches == nil || len(matches) == 0 {
		// No matches to display
		content = html.Div(
			html.Class("p-6 text-center text-gray-500"),
			gomponents.Text(locales["no_upcoming_matches"]),
		)
	} else {
		// Use the new match display component
		content = matchDisplay.MatchTable(matchDisplay.MatchDisplayProps{
			Matches: matches,
			Locales: locales,
			Format:  matchDisplay.CompactTableFormat,
		})
	}

	return card.Card(card.CardProps{
		Title:   locales["upcoming_matches"],
		Content: content,
		Footer: html.Div(
			html.Class("text-right"),
			html.A(
				html.Href("/app/matches"),
				html.Class("text-blue-600 hover:text-blue-800"),
				gomponents.Text(locales["view_all_matches"]),
			),
		),
	})
}
