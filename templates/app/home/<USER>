package templatesapphome

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type HomePageProps struct {
	Lang          string
	UserID        int32
	ActiveLink    string
	Matches       []db.Match
	Players       []db.Player
	Seasons       []db.Season
	IsSidebarOpen bool
	UnreadCount   int64
}

func HomePage(props HomePageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/home").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
			UnreadCount:   props.UnreadCount,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return HomePageContent(ctx.Lang, HomePageContentProps{
				Lang:    props.Lang,
				UserID:  props.UserID,
				Players: props.Players,
				Seasons: props.Seasons,
				Matches: props.Matches,
			})
		})
}
