package templatesapphome

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/card"
	"github.com/j-em/coachpad/templates/ui/matchDisplay"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type HomePageContentProps struct {
	Lang    string
	UserID  int32
	Players []db.Player
	Seasons []db.Season
	Matches []db.Match
}

func renderUpcomingMatchesCard(locales map[string]string, matches []db.Match) gomponents.Node {
	var content gomponents.Node

	if matches == nil || len(matches) == 0 {
		// No matches to display
		content = html.Div(
			html.Class("p-6 text-center text-gray-500"),
			gomponents.Text(locales["no_upcoming_matches"]),
		)
	} else {
		// Use the new match display component with custom format
		format := matchDisplay.DefaultTableFormat
		format.ShowGroup = true // Show group column for home page

		content = matchDisplay.MatchTable(matchDisplay.MatchDisplayProps{
			Matches: matches,
			Locales: locales,
			Format:  format,
		})
	}

	return card.Card(card.CardProps{
		Title:   locales["upcoming_matches"],
		Content: content,
		Footer: html.Div(
			html.Class("text-right"),
			html.A(
				html.Href("/app/matches"),
				html.Class("text-blue-600 hover:text-blue-800"),
				gomponents.Text(locales["view_all_matches"]),
			),
		),
	})
}

// HomePageContent renders the main content for the home page (welcome, matches, stats)
func HomePageContent(lang string, props HomePageContentProps) gomponents.Node {
	locales := i18n.MustLoadTemplateLocales("./templates/app/home/<USER>", lang)

	return html.Div(
		html.Class("h-full overflow-y-auto"),
		html.Div(
			html.Class("mx-auto px-4 py-6"),
			html.H1(
				html.Class("text-3xl font-bold mb-6"),
				gomponents.Text(locales["welcome"]),
			),
			html.Div(
				html.Class("grid grid-cols-1 md:grid-cols-2 gap-6 mb-6"),
				renderUpcomingMatchesCard(locales, props.Matches),
				UserHomeStats(props.Lang, props.Players, props.Seasons),
			),
		),
	)
}
