package templatesapphome

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/card"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// UserHomeStats renders a card displaying user statistics (total players and seasons)
func UserHomeStats(lang string, players []db.Player, seasons []db.Season) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/home/<USER>", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Create the content for the stats card
	content := html.Div(
		html.Class("space-y-4"),
		html.Div(
			html.Class("flex items-center justify-between py-2"),
			html.Div(
				html.Class("text-gray-600"),
				gomponents.Text(locales["total_players"]),
			),
			html.Div(
				html.Class("font-bold text-xl"),
				gomponents.Text(fmt.Sprintf("%d", len(players))),
			),
		),
		html.Hr(html.Class("border-gray-200")),
		html.Div(
			html.Class("flex items-center justify-between py-2"),
			html.Div(
				html.Class("text-gray-600"),
				gomponents.Text(locales["total_seasons"]),
			),
			html.Div(
				html.Class("font-bold text-xl"),
				gomponents.Text(fmt.Sprintf("%d", len(seasons))),
			),
		),
	)

	// Render the card using the UI card component
	return card.Card(card.CardProps{
		Title:   locales["stats_title"],
		Content: content,
	})
}
