package spending

import (
	"fmt"
	"math"
	"math/big"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"github.com/j-em/coachpad/templates/ui/inlinedatepicker"
	modalwrappers "github.com/j-em/coachpad/templates/ui/modal"
	"github.com/j-em/coachpad/templates/ui/toast"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"github.com/j-em/coachpad/utils/testid"
	"github.com/jackc/pgx/v5/pgtype"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// Helper function to convert pgtype.Numeric to float64
func getNumericValue(n pgtype.Numeric) float64 {
	if !n.Valid {
		return 0
	}

	if n.Int == nil {
		return 0
	}

	// Convert to float64 with proper decimal scaling
	bigFloat := new(big.Float).SetInt(n.Int)
	if n.Exp != 0 {
		// Apply the exponent (negative exp means divide by 10^exp)
		exp := new(big.Float).SetFloat64(10)
		for i := int32(0); i < -n.Exp; i++ {
			bigFloat.Quo(bigFloat, exp)
		}
	}

	val, _ := bigFloat.Float64()
	// Round to 2 decimal places to avoid floating point precision issues
	return math.Round(val*100) / 100
}

type SpendingPageProps struct {
	Spending      []db.Spending
	Lang          string
	Seasons       []db.Season
	IsSidebarOpen bool
	UserID        int32
	UnreadCount   int64
}

type SpendingPageContentProps struct {
	Spending []db.Spending
	Lang     string
}

func SpendingPage(props SpendingPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/spending").
		WithLocaleBase("app/spending/spending").
		WithTitle("page_title").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
			UserID:        props.UserID,
			UnreadCount:   props.UnreadCount,
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return SpendingPageContent(SpendingPageContentProps{
				Spending: props.Spending,
				Lang:     ctx.Lang,
			})
		})
}

func SpendingPageContent(props SpendingPageContentProps) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/spending/spending.locales.json", props.Lang)
	if err != nil {
		// Log error and continue with empty locales - the locale file should always exist
		fmt.Printf("Failed to load spending locales: %v\n", err)
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("h-full flex flex-col gap-3 p-4 overflow-auto"),
		html.Head(
			html.TitleEl(
				gomponents.Text(locales["page_title"]),
			),
		),
		html.H1(
			html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white mb-4"),
			gomponents.Attr("data-testid", "spending-page-header"),
			gomponents.Text(locales["page_title"]),
		),

		// Action buttons container
		html.Div(
			html.Class("flex gap-2 mb-4"),
			html.Button(
				html.Type("button"),
				html.Class("inline-block bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"),
				gomponents.Attr("data-testid", "add-spending-button"),
				gomponents.Attr("hx-get", "/app/spending/new"),
				gomponents.Attr("hx-target", "#modal-body-container"),
				gomponents.Text(locales["add_spending_button"]),
			),
			gomponents.If(len(props.Spending) > 0,
				html.A(
					html.Href("/app/spending/export/csv"),
					html.Class("inline-block bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"),
					gomponents.Attr("data-testid", "export-spending-csv-button"),
					gomponents.Text(locales["export_csv"]),
				),
			),
		),

		// Spending Total Section
		html.Div(
			html.ID("spending-total"),
			html.Class("mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800"),
			htmx.Get("/app/spending/total"),
			htmx.Trigger("updateSpendings from:body"),
			htmx.Swap("innerHTML"),
			SpendingTotal(props.Spending, locales),
		),

		// Search and Filter Section (exact matches pattern)
		html.Form(
			html.ID("spending-filters-form"),
			html.Class("mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700"),

			// Centralized HTMX attributes - exact matches pattern
			htmx.Get("/app/spending/search"),
			htmx.Target("#spending-table"),
			htmx.Trigger("change from:select[name='category'], input changed delay:500ms from:input[name='search'], change from:input[name='startDate'], change from:input[name='endDate']"),
			htmx.Include("closest form"),
			htmx.Sync("this:replace"),

			html.H3(
				html.Class("text-lg font-medium mb-3 text-gray-900 dark:text-white"),
				gomponents.Text(locales["search_filter_title"]),
			),
			html.Div(
				html.Class("grid grid-cols-1 md:grid-cols-4 gap-4"),

				// Search input (no individual HTMX attributes)
				html.Div(
					html.Label(
						html.Class("block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300"),
						gomponents.Text(locales["search_label"]),
					),
					html.Input(
						html.Type("text"),
						html.Name("search"),
						html.ID("search"),
						html.Placeholder(locales["search_placeholder"]),
						html.Class("w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
						gomponents.Attr("data-testid", "search-input"),
					),
				),

				// Category filter (no individual HTMX attributes)
				html.Div(
					html.Label(
						html.Class("block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300"),
						gomponents.Text(locales["category_label"]),
					),
					html.Select(
						html.Name("category"),
						html.ID("category"),
						html.Class("w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
						gomponents.Attr("data-testid", "category-filter"),
						html.Option(html.Value(""), gomponents.Text(locales["all_categories"])),
						html.Option(html.Value("Equipment"), gomponents.Text("Equipment")),
						html.Option(html.Value("Travel"), gomponents.Text("Travel")),
						html.Option(html.Value("Facilities"), gomponents.Text("Facilities")),
						html.Option(html.Value("Food"), gomponents.Text("Food")),
						html.Option(html.Value("Medical"), gomponents.Text("Medical")),
						html.Option(html.Value("Referee Fees"), gomponents.Text("Referee Fees")),
						html.Option(html.Value("Venue Rental"), gomponents.Text("Venue Rental")),
						html.Option(html.Value("Insurance"), gomponents.Text("Insurance")),
						html.Option(html.Value("Marketing"), gomponents.Text("Marketing")),
						html.Option(html.Value("Training Materials"), gomponents.Text("Training Materials")),
						html.Option(html.Value("Tournament Entry"), gomponents.Text("Tournament Entry")),
						html.Option(html.Value("Uniforms"), gomponents.Text("Uniforms")),
						html.Option(html.Value("Transportation"), gomponents.Text("Transportation")),
						html.Option(html.Value("Maintenance"), gomponents.Text("Maintenance")),
						html.Option(html.Value("Administration"), gomponents.Text("Administration")),
					),
				),

				// Start date filter (no individual HTMX attributes)
				html.Div(
					html.Label(
						html.Class("block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300"),
						gomponents.Text(locales["start_date_label"]),
					),
					html.Input(
						html.Type("date"),
						html.Name("startDate"),
						html.ID("startDate"),
						html.Class("w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
						gomponents.Attr("data-testid", "start-date-filter"),
					),
				),

				// End date filter (no individual HTMX attributes)
				html.Div(
					html.Label(
						html.Class("block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300"),
						gomponents.Text(locales["end_date_label"]),
					),
					html.Input(
						html.Type("date"),
						html.Name("endDate"),
						html.ID("endDate"),
						html.Class("w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
						gomponents.Attr("data-testid", "end-date-filter"),
					),
				),
			),
		),

		// Spending table container
		html.Div(
			html.ID("spending-table"),
			htmx.Get("/app/spending/table"),
			htmx.Trigger("updateSpendings from:body"),
			htmx.Swap("innerHTML"),
			SpendingTable(props.Spending, locales, props.Lang),
		),
	)
}

func SpendingTotal(spending []db.Spending, locales map[string]string) gomponents.Node {
	var total float64
	for _, s := range spending {
		total += getNumericValue(s.Amount)
	}

	return html.Div(
		html.Class("flex justify-between items-center"),
		html.H3(
			html.Class("text-lg font-semibold text-gray-900 dark:text-white"),
			gomponents.Text(locales["total_spending_label"]),
		),
		html.Span(
			html.Class("text-2xl font-bold text-blue-600 dark:text-blue-400"),
			gomponents.Attr("data-testid", "spending-total-amount"),
			gomponents.Text(fmt.Sprintf("$%.2f", total)),
		),
	)
}

func SpendingTable(spending []db.Spending, locales map[string]string, lang string) gomponents.Node {
	headerRow := html.Tr(
		html.Th(
			html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
			gomponents.Text(locales["date_header"]),
		),
		html.Th(
			html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
			gomponents.Text(locales["amount_header"]),
		),
		html.Th(
			html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
			gomponents.Text(locales["description_header"]),
		),
		html.Th(
			html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
			gomponents.Text(locales["category_header"]),
		),
		html.Th(
			html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
			gomponents.Text(locales["actions_header"]),
		),
	)

	if len(spending) == 0 {
		// Return table with header and empty state row
		emptyRow := html.Tr(
			html.Td(
				html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800"),
				gomponents.Attr("colspan", "5"),
				gomponents.Text(locales["no_records"]),
			),
		)
		allRows := []gomponents.Node{headerRow, emptyRow}
		tableNodes := append([]gomponents.Node{html.Class("w-full border-collapse bg-white dark:bg-gray-800"), gomponents.Attr("data-testid", "spending-list-table")}, allRows...)
		return html.Table(tableNodes...)
	}

	rows := make([]gomponents.Node, len(spending))
	for i, s := range spending {
		rows[i] = html.Tr(
			gomponents.Attr("data-testid", testid.Generate("spending-row", s.ID)),
			html.Td(
				html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"),
				// Inline date picker
				html.Span(
					inlinedatepicker.InlineDatePicker(inlinedatepicker.InlineDatePickerProps{
						ID:          fmt.Sprintf("%d", s.ID),
						Value:       s.Date.Time.Format("2006-01-02"),
						EndpointURL: fmt.Sprintf("/app/spending/%d/date", s.ID),
						DataTestID:  testid.Generate("date-edit", s.ID),
						Lang:        lang,
					}),
				),
			),
			html.Td(
				html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"),
				// Inline editable amount field
				html.Span(
					html.Class("inline-edit-amount cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded transition-colors"),
					gomponents.Attr("data-spending-id", fmt.Sprintf("%d", s.ID)),
					gomponents.Attr("hx-put", fmt.Sprintf("/app/spending/%d/amount", s.ID)),
					gomponents.Attr("hx-trigger", "blur changed delay:500ms"),
					gomponents.Attr("hx-target", "this"),
					gomponents.Attr("hx-swap", "outerHTML"),
					gomponents.Attr("contenteditable", "true"),
					gomponents.Attr("data-testid", testid.Generate("amount-edit", s.ID)),
					gomponents.Text(fmt.Sprintf("%.2f", getNumericValue(s.Amount))),
				),
			),
			html.Td(
				html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"),
				// Inline editable description field
				html.Span(
					html.Class("inline-edit-description cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded transition-colors"),
					gomponents.Attr("data-spending-id", fmt.Sprintf("%d", s.ID)),
					gomponents.Attr("hx-put", fmt.Sprintf("/app/spending/%d/description", s.ID)),
					gomponents.Attr("hx-trigger", "blur changed delay:500ms"),
					gomponents.Attr("hx-target", "this"),
					gomponents.Attr("hx-swap", "outerHTML"),
					gomponents.Attr("contenteditable", "true"),
					gomponents.Attr("data-testid", testid.Generate("description-edit", s.ID)),
					gomponents.Text(s.Description),
				),
			),
			html.Td(
				html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"),
				gomponents.Text(s.Category),
			),
			html.Td(
				html.Class("border border-gray-300 dark:border-gray-600 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"),
				html.Button(
					html.Type("button"),
					html.Class("bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white px-2 py-1 rounded text-sm transition-colors"),
					gomponents.Attr("data-testid", testid.Generate("delete-spending", s.ID)),
					gomponents.Attr("hx-delete", fmt.Sprintf("/app/spending/%d", s.ID)),
					gomponents.Attr("hx-target", fmt.Sprintf("[data-testid='%s']", testid.Generate("spending-row", s.ID))),
					gomponents.Attr("hx-swap", "outerHTML swap:0s"),
					gomponents.Attr("hx-confirm", locales["delete_confirm"]),
					gomponents.Text(locales["delete_button"]),
				),
			),
		)
	}

	allRows := append([]gomponents.Node{headerRow}, rows...)
	tableNodes := append([]gomponents.Node{html.Class("w-full border-collapse"), gomponents.Attr("data-testid", "spending-list-table")}, allRows...)

	return html.Table(tableNodes...)
}

func NewSpendingModal(teams []db.Team, players []db.Player, seasons []db.Season, matches []db.Match, lang string) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/app/spending/spending.locales.json", lang)
	if err != nil {
		// Log error and continue with empty locales
		fmt.Printf("Failed to load spending locales: %v\n", err)
		locales = make(map[string]string)
	}

	// Create team options
	teamOptions := make([]gomponents.Node, len(teams))
	for i, team := range teams {
		teamOptions[i] = html.Option(
			html.Value(fmt.Sprintf("%d", team.ID)),
			gomponents.Text(team.Name),
		)
	}

	// Create player options
	playerOptions := make([]gomponents.Node, len(players))
	for i, player := range players {
		playerOptions[i] = html.Option(
			html.Value(fmt.Sprintf("%d", player.ID)),
			gomponents.Text(player.Name),
		)
	}

	return modalwrappers.ClientModalWithAutoOpen(modalwrappers.ClientModalConfig{
		ModalID: "addSpending",
		Title:   locales["add_spending_button"],
		Content: html.Form(
			html.Method("POST"),
			htmx.Post("/app/spending"),
			htmx.Target("#toast-body-container"),
			htmx.Swap("afterbegin"),
			gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { addSpending_modalOpen = false; }"),
			html.Class("space-y-4"),

			// Amount field
			html.Div(
				html.Label(
					html.For("amount"),
					html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
					gomponents.Text(locales["amount_label"]),
				),
				html.Input(
					html.Type("number"),
					html.Name("amount"),
					html.ID("amount"),
					html.Step("0.01"),
					html.Min("0.01"),
					html.Max("1000000"),
					html.Required(),
					html.Class("mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
				),
			),

			// Description field
			html.Div(
				html.Label(
					html.For("description"),
					html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
					gomponents.Text(locales["description_label"]),
				),
				html.Input(
					html.Type("text"),
					html.Name("description"),
					html.ID("description"),
					html.Required(),
					html.Class("mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
				),
			),

			// Date field
			html.Div(
				html.Label(
					html.For("date"),
					html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
					gomponents.Text(locales["date_label"]),
				),
				html.Input(
					html.Type("date"),
					html.Name("date"),
					html.ID("date"),
					html.Required(),
					html.Value(time.Now().Format("2006-01-02")), // Default to today
					html.Class("mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
				),
			),

			// Category field
			uiselect.CategorySelect(uiselect.CategorySelectProps{
				Lang: lang,
				SelectProps: uiselect.SelectProps{
					ID:       "category",
					Name:     "category",
					Required: true,
					Label:    locales["category_label"],
				},
			}),

			// Team association
			html.Div(
				html.Label(
					html.Class("block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300"),
					gomponents.Text(locales["associate_teams"]),
				),
				html.Select(
					append([]gomponents.Node{
						html.Name("teamIds"),
						html.Multiple(),
						html.Class("block w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
					}, teamOptions...)...,
				),
			),

			// Player association
			html.Div(
				html.Label(
					html.Class("block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300"),
					gomponents.Text(locales["associate_players"]),
				),
				html.Select(
					append([]gomponents.Node{
						html.Name("playerIds"),
						html.Multiple(),
						html.Class("block w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"),
					}, playerOptions...)...,
				),
			),

			// Submit button
			html.Div(
				html.Class("flex justify-end mt-4"),
				html.Button(
					html.Type("submit"),
					html.Class("bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"),
					gomponents.Text(locales["create_spending_button"]),
				),
			),
		),
	})
}

// NewSpendingSuccessToast creates a success toast notification for spending operations
func NewSpendingSuccessToast(message, lang string) gomponents.Node {
	return toast.Toast(toast.ToastConfig{
		ID:        fmt.Sprintf("spending-success-toast-%d", time.Now().UnixNano()),
		Message:   message,
		Style:     "success",
		AutoClose: true, // Success toasts auto-close
		Lang:      lang,
	})
}

// NewSpendingErrorToast creates an error toast notification for spending operations
func NewSpendingErrorToast(message, lang string) gomponents.Node {
	return toast.Toast(toast.ToastConfig{
		ID:        fmt.Sprintf("spending-error-toast-%d", time.Now().UnixNano()),
		Message:   message,
		Style:     "error",
		AutoClose: true, // Error toasts auto-close
		Lang:      lang,
	})
}
