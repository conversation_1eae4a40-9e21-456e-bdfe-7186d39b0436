package templatesapplogout

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func LogoutSuccess(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/logout/success.locales.json", lang)
	if err != nil {
		// Handle error, perhaps log it or use default values
		locales = make(map[string]string)
	}
	content := html.Div(
		html.Class("w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md"),
		html.H2(
			html.Class("mt-6 text-center text-2xl font-bold text-gray-900"),
			gomponents.Text(locales["title"]),
		),
		html.P(
			html.Class("mt-2 text-center text-gray-600"),
			gomponents.Text(locales["message"]),
		),
		html.Div(
			html.Class("mt-6 flex justify-center"),
			html.A(
				html.Href("/signin"),
				html.Class("px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"),
				gomponents.Text(locales["signin"]),
			),
		),
	)

	// Using the new ClientModalWithAutoOpen wrapper makes the pattern explicit
	// This modal opens immediately when rendered and cannot be closed (unclosable)
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "logoutSuccess",
		Title:        locales["title"],
		Content:      content,
		IsUnclosable: true,
		DataTestID:   "logout-success-modal",
	})
}
