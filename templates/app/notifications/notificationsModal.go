package templatesappnotifications

import (
	"fmt"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	modalwrappers "github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// NotificationsModal renders the notifications list in a modal dialog.
func NotificationsModal(notifications []db.Notification, lang string, currentPage, totalPages int, unreadCount int64) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/notifications/notificationsPage.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Create the modal content
	content := NotificationsModalContent(notifications, lang, currentPage, totalPages, unreadCount)

	return modalwrappers.ClientModalWithAutoOpen(modalwrappers.ClientModalConfig{
		ModalID: "notificationsModal",
		Title:   locales["page_title"],
		Content: content,
	})
}

// NotificationsModalContent renders the content for the notifications modal
func NotificationsModalContent(notifications []db.Notification, lang string, currentPage, totalPages int, unreadCount int64) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/notifications/notificationsPage.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("max-h-96 overflow-y-auto"),

		// Mark all as read button (only show if there are unread notifications)
		gomponents.If(unreadCount > 0,
			html.Div(
				html.Class("flex justify-end mb-4"),
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       locales["mark_all_read"],
					Class:      "text-sm",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-post", "/app/notifications/read-all"),
						gomponents.Attr("hx-target", "#notificationsModalContainer"),
						gomponents.Attr("hx-swap", "innerHTML"),
					},
				}),
			),
		),

		// Notifications list
		gomponents.If(len(notifications) > 0,
			html.Div(
				html.Class("space-y-3"),
				gomponents.Group(renderModalNotifications(notifications)),
			),
		),

		// Empty state
		gomponents.If(len(notifications) == 0,
			html.Div(
				html.Class("text-center py-8"),
				html.P(
					html.Class("text-gray-500 dark:text-gray-400"),
					gomponents.Text(locales["no_notifications"]),
				),
			),
		),

		// Pagination (if more than one page)
		gomponents.If(totalPages > 1,
			html.Div(
				html.Class("mt-6 pt-4 border-t border-gray-200 dark:border-gray-700"),
				renderModalPagination(currentPage, totalPages),
			),
		),
	)
}

// renderModalNotifications renders notifications for the modal view
func renderModalNotifications(notifications []db.Notification) []gomponents.Node {
	var nodes []gomponents.Node

	for _, notification := range notifications {
		nodes = append(nodes, renderModalNotification(notification))
	}

	return nodes
}

// renderModalNotification renders a single notification for the modal
func renderModalNotification(notification db.Notification) gomponents.Node {
	// Determine if notification is unread
	isUnread := !notification.IsRead.Valid || !notification.IsRead.Bool

	// Get notification icon and color based on type
	icon, iconColor := getNotificationIcon(notification.Type)

	// Format timestamp
	timeStr := formatNotificationTime(notification.CreatedAt.Time)

	bgClass := "bg-white dark:bg-gray-800"
	if isUnread {
		bgClass = "bg-blue-50 dark:bg-blue-900/20"
	}

	return html.Div(
		html.Class("p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 "+bgClass),
		gomponents.Attr("hx-post", fmt.Sprintf("/app/notifications/%d/read", notification.ID)),
		gomponents.Attr("hx-target", "#notificationsModalContainer"),
		gomponents.Attr("hx-swap", "innerHTML"),

		html.Div(
			html.Class("flex items-start space-x-3"),

			// Icon
			html.Div(
				html.Class("flex-shrink-0 w-8 h-8 "+iconColor+" rounded-full flex items-center justify-center"),
				icon,
			),

			// Content
			html.Div(
				html.Class("flex-1 min-w-0"),
				html.Div(
					html.Class("flex items-center justify-between"),
					html.H4(
						html.Class("text-sm font-medium text-gray-900 dark:text-white"),
						gomponents.Text(notification.Title),
					),
					html.Div(
						html.Class("flex items-center space-x-2"),
						gomponents.If(isUnread,
							html.Span(
								html.Class("inline-block w-2 h-2 bg-blue-600 rounded-full"),
							),
						),
						html.Span(
							html.Class("text-xs text-gray-500 dark:text-gray-400"),
							gomponents.Text(timeStr),
						),
					),
				),
				html.P(
					html.Class("text-sm text-gray-600 dark:text-gray-300 mt-1"),
					gomponents.Text(notification.Message),
				),
			),
		),
	)
}

// renderModalPagination renders pagination for the modal
func renderModalPagination(currentPage, totalPages int) gomponents.Node {
	return html.Div(
		html.Class("flex items-center justify-between text-sm"),
		html.Div(
			html.Class("flex items-center space-x-2"),
			gomponents.If(currentPage > 1,
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       "Previous",
					Class:      "px-3 py-1 text-sm",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-get", fmt.Sprintf("/app/notifications?page=%d", currentPage-1)),
						gomponents.Attr("hx-target", "#notificationsModalContainer"),
						gomponents.Attr("hx-swap", "innerHTML"),
					},
				}),
			),
			html.Span(
				html.Class("text-gray-700 dark:text-gray-300"),
				gomponents.Text(fmt.Sprintf("Page %d of %d", currentPage, totalPages)),
			),
			gomponents.If(currentPage < totalPages,
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "button",
					Text:       "Next",
					Class:      "px-3 py-1 text-sm",
					Attrs: []gomponents.Node{
						gomponents.Attr("hx-get", fmt.Sprintf("/app/notifications?page=%d", currentPage+1)),
						gomponents.Attr("hx-target", "#notificationsModalContainer"),
						gomponents.Attr("hx-swap", "innerHTML"),
					},
				}),
			),
		),
	)
}

// getNotificationIcon returns the appropriate icon and color for a notification type
func getNotificationIcon(notificationType string) (gomponents.Node, string) {
	switch notificationType {
	case "match_update":
		return html.Span(
			html.Class("h-4 w-4"),
			icons.PencilSquare(),
		), "bg-blue-100 dark:bg-blue-900"
	case "schedule_change":
		return html.Span(
			html.Class("h-4 w-4"),
			icons.Calendar(),
		), "bg-yellow-100 dark:bg-yellow-900"
	case "result":
		return html.Span(
			html.Class("h-4 w-4"),
			icons.Trophy(),
		), "bg-green-100 dark:bg-green-900"
	case "announcement":
		return html.Span(
			html.Class("h-4 w-4"),
			icons.Megaphone(),
		), "bg-purple-100 dark:bg-purple-900"
	default:
		return html.Span(
			html.Class("h-4 w-4"),
			icons.InformationCircle(),
		), "bg-gray-100 dark:bg-gray-900"
	}
}

// formatNotificationTime formats a notification timestamp for display
func formatNotificationTime(timestamp time.Time) string {
	now := time.Now()
	diff := now.Sub(timestamp)

	switch {
	case diff < time.Minute:
		return "just now"
	case diff < time.Hour:
		minutes := int(diff.Minutes())
		if minutes == 1 {
			return "1 minute ago"
		}
		return fmt.Sprintf("%d minutes ago", minutes)
	case diff < 24*time.Hour:
		hours := int(diff.Hours())
		if hours == 1 {
			return "1 hour ago"
		}
		return fmt.Sprintf("%d hours ago", hours)
	case diff < 7*24*time.Hour:
		days := int(diff.Hours() / 24)
		if days == 1 {
			return "1 day ago"
		}
		return fmt.Sprintf("%d days ago", days)
	default:
		return timestamp.Format("Jan 2, 2006")
	}
}
