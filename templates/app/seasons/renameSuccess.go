package templatesappseasons

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/toast"
	"maragu.dev/gomponents"
)

func RenameSuccess(lang string) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/renameSuccess.locales.json", lang)
	if err != nil {
		locales = map[string]string{
			"message": "Season renamed successfully",
		}
	}

	return toast.Toast(toast.ToastConfig{
		ID:         "rename-season-success-toast",
		Message:    locales["message"],
		Style:      "success",
		DataTestID: "rename-season-success-toast",
		AutoClose:  true,
	})
}
