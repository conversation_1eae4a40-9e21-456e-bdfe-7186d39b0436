package templatesappseasons

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/utils/pagination"
)

// MatchesTableParams contains parameters for table filtering/sorting/pagination
type MatchesTableParams struct {
	pagination.SortablePaginationParams
	FilterToday bool
}

// MatchesTableProps contains props for the HTMX-enabled matches table
type MatchesTableProps struct {
	Matches       []db.GetMatchesBySeasonIdRow
	CustomColumns []db.MatchCustomColumn
	CustomValues  map[int32][]db.MatchCustomValue
	Lang          string
	SeasonId      int32
	SeasonName    string
	Params        MatchesTableParams
	TotalItems    int
	TotalPages    int
}
