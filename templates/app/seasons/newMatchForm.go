package templatesappseasons

import (
	"fmt"
	"strconv"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/modal"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type NewMatchFormProps struct {
	SeasonID int32
	Players  []db.Player
	Lang     string
}

func NewMatchForm(props NewMatchFormProps) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/newMatchForm.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{
			"title":             "Add New Match",
			"player_1_label":    "Player 1",
			"player_2_label":    "Player 2",
			"match_date_label":  "Match Date",
			"match_group_label": "Match Group",
			"player_1_points":   "Player 1 Points",
			"player_2_points":   "Player 2 Points",
			"create_match":      "Create Match",
			"cancel":            "Cancel",
			"select_player":     "Select a player",
		}
	}

	// Convert players to select items
	player1Items := []uiselect.SelectItem{{Value: "", Title: locales["select_player"]}}
	player2Items := []uiselect.SelectItem{{Value: "", Title: locales["select_player"]}}

	for _, player := range props.Players {
		item := uiselect.SelectItem{
			Value: fmt.Sprintf("%d", player.ID),
			Title: player.Name,
		}
		player1Items = append(player1Items, item)
		player2Items = append(player2Items, item)
	}

	// Get today's date as default
	today := time.Now().Format("2006-01-02")

	formContent := html.Div(
		html.Class("space-y-4"),

		// Player 1 Select
		html.Div(
			html.Label(
				html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
				gomponents.Text(locales["player_1_label"]),
				html.Span(
					html.Class("text-red-500 ml-1"),
					gomponents.Text("*"),
				),
			),
			uiselect.Select(uiselect.SelectProps{
				ID:          "player1",
				Name:        "playerId1",
				Required:    true,
				Placeholder: locales["select_player"],
				Items:       player1Items,
				DataTestID:  "player1-select",
			}),
		),

		// Player 2 Select
		html.Div(
			html.Label(
				html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
				gomponents.Text(locales["player_2_label"]),
				html.Span(
					html.Class("text-red-500 ml-1"),
					gomponents.Text("*"),
				),
			),
			uiselect.Select(uiselect.SelectProps{
				ID:          "player2",
				Name:        "playerId2",
				Required:    true,
				Placeholder: locales["select_player"],
				Items:       player2Items,
				DataTestID:  "player2-select",
			}),
		),

		// Match Date
		form.FormInput(form.FormInputProps{
			Label:      locales["match_date_label"],
			Type:       "date",
			ID:         "matchDate",
			Name:       "matchDate",
			Required:   true,
			Value:      today,
			DataTestID: "match-date-input",
		}),

		// Match Group
		form.FormInput(form.FormInputProps{
			Label:      locales["match_group_label"],
			Type:       "number",
			ID:         "matchGroup",
			Name:       "matchGroup",
			Required:   false,
			Value:      "1",
			DataTestID: "match-group-input",
		}),

		// Player 1 Points (optional)
		form.FormInput(form.FormInputProps{
			Label:       locales["player_1_points"],
			Type:        "number",
			ID:          "player1Points",
			Name:        "playerId1Points",
			Required:    false,
			Placeholder: "Optional",
			DataTestID:  "player1-points-input",
		}),

		// Player 2 Points (optional)
		form.FormInput(form.FormInputProps{
			Label:       locales["player_2_points"],
			Type:        "number",
			ID:          "player2Points",
			Name:        "playerId2Points",
			Required:    false,
			Placeholder: "Optional",
			DataTestID:  "player2-points-input",
		}),

		// Hidden season ID
		html.Input(
			html.Type("hidden"),
			html.Name("seasonId"),
			html.Value(strconv.Itoa(int(props.SeasonID))),
		),

		// Action buttons
		html.Div(
			html.Class("flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"),
			button.SecondaryButton(button.BaseButtonConfig{
				Text:       locales["cancel"],
				ButtonType: "button",
				Attrs: []gomponents.Node{
					gomponents.Attr("@click", "newMatchModal_modalOpen = false"),
				},
				DataTestID: "cancel-match-btn",
			}),
			button.PrimaryButton(button.BaseButtonConfig{
				Text:       locales["create_match"],
				ButtonType: "submit",
				DataTestID: "create-match-btn",
			}),
		),
	)

	// Form with HTMX attributes
	formWithHTMX := html.Form(
		htmx.Post("/app/matches"),
		htmx.Target("#toast-body-container"),
		htmx.Swap("beforeend"),
		formContent,
	)

	// Use the proper modal component instead of hardcoded bullshit
	return html.Div(
		gomponents.Attr("x-data", "{ newMatchModal_modalOpen: true }"),
		modal.Modal(modal.ModalConfig{
			ModalID: "newMatchModal",
			Title:   locales["title"],
			Content: formWithHTMX,
		}),
	)
}
