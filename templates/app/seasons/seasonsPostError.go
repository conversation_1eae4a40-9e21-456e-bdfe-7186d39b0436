package templatesappseasons

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// SeasonsPostError renders an error message fragment that can be inserted into the season page.
func SeasonsPostError(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/seasonsPostError.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"),
		html.Role("alert"),
		html.Div(
			html.Class("flex flex-col space-y-2"),
			html.Span(
				html.Class("block sm:inline"),
				gomponents.Text(locales["errorMessage"]),
			),
		),
	)
}
