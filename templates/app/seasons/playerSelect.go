package templatesappseasons

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/modal"
	"github.com/j-em/coachpad/utils/pagination"
	"github.com/j-em/coachpad/utils/testid"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// PlayerSelectParams holds pagination and search parameters for player selection
type PlayerSelectParams struct {
	pagination.BasePaginationParams
}

// Helper functions for player filtering, sorting, and pagination
func filterPlayers(players []db.Player, search string) []db.Player {
	if search == "" {
		return players
	}

	var filtered []db.Player
	searchLower := strings.ToLower(search)
	for _, player := range players {
		if strings.Contains(strings.ToLower(player.Name), searchLower) {
			filtered = append(filtered, player)
		}
	}
	return filtered
}

func paginatePlayers(players []db.Player, page, itemsPerPage int) []db.Player {
	start := (page - 1) * itemsPerPage
	if start >= len(players) {
		return []db.Player{}
	}

	end := min(start+itemsPerPage, len(players))

	return players[start:end]
}

func buildPlayerSelectURL(baseURL string, params PlayerSelectParams, page int) string {
	url := fmt.Sprintf("%s?page=%d&per_page=%d", baseURL, page, params.ItemsPerPage)
	if params.Search != "" {
		url += "&search=" + params.Search
	}
	return url
}

// PlayerSelect creates a component for selecting players via a modal
// This component will show selected players and open a modal when the button is clicked
func PlayerSelect(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/playerSelect.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		html.Class("mb-4"),
		gomponents.Attr("x-data", "{ playerSelectModal_modalOpen: false, selectedPlayers: [], selectedPlayerNames: [] }"),
		html.Label(
			html.Class("block text-gray-900 dark:text-gray-100 text-sm font-bold mb-2"),
			gomponents.Attr("for", "players"),
			gomponents.Text(locales["select_players"]),
			html.Span(
				html.Class("text-red-500 ml-1"),
				gomponents.Text("*"),
			),
		),
		// Button to trigger modal
		button.SecondaryButton(button.BaseButtonConfig{
			ID:         "select-players-button",
			ButtonType: "button",
			Text:       locales["select_players"],
			Class:      "mb-2",
			Attrs: []gomponents.Node{
				gomponents.Attr("hx-get", "/app/players/select-modal"),
				gomponents.Attr("hx-target", "#player-select-modal-container"),
				gomponents.Attr("hx-swap", "innerHTML"),
			},
		}),
		html.Div(html.ID("player-select-modal-container")),
		// Display selected players
		html.Div(
			html.ID("selected-players-display"),
			html.Class("mt-2"),
			gomponents.Attr("x-show", "selectedPlayers.length > 0"),
			html.P(
				html.Class("text-sm text-gray-600 dark:text-gray-400 mb-1"),
				gomponents.Text(locales["selected_players"]),
			),
			html.Div(
				html.Class("flex flex-wrap gap-2"),
				html.Template(
					gomponents.Attr("x-for", "name in selectedPlayerNames"),
					html.Span(
						html.Class("inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded"),
						gomponents.Attr("x-text", "name"),
					),
				),
			),
		),
		// Hidden inputs for selected player IDs
		html.Div(
			html.ID("selected-players-inputs"),
			html.Template(
				gomponents.Attr("x-for", "playerId in selectedPlayers"),
				html.Input(
					html.Type("hidden"),
					html.Name("playerIds[]"),
					gomponents.Attr("x-bind:value", "playerId"),
				),
			),
		),
	)
}

// Player2SelectForMatch renders a paginated, searchable list of available players for player2 replacement.
func Player2SelectForMatch(matchID int32, currentPlayer2ID int32, allPlayers []db.Player, params PlayerSelectParams, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/playerSelect.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Apply filtering and pagination
	filteredPlayers := filterPlayers(allPlayers, params.Search)
	totalItems := len(filteredPlayers)
	paginatedPlayers := paginatePlayers(filteredPlayers, params.Page, params.ItemsPerPage)

	// Calculate pagination info
	totalPages := (totalItems + params.ItemsPerPage - 1) / params.ItemsPerPage
	if totalPages == 0 {
		totalPages = 1
	}
	startItem := (params.Page-1)*params.ItemsPerPage + 1
	endItem := min(startItem+len(paginatedPlayers)-1, totalItems)

	baseURL := fmt.Sprintf("/app/matches/%d/player2/edit", matchID)

	return html.Div(
		gomponents.Attr("x-data", "{ player2SelectForMatch_modalOpen: true }"),
		modal.Modal(modal.ModalConfig{
			ModalID: "player2SelectForMatch",
			Title:   locales["select_player_2"],
			Content: html.Div(
				html.Class("space-y-4"),
				// Search bar with spinner
				html.Div(
					html.Class("mb-4"),
					html.Label(
						html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2"),
						gomponents.Text(locales["search_players"]),
						// Spinner (hidden by default, shown by HTMX)
						html.Span(
							html.ID("player2-search-spinner"),
							html.Class("block w-[16px] htmx-indicator"),
							icons.Spinner(),
						),
					),
					html.Input(
						html.Type("text"),
						html.Name("search"),
						html.Value(params.Search),
						html.Placeholder(locales["search_placeholder"]),
						html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
						gomponents.Attr("data-testid", "player-search-input"),
						htmx.Get(baseURL),
						htmx.Target("#modal-body-container"),
						htmx.Include("[name='page'], [name='per_page']"),
						htmx.Trigger("input changed delay:500ms"),
						htmx.Indicator("#player2-search-spinner"),
					),
					// Hidden form fields for state preservation
					html.Input(html.Type("hidden"), html.Name("page"), html.Value(strconv.Itoa(params.Page))),
					html.Input(html.Type("hidden"), html.Name("per_page"), html.Value(strconv.Itoa(params.ItemsPerPage))),
				),
				// Players list
				html.Div(
					html.ID("player2-select-content"),
					gomponents.If(len(paginatedPlayers) == 0,
						html.Div(
							html.Class("text-center text-gray-500 py-8"),
							gomponents.If(params.Search != "",
								gomponents.Text(locales["no_players_found"]),
							),
							gomponents.If(params.Search == "",
								gomponents.Text(locales["no_players_available"]),
							),
						),
					),
					gomponents.If(len(paginatedPlayers) > 0,
						html.Ul(
							html.Class("space-y-2 max-h-64 overflow-y-auto"),
							gomponents.Attr("data-testid", "player-select-modal"),
							gomponents.Map(paginatedPlayers, func(p db.Player) gomponents.Node {
								return html.Li(
									button.SecondaryButton(button.BaseButtonConfig{
										ID:         fmt.Sprintf("select-player2-%d", p.ID),
										Text:       p.Name,
										DataTestID: testid.Generate("player-select-option", p.ID),
										Class:      "w-full justify-start",
										Attrs: []gomponents.Node{
											gomponents.Attr("hx-patch", fmt.Sprintf("/app/matches/%d", matchID)),
											gomponents.Attr("hx-vals", fmt.Sprintf(`{"player_id2":%d}`, p.ID)),
											gomponents.Attr("hx-target", fmt.Sprintf("#match-row-%d", matchID)),
											gomponents.Attr("hx-swap", "outerHTML"),
										},
									}),
								)
							}),
						),
					),
				),
				// Pagination controls
				gomponents.If(totalPages > 1,
					html.Div(
						html.Class("mt-4 flex justify-between items-center border-t border-gray-200 dark:border-gray-600 pt-4"),
						// Previous/Next buttons
						html.Div(
							html.Class("flex space-x-2"),
							gomponents.If(params.Page > 1,
								button.SecondaryButton(button.BaseButtonConfig{
									Text: locales["previous"],
									Attrs: []gomponents.Node{
										htmx.Get(buildPlayerSelectURL(baseURL, params, params.Page-1)),
										htmx.Target("#modal-body-container"),
									},
								}),
							),
							gomponents.If(params.Page < totalPages,
								button.SecondaryButton(button.BaseButtonConfig{
									Text: locales["next"],
									Attrs: []gomponents.Node{
										htmx.Get(buildPlayerSelectURL(baseURL, params, params.Page+1)),
										htmx.Target("#modal-body-container"),
									},
								}),
							),
						),
						// Page info
						html.Div(
							html.Class("text-sm text-gray-700 dark:text-gray-300"),
							gomponents.If(totalItems > 0,
								gomponents.Text(fmt.Sprintf(locales["showing_x_to_y_of_z"], startItem, endItem, totalItems)),
							),
						),
						// Items per page selector
						html.Div(
							html.Class("flex items-center space-x-2"),
							html.Label(
								html.Class("text-sm text-gray-700 dark:text-gray-300"),
								gomponents.Text(locales["per_page"]),
							),
							html.Select(
								html.Name("per_page"),
								html.Class("border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 text-sm dark:bg-gray-700 dark:text-white"),
								htmx.Get(baseURL),
								htmx.Target("#modal-body-container"),
								htmx.Include("[name='search'], [name='page']"),
								htmx.Trigger("change"),
								gomponents.Map([]int{5, 10, 25, 50}, func(size int) gomponents.Node {
									return html.Option(
										html.Value(strconv.Itoa(size)),
										gomponents.If(size == params.ItemsPerPage, html.Selected()),
										gomponents.Text(strconv.Itoa(size)),
									)
								}),
							),
						),
					),
				),
			),
			IsOpen:       true,
			IsUnclosable: false,
		}),
	)
}

// PlayerSelectModal renders a modal with player selection checkboxes
func PlayerSelectModal(players []db.Player, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/playerSelect.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		gomponents.Attr("x-data", "{ playerSelectModal_modalOpen: false, modalSelectedPlayers: [], modalSelectedPlayerNames: [], searchQuery: '' }"),
		gomponents.Attr("x-init", "setTimeout(() => { playerSelectModal_modalOpen = true; modalSelectedPlayers = [...selectedPlayers]; modalSelectedPlayerNames = [...selectedPlayerNames] }, 0)"),
		modal.Modal(modal.ModalConfig{
			ModalID: "playerSelectModal",
			Title:   locales["select_players"],
			Content: html.Div(
				html.Class("space-y-4"),
				gomponents.If(len(players) == 0,
					html.Div(
						html.Class("text-center text-gray-500 py-4"),
						gomponents.Text(locales["no_players_available"]),
					),
				),
				gomponents.If(len(players) > 0,
					html.Div(
						html.Class("mb-4"),
						html.Label(
							html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"),
							gomponents.Text(locales["search_players"]),
						),
						html.Input(
							html.Type("text"),
							html.ID("player-search-input"),
							html.Placeholder(locales["search_placeholder"]),
							html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
							gomponents.Attr("x-model", "searchQuery"),
						),
					),
				),
				gomponents.If(len(players) > 0,
					html.Div(
						html.Class("max-h-64 overflow-y-auto"),
						html.Ul(
							html.ID("players-selection-list"),
							html.Class("space-y-2"),
							gomponents.Map(players, func(p db.Player) gomponents.Node {
								return html.Li(
									html.Class("flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded"),
									gomponents.Attr("x-show", fmt.Sprintf("searchQuery === '' || '%s'.toLowerCase().includes(searchQuery.toLowerCase())", p.Name)),
									html.Label(
										html.Class("flex items-center space-x-3 cursor-pointer flex-1"),
										html.Input(
											html.Type("checkbox"),
											html.Value(fmt.Sprintf("%d", p.ID)),
											gomponents.Attr("x-model", "modalSelectedPlayers"),
											gomponents.Attr("@change", `
												if ($event.target.checked) {
													if (!modalSelectedPlayerNames.includes('`+p.Name+`')) {
														modalSelectedPlayerNames.push('`+p.Name+`');
													}
												} else {
													modalSelectedPlayerNames = modalSelectedPlayerNames.filter(name => name !== '`+p.Name+`');
												}
											`),
											html.Class("form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"),
										),
										html.Span(
											html.Class("text-gray-900 dark:text-gray-100 font-medium"),
											gomponents.Text(p.Name),
										),
									),
								)
							}),
						),
					),
				),
				html.Div(
					html.Class("flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"),
					button.SecondaryButton(button.BaseButtonConfig{
						Text:       locales["cancel"],
						ButtonType: "button",
						Attrs: []gomponents.Node{
							gomponents.Attr("@click", "playerSelectModal_modalOpen = false"),
						},
					}),
					button.PrimaryButton(button.BaseButtonConfig{
						Text:       locales["confirm_selection"],
						ButtonType: "button",
						Attrs: []gomponents.Node{
							gomponents.Attr("@click", `
								selectedPlayers = [...modalSelectedPlayers];
								selectedPlayerNames = [...modalSelectedPlayerNames];
								playerSelectModal_modalOpen = false;
							`),
						},
					}),
				),
			),
			IsOpen:       true,
			IsUnclosable: false,
		}),
	)
}

// Player1SelectForMatch renders a paginated, searchable list of available players for player1 replacement.
func Player1SelectForMatch(matchID int32, currentPlayer1ID int32, allPlayers []db.Player, params PlayerSelectParams, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/playerSelect.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Apply filtering and pagination
	filteredPlayers := filterPlayers(allPlayers, params.Search)
	totalItems := len(filteredPlayers)
	paginatedPlayers := paginatePlayers(filteredPlayers, params.Page, params.ItemsPerPage)

	// Calculate pagination info
	totalPages := (totalItems + params.ItemsPerPage - 1) / params.ItemsPerPage
	if totalPages == 0 {
		totalPages = 1
	}
	startItem := (params.Page-1)*params.ItemsPerPage + 1
	endItem := min(startItem+len(paginatedPlayers)-1, totalItems)

	baseURL := fmt.Sprintf("/app/matches/%d/player1/edit", matchID)

	return html.Div(
		gomponents.Attr("x-data", "{ player1SelectForMatch_modalOpen: true }"),
		modal.Modal(modal.ModalConfig{
			ModalID: "player1SelectForMatch",
			Title:   locales["select_player_1"],
			Content: html.Div(
				html.Class("space-y-4"),
				// Search bar with spinner
				html.Div(
					html.Class("mb-4"),
					html.Label(
						html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2"),
						gomponents.Text(locales["search_players"]),
						// Spinner (hidden by default, shown by HTMX)
						html.Span(
							html.ID("player1-search-spinner"),
							html.Class("block w-[16px] htmx-indicator"),
							icons.Spinner(),
						),
					),
					html.Input(
						html.Type("text"),
						html.Name("search"),
						html.Value(params.Search),
						html.Placeholder(locales["search_placeholder"]),
						html.Class("w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"),
						gomponents.Attr("data-testid", "player-search-input"),
						htmx.Get(baseURL),
						htmx.Target("#modal-body-container"),
						htmx.Include("[name='page'], [name='per_page']"),
						htmx.Trigger("input changed delay:500ms"),
						gomponents.Attr("hx-indicator", "#player1-search-spinner"),
					),
					// Hidden form fields for state preservation
					html.Input(html.Type("hidden"), html.Name("page"), html.Value(strconv.Itoa(params.Page))),
					html.Input(html.Type("hidden"), html.Name("per_page"), html.Value(strconv.Itoa(params.ItemsPerPage))),
				),
				// Players list
				html.Div(
					html.ID("player1-select-content"),
					gomponents.If(len(paginatedPlayers) == 0,
						html.Div(
							html.Class("text-center text-gray-500 py-8"),
							gomponents.If(params.Search != "",
								gomponents.Text(locales["no_players_found"]),
							),
							gomponents.If(params.Search == "",
								gomponents.Text(locales["no_players_available"]),
							),
						),
					),
					gomponents.If(len(paginatedPlayers) > 0,
						html.Ul(
							html.Class("space-y-2 max-h-64 overflow-y-auto"),
							gomponents.Attr("data-testid", "player-select-modal"),
							gomponents.Map(paginatedPlayers, func(p db.Player) gomponents.Node {
								return html.Li(
									button.SecondaryButton(button.BaseButtonConfig{
										ID:         fmt.Sprintf("select-player1-%d", p.ID),
										Text:       p.Name,
										DataTestID: testid.Generate("player-select-option", p.ID),
										Class:      "w-full justify-start",
										Attrs: []gomponents.Node{
											gomponents.Attr("hx-patch", fmt.Sprintf("/app/matches/%d", matchID)),
											gomponents.Attr("hx-vals", fmt.Sprintf(`{"player_id1":%d}`, p.ID)),
											gomponents.Attr("hx-target", fmt.Sprintf("#match-row-%d", matchID)),
											gomponents.Attr("hx-swap", "outerHTML"),
										},
									}),
								)
							}),
						),
					),
				),
				// Pagination controls
				gomponents.If(totalPages > 1,
					html.Div(
						html.Class("mt-4 flex justify-between items-center border-t border-gray-200 dark:border-gray-600 pt-4"),
						// Previous/Next buttons
						html.Div(
							html.Class("flex space-x-2"),
							gomponents.If(params.Page > 1,
								button.SecondaryButton(button.BaseButtonConfig{
									ID:   "prev-page-btn",
									Text: locales["previous"],
									Attrs: []gomponents.Node{
										htmx.Get(buildPlayerSelectURL(baseURL, params, params.Page-1)),
										htmx.Target("#modal-body-container"),
									},
								}),
							),
							gomponents.If(params.Page < totalPages,
								button.SecondaryButton(button.BaseButtonConfig{
									ID:   "next-page-btn",
									Text: locales["next"],
									Attrs: []gomponents.Node{
										htmx.Get(buildPlayerSelectURL(baseURL, params, params.Page+1)),
										htmx.Target("#modal-body-container"),
									},
								}),
							),
						),
						// Page info
						html.Div(
							html.Class("text-sm text-gray-700 dark:text-gray-300"),
							gomponents.If(totalItems > 0,
								gomponents.Text(fmt.Sprintf(locales["showing_x_to_y_of_z"], startItem, endItem, totalItems)),
							),
						),
						// Items per page selector
						html.Div(
							html.Class("flex items-center space-x-2"),
							html.Label(
								html.Class("text-sm text-gray-700 dark:text-gray-300"),
								gomponents.Text(locales["per_page"]),
							),
							html.Select(
								html.Name("per_page"),
								html.Class("border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 text-sm dark:bg-gray-700 dark:text-white"),
								htmx.Get(baseURL),
								htmx.Target("#modal-body-container"),
								htmx.Include("[name='search'], [name='page']"),
								htmx.Trigger("change"),
								gomponents.Map([]int{5, 10, 25, 50}, func(size int) gomponents.Node {
									return html.Option(
										html.Value(strconv.Itoa(size)),
										gomponents.If(size == params.ItemsPerPage, html.Selected()),
										gomponents.Text(strconv.Itoa(size)),
									)
								}),
							),
						),
					),
				),
			),
			IsOpen:       true,
			IsUnclosable: false,
		}),
	)
}
