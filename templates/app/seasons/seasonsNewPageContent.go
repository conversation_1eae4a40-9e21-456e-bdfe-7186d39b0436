package templatesappseasons

import (
	"encoding/json"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/datepicker"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/infobubble"
	templatesuistepindicator "github.com/j-em/coachpad/templates/ui/stepindicator"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type SeasonsNewPageContentProps struct {
	ErrorMsg      string
	Seasons       []db.Season
	AllPlayers    []db.Player
	IsSidebarOpen bool
	ActiveLink    string
	Lang          string
}

func SeasonsNewPageContent(props SeasonsNewPageContentProps) gomponents.Node {
	// Convert players to JSON for JavaScript
	playersJSON := "[]"
	if len(props.AllPlayers) > 0 {
		if playersBytes, err := json.Marshal(props.AllPlayers); err == nil {
			playersJSON = string(playersBytes)
		}
	}

	return pagebuilder.NewPage("/app/seasons/new").
		WithLocaleBase("app/seasons/seasonsNewPageContent").
		RenderContent(props.Lang, func(ctx pagebuilder.PageContext) gomponents.Node {
			return html.Div(
				html.Class("container mx-auto px-4 py-8 dark:bg-gray-900 dark:text-gray-100 h-full overflow-y-auto"),
				html.Div(
					html.Class("flex justify-between items-center mb-6"),
					html.H1(
						html.Class("text-3xl font-bold tracking-tight text-gray-900 dark:text-white"),
						gomponents.Text(ctx.Locales["create_new_season"]),
					),
				),
				gomponents.If(props.ErrorMsg != "",
					html.Div(
						html.Class("bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded relative mb-4"),
						gomponents.Attr("role", "alert"),
						html.Span(
							html.Class("block sm:inline"),
							gomponents.Text(props.ErrorMsg),
						),
					),
				),
				html.Form(
					html.Class("bg-white dark:bg-gray-800 shadow rounded-lg p-6"),
					gomponents.Attr("hx-post", "/app/seasons"),
					gomponents.Attr("hx-target", "this"),
					gomponents.Attr("hx-swap", "outerHTML"),
					gomponents.Attr("x-data", "multiStepNewSeasonForm()"),
					gomponents.Attr("x-validate-form", ""),
					gomponents.Attr("@submit", "syncFormData"),

					// Step Progress Indicator
					templatesuistepindicator.StepIndicator(templatesuistepindicator.StepIndicatorProps{
						ShowNumbers:    true,
						ShowConnectors: true,
						ShowTitle:      true,
						ShowStepOfText: true,
						Size:           "md",
						Lang:           props.Lang,
					}),

					html.Div(
						html.Class("space-y-4"),
						// Step 1: Basic Info
						html.Template(
							gomponents.Attr("x-if", "currentStep === 1"),
							html.Div(
								form.FormInput(form.FormInputProps{
									Label:       ctx.Locales["season_name"],
									Type:        "text",
									ID:          "name",
									Name:        "name",
									Required:    true,
									Placeholder: ctx.Locales["enter_season_name"],
									XModel:      "formData.name",
								}),
								html.Div(
									html.Label(
										html.For("description"),
										html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
										gomponents.Text("Description"),
									),
									html.Textarea(
										html.ID("description"),
										html.Name("description"),
										html.Class("appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300"),
										html.Placeholder("Enter a description (optional)"),
										gomponents.Attr("x-model", "formData.description"),
									),
								),
							),
						),
						// Step 2: Dates
						html.Template(
							gomponents.Attr("x-if", "currentStep === 2"),
							html.Div(
								html.Div(
									html.Class("pb-5"),
									infobubble.InfoBubble(infobubble.InfoBubbleProps{
										TitleKey:   "start_date_title",
										LocaleKey:  "start_date_explanation",
										Lang:       ctx.Lang,
										Class:      "mt-2",
										DataTestID: "start-date-info-box",
									}),
								),
								// Use DatePickerComponent for start and end dates
								// Start Date
								datepicker.DatePickerComponent(datepicker.DatePickerProps{
									Label:       ctx.Locales["start_date"],
									Name:        "startDate",
									Placeholder: "Select a start date",
									Class:       "",
									Lang:        ctx.Lang,
									XModel:      "formData.startDate",
								}),
							),
						),
						// Step 3: Settings
						html.Template(
							gomponents.Attr("x-if", "currentStep === 3"),
							html.Div(
								uiselect.Select(uiselect.SelectProps{
									Items: []uiselect.SelectItem{
										{Value: "pool", Title: "Pool"},
										{Value: "bowling", Title: "Bowling"},
										{Value: "other", Title: "Other"},
									},
									Name:        "seasontype",
									ID:          "seasontype",
									Class:       "w-full",
									Placeholder: "Select season type",
									Label:       "Season Type",
									Required:    true,
									XModel:      "formData.seasontype",
									DataTestID:  "seasontype-button",
								}),
								uiselect.Select(uiselect.SelectProps{
									Items: []uiselect.SelectItem{
										{Value: "weekly", Title: "Weekly"},
										{Value: "biweekly", Title: "Biweekly"},
										{Value: "monthly", Title: "Monthly"},
										{Value: "quarterly", Title: "Quarterly"},
										{Value: "yearly", Title: "Yearly"},
									},
									Name:        "frequency",
									ID:          "frequency",
									Class:       "w-full",
									Placeholder: "Select frequency",
									Label:       "Frequency",
									Required:    true,
									XModel:      "formData.frequency",
									DataTestID:  "frequency-button",
								}),
								form.FormInput(form.FormInputProps{
									Label:       "Amount of Tables",
									Type:        "number",
									ID:          "amountOfTables",
									Name:        "amountOfTables",
									Required:    true,
									Placeholder: "Enter number of tables",
									XModel:      "formData.amountOfTables",
								}),
							),
						),
						// Step 4: Player Selection
						html.Template(
							gomponents.Attr("x-if", "currentStep === 4"),
							html.Div(
								gomponents.Attr("x-data", "{ allPlayers: "+playersJSON+", playerSearchQuery: '', get filteredPlayers() { return this.playerSearchQuery ? this.allPlayers.filter(player => player.Name.toLowerCase().includes(this.playerSearchQuery.toLowerCase())) : this.allPlayers } }"),
								html.Label(
									html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"),
									gomponents.Text("Select Players (minimum 2 required)"),
								),
								// Search input for filtering players
								html.Div(
									html.Class("mb-4"),
									html.Input(
										html.Type("text"),
										html.Placeholder("Search players..."),
										html.Class("appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300"),
										gomponents.Attr("x-model", "playerSearchQuery"),
									),
								),
								html.Div(
									html.Class("border border-gray-300 rounded-md p-4 max-h-60 overflow-y-auto"),
									html.Template(
										gomponents.Attr("x-for", "(player, index) in filteredPlayers"),
										gomponents.Attr(":key", "player.ID"),
										html.Label(
											html.Class("flex items-center space-x-2 py-1"),
											html.Input(
												html.Type("checkbox"),
												html.Name("playerIds[]"),
												gomponents.Attr("x-bind:value", "player.ID"),
												gomponents.Attr("x-model", "formData.playerIds"),
												html.Class("rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"),
											),
											html.Span(
												gomponents.Attr("x-text", "player.Name"),
												html.Class("text-sm text-gray-700 dark:text-gray-300"),
											),
										),
									),
								),
							),
						),
						// Step 5: Review & Confirm
						html.Template(
							gomponents.Attr("x-if", "currentStep === 5"),
							html.Div(
								html.Class("space-y-6"),
								// Title and subtitle
								html.Div(
									html.Class("text-center"),
									html.H3(
										html.Class("text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"),
										gomponents.Text(ctx.Locales["review_summary_title"]),
									),
									html.P(
										html.Class("text-sm text-gray-600 dark:text-gray-400"),
										gomponents.Text(ctx.Locales["review_summary_subtitle"]),
									),
								),
								// Review sections
								html.Div(
									html.Class("grid gap-6 md:grid-cols-2"),
									// Basic Information
									html.Div(
										html.Class("bg-gray-50 dark:bg-gray-700 rounded-lg p-4"),
										html.H4(
											html.Class("font-medium text-gray-900 dark:text-gray-100 mb-3"),
											gomponents.Text(ctx.Locales["review_basic_info"]),
										),
										html.Dl(
											html.Class("space-y-2"),
											html.Div(
												html.Dt(
													html.Class("text-sm font-medium text-gray-500 dark:text-gray-400"),
													gomponents.Text(ctx.Locales["season_name"]),
												),
												html.Dd(
													html.Class("text-sm text-gray-900 dark:text-gray-100"),
													gomponents.Attr("x-text", "formData.name"),
												),
											),
										),
									),
									// Schedule
									html.Div(
										html.Class("bg-gray-50 dark:bg-gray-700 rounded-lg p-4"),
										html.H4(
											html.Class("font-medium text-gray-900 dark:text-gray-100 mb-3"),
											gomponents.Text(ctx.Locales["review_schedule"]),
										),
										html.Dl(
											html.Class("space-y-2"),
											html.Div(
												html.Dt(
													html.Class("text-sm font-medium text-gray-500 dark:text-gray-400"),
													gomponents.Text(ctx.Locales["start_date"]),
												),
												html.Dd(
													html.Class("text-sm text-gray-900 dark:text-gray-100"),
													gomponents.Attr("x-text", "formData.startDate"),
												),
											),
										),
									),
									// Settings
									html.Div(
										html.Class("bg-gray-50 dark:bg-gray-700 rounded-lg p-4"),
										html.H4(
											html.Class("font-medium text-gray-900 dark:text-gray-100 mb-3"),
											gomponents.Text(ctx.Locales["review_settings"]),
										),
										html.Dl(
											html.Class("space-y-2"),
											html.Div(
												html.Dt(
													html.Class("text-sm font-medium text-gray-500 dark:text-gray-400"),
													gomponents.Text(ctx.Locales["season_type"]),
												),
												html.Dd(
													html.Class("text-sm text-gray-900 dark:text-gray-100"),
													html.Template(
														gomponents.Attr("x-if", "formData.seasontype === 'pool'"),
														gomponents.Text(ctx.Locales["season_type_pool"]),
													),
													html.Template(
														gomponents.Attr("x-if", "formData.seasontype === 'bowling'"),
														gomponents.Text(ctx.Locales["season_type_bowling"]),
													),
													html.Template(
														gomponents.Attr("x-if", "formData.seasontype === 'other'"),
														gomponents.Text(ctx.Locales["season_type_other"]),
													),
												),
											),
											html.Div(
												html.Dt(
													html.Class("text-sm font-medium text-gray-500 dark:text-gray-400"),
													gomponents.Text(ctx.Locales["frequency"]),
												),
												html.Dd(
													html.Class("text-sm text-gray-900 dark:text-gray-100"),
													html.Template(
														gomponents.Attr("x-if", "formData.frequency === 'weekly'"),
														gomponents.Text(ctx.Locales["frequency_weekly"]),
													),
													html.Template(
														gomponents.Attr("x-if", "formData.frequency === 'biweekly'"),
														gomponents.Text(ctx.Locales["frequency_biweekly"]),
													),
													html.Template(
														gomponents.Attr("x-if", "formData.frequency === 'monthly'"),
														gomponents.Text(ctx.Locales["frequency_monthly"]),
													),
													html.Template(
														gomponents.Attr("x-if", "formData.frequency === 'quarterly'"),
														gomponents.Text(ctx.Locales["frequency_quarterly"]),
													),
													html.Template(
														gomponents.Attr("x-if", "formData.frequency === 'yearly'"),
														gomponents.Text(ctx.Locales["frequency_yearly"]),
													),
												),
											),
											html.Div(
												html.Dt(
													html.Class("text-sm font-medium text-gray-500 dark:text-gray-400"),
													gomponents.Text(ctx.Locales["number_of_tables"]),
												),
												html.Dd(
													html.Class("text-sm text-gray-900 dark:text-gray-100"),
													gomponents.Attr("x-text", "formData.amountOfTables"),
												),
											),
										),
									),
									// Players
									html.Div(
										html.Class("bg-gray-50 dark:bg-gray-700 rounded-lg p-4"),
										html.H4(
											html.Class("font-medium text-gray-900 dark:text-gray-100 mb-3"),
											gomponents.Text(ctx.Locales["review_players"]),
										),
										html.Dl(
											html.Class("space-y-2"),
											html.Div(
												html.Dt(
													html.Class("text-sm font-medium text-gray-500 dark:text-gray-400"),
													gomponents.Text(ctx.Locales["review_players_count"]),
												),
												html.Dd(
													html.Class("text-sm text-gray-900 dark:text-gray-100"),
													gomponents.Attr("x-text", "formData.playerIds ? formData.playerIds.length : 0"),
												),
											),
										),
									),
								),
							),
						),
					),
					html.Div(
						html.Class("mt-6 flex justify-between"),
						// Previous button
						button.SecondaryButton(button.BaseButtonConfig{
							ID:         "prev-step-btn",
							ButtonType: "button",
							Text:       "Previous",
							Attrs: []gomponents.Node{
								gomponents.Attr("@click", "prevStep()"),
								gomponents.Attr("x-show", "currentStep > 1"),
							},
						}),
						// Next button
						button.PrimaryButton(button.BaseButtonConfig{
							ID:         "next-step-btn",
							ButtonType: "button",
							Text:       "Next",
							Attrs: []gomponents.Node{
								gomponents.Attr("@click.prevent", "nextStep()"),
								gomponents.Attr("x-show", "currentStep < totalSteps"),
								gomponents.Attr(":disabled", "!canProceedToNextStep()"),
							},
						}),
						// Submit button
						button.PrimaryButton(button.BaseButtonConfig{
							ID:         "create-season-submit",
							ButtonType: "submit",
							Text:       ctx.Locales["create_season"],
							Attrs: []gomponents.Node{
								gomponents.Attr("x-show", "currentStep === totalSteps"),
							},
						}),
					),
				),
			)
		})
}
