package templatesappseasons

import (
	"strconv"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type DeleteConfirmModalProps struct {
	SeasonID   int32
	SeasonName string
	Lang       string
}

func DeleteConfirmModal(props DeleteConfirmModalProps) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/deleteConfirmModal.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{
			"title":   "Delete Season",
			"message": "Are you sure you want to delete this season?",
			"confirm": "Delete",
			"cancel":  "Cancel",
		}
	}

	seasonIdStr := strconv.Itoa(int(props.SeasonID))

	confirmButton := button.PrimaryIconButton(button.IconButtonConfig{
		ButtonType: "button",
		Text:       locales["confirm"],
		DataTestID: "confirm-delete-season",
		Class:      "bg-red-600 hover:bg-red-700 border-red-600 focus:ring-red-500",
		Icon:       icons.Trash(html.Class("w-4 h-4")),
		Attrs: []gomponents.Node{
			htmx.Delete("/app/seasons/" + seasonIdStr),
			htmx.Target("#toast-body-container"),
			htmx.Swap("afterbegin"),
			gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { deleteSeasonModal_modalOpen = false }"),
		},
	})

	// Create modal content for confirmation
	modalContent := html.Div(
		html.Class("space-y-4"),
		html.P(
			html.Class("text-gray-700 dark:text-gray-300"),
			gomponents.Text(locales["message"]+" \""+props.SeasonName+"\"?"),
		),
		html.Div(
			html.Class("flex justify-end space-x-3 pt-4"),
			button.BaseButton(button.BaseButtonConfig{
				Text:       locales["cancel"],
				ButtonType: "button",
				Class:      "px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-700",
				Attrs: []gomponents.Node{
					gomponents.Attr("@click", "deleteSeasonModal_modalOpen = false"),
				},
			}),
			confirmButton,
		),
	)

	// Use ClientModalWithAutoOpen since this is loaded via HTMX and should open immediately
	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:    "deleteSeasonModal",
		Title:      locales["title"],
		Content:    modalContent,
		DataTestID: "delete-season-modal",
	})
}
