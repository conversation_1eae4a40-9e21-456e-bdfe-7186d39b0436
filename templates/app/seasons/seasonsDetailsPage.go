package templatesappseasons

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type SeasonsDetailsProps struct {
	Matches       []db.GetMatchesBySeasonIdRow
	CustomColumns []db.MatchCustomColumn
	CustomValues  map[int32][]db.MatchCustomValue
	Lang          string
	SeasonName    string // Added season name for the title
	SeasonId      int32
	Seasons       []db.Season
	IsSideBarOpen bool
	Params        MatchesTableParams
	TotalItems    int
	TotalPages    int
}

func SeasonsDetails(props SeasonsDetailsProps) gomponents.Node {
	return pagebuilder.NewPage("/app/seasons").
		WithLocaleBase("app/seasons/seasonsDetailsPage").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSideBarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			return SeasonDetailsPageContent(SeasonDetailsPageContentProps{
				Matches:       props.Matches,
				CustomColumns: props.CustomColumns,
				CustomValues:  props.CustomValues,
				Lang:          props.Lang,
				SeasonName:    props.SeasonName,
				SeasonId:      props.SeasonId,
				Params:        props.Params,
				TotalItems:    props.TotalItems,
				TotalPages:    props.TotalPages,
			})
		})
}
