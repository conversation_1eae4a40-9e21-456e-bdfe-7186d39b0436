package templatesappseasons

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/toast"
	"maragu.dev/gomponents"
)

func DeleteSuccess(lang string) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/deleteSuccess.locales.json", lang)
	if err != nil {
		locales = map[string]string{
			"message": "Season deleted successfully",
		}
	}

	return toast.Toast(toast.ToastConfig{
		ID:         "delete-season-success-toast",
		Message:    locales["message"],
		Style:      "success",
		DataTestID: "delete-season-success-toast",
		AutoClose:  true,
	})
}
