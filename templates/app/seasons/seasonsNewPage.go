package templatesappseasons

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/pagebuilder"
	"maragu.dev/gomponents"
)

type SeasonsNewPageProps struct {
	Seasons       []db.Season
	AllPlayers    []db.Player
	IsSidebarOpen bool
	ActiveLink    string
	Lang          string
}

func SeasonsNewPage(props SeasonsNewPageProps) gomponents.Node {
	return pagebuilder.NewPage("/app/seasons/new").
		WithLocaleBase("app/seasons/seasonsNewPage").
		RenderFullPage(pagebuilder.AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: pagebuilder.IsDevelopment(),
		}, func(ctx pagebuilder.PageContext) gomponents.Node {
			contentProps := SeasonsNewPageContentProps{
				Seasons:       props.Seasons,
				AllPlayers:    props.AllPlayers,
				IsSidebarOpen: props.IsSidebarO<PERSON>,
				ActiveLink:    props.ActiveLink,
				Lang:          props.Lang,
			}
			return SeasonsNewPageContent(contentProps)
		})
}
