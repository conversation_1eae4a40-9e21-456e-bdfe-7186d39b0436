package templatesappseasons

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// SeasonsError renders an error message for the seasons page.
func SeasonsError(errorMessage string) gomponents.Node {
	return html.Div(
		html.Class("flex items-center justify-center min-h-screen bg-gray-100"),
		html.Div(
			html.Class("w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md"),
			html.Div(
				html.Class("bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"),
				html.Role("alert"),
				html.Span(
					html.Class("block sm:inline"),
					gomponents.Text(errorMessage),
				),
			),
		),
	)
}

// SeasonsSuccess renders a success message fragment that can be inserted into the season page.
func SeasonsSuccess(successMessage string) gomponents.Node {
	return html.Div(
		html.Class("bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4"),
		html.Role("alert"),
		html.Span(
			html.Class("block sm:inline"),
			gomponents.Text(successMessage),
		),
	)
}
