package templatesappseasons

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatesprint "github.com/j-em/coachpad/templates/ui/print"
	"github.com/j-em/coachpad/utils/matchutils"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PrintMatchesProps struct {
	Matches    []db.GetMatchesBySeasonIdRow
	SeasonName string
	Lang       string
}

func PrintMatches(props PrintMatchesProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/printMatches.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	content := html.Div(
		html.Class("print-header"),
		html.H1(
			html.Class("print-title"),
			gomponents.Text(locales["print_title"]+" - "+props.SeasonName),
		),
		html.Table(
			html.Class("print-table"),
			html.THead(
				html.Tr(
					html.Th(gomponents.Text(locales["date"])),
					html.Th(gomponents.Text(locales["player_1"])),
					html.Th(gomponents.Text(locales["points_1"])),
					html.Th(gomponents.Text(locales["player_2"])),
					html.Th(gomponents.Text(locales["points_2"])),
					html.Th(gomponents.Text(locales["group"])),
					html.Th(gomponents.Text(locales["winner"])),
				),
			),
			html.TBody(
				gomponents.Group(
					gomponents.Map(props.Matches, func(match db.GetMatchesBySeasonIdRow) gomponents.Node {
						return html.Tr(
							html.Td(gomponents.Text(matchutils.FormatMatchDate(match.MatchDate, matchutils.DateFormatYMD))),
							html.Td(gomponents.Text(match.Player1Name.String)),
							html.Td(gomponents.Text(matchutils.FormatPlayerPoints(match.PlayerId1Points))),
							html.Td(gomponents.Text(match.Player2Name.String)),
							html.Td(gomponents.Text(matchutils.FormatPlayerPoints(match.PlayerId2Points))),
							html.Td(gomponents.Text(matchutils.FormatMatchGroup(match.MatchGroup))),
							html.Td(gomponents.Text(matchutils.GetWinnerName(match))),
						)
					}),
				),
			),
		),
	)

	return templatesprint.PrintPage(templatesprint.PrintPageProps{
		Title:   locales["print_title"] + " - " + props.SeasonName,
		Lang:    props.Lang,
		Content: content,
	})
}
