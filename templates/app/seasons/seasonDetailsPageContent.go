package templatesappseasons

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	tablesearchbar "github.com/j-em/coachpad/templates/ui/tableSearchBar"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type SeasonDetailsPageContentProps struct {
	Matches       []db.GetMatchesBySeasonIdRow
	CustomColumns []db.MatchCustomColumn
	CustomValues  map[int32][]db.MatchCustomValue
	Lang          string
	SeasonName    string // Added to include season name in the content
	SeasonId      int32
	Params        MatchesTableParams
	TotalItems    int
	TotalPages    int
}

func SeasonDetailsPageContent(props SeasonDetailsPageContentProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/seasonDetailsPageContent.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		html.Class("h-full flex flex-col md:px-5 py-2"),
		html.Head(
			html.Title(locales["page_title"]),
		),
		html.Div(
			html.Class("min-h-0 flex flex-col"),
			// Search bar stays outside the refreshable container
			tablesearchbar.TableSearchBar(tablesearchbar.TableSearchBarProps{
				SearchValue:      props.Params.Search,
				SearchDataTestID: "match-search-input",
				BaseURL:          fmt.Sprintf("/app/seasons/%d/matches-table", props.SeasonId),
				TargetID:         "matches-table-content",
				Filters: []tablesearchbar.FilterOption{
					{
						Type:       tablesearchbar.FilterTypeCheckbox,
						Name:       "filter_today",
						Label:      "", // Will use default from locales
						Value:      "true",
						Checked:    props.Params.FilterToday,
						DataTestID: "",
					},
				},
				HiddenInputs: map[string]string{
					"page":     strconv.Itoa(props.Params.Page),
					"per_page": strconv.Itoa(props.Params.ItemsPerPage),
					"sort":     props.Params.Sort,
					"dir":      props.Params.Direction,
				},
				Lang: props.Lang,
			}),
			MatchesDashboardPageContent(MatchesTableProps{
				Matches:       props.Matches,
				CustomColumns: props.CustomColumns,
				CustomValues:  props.CustomValues,
				Lang:          props.Lang,
				SeasonId:      props.SeasonId,
				SeasonName:    props.SeasonName,
				Params:        props.Params,
				TotalItems:    props.TotalItems,
				TotalPages:    props.TotalPages,
			}),
		),
	)
}
