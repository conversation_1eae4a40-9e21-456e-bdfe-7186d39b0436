package pagebuilder

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/titleservice"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// PageBuilder provides a fluent interface for building pages with consistent patterns
type PageBuilder struct {
	route        string
	localeBase   string
	titleKey     string
	target       string
	swap         string
	trigger      string
	htmxAttrs    []gomponents.Node
	class        string
	titleService *titleservice.TitleService
}

// PageContext provides standardized context for page rendering
type PageContext struct {
	Lang          string
	Locales       map[string]string
	Route         string
	StandardProps AppLayoutProps
}

// AppLayoutProps mirrors the existing layouts.AppLayoutProps for consistency
type AppLayoutProps struct {
	Title         string
	Year          int
	ShowFooter    bool
	ActiveLink    string
	Seasons       []db.Season
	IsSidebarOpen bool
	Lang          string
	IsDevelopment bool
	UserID        int32
	UnreadCount   int64
}

// ContentFunc represents a function that renders page content
type ContentFunc func(ctx PageContext) gomponents.Node

// NewPage creates a new PageBuilder with sensible defaults
func NewPage(route string) *PageBuilder {
	return &PageBuilder{
		route:        route,
		localeBase:   deriveLocaleBase(route),
		titleKey:     "page_title",
		target:       "#app-layout-content",
		swap:         "innerHTML",
		htmxAttrs:    []gomponents.Node{},
		titleService: titleservice.NewTitleService(),
	}
}

// WithLocaleBase sets a custom locale base path
func (pb *PageBuilder) WithLocaleBase(localeBase string) *PageBuilder {
	pb.localeBase = localeBase
	return pb
}

// WithTitle sets a custom title key (defaults to "page_title")
func (pb *PageBuilder) WithTitle(titleKey string) *PageBuilder {
	pb.titleKey = titleKey
	return pb
}

// WithTarget sets a custom HTMX target (defaults to "#app-layout-content")
func (pb *PageBuilder) WithTarget(target string) *PageBuilder {
	pb.target = target
	return pb
}

// WithSwap sets a custom HTMX swap strategy (defaults to "innerHTML")
func (pb *PageBuilder) WithSwap(swap string) *PageBuilder {
	pb.swap = swap
	return pb
}

// WithTrigger sets an HTMX trigger
func (pb *PageBuilder) WithTrigger(trigger string) *PageBuilder {
	pb.trigger = trigger
	return pb
}

// WithHTMXAttr adds custom HTMX attributes
func (pb *PageBuilder) WithHTMXAttr(attr gomponents.Node) *PageBuilder {
	pb.htmxAttrs = append(pb.htmxAttrs, attr)
	return pb
}

// WithClass sets custom CSS classes for the content wrapper div
func (pb *PageBuilder) WithClass(class string) *PageBuilder {
	pb.class = class
	return pb
}

// GetTitleService returns the title service for external use
func (pb *PageBuilder) GetTitleService() *titleservice.TitleService {
	return pb.titleService
}

// RenderContent renders a page content component (for HTMX swaps)
func (pb *PageBuilder) RenderContent(lang string, contentFunc ContentFunc) gomponents.Node {
	ctx := pb.buildContext(lang)

	// Get title using the title service
	title := pb.titleService.GetTitleFromLocales(ctx.Locales, pb.titleKey)

	// Build attributes including HTMX configuration
	attrs := []gomponents.Node{
		// Auto-inject title for HTMX head support
		html.Head(
			html.TitleEl(
				gomponents.Text(title),
			),
		),
	}

	// Add CSS classes if specified
	if pb.class != "" {
		attrs = append(attrs, html.Class(pb.class))
	}

	// Add HTMX attributes
	attrs = append(attrs, pb.buildHTMXAttrs()...)

	// Add the actual content
	attrs = append(attrs, contentFunc(ctx))

	return html.Div(attrs...)
}

// RenderFullPage renders a complete page with layout (for initial loads)
func (pb *PageBuilder) RenderFullPage(props AppLayoutProps, contentFunc ContentFunc) gomponents.Node {
	ctx := pb.buildContextFromProps(props)

	// Get base title using the title service
	baseTitle := pb.titleService.GetTitleFromLocales(ctx.Locales, pb.titleKey)

	// Format title with notification count for initial page load
	finalTitle := pb.titleService.GetTitleWithNotificationCount(baseTitle, props.UnreadCount)

	return layouts.AppLayout(layouts.AppLayoutProps{
		Title:         finalTitle,
		Year:          time.Now().Year(),
		ShowFooter:    true,
		ActiveLink:    pb.route,
		Seasons:       props.Seasons,
		IsSidebarOpen: props.IsSidebarOpen,
		Lang:          props.Lang,
		IsDevelopment: props.IsDevelopment,
		UserID:        props.UserID,
		UnreadCount:   props.UnreadCount,
		Content:       contentFunc(ctx),
	})
}

// buildContext creates a PageContext with loaded locales
func (pb *PageBuilder) buildContext(lang string) PageContext {
	locales := pb.loadLocales(lang)

	return PageContext{
		Lang:    lang,
		Locales: locales,
		Route:   pb.route,
		StandardProps: AppLayoutProps{
			Lang: lang,
		},
	}
}

// buildContextFromProps creates a PageContext from existing props
func (pb *PageBuilder) buildContextFromProps(props AppLayoutProps) PageContext {
	locales := pb.loadLocales(props.Lang)

	return PageContext{
		Lang:          props.Lang,
		Locales:       locales,
		Route:         pb.route,
		StandardProps: props,
	}
}

// loadLocales loads locales with fallback error handling
func (pb *PageBuilder) loadLocales(lang string) map[string]string {
	localePath := fmt.Sprintf("./templates/%s.locales.json", pb.localeBase)
	locales, err := i18n.LoadTemplateLocales(localePath, lang)
	if err != nil {
		// Fallback to empty map with default page title from title service
		locales = map[string]string{
			"page_title": pb.titleService.GetDefaultTitle(),
		}
	}
	return locales
}

// buildHTMXAttrs constructs HTMX attributes based on configuration
func (pb *PageBuilder) buildHTMXAttrs() []gomponents.Node {
	attrs := []gomponents.Node{}

	if pb.target != "" {
		attrs = append(attrs, htmx.Target(pb.target))
	}

	if pb.swap != "" {
		attrs = append(attrs, htmx.Swap(pb.swap))
	}

	if pb.trigger != "" {
		attrs = append(attrs, htmx.Trigger(pb.trigger))
	}

	// Add any custom HTMX attributes
	attrs = append(attrs, pb.htmxAttrs...)

	return attrs
}

// deriveLocaleBase automatically derives locale base path from route
func deriveLocaleBase(route string) string {
	// Remove leading slash and convert to file path
	// /app/players -> app/players/players
	// /app/settings/user -> app/settings/user

	trimmed := strings.TrimPrefix(route, "/")
	parts := strings.Split(trimmed, "/")

	if len(parts) >= 2 {
		// For routes like /app/players, use app/players/players
		basePath := strings.Join(parts, "/")
		lastPart := parts[len(parts)-1]
		return fmt.Sprintf("%s/%s", basePath, lastPart)
	}

	// Fallback for simple routes
	return trimmed
}

// LoadLocalesOnly loads locales without rendering (useful for HTMLLayout pages)
func (pb *PageBuilder) LoadLocalesOnly(lang string) map[string]string {
	return pb.loadLocales(lang)
}

// Helper function to check if development environment
func IsDevelopment() bool {
	return os.Getenv("APP_ENV") == "development"
}
