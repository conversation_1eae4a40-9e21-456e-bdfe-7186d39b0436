package pagebuilder

import (
	"github.com/j-em/coachpad/db"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// This code is for demonstration purposes only and should not be used in production.
// ExamplePageProps demonstrates the expected props structure
type ExamplePageProps struct {
	Lang          string
	Seasons       []db.Season
	IsSidebarOpen bool
	SomeData      []string // Custom data for this page
}

// ExamplePageContent demonstrates using PageBuilder for content components
func ExamplePageContent(lang string, data []string) gomponents.Node {
	return NewPage("/app/example").
		WithLocaleBase("app/example/example"). // Custom locale path
		WithTitle("page_title").               // Uses "page_title" key from locales
		RenderContent(lang, func(ctx PageContext) gomponents.Node {
			return html.Div(
				html.Class("p-4"),
				html.H1(
					html.Class("text-2xl font-bold"),
					gomponents.Text(ctx.Locales["welcome_message"]),
				),
				html.Ul(
					gomponents.Group(
						gomponents.Map(data, func(item string) gomponents.Node {
							return html.Li(gomponents.Text(item))
						}),
					),
				),
			)
		})
}

// ExamplePage demonstrates using PageBuilder for full page components
func ExamplePage(props ExamplePageProps) gomponents.Node {
	return NewPage("/app/example").
		WithLocaleBase("app/example/examplePage").
		RenderFullPage(AppLayoutProps{
			Lang:          props.Lang,
			Seasons:       props.Seasons,
			IsSidebarOpen: props.IsSidebarOpen,
			IsDevelopment: IsDevelopment(),
		}, func(ctx PageContext) gomponents.Node {
			return ExamplePageContent(ctx.Lang, props.SomeData)
		})
}

// ExampleWithCustomHTMX demonstrates advanced HTMX configuration
func ExampleWithCustomHTMX(lang string) gomponents.Node {
	return NewPage("/app/example/search").
		WithTarget("#search-results").
		WithSwap("outerHTML").
		WithTrigger("search from:body").
		RenderContent(lang, func(ctx PageContext) gomponents.Node {
			return html.Div(
				html.ID("search-results"),
				html.P(gomponents.Text(ctx.Locales["search_results"])),
			)
		})
}

// ExampleWithCustomClass demonstrates using custom CSS classes on the content wrapper
func ExampleWithCustomClass(lang string) gomponents.Node {
	return NewPage("/app/example/styled").
		WithClass("bg-gray-100 p-6 rounded-lg shadow-md").
		WithTarget("#styled-content").
		RenderContent(lang, func(ctx PageContext) gomponents.Node {
			return html.Div(
				html.Class("space-y-4"),
				html.H2(
					html.Class("text-xl font-semibold"),
					gomponents.Text(ctx.Locales["styled_title"]),
				),
				html.P(
					html.Class("text-gray-600"),
					gomponents.Text(ctx.Locales["styled_description"]),
				),
			)
		})
}
