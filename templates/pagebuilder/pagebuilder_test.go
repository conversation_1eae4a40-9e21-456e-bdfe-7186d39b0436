package pagebuilder

import (
	"strings"
	"testing"

	"maragu.dev/gomponents"
)

func TestPageBuilderWithTitleService(t *testing.T) {
	// Test that PageBuilder correctly initializes with title service
	pb := NewPage("/app/test")

	if pb.titleService == nil {
		t.Fatal("PageBuilder should have title service initialized")
	}

	if pb.titleService.GetDefaultTitle() != "GamePlan" {
		t.<PERSON>("Expected default title 'GamePlan', got '%s'", pb.titleService.GetDefaultTitle())
	}
}

func TestPageBuilderTitleInContent(t *testing.T) {
	// Test that title appears in rendered content
	pb := NewPage("/app/test").WithTitle("page_title")

	// Mock content function
	contentFunc := func(ctx PageContext) gomponents.Node {
		return gomponents.Text("Test content")
	}

	content := pb.RenderContent("en", contentFunc)

	// Convert to string to check if title is included
	var builder strings.Builder
	err := content.Render(&builder)
	if err != nil {
		t.Fatalf("Failed to render content: %v", err)
	}

	html := builder.String()

	// Should contain a title element (even if it's the fallback)
	if !strings.Contains(html, "<title>") {
		t.Error("Rendered content should contain title element")
	}

	// Should contain the test content
	if !strings.Contains(html, "Test content") {
		t.Error("Rendered content should contain the test content")
	}
}
