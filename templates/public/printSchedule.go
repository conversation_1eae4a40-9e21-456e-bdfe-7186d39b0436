package public

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatesprint "github.com/j-em/coachpad/templates/ui/print"
	"github.com/j-em/coachpad/utils/matchutils"
	"github.com/jackc/pgx/v5/pgtype"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PrintScheduleProps struct {
	Matches       []db.GetMatchesBySeasonIdRow
	CustomColumns []db.MatchCustomColumn
	Season        db.Season
	Lang          string
}

func PrintSchedule(props PrintScheduleProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/public/printSchedule.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	content := html.Div(
		html.Class("print-header"),
		html.H1(
			html.Class("print-title"),
			gomponents.Text(locales["print_title"]+" - "+props.Season.Name),
		),
		html.P(
			html.Class("print-subtitle"),
			gomponents.Text(locales["season_info"]+" "+props.Season.Name),
		),
		html.Div(
			html.Class("print-table-container"),
			PrintScheduleTable(props.Matches, props.CustomColumns, locales),
		),
	)

	return templatesprint.PrintPage(templatesprint.PrintPageProps{
		Title:    locales["print_title"] + " - " + props.Season.Name,
		Lang:     props.Lang,
		PrintCSS: getSchedulePrintCSS(),
		Content:  content,
	})
}

func PrintScheduleTable(matches []db.GetMatchesBySeasonIdRow, customColumns []db.MatchCustomColumn, locales map[string]string) gomponents.Node {
	if len(matches) == 0 {
		return html.Div(
			html.Class("text-center py-8"),
			html.P(
				html.Class("text-gray-500"),
				gomponents.Text(locales["no_matches"]),
			),
		)
	}

	return html.Table(
		html.Class("print-table"),
		html.THead(
			html.Tr(
				html.Th(gomponents.Text(locales["match_date"])),
				html.Th(gomponents.Text(locales["player1"])),
				html.Th(gomponents.Text(locales["score"])),
				html.Th(gomponents.Text(locales["player2"])),
				html.Th(gomponents.Text(locales["group"])),
				// Add custom columns headers
				gomponents.Group(func() []gomponents.Node {
					var headers []gomponents.Node
					for _, col := range customColumns {
						if col.IsActive.Bool {
							headers = append(headers, html.Th(gomponents.Text(col.Name)))
						}
					}
					return headers
				}()),
			),
		),
		html.TBody(
			gomponents.Group(func() []gomponents.Node {
				var rows []gomponents.Node
				for _, match := range matches {
					rows = append(rows, PrintScheduleRow(match, customColumns))
				}
				return rows
			}()),
		),
	)
}

func PrintScheduleRow(match db.GetMatchesBySeasonIdRow, customColumns []db.MatchCustomColumn) gomponents.Node {
	return html.Tr(
		html.Td(gomponents.Text(matchutils.FormatMatchDate(match.MatchDate, matchutils.DateFormatYMD))),
		html.Td(gomponents.Text(match.Player1Name.String)),
		html.Td(gomponents.Text(formatScore(match.PlayerId1Points, match.PlayerId2Points))),
		html.Td(gomponents.Text(match.Player2Name.String)),
		html.Td(gomponents.Text(matchutils.FormatMatchGroup(match.MatchGroup))),
		// Add custom columns values (empty for now, can be expanded later)
		gomponents.Group(func() []gomponents.Node {
			var cells []gomponents.Node
			for _, col := range customColumns {
				if col.IsActive.Bool {
					cells = append(cells, html.Td(gomponents.Text(""))) // Empty for now
				}
			}
			return cells
		}()),
	)
}

func formatScore(points1, points2 pgtype.Int4) string {
	p1 := matchutils.FormatPlayerPoints(points1)
	p2 := matchutils.FormatPlayerPoints(points2)
	if p1 == "-" && p2 == "-" {
		return "-"
	}
	return p1 + " - " + p2
}

func getSchedulePrintCSS() string {
	return `
		.print-table-container {
			margin-top: 20px;
		}

		.print-table th:first-child,
		.print-table td:first-child {
			width: 100px;
			text-align: left;
		}

		.print-table th:nth-child(2),
		.print-table td:nth-child(2),
		.print-table th:nth-child(4),
		.print-table td:nth-child(4) {
			text-align: left;
		}

		.print-table th:nth-child(3),
		.print-table td:nth-child(3) {
			width: 80px;
			text-align: center;
		}

		.print-table th:nth-child(5),
		.print-table td:nth-child(5) {
			width: 80px;
			text-align: center;
		}
	`
}
