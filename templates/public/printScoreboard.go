package public

import (
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatesprint "github.com/j-em/coachpad/templates/ui/print"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PrintScoreboardProps struct {
	ScoreboardData []db.GetSeasonScoreboardRow
	Season         db.Season
	Lang           string
}

func PrintScoreboard(props PrintScoreboardProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/public/printScoreboard.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	content := html.Div(
		html.Class("print-header"),
		html.H1(
			html.Class("print-title"),
			gomponents.Text(locales["print_title"]+" - "+props.Season.Name),
		),
		html.P(
			html.Class("print-subtitle"),
			gomponents.Text(locales["season_info"]+" "+props.Season.Name),
		),
		html.Div(
			html.Class("print-table-container"),
			PrintScoreboardTable(props.ScoreboardData, locales),
		),
	)

	return templatesprint.PrintPage(templatesprint.PrintPageProps{
		Title:    locales["print_title"] + " - " + props.Season.Name,
		Lang:     props.Lang,
		PrintCSS: getScoreboardPrintCSS(),
		Content:  content,
	})
}

func PrintScoreboardTable(scoreboardData []db.GetSeasonScoreboardRow, locales map[string]string) gomponents.Node {
	if len(scoreboardData) == 0 {
		return html.Div(
			html.Class("text-center py-8"),
			html.P(
				html.Class("text-gray-500"),
				gomponents.Text(locales["no_players"]),
			),
		)
	}

	return html.Table(
		html.Class("print-table"),
		html.THead(
			html.Tr(
				html.Th(gomponents.Text(locales["rank"])),
				html.Th(gomponents.Text(locales["player_name"])),
				html.Th(gomponents.Text(locales["wins"])),
			),
		),
		html.TBody(
			gomponents.Group(func() []gomponents.Node {
				var rows []gomponents.Node
				for i, player := range scoreboardData {
					rows = append(rows, PrintScoreboardRow(i+1, player))
				}
				return rows
			}()),
		),
	)
}

func PrintScoreboardRow(rank int, player db.GetSeasonScoreboardRow) gomponents.Node {
	return html.Tr(
		html.Td(gomponents.Text("#"+strconv.Itoa(rank))),
		html.Td(gomponents.Text(player.PlayerName)),
		html.Td(gomponents.Text(strconv.FormatInt(player.Wins, 10))),
	)
}

func getScoreboardPrintCSS() string {
	return `
		.print-table-container {
			margin-top: 20px;
		}

		.print-table th:first-child,
		.print-table td:first-child {
			width: 80px;
			text-align: center;
		}

		.print-table th:last-child,
		.print-table td:last-child {
			width: 100px;
			text-align: center;
		}

		.print-table th:nth-child(2),
		.print-table td:nth-child(2) {
			text-align: left;
		}
	`
}
