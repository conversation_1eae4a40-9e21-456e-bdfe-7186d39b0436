package templatescomponents

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PublicNavConfig struct {
	CurrentPath string
	SeasonId    string
	Lang        string // Language for localization
}

func PublicNav(config PublicNavConfig) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/components/public_nav.locales.json", config.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Build season-specific paths if seasonId is provided
	schedulePath := "/public/schedule"
	scoreboardPath := "/public/scoreboard"

	if config.SeasonId != "" {
		schedulePath = "/public/seasons/" + config.SeasonId
		scoreboardPath = "/public/seasons/" + config.SeasonId + "/scoreboard"
	}
	return html.Nav(
		html.Class("bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"),
		gomponents.Attr("x-data", "mobileMenuToggle"),
		html.Div(
			html.Class("px-4 mx-auto max-w-7xl sm:px-6 lg:px-8"),
			html.Div(
				html.Class("flex justify-between h-16"),
				html.Div(
					html.Class("flex space-x-8"),
					// Desktop navigation
					html.Div(
						html.Class("hidden md:flex md:space-x-8"),
						PublicNavLink(schedulePath, locales["schedule"], config.CurrentPath == schedulePath || (config.SeasonId != "" && config.CurrentPath == "/public/seasons")),
						PublicNavLink(scoreboardPath, locales["scoreboard"], config.CurrentPath == scoreboardPath),
					),
					// Mobile menu button
					html.Div(
						html.Class("md:hidden flex items-center"),
						html.Button(
							html.Type("button"),
							html.Class("inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"),
							gomponents.Attr("@click", "toggle"),
							gomponents.Attr("aria-expanded", "false"),
							html.Span(html.Class("sr-only"), gomponents.Text(locales["open_main_menu"])),
							// Hamburger icon
							html.Div(
								html.Class("block h-6 w-6"),
								icons.Bars4(),
							),
						),
					),
				),
			),
			// Mobile menu
			html.Div(
				html.ID("mobile-menu"),
				html.Class("md:hidden hidden"),
				html.Div(
					html.Class("px-2 pt-2 pb-3 space-y-1 sm:px-3"),
					PublicNavLink(schedulePath, locales["schedule"], config.CurrentPath == schedulePath || (config.SeasonId != "" && config.CurrentPath == "/public/seasons")),
					PublicNavLink(scoreboardPath, locales["scoreboard"], config.CurrentPath == scoreboardPath),
				),
			),
		),
	)
}

func PublicNavLink(href, text string, isActive bool) gomponents.Node {
	activeClasses := "border-primary text-primary-900 dark:text-primary-100"
	inactiveClasses := "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-300 dark:hover:text-gray-100"

	classes := "inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
	if isActive {
		classes += " " + activeClasses
	} else {
		classes += " " + inactiveClasses
	}

	return html.A(
		html.Href(href),
		html.Class(classes),
		gomponents.Text(text),
	)
}
