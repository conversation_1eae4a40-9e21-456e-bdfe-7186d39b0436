package errortmpl

import (
	"github.com/j-em/coachpad/i18n"
	templatescomponents "github.com/j-em/coachpad/templates/components"
	"github.com/j-em/coachpad/templates/layouts"
	templatesuibutton "github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func Unauthorized401Page(lang string, message string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/components/errortmpl/unauthorized_401.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return layouts.HTMLLayout(locales["page_title"], html.Div(
		html.Class("min-h-screen bg-gray-50 dark:bg-gray-900"),

		// Public Header
		templatescomponents.PublicHeader(lang, nil),

		// Main Content
		html.Main(
			html.Class("flex-1 flex items-center justify-center px-4 py-16"),
			html.Div(
				html.Class("max-w-md w-full space-y-8 text-center"),

				// Error Code
				html.Div(
					html.Class("text-6xl font-extrabold text-red-500 dark:text-red-400"),
					gomponents.Text("401"),
				),

				// Main Heading
				html.H1(
					html.Class("mt-6 text-3xl font-bold text-gray-900 dark:text-white"),
					gomponents.Attr("data-testid", "401-heading"),
					gomponents.Text(locales["heading"]),
				),

				// Description
				html.P(
					html.Class("mt-4 text-lg text-gray-600 dark:text-gray-300"),
					gomponents.Attr("data-testid", "401-description"),
					gomponents.Text(locales["description"]),
				),

				// Error Message (if provided)
				gomponents.If(message != "",
					html.P(
						html.Class("mt-2 text-sm text-gray-500 dark:text-gray-400 italic"),
						gomponents.Text(message),
					),
				),

				// Action Buttons
				html.Div(
					html.Class("mt-8 space-y-4"),
					templatesuibutton.PrimaryButtonLink(templatesuibutton.ButtonLinkProps{
						Href:  "/signin",
						Text:  locales["sign_in_button"],
						Class: "w-full",
					}),
					html.Div(
						html.Class("w-full"),
						templatesuibutton.SecondaryButtonLink("/signup", locales["sign_up_button"]),
					),
				),
			),
		),
	))
}
