package errortmpl

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/layouts"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func ErrorPage(lang string, code int, message string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/components/errortmpl/error.locales.json", lang)
	if err != nil {
		// Handle error, perhaps log it or use default values
		locales = make(map[string]string)
	}
	return layouts.HTMLLayout("Coachpad - Error", html.Div(
		html.Class("flex items-center justify-center min-h-screen bg-gray-100"),
		html.Div(
			html.Class("w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md"),
			html.H1(
				html.Class("text-center text-3xl font-bold text-red-600"),
				gomponents.Textf("%d", code),
			),
			html.H2(
				html.Class("mt-6 text-center text-2xl font-bold text-gray-900"),
				gomponents.Text(locales["title"]),
			),
			html.P(
				html.Class("mt-2 text-center text-gray-600"),
				gomponents.Text(message),
			),
			html.Div(
				html.Class("mt-6 text-center"),
				html.A(
					html.Href("/"),
					html.Class("text-indigo-600 hover:text-indigo-500"),
					gomponents.Text(locales["return_home"]),
				),
			),
		),
	))
}
