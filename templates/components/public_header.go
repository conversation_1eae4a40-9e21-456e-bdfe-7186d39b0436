package templatescomponents

import (
	"github.com/j-em/coachpad/templates/components/applogo"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func PublicHeader(currentLang string, rightSide gomponents.Node) gomponents.Node {
	return html.Div(
		html.Class("flex items-center flex-col md:flex-row gap-7 px-3 py-3 sm:px-7 justify-between border-b bg-surface dark:bg-surface-dark"),
		html.Div(
			html.Class("flex gap-1 justify-center md:flex-row min-h-[100px] items-center"),
			html.Div(
				html.Class("flex items-center gap-3 w-[175px]"),
				applogo.AppLogo(currentLang),
			),
			PublicLangSwitcher(currentLang),
		),
		rightSide,
	)
}
