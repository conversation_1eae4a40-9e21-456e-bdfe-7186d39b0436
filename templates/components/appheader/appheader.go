package appheader

import (
	nav "github.com/j-em/coachpad/templates/app/nav"
	"github.com/j-em/coachpad/templates/components/applogo"
	"github.com/j-em/coachpad/templates/components/notificationbell"
	gomponents "maragu.dev/gomponents"
	html "maragu.dev/gomponents/html"
)

// AppHeader renders a global header shown when the sidebar is closed.
func AppHeader(lang string, userID int32, unreadCount int64) gomponents.Node {
	return gomponents.Group([]gomponents.Node{
		// Modal container for notifications (placed outside header to avoid positioning issues)
		html.Div(
			html.ID("notificationsModalContainer"),
		),
		html.Header(
			html.Class("fixed top-0 left-0 w-full h-19 shadow flex items-center px-6 z-[60] border-b bg-surface dark:bg-surface-dark"),
			gomponents.Attr("x-show", "!isSidebarOpen"),
			gomponents.Attr("x-cloak"),
			gomponents.Attr("x-transition:enter", "transition-all duration-150 ease-out"),
			gomponents.Attr("x-transition:enter-start", "opacity-0 transform -translate-y-full"),
			gomponents.Attr("x-transition:enter-end", "opacity-100 transform translate-y-0"),
			gomponents.Attr("x-transition:leave", "transition-all duration-150 ease-in"),
			gomponents.Attr("x-transition:leave-start", "transform translate-y-0"),
			gomponents.Attr("x-transition:leave-end", "transform -translate-y-full"),
			nav.NavToggleButton(true),
			html.Div(
				html.Class("flex items-center ml-3 w-[150px]"),
				applogo.AppLogo(lang),
			),
			// Notification bell
			notificationbell.NotificationBell(notificationbell.NotificationBellProps{
				Lang:        lang,
				UserID:      userID,
				UnreadCount: unreadCount,
				ID:          "notification-bell-header",
				HxTarget:    "#notification-bell-header",
			}),
		),
	})
}
