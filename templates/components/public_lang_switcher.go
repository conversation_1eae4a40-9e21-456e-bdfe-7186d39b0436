package templatescomponents

import (
	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PublicLangSwitcher renders the public header with language selector showing only the language
// that is not currently active
func PublicLangSwitcher(currentLang string) gomponents.Node {
	return html.Div(
		html.Class("flex space-x-2 items-center ml-4"),
		gomponents.If(currentLang != "en",
			html.Form(
				gomponents.Attr("hx-put", "/lang"),
				gomponents.Attr("hx-swap", "none"),
				gomponents.Attr("hx-on::after-request", "location.reload();"),
				html.Input(
					html.Type("hidden"),
					html.Name("lang"),
					html.Value("en"),
				),
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "submit",
					Text:       "EN",
				}),
			),
		),
		gomponents.If(currentLang != "fr",
			html.Form(
				gomponents.Attr("hx-put", "/lang"),
				gomponents.Attr("hx-swap", "none"),
				gomponents.Attr("hx-trigger", "click"),
				gomponents.Attr("hx-boost", "true"),
				gomponents.Attr("hx-on::after-request", "location.reload();"),
				html.Input(
					html.Type("hidden"),
					html.Name("lang"),
					html.Value("fr"),
				),
				button.SecondaryButton(button.BaseButtonConfig{
					ButtonType: "submit",
					Text:       "FR",
				}),
			),
		),
	)
}
