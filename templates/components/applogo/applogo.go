package applogo

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func AppLogo(lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/components/applogo/applogo.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return html.Picture(
		html.Source(
			html.SrcSet("/images/coachpad_dark_cropped.svg"),
			gomponents.Attr("media", "(prefers-color-scheme: dark)"),
		),
		html.Img(
			html.Src("/images/coachpad_light_cropped.svg"),
			html.Alt(locales["logo_alt"]),
		),
	)
}
