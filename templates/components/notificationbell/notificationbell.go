package notificationbell

import (
	"fmt"

	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// NotificationBellProps holds the data needed to render the notification bell
type NotificationBellProps struct {
	Lang        string
	UserID      int32
	UnreadCount int64
	ID          string // Unique ID for the element
	HxTarget    string // HTMX target selector
}

// NotificationBell renders a notification bell with unread count badge and recent notifications
func NotificationBell(props NotificationBellProps) gomponents.Node {
	// Don't render notification bell if no valid user ID
	if props.UserID == 0 {
		return html.Div() // Empty div as placeholder
	}

	return html.Div(
		html.Class("relative ml-auto mr-4"),
		html.ID(props.ID),
		gomponents.Attr("sse-connect", "/app/sse"),
		gomponents.Attr("sse-swap", "notification_count"),
		gomponents.Attr("hx-trigger", "sse:notification_count"),
		gomponents.Attr("hx-get", "/app/notifications/bell-html"),
		gomponents.Attr("hx-target", props.HxTarget),
		gomponents.Attr("hx-swap", "outerHTML"),

		// Bell button
		html.Button(
			html.Type("button"),
			html.Class("relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"),
			gomponents.Attr("hx-get", "/app/notifications"),
			gomponents.Attr("hx-target", "#notificationsModalContainer"),
			gomponents.Attr("hx-swap", "innerHTML"),
			gomponents.Attr("aria-label", "Notifications"),
			gomponents.Attr("data-testid", "notification-bell-button"),

			// Bell icon
			icons.Bell(html.Class("w-6 h-6")),

			// Unread count badge
			gomponents.If(props.UnreadCount > 0,
				html.Span(
					html.Class("absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full"),
					gomponents.Textf("%s", formatCount(props.UnreadCount)),
				),
			),
		),
	)
}

// formatCount formats the notification count for display (99+ for counts over 99)
func formatCount(count int64) string {
	if count > 99 {
		return "99+"
	}
	return fmt.Sprintf("%d", count)
}
