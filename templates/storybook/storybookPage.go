package storybook

import (
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/datepicker"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/icons"
	templatesuilangselect "github.com/j-em/coachpad/templates/ui/langselect"
	toast "github.com/j-em/coachpad/templates/ui/toast"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	gomponents "maragu.dev/gomponents"
	html "maragu.dev/gomponents/html"
)

// Storybook where we can test components
func StorybookPage() gomponents.Node {
	// SECTION: FormInput with Alpine.js real-time validation example
	// This example demonstrates real-time validation using Alpine.js and the FormInput component.

	// Add top-level Alpine.js data for error tracking
	return layouts.HTMLLayout("Coachpad - Storybook",
		html.Div(
			html.Class("flex flex-col min-h-screen"),
			gomponents.Attr("x-data", "{ errors: {}, errorMessages: {} }"),
			html.Div(
				html.Class("flex-1 p-4"),
				// Page Title & Description
				html.H1(
					html.Class("text-2xl font-bold mb-4"),
					gomponents.Text("Storybook"),
				),
				html.P(
					html.Class("mb-4"),
					gomponents.Text("This is a storybook page where you can test components."),
				),

				// SECTION: Buttons
				html.Div(
					html.Class("mb-8"),
					html.H2(
						html.Class("text-xl font-semibold mb-2"),
						gomponents.Text("Buttons"),
					),
					// Basic Buttons
					html.Div(
						html.Class("flex flex-wrap gap-4 items-center mb-4"),
						button.PrimaryButton(button.BaseButtonConfig{
							ButtonType: "button",
							Text:       "Primary Button",
						}),
						button.SecondaryButton(button.BaseButtonConfig{
							ButtonType: "button",
							Text:       "Secondary Button",
						}),
					),

					// Icon Buttons by Size
					html.Div(
						html.Class("flex flex-wrap gap-4 items-center mb-4"),
						html.Div(
							html.Class("flex flex-col gap-2"),
							html.Span(html.Class("font-semibold"), gomponents.Text("Small Icon Buttons")),
							button.PrimaryIconButton(button.IconButtonConfig{
								ID:         "storybook-add-btn-small",
								DataTestID: "storybook-add-btn-small",
								ButtonType: "button",
								Text:       "Add",
								Icon:       icons.Plus(),
								Size:       "small",
							}),
							button.SecondaryIconButton(button.IconButtonConfig{
								ID:         "storybook-check-btn-small",
								DataTestID: "storybook-check-btn-small",
								ButtonType: "button",
								Text:       "Check",
								Icon:       icons.Check(),
								Size:       "small",
							}),
						),
						html.Div(
							html.Class("flex flex-col gap-2"),
							html.Span(html.Class("font-semibold"), gomponents.Text("Default Icon Buttons")),
							button.PrimaryIconButton(button.IconButtonConfig{
								ID:         "storybook-add-btn-default",
								DataTestID: "storybook-add-btn-default",
								ButtonType: "button",
								Text:       "Add",
								Icon:       icons.Plus(),
								Size:       "default",
							}),
							button.SecondaryIconButton(button.IconButtonConfig{
								ID:         "storybook-check-btn-default",
								DataTestID: "storybook-check-btn-default",
								ButtonType: "button",
								Text:       "Check",
								Icon:       icons.Check(),
								Size:       "default",
							}),
						),
						html.Div(
							html.Class("flex flex-col gap-2"),
							html.Span(html.Class("font-semibold"), gomponents.Text("Large Icon Buttons")),
							button.PrimaryIconButton(button.IconButtonConfig{
								ID:         "storybook-add-btn-large",
								DataTestID: "storybook-add-btn-large",
								ButtonType: "button",
								Text:       "Add",
								Icon:       icons.Plus(),
								Size:       "large",
							}),
							button.SecondaryIconButton(button.IconButtonConfig{
								ID:         "storybook-check-btn-large",
								DataTestID: "storybook-check-btn-large",
								ButtonType: "button",
								Text:       "Check",
								Icon:       icons.Check(),
								Size:       "large",
							}),
						),
					),

					// IconButtonLink Examples
					html.Div(
						html.Class("flex flex-wrap gap-4 items-center mb-4"),
						button.IconButtonLink(button.IconButtonLinkConfig{
							ID:         "storybook-link-add",
							DataTestID: "storybook-link-add",
							Href:       "/add",
							Text:       "Add Link",
							Icon:       icons.Plus(),
						}),
						button.IconButtonLink(button.IconButtonLinkConfig{
							ID:         "storybook-link-check",
							DataTestID: "storybook-link-check",
							Href:       "/check",
							Text:       "Check Link",
							Icon:       icons.Check(),
						}),
						button.SecondaryIconButtonLink(button.IconButtonLinkConfig{
							ID:         "storybook-link-check",
							DataTestID: "storybook-link-check",
							Href:       "/check",
							Text:       "Check Link",
							Icon:       icons.Check(),
						}),
						button.PrimaryIconButtonLink(button.IconButtonLinkConfig{
							ID:         "storybook-link-add",
							DataTestID: "storybook-link-add",
							Href:       "/add",
							Text:       "Add Link",
							Icon:       icons.Plus(),
						}),
					),

					// Link Buttons
					html.Div(
						html.Class("flex flex-wrap gap-4 items-center mb-4"),
						button.PrimaryButtonLink(button.ButtonLinkProps{
							Href: "/",
							Text: "Primary Link Button",
						}),
						button.SecondaryButtonLink("/", "Secondary Link Button"),
					),

					// Loading State Test Buttons
					html.Div(
						html.Class("mb-4"),
						html.H2(
							html.Class("text-xl font-semibold mb-2"),
							gomponents.Text("Loading State Test Buttons"),
						),
						html.P(
							html.Class("mb-4 text-gray-600"),
							gomponents.Text("These buttons have htmx-request class added to demonstrate loading states:"),
						),
						html.Div(
							html.Class("flex flex-wrap gap-4 items-center mb-4"),
							button.PrimaryButton(button.BaseButtonConfig{
								ID:         "loading-test-base",
								ButtonType: "button",
								Text:       "Base Button Loading",
								Class:      "htmx-request",
							}),
							button.PrimaryIconButton(button.IconButtonConfig{
								ID:         "loading-test-icon",
								ButtonType: "button",
								Text:       "Icon Button Loading",
								Icon:       icons.Plus(),
								Class:      "htmx-request",
							}),
							button.PrimaryButtonLink(button.ButtonLinkProps{
								Href:  "#",
								Text:  "Button Link Loading",
								Class: "htmx-request",
							}),
							button.PrimaryIconButtonLink(button.IconButtonLinkConfig{
								ID:    "loading-test-icon-link",
								Href:  "#",
								Text:  "Icon Button Link Loading",
								Icon:  icons.Check(),
								Class: "htmx-request",
							}),
						),
					),

					// Regular Link Example
					html.Div(
						html.Class("mb-4"),
						html.H2(
							html.Class("text-xl font-semibold mb-2"),
							gomponents.Text("Regular Link"),
						),
						html.A(
							html.Href("https://example.com"),
							html.Class("text-blue-600 underline hover:text-blue-800"),
							gomponents.Text("Visit Example.com"),
						),
					),
				),

				// SECTION: Toasts
				html.Div(
					html.Class("mb-8"),
					html.H2(
						html.Class("text-xl font-semibold mb-2"),
						gomponents.Text("Toasts"),
					),
					toast.Container(
						toast.Toast(toast.ToastConfig{
							ID:         "toast-success",
							Message:    "This is a success message",
							Style:      "success",
							DataTestID: "toast-success-test",
							AutoClose:  false,
						}),
						toast.Toast(toast.ToastConfig{
							ID:         "toast-error",
							Message:    "This is an error message",
							Style:      "error",
							DataTestID: "toast-error-test",
							AutoClose:  true,
						}),
						toast.Toast(toast.ToastConfig{
							ID:         "toast-info",
							Message:    "This is an info message",
							Style:      "info",
							DataTestID: "toast-info-test",
							AutoClose:  true,
						}),
					),
				),
				// SECTION: Selects & Pickers
				html.Div(
					html.Class("mb-8"),
					html.H2(
						html.Class("text-xl font-semibold mb-2"),
						gomponents.Text("Select"),
					),
					// Basic Select
					uiselect.Select(uiselect.SelectProps{
						Items: []uiselect.SelectItem{
							{Value: "apple", Title: "Apple"},
							{Value: "banana", Title: "Banana"},
							{Value: "cherry", Title: "Cherry"},
							{Value: "date", Title: "Date", Disabled: true},
							{Value: "elderberry", Title: "Elderberry"},
						},
						Name:        "fruit",
						ID:          "storybook-fruit-select",
						Class:       "w-64",
						Placeholder: "Select a fruit",
						Label:       "Fruit",
						Required:    true,
					}),

					// Country Select
					html.H2(
						html.Class("text-xl font-semibold mb-2 mt-8"),
						gomponents.Text("Country Select"),
					),
					uiselect.CountrySelect(uiselect.CountrySelectProps{
						SelectProps: uiselect.SelectProps{
							ID:    "storybook-country-select",
							Name:  "country",
							Class: "w-64",
							Label: "Country",
						},
						Lang: "en",
					}),

					// Date Picker
					html.H2(
						html.Class("text-xl font-semibold mb-2 mt-8"),
						gomponents.Text("Date Picker"),
					),
					html.Label(
						html.For("storybook-datepicker"),
						html.Class("block mb-1 font-medium"),
						gomponents.Text("Pick a date"),
					),
					datepicker.DatePickerComponent(datepicker.DatePickerProps{
						Name:        "storybook-datepicker",
						Placeholder: "Select a date",
						Class:       "w-64",
					}),

					// Language Select
					html.H2(
						html.Class("text-xl font-semibold mb-2 mt-8"),
						gomponents.Text("Language Select"),
					),
					templatesuilangselect.LanguageSelect(templatesuilangselect.LanguageSelectConfig{
						Name:         "lang",
						DefaultValue: "en",
					}),

					html.P(gomponents.Text("With fr default value")),

					templatesuilangselect.LanguageSelect(templatesuilangselect.LanguageSelectConfig{
						Name:         "lang-fr",
						DefaultValue: "fr",
					}),
				),
				html.Div(
					html.Class("mb-8"),
					html.H2(
						html.Class("text-xl font-semibold mb-2 mt-8"),
						gomponents.Text("FormInput with Real-Time Validation (Alpine.js)"),
					),
					html.P(
						html.Class("mb-2 text-gray-600"),
						gomponents.Text("This example demonstrates real-time validation using Alpine.js and the FormInput component."),
					),
					html.Form(
						gomponents.Attr("x-data", "{ errors: {}, errorMessages: {}, fields: {} }"),
						gomponents.Attr("x-validate-form", ""),
						html.Class("space-y-4 max-w-md"),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:       "Name",
								Type:        "text",
								ID:          "name",
								Name:        "name",
								Required:    true,
								Placeholder: "Enter your name",
							}),
						),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:       "Email",
								Type:        "email",
								ID:          "email",
								Name:        "email",
								Required:    true,
								Placeholder: "Enter your email",
							}),
						),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:       "Password",
								Type:        "password",
								ID:          "password",
								Name:        "password",
								Required:    true,
								MinLength:   8,
								Placeholder: "Enter password",
							}),
						),
						html.Div(
							form.FormInput(form.FormInputProps{
								Label:               "Confirm Password",
								Type:                "password",
								ID:                  "confirmPassword",
								Name:                "confirmPassword",
								Required:            true,
								ValidateWith:        "password",
								RelatedErrorMessage: "Passwords do not match.",
								Placeholder:         "Re-enter password",
							}),
						),
						html.Button(
							html.Type("submit"),
							html.Class("inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"),
							gomponents.Text("Submit"),
						),
					),
				),

				// SECTION: FormTextArea
				html.Div(
					html.Class("mb-8"),
					html.H2(
						html.Class("text-xl font-semibold mb-2"),
						gomponents.Text("FormTextArea"),
					),
					html.P(
						html.Class("mb-4 text-gray-600"),
						gomponents.Text("Example of FormTextArea component with various configurations."),
					),
					html.Div(
						html.Class("space-y-4 max-w-md"),
						form.FormTextArea(form.FormTextAreaProps{
							Label:       "Description",
							ID:          "description",
							Name:        "description",
							Required:    true,
							Placeholder: "Enter a description",
							Rows:        4,
						}),
						form.FormTextArea(form.FormTextAreaProps{
							Label:       "Comments",
							ID:          "comments",
							Name:        "comments",
							Required:    false,
							Placeholder: "Enter your comments",
							Rows:        6,
							MaxLength:   500,
						}),
						form.FormTextArea(form.FormTextAreaProps{
							Label:     "Large Text Area",
							ID:        "large-text",
							Name:      "large-text",
							Required:  false,
							Rows:      8,
							MinLength: 10,
							MaxLength: 1000,
						}),
					),
				),

				// SECTION: Dropdown
				html.Div(
					html.Class("mb-8"),
					html.H2(
						html.Class("text-xl font-semibold mb-2"),
						gomponents.Text("Dropdown"),
					),
					dropdown.Dropdown(dropdown.DropdownConfig{
						ID:         "storybook-dropdown",
						DataTestID: "storybook-dropdown",
						ButtonText: "'dropdown'",
						ButtonIcon: icons.ChevronDown(),
						Items: []dropdown.DropdownItem{
							{
								Text: "Profile",
								Href: "/profile",
								Icon: icons.User(),
							},
							{
								Text: "Settings",
								Href: "/settings",
								Icon: icons.Cog(),
							},
							{
								Text: "Log out",
								Href: "/logout",
								Icon: icons.ShieldExclamation(),
							},
						},
						Class: "w-64",
					}),
				),
			),
		),
	)
}
