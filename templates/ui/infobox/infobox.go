package infobox

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// InfoBoxConfig holds the configuration for the InfoBox component
type InfoBoxConfig struct {
	ID          string
	UserName    string
	Style       string // "info", "success", "warning" (default: "info")
	Lang        string
	DataTestID  string
	Dismissible bool
}

func getLabels(lang string) map[string]string {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/infobox/infobox.locales.json", lang)
	if err != nil {
	}
	return locales
}

// InfoBox renders an informational message box showing signed-in user status
func InfoBox(config InfoBoxConfig) gomponents.Node {
	labels := getLabels(config.Lang)

	var bg, border, text, iconColor string
	switch config.Style {
	case "success":
		bg = "bg-green-50 dark:bg-green-900/20"
		border = "border-green-200 dark:border-green-800"
		text = "text-green-800 dark:text-green-200"
		iconColor = "text-green-600 dark:text-green-400"
	case "warning":
		bg = "bg-yellow-50 dark:bg-yellow-900/20"
		border = "border-yellow-200 dark:border-yellow-800"
		text = "text-yellow-800 dark:text-yellow-200"
		iconColor = "text-yellow-600 dark:text-yellow-400"
	default: // info
		bg = "bg-blue-50 dark:bg-blue-900/20"
		border = "border-blue-200 dark:border-blue-800"
		text = "text-blue-800 dark:text-blue-200"
		iconColor = "text-blue-600 dark:text-blue-400"
	}

	// Build container attributes
	containerAttrs := []gomponents.Node{
		html.Class(fmt.Sprintf("rounded-lg border %s %s p-4 mb-4", bg, border)),
	}

	if config.ID != "" {
		containerAttrs = append(containerAttrs, gomponents.Attr("id", config.ID))
	}
	if config.DataTestID != "" {
		containerAttrs = append(containerAttrs, gomponents.Attr("data-testid", config.DataTestID))
	}

	// Add Alpine.js for dismissible functionality
	if config.Dismissible {
		containerAttrs = append(containerAttrs,
			gomponents.Attr("x-data", "{ open: true }"),
			gomponents.Attr("x-show", "open"),
			gomponents.Attr("x-transition:enter", "transition ease-out duration-300"),
			gomponents.Attr("x-transition:enter-start", "opacity-0 transform scale-95"),
			gomponents.Attr("x-transition:enter-end", "opacity-100 transform scale-100"),
			gomponents.Attr("x-transition:leave", "transition ease-in duration-200"),
			gomponents.Attr("x-transition:leave-start", "opacity-100 transform scale-100"),
			gomponents.Attr("x-transition:leave-end", "opacity-0 transform scale-95"),
		)
	}

	// Content
	content := []gomponents.Node{
		html.Div(
			html.Class("flex flex-col gap-4"),
			// Left side with icon and text
			html.Div(
				html.Div(
					html.Class(fmt.Sprintf("text-sm font-medium %s", text)),
					gomponents.Text(fmt.Sprintf("%s %s", labels["already_signed_in"], config.UserName)),
				),
			),
			// Right side with action button and close button
			html.Div(
				html.Class("flex items-center space-x-2"),
				// Go to App button
				html.A(
					html.Href("/app/home"),
					html.Class(fmt.Sprintf("text-sm font-medium %s hover:underline", text)),
					gomponents.Attr("data-testid", fmt.Sprintf("%s-go-to-app", config.DataTestID)),
					gomponents.Text(labels["go_to_app"]),
				),
				// Close button (only if dismissible)
				gomponents.If(config.Dismissible,
					html.Button(
						html.Type("button"),
						html.Class(fmt.Sprintf("ml-2 flex-shrink-0 rounded-md %s p-1.5 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2", iconColor)),
						gomponents.Attr("@click", "open = false"),
						gomponents.Attr("aria-label", labels["close_label"]),
						gomponents.Attr("data-testid", fmt.Sprintf("%s-close", config.DataTestID)),
						html.Span(
							html.Class("sr-only"),
							gomponents.Text(labels["close_label"]),
						),
						html.Span(
							html.Class("h-4 w-4"),
							icons.XCircle(),
						),
					),
				),
			),
		),
	}

	return html.Div(append(containerAttrs, content...)...)
}
