package link

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type LinkProps struct {
	Href        string
	Text        string
	Class       string
	Target      string
	Rel         string
	HxGet       string
	HxSwap      string
	HxTrigger   string
	AriaLabel   string
	DataTestID  string // Optional data-testid attribute
	XOn<PERSON>lick    string // Custom JavaScript to execute on click with x-on:click
	XClass      string // Alpine.js x-class directive for dynamic classes
	XDataActive string // Alpine.js :data-active directive for active state binding
}

// Link creates a basic anchor element with the provided properties

func Link(props LinkProps) gomponents.Node {
	nodes := []gomponents.Node{
		html.Href(props.Href),
		html.Class(props.Class),
		gomponents.Text(props.Text),
	}
	if props.DataTestID != "" {
		nodes = append(nodes, gomponents.Attr("data-testid", props.DataTestID))
	}

	// Add optional attributes if provided
	if props.Target != "" {
		nodes = append(nodes, html.Target(props.Target))
	}

	if props.Rel != "" {
		nodes = append(nodes, html.Rel(props.Rel))
	}

	if props.HxGet != "" {
		nodes = append(nodes, gomponents.Attr("hx-get", props.HxGet))
	}

	if props.HxSwap != "" {
		nodes = append(nodes, gomponents.Attr("hx-swap", props.HxSwap))
	}

	if props.HxTrigger != "" {
		nodes = append(nodes, gomponents.Attr("hx-trigger", props.HxTrigger))
	}

	if props.AriaLabel != "" {
		nodes = append(nodes, gomponents.Attr("aria-label", props.AriaLabel))
	}

	if props.XOnClick != "" {
		nodes = append(nodes, gomponents.Attr("x-on:click", props.XOnClick))
	}

	if props.XClass != "" {
		nodes = append(nodes, gomponents.Attr(":class", props.XClass))
	}

	if props.XDataActive != "" {
		nodes = append(nodes, gomponents.Attr(":data-active", props.XDataActive))
	}

	return html.A(nodes...)
}

type IconLinkProps struct {
	LinkProps    LinkProps
	Icon         gomponents.Node
	IconPosition string // "start" or "end"
}

// IconLink creates a link with an icon either at the start or end of the text
func IconLink(props IconLinkProps) gomponents.Node {
	position := props.IconPosition
	if position == "" {
		position = "start" // Default to start position
	}

	var content []gomponents.Node

	if position == "start" {
		content = []gomponents.Node{
			props.Icon,
			gomponents.Text(" " + props.LinkProps.Text),
		}
	} else {
		content = []gomponents.Node{
			gomponents.Text(props.LinkProps.Text + " "),
			props.Icon,
		}
	}

	nodes := []gomponents.Node{
		html.Href(props.LinkProps.Href),
		html.Class(props.LinkProps.Class),
		gomponents.Group(content),
	}

	if props.LinkProps.Target != "" {
		nodes = append(nodes, html.Target(props.LinkProps.Target))
	}

	if props.LinkProps.Rel != "" {
		nodes = append(nodes, html.Rel(props.LinkProps.Rel))
	}

	if props.LinkProps.HxGet != "" {
		nodes = append(nodes, gomponents.Attr("hx-get", props.LinkProps.HxGet))
	}

	if props.LinkProps.HxSwap != "" {
		nodes = append(nodes, gomponents.Attr("hx-swap", props.LinkProps.HxSwap))
	}

	if props.LinkProps.HxTrigger != "" {
		nodes = append(nodes, gomponents.Attr("hx-trigger", props.LinkProps.HxTrigger))
	}

	if props.LinkProps.AriaLabel != "" {
		nodes = append(nodes, gomponents.Attr("aria-label", props.LinkProps.AriaLabel))
	}

	if props.LinkProps.XOnClick != "" {
		nodes = append(nodes, gomponents.Attr("x-on:click", props.LinkProps.XOnClick))
	}

	if props.LinkProps.XClass != "" {
		nodes = append(nodes, gomponents.Attr(":class", props.LinkProps.XClass))
	}

	if props.LinkProps.XDataActive != "" {
		nodes = append(nodes, gomponents.Attr(":data-active", props.LinkProps.XDataActive))
	}

	return html.A(nodes...)
}

type LinkStyle string

const (
	PrimaryStyle   LinkStyle = "primary"
	SecondaryStyle LinkStyle = "secondary"
	TextStyle      LinkStyle = "text"
)

// getLinkClass returns appropriate CSS classes based on the link style
func getLinkClass(style LinkStyle, baseClass string) string {
	switch style {
	case PrimaryStyle:
		return baseClass + " inline-flex items-center text-white bg-indigo-600 hover:bg-indigo-700 px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
	case SecondaryStyle:
		return baseClass + " inline-flex items-center text-indigo-600 bg-white hover:bg-gray-50 border border-indigo-300 px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
	case TextStyle:
		return baseClass + " text-indigo-600 hover:text-indigo-800 hover:underline text-sm font-medium focus:outline-none"
	default:
		return baseClass
	}
}

// PrimaryLink creates a link with primary styling
func PrimaryLink(href, text string) gomponents.Node {
	return Link(LinkProps{
		Href:  href,
		Text:  text,
		Class: getLinkClass(PrimaryStyle, ""),
	})
}

// SecondaryLink creates a link with secondary styling
func SecondaryLink(href, text string) gomponents.Node {
	return Link(LinkProps{
		Href:  href,
		Text:  text,
		Class: getLinkClass(SecondaryStyle, ""),
	})
}

// TextLink creates a simple text link
func TextLink(href, text string) gomponents.Node {
	return Link(LinkProps{
		Href:  href,
		Text:  text,
		Class: getLinkClass(TextStyle, ""),
	})
}

// ExternalLink creates a link to an external site with proper attributes
func ExternalLink(href, text string, style LinkStyle) gomponents.Node {
	return Link(LinkProps{
		Href:   href,
		Text:   text,
		Class:  getLinkClass(style, ""),
		Target: "_blank",
		Rel:    "noopener noreferrer",
	})
}
