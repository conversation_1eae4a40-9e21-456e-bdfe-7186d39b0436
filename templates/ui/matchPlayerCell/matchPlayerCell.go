package matchplayercell

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/utils/matchutils"
	"github.com/j-em/coachpad/utils/testid"
	"github.com/jackc/pgx/v5/pgtype"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// MatchPlayerCellProps contains props for the match player cell
type MatchPlayerCellProps struct {
	Match        db.GetMatchesBySeasonIdRow
	PlayerNumber int
	PlayerName   string
	PlayerID     pgtype.Int4
}

// MatchPlayerCell creates a player cell with optional crown icon
func MatchPlayerCell(props MatchPlayerCellProps) gomponents.Node {
	isWinner := matchutils.IsPlayerWinner(props.Match, props.PlayerID)

	return html.Td(
		html.ID(fmt.Sprintf("player%d-cell-%d", props.PlayerNumber, props.Match.ID)), // OOB targetable ID
		html.Class("px-6 py-4"),
		html.Div(
			html.Class("flex items-center space-x-2"),
			// Crown icon (conditionally rendered)
			gomponents.If(isWinner,
				html.Span(
					html.Class("h-4 w-4 text-yellow-500 inline-block"),
					gomponents.Attr("data-testid", testid.Generate("player", props.PlayerNumber, "crown", props.Match.ID)),
					gomponents.El("svg",
						gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
						gomponents.Attr("fill", "currentColor"),
						gomponents.Attr("viewBox", "0 0 24 24"),
						gomponents.El("path",
							gomponents.Attr("d", "M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5z"),
						),
					),
				),
			),
			// Player dropdown
			dropdown.Dropdown(dropdown.DropdownConfig{
				ButtonText: props.PlayerName,
				ButtonIcon: icons.User(),
				ID:         fmt.Sprintf("player%d-dropdown-%d", props.PlayerNumber, props.Match.ID),
				DataTestID: testid.Generate("player", props.PlayerNumber, "dropdown", props.Match.ID),
				Class: fmt.Sprintf("dropdown%s-%d", func() string {
					if props.PlayerNumber == 2 {
						return "2"
					} else {
						return ""
					}
				}(), props.Match.ID),
				Items: []dropdown.DropdownItem{
					{
						Text:       "Update Player Data",
						XHxGet:     fmt.Sprintf("'/app/players/%d/edit'", props.PlayerID.Int32),
						HxTarget:   "#modal-body-container",
						Icon:       icons.Pencil(),
						DataTestID: testid.Generate("player", props.PlayerNumber, "dropdown", props.Match.ID, "edit"),
					},
					{
						Text:       "Change Player",
						XHxGet:     fmt.Sprintf("'/app/matches/%d/player%d/edit'", props.Match.ID, props.PlayerNumber),
						HxTarget:   "#modal-body-container",
						Icon:       icons.User(),
						DataTestID: testid.Generate("player", props.PlayerNumber, "dropdown", props.Match.ID, "change"),
					},
				},
			}),
		),
	)
}
