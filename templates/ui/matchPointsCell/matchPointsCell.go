package matchpointscell

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/utils/matchutils"
	"github.com/jackc/pgx/v5/pgtype"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// MatchPointsCellProps contains props for the match points cell
type MatchPointsCellProps struct {
	Match        db.GetMatchesBySeasonIdRow
	PlayerNumber int
	Points       pgtype.Int4
}

// MatchPointsCell creates a reusable player points input cell
func MatchPointsCell(props MatchPointsCellProps) gomponents.Node {
	fieldName := fmt.Sprintf("player%dPoints", props.PlayerNumber)
	testId := fmt.Sprintf("match-%d-player%d-points", props.Match.ID, props.PlayerNumber)

	return html.Td(
		html.ID(fmt.Sprintf("player%d-points-cell-%d", props.PlayerNumber, props.Match.ID)), // OOB targetable ID
		html.Class("px-6 py-4"),
		html.Input(
			html.Type("number"),
			html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
			html.Value(matchutils.FormatPlayerPoints(props.Points)),
			gomponents.Attr("data-testid", testId),
			html.Name(fieldName),
		),
	)
}
