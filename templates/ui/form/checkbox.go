package form

import (
	"fmt"

	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// FormCheckboxProps defines the properties for a checkbox input field.
type FormCheckboxProps struct {
	Label        string
	ID           string
	Name         string
	Required     bool
	Value        string // Optional value for the checkbox
	Checked      bool   // Whether the checkbox is checked by default
	ExtraClasses string // Optional additional classes for the input
	ErrorKey     string // Optional key for error display
	HxOnChange   string // Optional HTMX on change attribute
}

// FormCheckbox creates a checkbox input field with a label and error message.
func FormCheckbox(props FormCheckboxProps) gomponents.Node {
	inputClasses := "h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300"
	if props.ExtraClasses != "" {
		inputClasses += " " + props.ExtraClasses
	}

	inputNodes := []gomponents.Node{
		html.Type("checkbox"),
		html.ID(props.ID),
		html.Name(props.Name),
		html.Class(inputClasses),
		gomponents.If(props.Required, html.Required()),
	}
	if props.Value != "" {
		inputNodes = append(inputNodes, html.Value(props.Value))
	}
	if props.Checked {
		inputNodes = append(inputNodes, gomponents.Attr("checked", "checked"))
	}
	if props.HxOnChange != "" {
		inputNodes = append(inputNodes, gomponents.Attr("hx-on-change", props.HxOnChange))
	}
	if props.ID != "" {
		inputNodes = append(inputNodes, gomponents.Attr("data-field-name", props.ID))
	}

	// Use ErrorKey if provided, otherwise use ID
	errorKey := props.ID
	if props.ErrorKey != "" {
		errorKey = props.ErrorKey
	}

	// Checkbox label should come after the input for accessibility
	return html.Div(
		html.Class("flex items-center space-x-2"),
		html.Input(inputNodes...),
		html.Label(
			html.For(props.ID),
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300 select-none"),
			gomponents.Text(props.Label),
		),
		html.Div(
			html.Class("h-5"),
			html.Div(
				html.Class("text-red-500 text-sm"),
				gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", errorKey)),
				gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", errorKey)),
			),
		),
	)
}
