package form

import (
	"fmt"
	"strconv"

	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// FormInputProps defines the properties for a standard form input field.
type FormInputProps struct {
	Label               string
	Type                string
	ID                  string
	Name                string
	Required            bool
	Autofocus           bool    // Optional autofocus attribute
	Placeholder         string  // Optional placeholder text
	ExtraClasses        string  // Optional additional classes for the input
	Value               string  // Optional value for the input
	ErrorKey            string  // Optional key for error display
	HxOnChange          string  // Optional HTMX on change attribute
	MinLength           int     // Optional minimum length for validation
	MinLengthMessage    string  // Optional custom error message for minimum length validation
	MaxLength           int     // Optional maximum length for validation
	Pattern             string  // Optional regex pattern for validation
	PatternMessage      string  // Optional custom error message for pattern mismatch
	MinValue            float64 // Optional minimum value for numeric inputs
	MaxValue            float64 // Optional maximum value for numeric inputs
	ValidateWith        string  // Field name to validate against (e.g., for password matching)
	ValidateType        string  // Type of cross-field validation (default: 'match')
	RelatedErrorMessage string  // Custom error message for cross-field validation failure
	XModel              string  // Optional Alpine.js x-model directive
	DataTestID          string  // Optional data-testid attribute for testing
}

// FormInput creates a standard form input field wrapped in a div with a label.
func FormInput(props FormInputProps) gomponents.Node {
	// Base classes for the input element, similar to the original signin input
	inputClasses := "appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300"
	if props.ExtraClasses != "" {
		inputClasses += " " + props.ExtraClasses
	}

	// Build the input node with optional attributes
	inputNodes := []gomponents.Node{
		html.Type(props.Type),
		html.ID(props.ID),
		html.Name(props.Name),
		gomponents.If(props.Required, html.Required()),
		gomponents.If(props.Autofocus, html.AutoFocus()),
		html.Class(inputClasses),
	}
	if props.Placeholder != "" {
		inputNodes = append(inputNodes, html.Placeholder(props.Placeholder))
	}
	if props.HxOnChange != "" {
		inputNodes = append(inputNodes, gomponents.Attr("hx-on-change", props.HxOnChange))
	}
	if props.MinLength > 0 {
		inputNodes = append(inputNodes, gomponents.Attr("data-min-length", strconv.Itoa(props.MinLength)))
		inputNodes = append(inputNodes, html.MinLength(strconv.Itoa(props.MinLength)))
		if props.MinLengthMessage != "" {
			inputNodes = append(inputNodes, gomponents.Attr("data-minlength-message", props.MinLengthMessage))
		}
	}
	if props.MaxLength > 0 {
		inputNodes = append(inputNodes, gomponents.Attr("data-max-length", strconv.Itoa(props.MaxLength)))
		inputNodes = append(inputNodes, html.MaxLength(strconv.Itoa(props.MaxLength)))
	}
	if props.Pattern != "" {
		inputNodes = append(inputNodes, gomponents.Attr("data-pattern", props.Pattern))
		inputNodes = append(inputNodes, html.Pattern(props.Pattern))
		if props.PatternMessage != "" {
			inputNodes = append(inputNodes, gomponents.Attr("data-pattern-message", props.PatternMessage))
		}
	}
	// Add Value after other attributes
	if props.Value != "" {
		inputNodes = append(inputNodes, html.Value(props.Value))
	}
	if props.Type == "number" {
		if props.MinValue != 0 || props.MaxValue != 0 {
			if props.MinValue != 0 {
				inputNodes = append(inputNodes, gomponents.Attr("data-min-value", strconv.FormatFloat(props.MinValue, 'f', -1, 64)))
				inputNodes = append(inputNodes, html.Min(strconv.FormatFloat(props.MinValue, 'f', -1, 64)))
			}
			if props.MaxValue != 0 {
				inputNodes = append(inputNodes, gomponents.Attr("data-max-value", strconv.FormatFloat(props.MaxValue, 'f', -1, 64)))
				inputNodes = append(inputNodes, html.Max(strconv.FormatFloat(props.MaxValue, 'f', -1, 64)))
			}
		}
	}
	if props.ID != "" {
		inputNodes = append(inputNodes, gomponents.Attr("data-field-name", props.ID))
	}
	if props.ValidateWith != "" {
		inputNodes = append(inputNodes, gomponents.Attr("data-validate-with", props.ValidateWith))
		if props.ValidateType != "" {
			inputNodes = append(inputNodes, gomponents.Attr("data-validate-type", props.ValidateType))
		}
		if props.RelatedErrorMessage != "" {
			inputNodes = append(inputNodes, gomponents.Attr("data-related-error-message", props.RelatedErrorMessage))
		}
	}
	if props.XModel != "" {
		inputNodes = append(inputNodes, gomponents.Attr("x-model", props.XModel))
	}
	if props.DataTestID != "" {
		inputNodes = append(inputNodes, gomponents.Attr("data-testid", props.DataTestID))
	}
	// Note: x-data is set on the form level for cross-field validation, not on individual inputs
	// inputNodes = append(inputNodes, gomponents.Attr("x-data", "formValidationGroup"))
	// No need for x-on:change here as it's handled by the component's event listeners

	// Wrap the error message in a div with a fixed height to avoid layout shift
	return html.Div(
		// You might want to add specific classes to this outer div if needed
		html.Label(
			html.For(props.ID),
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
			gomponents.Text(props.Label),
		),
		html.Input(inputNodes...),
		html.Div(
			html.Class("h-5"), // Set a fixed height for the error message container
			html.Div(
				html.Class("text-red-500 text-sm"),
				gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", props.ID)),
				gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", props.ID)),
			),
		),
	)
}

// FormTextAreaProps defines the properties for a standard form textarea field.
type FormTextAreaProps struct {
	Label        string
	ID           string
	Name         string
	Required     bool
	Placeholder  string // Optional placeholder text
	ExtraClasses string // Optional additional classes for the textarea
	Value        string // Optional value for the textarea
	Rows         int    // Optional number of rows (default: 4)
	MinLength    int    // Optional minimum length for validation
	MaxLength    int    // Optional maximum length for validation
	DataTestID   string // Optional data-testid attribute for testing
}

// FormTextArea creates a standard form textarea field wrapped in a div with a label.
func FormTextArea(props FormTextAreaProps) gomponents.Node {
	// Base classes for the textarea element
	textareaClasses := "appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300"
	if props.ExtraClasses != "" {
		textareaClasses += " " + props.ExtraClasses
	}

	// Build the textarea node with optional attributes
	textareaNodes := []gomponents.Node{
		html.ID(props.ID),
		html.Name(props.Name),
		gomponents.If(props.Required, html.Required()),
		html.Class(textareaClasses),
	}

	if props.Placeholder != "" {
		textareaNodes = append(textareaNodes, html.Placeholder(props.Placeholder))
	}
	if props.Value != "" {
		textareaNodes = append(textareaNodes, gomponents.Text(props.Value))
	}
	if props.Rows > 0 {
		textareaNodes = append(textareaNodes, html.Rows(strconv.Itoa(props.Rows)))
	} else {
		textareaNodes = append(textareaNodes, html.Rows("4")) // Default rows
	}
	if props.MinLength > 0 {
		textareaNodes = append(textareaNodes, html.MinLength(strconv.Itoa(props.MinLength)))
	}
	if props.MaxLength > 0 {
		textareaNodes = append(textareaNodes, html.MaxLength(strconv.Itoa(props.MaxLength)))
	}
	if props.DataTestID != "" {
		textareaNodes = append(textareaNodes, gomponents.Attr("data-testid", props.DataTestID))
	}

	return html.Div(
		html.Label(
			html.For(props.ID),
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
			gomponents.Text(props.Label),
			gomponents.If(props.Required, html.Span(
				html.Class("text-red-500 ml-1"),
				gomponents.Text("*"),
			)),
		),
		html.Textarea(textareaNodes...),
		html.Div(
			html.Class("h-5"), // Set a fixed height for the error message container
			html.Div(
				html.Class("text-red-500 text-sm"),
				gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", props.ID)),
				gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", props.ID)),
			),
		),
	)
}

// UniqueEmailInputProps defines the properties for an email input that validates uniqueness
type UniqueEmailInputProps struct {
	Label        string
	ID           string
	Name         string
	Required     bool
	Placeholder  string
	ExtraClasses string
	Value        string
	ErrorKey     string
	XModel       string // Optional Alpine.js x-model directive
}

// UniqueEmailInput creates an email input field that checks for email uniqueness via HTMX
func UniqueEmailInput(props UniqueEmailInputProps) gomponents.Node {
	// Base classes for the input element
	inputClasses := "appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300"
	if props.ExtraClasses != "" {
		inputClasses += " " + props.ExtraClasses
	}

	// Build the input node with HTMX attributes for email checking
	inputNodes := []gomponents.Node{
		html.Type("email"),
		html.ID(props.ID),
		html.Name(props.Name),
		gomponents.If(props.Required, html.Required()),
		html.Class(inputClasses),
		// HTMX attributes for email validation
		gomponents.Attr("hx-post", "/check-email"),
		gomponents.Attr("hx-trigger", "blur changed"),
		gomponents.Attr("hx-target", fmt.Sprintf("#%s-validation", props.ID)),
		gomponents.Attr("hx-swap", "innerHTML"),
		gomponents.Attr("hx-include", fmt.Sprintf("input[name='%s']", props.Name)),
	}

	if props.Placeholder != "" {
		inputNodes = append(inputNodes, html.Placeholder(props.Placeholder))
	}
	if props.Value != "" {
		inputNodes = append(inputNodes, html.Value(props.Value))
	}
	if props.ID != "" {
		inputNodes = append(inputNodes, gomponents.Attr("data-field-name", props.ID))
	}
	if props.XModel != "" {
		inputNodes = append(inputNodes, gomponents.Attr("x-model", props.XModel))
	}

	return html.Div(
		html.Label(
			html.For(props.ID),
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
			gomponents.Text(props.Label),
		),
		html.Input(inputNodes...),
		html.Div(
			html.Class("h-5"), // Set a fixed height for the validation message container
			html.Div(
				html.ID(fmt.Sprintf("%s-validation", props.ID)),
				html.Class("text-sm"),
				// This will be populated by HTMX response
			),
		),
	)
}
