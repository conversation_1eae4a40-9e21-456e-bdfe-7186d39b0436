package matchessearchbar

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// MatchesSearchBarProps contains props for the matches search bar
type MatchesSearchBarProps struct {
	Page         int
	ItemsPerPage int
	Sort         string
	Direction    string
	Search       string
	FilterToday  bool
	SeasonId     int32
	Lang         string
}

// MatchesSearchBar renders the search bar and filters that stay outside the refreshable table area
func MatchesSearchBar(props MatchesSearchBarProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/matchesSearchBar/matchesSearchBar.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	baseURL := fmt.Sprintf("/app/seasons/%d/matches-table", props.SeasonId)

	return html.Form(
		html.ID("matches-filters-form"),
		html.Class("mb-4 flex flex-wrap justify-between items-center gap-4"),

		// Centralized HTMX attributes for all filter updates - same pattern as match table
		htmx.Get(baseURL),
		htmx.Target("#matches-table-content"),
		htmx.Trigger("change from:input[name='filter_today'], input changed delay:500ms from:input[name='search']"),
		htmx.Include("closest form"),
		htmx.Sync("this:replace"),

		// Filter by today's matches
		html.Div(
			html.Class("flex items-center"),
			html.Label(
				html.Class("inline-flex items-center"),
				html.Input(
					html.Type("checkbox"),
					html.Class("form-checkbox h-5 w-5 text-gray-600 dark:text-gray-300 rounded mr-2"),
					gomponents.If(props.FilterToday, html.Checked()),
					html.Name("filter_today"),
					html.Value("true"),
				),
				html.Span(
					html.Class("ml-2 text-gray-700 dark:text-gray-200"),
					gomponents.Text(locales["show_todays_matches"]),
				),
			),
		),

		// Search input with icon
		html.Div(
			html.Class("relative"),
			html.Input(
				html.Type("text"),
				html.Class("border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-md px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"),
				html.Value(props.Search),
				gomponents.Attr("placeholder", locales["search_matches"]),
				gomponents.Attr("data-testid", "match-search-input"),
				html.Name("search"),
			),
			html.Div(
				html.Class("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"),
				gomponents.El("svg",
					html.Class("h-5 w-5"),
					gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
					gomponents.Attr("fill", "none"),
					gomponents.Attr("viewBox", "0 0 24 24"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.El("path",
						gomponents.Attr("stroke-linecap", "round"),
						gomponents.Attr("stroke-linejoin", "round"),
						gomponents.Attr("stroke-width", "2"),
						gomponents.Attr("d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"),
					),
				),
			),
		),

		// Hidden inputs to preserve current state
		html.Input(html.Type("hidden"), html.Name("page"), html.Value(strconv.Itoa(props.Page))),
		html.Input(html.Type("hidden"), html.Name("per_page"), html.Value(strconv.Itoa(props.ItemsPerPage))),
		html.Input(html.Type("hidden"), html.Name("sort"), html.Value(props.Sort)),
		html.Input(html.Type("hidden"), html.Name("dir"), html.Value(props.Direction)),
	)
}
