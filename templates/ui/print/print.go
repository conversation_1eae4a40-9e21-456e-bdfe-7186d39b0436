package templatesprint

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type PrintPageProps struct {
	Title    string
	Lang     string
	PrintCSS string // Optional custom CSS for specific print needs
	Content  gomponents.Node
}

// PrintPage creates a standardized print page template
func PrintPage(props PrintPageProps) gomponents.Node {
	return gomponents.Group([]gomponents.Node{
		gomponents.Raw("<!DOCTYPE html>"),
		html.HTML(
			html.Lang(props.Lang),
			html.Head(
				html.Meta(html.Charset("utf-8")),
				html.TitleEl(gomponents.Text(props.Title)),
				html.Script(gomponents.Raw(`window.onload = function() { window.print(); };`)),
				html.StyleEl(gomponents.Raw(getBasePrintCSS()+props.PrintCSS)),
			),
			html.Body(
				props.Content,
			),
		),
	})
}

func getBasePrintCSS() string {
	return `
		/* Screen styles (preview) */
		body { 
			font-family: Arial, sans-serif; 
			margin: 0;
			padding: 20px;
			line-height: 1.4;
			color: #333;
		}
		
		.print-header {
			margin-bottom: 30px;
			border-bottom: 2px solid #ddd;
			padding-bottom: 10px;
		}
		
		.print-title { 
			margin: 0 0 10px 0;
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
		
		.print-subtitle {
			margin: 0;
			font-size: 14px;
			color: #666;
		}
		
		.print-table { 
			width: 100%; 
			border-collapse: collapse; 
			margin-top: 20px;
			box-shadow: 0 1px 3px rgba(0,0,0,0.1);
		}
		
		.print-table th, .print-table td { 
			border: 1px solid #ddd; 
			padding: 12px 8px; 
			text-align: left;
			vertical-align: top;
		}
		
		.print-table th { 
			background-color: #f9f9f9; 
			font-weight: bold;
			color: #333;
		}
		
		.print-table tbody tr:nth-child(even) {
			background-color: #f8f9fa;
		}
		
		.print-table tbody tr:hover {
			background-color: #e8f4f8;
		}
		
		.print-footer {
			margin-top: 30px;
			padding-top: 20px;
			border-top: 1px solid #ddd;
			font-size: 12px;
			color: #666;
		}
		
		.no-print {
			display: inline;
		}
		
		/* Print-specific styles */
		@media print {
			@page {
				margin: 1cm;
				size: A4;
			}
			
			body { 
				font-family: Arial, sans-serif; 
				font-size: 11px; 
				margin: 0;
				padding: 0;
				line-height: 1.3;
				color: #000;
				background: white;
			}
			
			.print-header {
				margin-bottom: 20px;
				border-bottom: 2px solid #000;
				padding-bottom: 8px;
			}
			
			.print-title { 
				margin: 0 0 8px 0;
				font-size: 16px;
				font-weight: bold;
				color: #000;
			}
			
			.print-subtitle {
				margin: 0;
				font-size: 12px;
				color: #333;
			}
			
			.print-table { 
				width: 100%; 
				border-collapse: collapse; 
				margin-top: 15px;
				box-shadow: none;
			}
			
			.print-table th, .print-table td { 
				border: 1px solid #000; 
				padding: 6px 4px; 
				text-align: left;
				vertical-align: top;
				word-wrap: break-word;
			}
			
			.print-table th { 
				background-color: #e9e9e9 !important;
				font-weight: bold;
				color: #000;
				-webkit-print-color-adjust: exact;
				print-color-adjust: exact;
			}
			
			.print-table tbody tr:nth-child(even) {
				background-color: #f5f5f5 !important;
				-webkit-print-color-adjust: exact;
				print-color-adjust: exact;
			}
			
			.print-table tbody tr:hover {
				background-color: inherit !important;
			}
			
			.print-footer {
				margin-top: 20px;
				padding-top: 15px;
				border-top: 1px solid #000;
				font-size: 10px;
				color: #333;
			}
			
			.no-print {
				display: none !important;
			}
			
			/* Prevent page breaks inside table rows */
			.print-table tr {
				page-break-inside: avoid;
			}
			
			/* Ensure table headers repeat on each page */
			.print-table thead {
				display: table-header-group;
			}
			
			/* Control page breaks */
			.page-break-before {
				page-break-before: always;
			}
			
			.page-break-after {
				page-break-after: always;
			}
			
			.page-break-avoid {
				page-break-inside: avoid;
			}
			
			/* Hide interactive elements */
			button, input, select, textarea,
			.btn, .button, .dropdown,
			.interactive, .clickable {
				display: none !important;
			}
			
			/* Optimize text for print */
			a {
				text-decoration: none;
				color: #000;
			}
			
			a[href]:after {
				content: " (" attr(href) ")";
				font-size: 90%;
				color: #666;
			}
			
			/* Make sure all text is black for print */
			* {
				color: #000 !important;
				text-shadow: none !important;
			}
		}
	`
}
