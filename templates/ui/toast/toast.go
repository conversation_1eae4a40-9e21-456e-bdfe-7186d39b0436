package toast

import (
	"fmt"
	"strings"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// ToastCloseButton is a local copy of the IconButton component, specialized for use in Toasts.
type ToastCloseButtonConfig struct {
	ID         string
	DataTestID string
	ButtonType string
	Text       string
	Class      string
	Icon       gomponents.Node
	Attrs      []gomponents.Node
	Size       string // "small", "default", "large"
	Lang       string
}

func getToastCloseButtonClass(size string) string {
	switch size {
	default:
		return "text-sm cursor-pointer w-7 h-7 [&>svg]:m-auto [&>svg]:w-6 [&>svg]:h-6"
	}
}

func ToastCloseButton(config ToastCloseButtonConfig) gomponents.Node {
	buttonClass := getToastCloseButtonClass(config.Size)
	nodes := []gomponents.Node{
		html.ID(config.ID),
		gomponents.Attr("data-testid", config.DataTestID),
		html.Type(config.ButtonType),
		html.Class(config.Class + " " + buttonClass),
		gomponents.Group([]gomponents.Node{
			config.Icon,
			gomponents.Text(" " + config.Text),
		}),
	}
	if config.Attrs != nil {
		nodes = append(nodes, config.Attrs...)
	}
	return html.Button(nodes...)
}

func getCloseLabel(lang string) string {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/toast/toast.locales.json", lang)
	if err != nil {
		// Fallback to English if locales can't be loaded
		if lang == "fr" {
			return "Fermer"
		}
		return "Close"
	}
	return locales["close_label"]
}

// Toast renders a toast message with a close button. Valid styles: "success", "error", "info" (default).
// Uses Alpine.js for open/close state. Pass a boolean to disable auto-close: false disables the default 5s timeout.
type ToastConfig struct {
	ID         string
	Message    string
	Style      string
	DataTestID string
	AutoClose  bool
	Lang       string
}

func Toast(config ToastConfig) gomponents.Node {
	var bg, border, text string
	switch config.Style {
	case "success":
		bg = "bg-green-100"
		border = "border-green-400"
		text = "text-green-700"
	case "error":
		bg = "bg-red-100"
		border = "border-red-400"
		text = "text-red-700"
	default:
		bg = "bg-blue-100"
		border = "border-blue-400"
		text = "text-blue-700"
	}
	// determine if the toast should auto-close
	shouldAutoClose := config.AutoClose

	// build attributes for the toast container
	divAttrs := []gomponents.Node{
		gomponents.Attr("id", config.ID),
		gomponents.Attr("data-testid", config.DataTestID),
		gomponents.Attr("x-data", "{ open: false }"),
	}
	xInitParts := []string{"open = true"}
	if shouldAutoClose {
		xInitParts = append(xInitParts, "setTimeout(() => open = false, 5000)")
	}
	xInitValue := strings.Join(xInitParts, "; ")
	divAttrs = append(divAttrs, gomponents.Attr("x-init", fmt.Sprintf("$nextTick(() => { %s })", xInitValue)))
	divAttrs = append(divAttrs,
		gomponents.Attr("x-show", "open"),
		gomponents.Attr("x-transition:enter", "transition ease-out duration-300"),
		gomponents.Attr("x-transition:enter-start", "opacity-0"),
		gomponents.Attr("x-transition:enter-end", "opacity-100"),
		gomponents.Attr("x-transition:leave", "transition ease-in duration-300"),
		gomponents.Attr("x-transition:leave-start", "opacity-100"),
		gomponents.Attr("x-transition:leave-end", "opacity-0"),
		gomponents.Attr("@keydown.escape.window", "open = false"),
		html.Class(fmt.Sprintf("max-w-sm w-full %s border %s px-4 py-3 rounded text-sm font-medium %s flex items-center justify-between", bg, border, text)),
	)

	// children nodes
	children := []gomponents.Node{
		gomponents.Text(config.Message),
		ToastCloseButton(ToastCloseButtonConfig{
			ButtonType: "button",
			Icon:       icons.XCircle(),
			Attrs: []gomponents.Node{
				gomponents.Attr("@click", "open = false; console.log('Toast closed')"),
				gomponents.Attr("aria-label", getCloseLabel(config.Lang)),
			},
			Size: "default",
			Lang: config.Lang,
		}),
	}

	return html.Div(append(divAttrs, children...)...)
}

// Container renders an absolute positioned container in which multiple toasts can be displayed.
func Container(toasts ...gomponents.Node) gomponents.Node {
	return html.Div(
		html.Class("absolute bottom-4 right-4 z-60 space-y-2"),
		gomponents.Group(toasts),
	)
}
