# Form Input Validation Examples

This document provides examples of how to use the `FormInput` component with the validation system in your Go templates. The validation system supports individual field rules as well as cross-field validation for scenarios like password matching.

## Basic Field Validation

### 1. Required Text Field with Minimum Length
This example shows a simple text input that is required and must have a minimum length of 3 characters.

```go
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:      "Username",
    Type:       "text",
    ID:         "username",
    Name:       "username",
    FieldName:  "username",
    Required:   true,
    MinLength:  3,
})
```

### 2. Email Field with Format Validation
This example validates an email field for correct format using the built-in email type validation.

```go
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:      "Email",
    Type:       "email",
    ID:         "email",
    Name:       "email",
    FieldName:  "email",
    Required:   true,
})
```

### 3. Numeric Field with Range
This example sets a numeric input with a minimum value of 18 and a maximum value of 120.

```go
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:      "Age",
    Type:       "number",
    ID:         "age",
    Name:       "age",
    FieldName:  "age",
    Required:   true,
    MinValue:   18,
    MaxValue:   120,
})
```

### 4. Text Field with Regex Pattern
This example enforces a specific pattern for a phone number (e.g., XXX-XXX-XXXX format).

```go
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:          "Phone Number",
    Type:           "text",
    ID:             "phone",
    Name:           "phone",
    FieldName:      "phone",
    Pattern:        "^\\d{3}-\\d{3}-\\d{4}$",
    PatternMessage: "Please enter phone number in format XXX-XXX-XXXX",
})
```

## Cross-Field Validation

### 5. Password and Confirm Password Matching
This example demonstrates how to set up a password field and a confirm password field that must match each other. The `ValidateWith` attribute specifies the field to validate against, and `RelatedErrorMessage` provides a custom error message for mismatch.

```go
// Password Field
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:          "Password",
    Type:           "password",
    ID:             "password",
    Name:           "password",
    FieldName:      "password",
    Required:       true,
    MinLength:      8,
})

// Confirm Password Field (validates against password)
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:                "Confirm Password",
    Type:                 "password",
    ID:                   "confirmPassword",
    Name:                 "confirmPassword",
    FieldName:            "confirmPassword",
    Required:             true,
    ValidateWith:         "password",
    ValidateType:         "match",
    RelatedErrorMessage:  "Passwords do not match.",
})
```

**Note**: Ensure that the form or container wrapping these fields has `x-data="formValidationGroup"` to enable shared state for cross-field validation. For example:

```go
html.Form(
    html.Method("POST"),
    html.Action("/signup"),
    html.Class("mt-8 space-y-6"),
    gomponents.Attr("x-data", "formValidationGroup"),
    // ... form inputs here ...
)
```

## Combining Multiple Validations
### 6. Complex Field with Multiple Rules
This example combines multiple validation rules for a single field, requiring it to be filled, meet length constraints, and match a pattern.

```go
templatescomponents.FormInput(templatescomponents.FormInputProps{
    Label:          "Custom Code",
    Type:           "text",
    ID:             "customCode",
    Name:           "customCode",
    FieldName:      "customCode",
    Required:       true,
    MinLength:      5,
    MaxLength:      10,
    Pattern:        "^[A-Z0-9]+$",
    PatternMessage: "Must contain only uppercase letters and numbers.",
})
```

These examples cover the most common use cases for form validation. By setting the appropriate properties in `FormInputProps`, you can tailor the validation to meet specific requirements, including dependencies between fields for more complex forms.