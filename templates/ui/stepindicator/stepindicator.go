package templatesuistepindicator

import (
	"strconv"

	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// StepIndicatorProps configures the step indicator component
type StepIndicatorProps struct {
	ShowNumbers    bool   // Show numbered circles vs dots
	ShowConnectors bool   // Show lines between steps
	ShowTitle      bool   // Show step title
	ShowStepOfText bool   // Show "Step X of Y" text
	Size           string // sm, md, lg
	MaxSteps       int    // Maximum number of steps to show (for responsive design)
	Lang           string // Language for localization
}

// StepIndicator creates a reusable step progress indicator component
// This component works with Alpine.js multiStepForm components that expose:
// - currentStep (number): current step (1-indexed)
// - totalSteps (number): total number of steps
// - getStepTitle() (function): returns current step title
// - getStepOfText() (function): returns formatted "Step X of Y" text
func StepIndicator(props StepIndicatorProps) gomponents.Node {
	// Load locales with error handling and fallback
	_, err := i18n.LoadTemplateLocales("./templates/ui/stepindicator/stepindicator.locales.json", props.Lang)
	if err != nil {
		// Locales loaded but not used directly in this component
		// The Alpine.js multiStepForm handles localization internally
	}

	// Set defaults
	if props.Size == "" {
		props.Size = "md"
	}
	if props.MaxSteps == 0 {
		props.MaxSteps = 5 // Default max steps shown before truncation
	}

	// Size variants for responsive design
	sizeClasses := map[string]string{
		"sm": "w-6 h-6 text-xs",
		"md": "w-8 h-8 text-sm",
		"lg": "w-10 h-10 text-base",
	}

	circleSize := sizeClasses[props.Size]
	if circleSize == "" {
		circleSize = sizeClasses["md"] // fallback
	}

	return html.Div(
		html.Class("step-indicator-container"),
		// Progress indicator with circles and connectors
		html.Div(
			html.Class("flex items-center justify-between mb-4"),
			html.Template(
				gomponents.Attr("x-for", "step in Array.from({length: Math.min(totalSteps, "+strconv.Itoa(props.MaxSteps)+")},(_, i) => i + 1)"),
				gomponents.Attr("x-key", "step"),
				html.Div(
					html.Class("flex items-center"),
					gomponents.Attr(":class", "step < totalSteps ? 'flex-1' : ''"),
					// Step circle
					html.Div(
						html.Class("flex items-center justify-center rounded-full font-medium step-indicator transition-all duration-200 "+circleSize),
						gomponents.Attr(":class", `{
							'bg-indigo-600 text-white': step < currentStep,
							'bg-indigo-600 text-white border-2 border-indigo-200 shadow-lg': step === currentStep,
							'bg-gray-200 text-gray-500 dark:bg-gray-600 dark:text-gray-400': step > currentStep
						}`),
						gomponents.If(props.ShowNumbers,
							gomponents.Attr("x-text", "step"),
						),
						gomponents.If(!props.ShowNumbers,
							html.Div(
								html.Class("w-2 h-2 rounded-full"),
								gomponents.Attr(":class", `{
									'bg-white': step <= currentStep,
									'bg-gray-400': step > currentStep
								}`),
							),
						),
					),
					// Connector line
					gomponents.If(props.ShowConnectors,
						html.Template(
							gomponents.Attr("x-if", "step < Math.min(totalSteps, "+strconv.Itoa(props.MaxSteps)+")"),
							html.Div(
								html.Class("flex-1 h-0.5 mx-2 step-transition transition-all duration-300"),
								gomponents.Attr(":class", "step < currentStep ? 'bg-indigo-600' : 'bg-gray-200 dark:bg-gray-600'"),
							),
						),
					),
				),
			),
		),
		// Step information
		gomponents.If(props.ShowTitle || props.ShowStepOfText,
			html.Div(
				html.Class("text-center"),
				gomponents.If(props.ShowTitle,
					html.H2(
						html.Class("text-lg font-semibold text-gray-900 dark:text-white"),
						gomponents.Attr("x-text", "getStepTitle()"),
					),
				),
				gomponents.If(props.ShowStepOfText,
					html.P(
						html.Class("text-sm text-gray-600 dark:text-gray-400 mt-1"),
						gomponents.Attr("x-text", "getStepOfText()"),
					),
				),
			),
		),
		// Overflow indicator for mobile (when there are too many steps)
		html.Template(
			gomponents.Attr("x-if", "totalSteps > "+strconv.Itoa(props.MaxSteps)),
			html.Div(
				html.Class("text-center mt-2"),
				html.P(
					html.Class("text-xs text-gray-500 dark:text-gray-400"),
					gomponents.Attr("x-text", "`${currentStep} / ${totalSteps}`"),
				),
			),
		),
	)
}
