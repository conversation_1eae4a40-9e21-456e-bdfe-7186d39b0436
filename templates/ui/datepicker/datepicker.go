package datepicker

import (
	"fmt"
	"time"

	"github.com/j-em/coachpad/i18n"
	gomponents "maragu.dev/gomponents"
	solid "maragu.dev/gomponents-heroicons/v3/solid"
	html "maragu.dev/gomponents/html"
)

type DatePickerProps struct {
	Class       string
	Placeholder string
	Name        string // Added Name property to support form field naming
	Value       string // Optional value for the input
	Label       string // Label for the date picker
	Lang        string // Language for localization
	XModel      string // Alpine.js x-model binding for connecting to parent data
}

type TeleportDatePickerProps struct {
	Class       string
	Placeholder string
	Name        string
	Value       string
	Label       string
	Lang        string
	XModel      string
	UniqueId    string // Required for teleporting
}

func DatePickerComponent(props DatePickerProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/datepicker/datepicker.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}
	return html.Div(
		html.Class("mb-4"),
		html.Label(
			html.For(props.Name),
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
			gomponents.Text(props.Label),
		),
		html.Div(
			html.Class("relative "+props.Class+" isolate"),
			gomponents.Attr("x-data", fmt.Sprintf("datePickerComponent('%s')", props.Value)),
			gomponents.If(props.XModel != "", gomponents.Attr("x-init", fmt.Sprintf(`
				$watch('datePickerValue', value => %s = value);
				if (!datePickerValue && %s) {
					datePickerValue = %s;
					datePickerResetToDefault();
				} else if (!%s && datePickerValue) {
					%s = datePickerValue;
				}
			`, props.XModel, props.XModel, props.XModel, props.XModel, props.XModel))),
			html.Input(
				gomponents.Attr("x-ref", "datePickerInput"),
				gomponents.Attr("type", "text"),
				gomponents.Attr("@click", "datePickerOpen=!datePickerOpen"),
				gomponents.Attr("x-model", "datePickerValue"),
				gomponents.Attr("x-on:keydown.escape", "datePickerOpen=false"),
				html.Class("flex w-full h-10 px-3 py-2 text-sm bg-white border rounded-md text-neutral-600 border-neutral-300 ring-offset-background placeholder:text-neutral-400 focus:border-neutral-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300 dark:placeholder:text-neutral-500"),
				gomponents.Attr("placeholder", props.Placeholder),
				gomponents.Attr("readonly", "true"),
				gomponents.Attr("name", props.Name),
				gomponents.If(props.Value != "", gomponents.Attr("value", props.Value)),
			),
			html.Div(
				gomponents.Attr("x-show", "datePickerOpen"),
				gomponents.Attr("@click.away", "datePickerOpen = false"),
				html.Class("absolute top-full left-0 max-w-lg p-4 mt-1 antialiased bg-white border rounded-lg shadow border-neutral-200/70 z-[100] dark:bg-neutral-800 dark:border-neutral-600"),
				html.Div(
					html.Class("flex items-center justify-between mb-2 gap-2"),
					// Year navigation left
					html.Button(
						html.Type("button"),
						gomponents.Attr("@click", "datePickerPreviousYear()"),
						gomponents.Attr("data-testid", "datepicker-prev-year"),
						html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
						solid.ChevronDoubleLeft(
							html.Class("inline-flex w-6 h-6 text-gray-400"),
						),
					),
					// Month/year display
					html.Div(
						html.Class("w-[145px] flex justify-center items-center gap-1"),
						html.Span(
							gomponents.Attr("x-text", "datePickerMonthNames[datePickerMonth]"),
							html.Class("text-lg font-bold text-gray-800 dark:text-white"),
							gomponents.Attr("data-testid", "datepicker-month-name"),
						),
						html.Span(
							gomponents.Attr("x-text", "datePickerYear"),
							html.Class("text-lg font-normal text-gray-600 dark:text-white cursor-pointer hover:text-blue-600"),
							gomponents.Attr("data-testid", "datepicker-year"),
							gomponents.Attr("@click", "datePickerToggleYearGrid()"),
						),
					),
					// Month navigation
					html.Button(
						html.Type("button"),
						gomponents.Attr("@click", "datePickerPreviousMonth()"),
						gomponents.Attr("data-testid", "datepicker-prev-month"),
						html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
						solid.ChevronLeft(
							html.Class("inline-flex w-6 h-6 text-gray-400"),
						),
					),
					html.Button(
						html.Type("button"),
						gomponents.Attr("@click", "datePickerNextMonth()"),
						gomponents.Attr("data-testid", "datepicker-next-month"),
						html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
						solid.ChevronRight(
							html.Class("inline-flex w-6 h-6 text-gray-400"),
						),
					),
					// Year navigation right
					html.Button(
						html.Type("button"),
						gomponents.Attr("@click", "datePickerNextYear()"),
						gomponents.Attr("data-testid", "datepicker-next-year"),
						html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
						solid.ChevronDoubleRight(
							html.Class("inline-flex w-6 h-6 text-gray-400"),
						),
					),
				),
				// Year grid or month/day view
				html.Template(
					gomponents.Attr("x-if", "datePickerShowYearGrid"),
					html.Div(
						html.Class("mb-3"),
						// Year grid navigation
						html.Div(
							html.Class("flex justify-between items-center mb-2"),
							html.Button(
								html.Type("button"),
								gomponents.Attr("@click", "datePickerPreviousYearGrid()"),
								gomponents.Attr("data-testid", "datepicker-prev-year-grid"),
								html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
								solid.ChevronLeft(
									html.Class("w-6 h-6 text-gray-400"),
								),
							),
							html.Span(
								html.Class("text-base font-semibold text-gray-700 dark:text-white"),
								gomponents.Attr("x-text", "datePickerYearGrid[0] + ' - ' + datePickerYearGrid[datePickerYearGrid.length-1]"),
								gomponents.Attr("data-testid", "datepicker-year-grid-range"),
							),
							html.Button(
								html.Type("button"),
								gomponents.Attr("@click", "datePickerNextYearGrid()"),
								gomponents.Attr("data-testid", "datepicker-next-year-grid"),
								html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
								solid.ChevronRight(
									html.Class("w-6 h-6 text-gray-400"),
								),
							),
						),
						html.Div(
							html.Class("grid grid-cols-5 gap-2"),
							gomponents.El("template",
								gomponents.Attr("x-for", "year in datePickerYearGrid"),
								gomponents.Attr(":key", "'year-' + year"),
								html.Button(
									html.Type("button"),
									gomponents.Attr("@click", "datePickerSelectYear(year)"),
									html.Class("w-14 h-10 flex items-center justify-center rounded-md transition-colors duration-100 text-base focus:outline-none hover:bg-blue-100"),
									gomponents.Attr(":class", "{'bg-blue-600 text-white font-bold shadow': datePickerYear === year}"),
									gomponents.Attr(":data-testid", "'datepicker-year-' + year"),
									html.Span(gomponents.Attr("x-text", "year")),
								),
							),
						),
					),
				),
				html.Template(
					gomponents.Attr("x-if", "!datePickerShowYearGrid"),
					html.Div(
						html.Div(
							html.Class("grid grid-cols-7 mb-3"),
							gomponents.Map([]string{
								locales["sun"], locales["mon"], locales["tue"],
								locales["wed"], locales["thu"], locales["fri"], locales["sat"],
							}, func(day string) gomponents.Node {
								return html.Div(
									html.Class("px-0.5"),
									html.Div(
										gomponents.Text(day),
										html.Class("text-xs font-medium text-center text-gray-800"),
									),
								)
							}),
						),
						html.Div(
							html.Class("grid grid-cols-7 gap-1"),
							// Blank days
							gomponents.El("template",
								gomponents.Attr("x-for", "blankday in datePickerBlankDaysInMonth"),
								gomponents.Attr(":key", "'blank-' + blankday"),
								html.Div(),
							),
							// Actual days
							html.Template(gomponents.Attr("x-for", "day in datePickerDaysInMonth"),
								gomponents.Attr(":key", "day"),
								html.Button(
									html.Type("button"),
									html.Class("w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-100 text-sm focus:outline-none"),
									gomponents.Attr(":class", `{
                                     'bg-blue-600 text-white font-bold shadow': datePickerIsSelectedDate(day),
                                     'bg-gray-200 text-gray-900': datePickerIsToday(day) && !datePickerIsSelectedDate(day),
                                     'hover:bg-blue-100': !datePickerIsSelectedDate(day) && !datePickerIsToday(day)
                                   }`),
									gomponents.Attr("@click", "datePickerDayClicked(day)"),
									gomponents.Attr(":data-testid", "'datepicker-day-' + day"),
									html.Span(
										gomponents.Attr("x-text", "day"),
									),
								),
							),
						),
					),
				),
			),
		),
		html.Div(
			html.Class("h-5"), // Set a fixed height for the error message container
			html.Div(
				html.Class("text-red-500 text-sm"),
				gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", props.Name)),
				gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", props.Name)),
			),
		),
	)
}

func TeleportDatePickerComponent(props TeleportDatePickerProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/datepicker/datepicker.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	uniqueId := props.UniqueId
	if uniqueId == "" {
		uniqueId = props.Name + "-" + fmt.Sprintf("%d", time.Now().UnixNano())
	}

	return html.Div(
		html.Class("mb-4"),
		html.Label(
			html.For(props.Name),
			html.Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
			gomponents.Text(props.Label),
		),
		html.Div(
			html.Class("relative "+props.Class),
			gomponents.Attr("x-data", fmt.Sprintf("Object.assign(teleportDatePickerComponent('%s', '%s'), { onDateChange() { $el.querySelector('input[name=\"%s\"]').value = this.datePickerValue; } })", props.Value, uniqueId, props.Name)),
			gomponents.If(props.XModel != "", gomponents.Attr("x-init", fmt.Sprintf(`
				$watch('datePickerValue', value => {
					%s = value;
					onDateChange();
				});
				if (!datePickerValue && %s) {
					datePickerValue = %s;
					datePickerResetToDefault();
				} else if (!%s && datePickerValue) {
					%s = datePickerValue;
				}
			`, props.XModel, props.XModel, props.XModel, props.XModel, props.XModel))),
			html.Input(
				gomponents.Attr("x-ref", "datePickerInput"),
				gomponents.Attr("type", "text"),
				gomponents.Attr("@click", "openDatePicker()"),
				gomponents.Attr("x-model", "datePickerValue"),
				gomponents.Attr("x-on:keydown.escape", "closeDatePicker()"),
				html.Class("flex w-full h-10 px-3 py-2 text-sm bg-white border rounded-md text-neutral-600 border-neutral-300 ring-offset-background placeholder:text-neutral-400 focus:border-neutral-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-neutral-800 dark:border-neutral-600 dark:text-gray-300 dark:placeholder:text-neutral-500"),
				gomponents.Attr("placeholder", props.Placeholder),
				gomponents.Attr("readonly", "true"),
				gomponents.Attr("name", props.Name),
				gomponents.Attr("data-testid", fmt.Sprintf("datepicker-%s", props.Name)),
				gomponents.If(props.Value != "", gomponents.Attr("value", props.Value)),
			),

			// Teleported datepicker popup
			html.Template(
				gomponents.Attr("x-teleport", "#datepicker-portal"),
				html.Div(
					gomponents.Attr("x-show", "datePickerOpen"),
					gomponents.Attr("@click.away", "closeDatePicker()"),
					gomponents.Attr("data-teleport-id", uniqueId),
					html.Class("fixed max-w-lg p-4 antialiased bg-white border rounded-lg shadow border-neutral-200/70 z-50 dark:bg-neutral-800 dark:border-neutral-600"),
					gomponents.Attr("style", "display: none;"), // Initially hidden

					// Month/year navigation
					html.Div(
						html.Class("flex items-center justify-between mb-2 gap-2"),
						// Year navigation left
						html.Button(
							html.Type("button"),
							gomponents.Attr("@click", "datePickerPreviousYear()"),
							gomponents.Attr("data-testid", "datepicker-prev-year"),
							html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
							solid.ChevronDoubleLeft(
								html.Class("inline-flex w-6 h-6 text-gray-400"),
							),
						),
						// Month navigation
						html.Button(
							html.Type("button"),
							gomponents.Attr("@click", "datePickerPreviousMonth()"),
							gomponents.Attr("data-testid", "datepicker-prev-month"),
							html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
							solid.ChevronLeft(
								html.Class("inline-flex w-6 h-6 text-gray-400"),
							),
						),
						// Month/year display
						html.Div(
							html.Class("w-[145px] flex justify-center items-center gap-1"),
							html.Span(
								gomponents.Attr("x-text", "datePickerMonthNames[datePickerMonth]"),
								html.Class("text-lg font-bold text-gray-800 dark:text-white"),
								gomponents.Attr("data-testid", "datepicker-month-name"),
							),
							html.Span(
								gomponents.Attr("x-text", "datePickerYear"),
								html.Class("text-lg font-normal text-gray-600 dark:text-white cursor-pointer hover:text-blue-600"),
								gomponents.Attr("data-testid", "datepicker-year"),
								gomponents.Attr("@click", "datePickerToggleYearGrid()"),
							),
						),
						html.Button(
							html.Type("button"),
							gomponents.Attr("@click", "datePickerNextMonth()"),
							gomponents.Attr("data-testid", "datepicker-next-month"),
							html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
							solid.ChevronRight(
								html.Class("inline-flex w-6 h-6 text-gray-400"),
							),
						),
						// Year navigation right
						html.Button(
							html.Type("button"),
							gomponents.Attr("@click", "datePickerNextYear()"),
							gomponents.Attr("data-testid", "datepicker-next-year"),
							html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
							solid.ChevronDoubleRight(
								html.Class("inline-flex w-6 h-6 text-gray-400"),
							),
						),
					),

					// Year grid or month/day view
					html.Template(
						gomponents.Attr("x-if", "datePickerShowYearGrid"),
						html.Div(
							html.Class("mb-3"),
							// Year grid navigation
							html.Div(
								html.Class("flex justify-between items-center mb-2"),
								html.Button(
									html.Type("button"),
									gomponents.Attr("@click", "datePickerPreviousYearGrid()"),
									gomponents.Attr("data-testid", "datepicker-prev-year-grid"),
									html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
									solid.ChevronLeft(
										html.Class("w-6 h-6 text-gray-400"),
									),
								),
								html.Span(
									html.Class("text-base font-semibold text-gray-700 dark:text-white"),
									gomponents.Attr("x-text", "datePickerYearGrid[0] + ' - ' + datePickerYearGrid[datePickerYearGrid.length-1]"),
									gomponents.Attr("data-testid", "datepicker-year-grid-range"),
								),
								html.Button(
									html.Type("button"),
									gomponents.Attr("@click", "datePickerNextYearGrid()"),
									gomponents.Attr("data-testid", "datepicker-next-year-grid"),
									html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
									solid.ChevronRight(
										html.Class("w-6 h-6 text-gray-400"),
									),
								),
							),
							html.Div(
								html.Class("grid grid-cols-5 gap-2"),
								gomponents.El("template",
									gomponents.Attr("x-for", "year in datePickerYearGrid"),
									gomponents.Attr(":key", "'year-' + year"),
									html.Button(
										html.Type("button"),
										gomponents.Attr("@click", "datePickerSelectYear(year)"),
										html.Class("w-14 h-10 flex items-center justify-center rounded-md transition-colors duration-100 text-base focus:outline-none hover:bg-blue-100"),
										gomponents.Attr(":class", "{'bg-blue-600 text-white font-bold shadow': datePickerYear === year}"),
										gomponents.Attr(":data-testid", "'datepicker-year-' + year"),
										html.Span(gomponents.Attr("x-text", "year")),
									),
								),
							),
						),
					),
					html.Template(
						gomponents.Attr("x-if", "!datePickerShowYearGrid"),
						html.Div(
							html.Div(
								html.Class("grid grid-cols-7 mb-3"),
								gomponents.Map([]string{
									locales["sun"], locales["mon"], locales["tue"],
									locales["wed"], locales["thu"], locales["fri"], locales["sat"],
								}, func(day string) gomponents.Node {
									return html.Div(
										html.Class("px-0.5"),
										html.Div(
											gomponents.Text(day),
											html.Class("text-xs font-medium text-center text-gray-800"),
										),
									)
								}),
							),
							html.Div(
								html.Class("grid grid-cols-7 gap-1"),
								// Blank days
								gomponents.El("template",
									gomponents.Attr("x-for", "blankday in datePickerBlankDaysInMonth"),
									gomponents.Attr(":key", "'blank-' + blankday"),
									html.Div(),
								),
								// Actual days
								html.Template(gomponents.Attr("x-for", "day in datePickerDaysInMonth"),
									gomponents.Attr(":key", "day"),
									html.Button(
										html.Type("button"),
										html.Class("w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-100 text-sm focus:outline-none"),
										gomponents.Attr(":class", `{
											'bg-blue-600 text-white font-bold shadow': datePickerIsSelectedDate(day),
											'bg-gray-200 text-gray-900': datePickerIsToday(day) && !datePickerIsSelectedDate(day),
											'hover:bg-blue-100': !datePickerIsSelectedDate(day) && !datePickerIsToday(day)
										}`),
										gomponents.Attr("@click", "datePickerDayClicked(day)"),
										gomponents.Attr(":data-testid", "'datepicker-day-' + day"),
										html.Span(
											gomponents.Attr("x-text", "day"),
										),
									),
								),
							),
						),
					),
				),
			),
		),
		html.Div(
			html.Class("h-5"), // Set a fixed height for the error message container
			html.Div(
				html.Class("text-red-500 text-sm"),
				gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", props.Name)),
				gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", props.Name)),
			),
		),
	)
}
