package uiselect

import (
	"github.com/j-em/coachpad/i18n"
	gomponents "maragu.dev/gomponents"
)

// CountrySelectProps extends SelectProps with language support
type CountrySelectProps struct {
	SelectProps
	Lang string
}

// CountrySelect renders a country dropdown using the custom Select component.
func CountrySelect(props CountrySelectProps) gomponents.Node {
	// Load locales for internationalization
	locales, err := i18n.LoadTemplateLocales("./templates/ui/uiselect/countryselect.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Predefined country options with localized titles
	items := []SelectItem{
		{Value: "", Title: locales["select_country"], Disabled: false},
		{Value: "us", Title: locales["united_states"], Disabled: false},
		{Value: "ca", Title: locales["canada"], Disabled: false},
		{Value: "other", Title: locales["other"], Disabled: false},
	}

	props.SelectProps.Items = items
	if props.SelectProps.Placeholder == "" {
		props.SelectProps.Placeholder = locales["select_country"]
	}
	if props.SelectProps.Label == "" {
		props.SelectProps.Label = locales["country"]
	}
	if props.SelectProps.ID == "" {
		props.SelectProps.ID = "country"
		props.SelectProps.XID = "$id('country')"
	}
	if props.SelectProps.Name == "" {
		props.SelectProps.Name = "country"
	}
	return Select(props.SelectProps)
}
