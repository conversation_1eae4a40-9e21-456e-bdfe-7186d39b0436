package uiselect

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/utils/jsonutils"
	gomponents "maragu.dev/gomponents"
	html "maragu.dev/gomponents/html" // Alias the html package
)

type SelectProps struct {
	Items        []SelectItem
	Class        string
	Placeholder  string
	Name         string
	ID           string
	XID          string // Optional x-id for Alpine.js or HTMX interactions
	Label        string
	Required     bool
	DefaultValue string // Optional default selected item value (id)
	XModel       string // Optional Alpine.js x-model directive
	DataTestID   string // Optional data-testid attribute for testing
}

type SelectItem struct {
	Value    string `json:"value"`
	Title    string `json:"title"`
	Disabled bool   `json:"disabled"`
}

func Select(props SelectProps) gomponents.Node {
	selectItemsJson, err := jsonutils.Marshal(props.Items)
	if err != nil {
		selectItemsJson = []byte("[]")
	}

	defaultValueJson := []byte("null")
	if props.DefaultValue != "" {
		defaultValueJson, _ = jsonutils.Marshal(props.DefaultValue)
	}

	return html.Div(
		html.Class("mb-4"),
		gomponents.If(props.Label != "",
			html.P(
				html.Class("block text-slate-900 text-sm font-semibold mb-2 dark:text-slate-300"),
				gomponents.Text(props.Label),
				gomponents.If(props.Required,
					html.Span(
						html.Class("text-red-500 ml-1"),
						gomponents.Text("*"),
					),
				),
			),
		),
		html.Div(
			html.Class("relative "+props.Class),
			gomponents.Attr("x-bind:id", fmt.Sprintf("%s +'-dropdown'", props.XID)),
			gomponents.Attr("x-data", "selectComponent("+string(selectItemsJson)+", "+string(defaultValueJson)+")"),
			html.Button(
				html.Type("button"),
				html.ID(props.ID+"-button"),
				gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),
				gomponents.Attr("x-ref", "selectButton"),
				gomponents.Attr("@click", "selectOpen=!selectOpen"),
				html.Class("relative flex items-center justify-between w-full py-2 pl-3 pr-12 text-left bg-gradient-to-br from-white to-slate-50 border border-slate-300 rounded-md shadow-sm cursor-pointer focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm text-slate-800 transition-all duration-300 ease-out hover:from-slate-50 hover:to-slate-100 hover:border-slate-400 hover:shadow-lg hover:-translate-y-0.5 dark:from-slate-700 dark:to-slate-800 dark:border-slate-600 dark:text-slate-200 dark:hover:from-slate-600 dark:hover:to-slate-700"),
				html.Span(
					gomponents.Attr("x-text", "selectedItemTitle || '"+props.Placeholder+"'"),
					html.Class("truncate"),
				),
				html.Span(
					html.Class("absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"),
					gomponents.Raw(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="w-5 h-5 text-gray-400 dark:text-gray-500"><path fill-rule="evenodd" d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z" clip-rule="evenodd"></path></svg>`),
				),
			),
			html.Ul(
				// WARNING: Setting an ID on this conflicts with HTMX logic: https://github.com/alpinejs/alpine/discussions/4485
				gomponents.Attr("data-testid", props.ID+"-dropdown"),
				gomponents.Attr("x-effect", "selectOpen && htmx.process($el)"),
				gomponents.Attr("x-show", "selectOpen"),
				gomponents.Attr("x-transition:enter", "transition ease-out duration-300"),
				gomponents.Attr("x-transition:enter-start", "opacity-0 scale-95 -translate-y-2"),
				gomponents.Attr("x-transition:enter-end", "opacity-100 scale-100 translate-y-0"),
				gomponents.Attr("x-transition:leave", "transition ease-in duration-200"),
				gomponents.Attr("x-transition:leave-start", "opacity-100 scale-100 translate-y-0"),
				gomponents.Attr("x-transition:leave-end", "opacity-0 scale-95 -translate-y-2"),
				gomponents.Attr("x-ref", "selectableItemsList"),
				gomponents.Attr("@click.away", "selectOpen = false"),
				gomponents.Attr(":class", "{ 'bottom-0 mb-15' : selectDropdownPosition == 'top', 'top-0 mt-1' : selectDropdownPosition == 'bottom' }"),
				html.Class("absolute w-full z-50 py-1 overflow-hidden text-base bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-2xl max-h-56 ring-1 ring-slate-300 backdrop-blur-md focus:outline-none border border-slate-300 dark:from-slate-700 dark:to-slate-800 dark:ring-slate-600 dark:border-slate-600"),
				gomponents.Map(props.Items, func(item SelectItem) gomponents.Node {
					jsonItem, err := jsonutils.Marshal(item)
					if err != nil {
						jsonItem = []byte("{}")
					}

					return html.Li(
						html.ID(props.ID+"-option-"+item.Value),
						gomponents.Attr("@click", `selectItem(`+string(jsonItem)+`)`),
						gomponents.Attr("data-value", item.Value),
						gomponents.Attr("data-disabled", strconv.FormatBool(item.Disabled)),
						gomponents.Attr(":class", "selectedItemValue === '"+item.Value+"' ? 'bg-blue-100 text-blue-600 font-semibold dark:bg-blue-600/20 dark:text-blue-300' : ''"),
						html.Class("relative flex items-center h-full py-2 pl-3 pr-3 text-slate-800 cursor-pointer select-none transition-all duration-200 ease-out hover:bg-gradient-to-r hover:from-blue-600 hover:to-blue-500 hover:text-white hover:translate-x-1 border-b border-slate-300/40 last:border-b-0 dark:text-slate-200 dark:border-slate-600/30"),
						html.Span(
							gomponents.Text(item.Title),
							html.Class("block font-medium truncate flex-1"),
						),
						html.Span(
							gomponents.Attr("x-show", "selectedItemValue === '"+item.Value+"'"),
							html.Class("text-blue-600 ml-2 font-bold dark:text-blue-400"),
							gomponents.Text("✓"),
						),
					)
				}),
			),
			// Hidden input to store the actual value
			html.Input(
				html.Type("hidden"),
				html.Name(props.Name),
				html.ID(props.ID),
				gomponents.Attr("x-model", func() string {
					if props.XModel != "" {
						return props.XModel
					}
					return "selectedItemValue"
				}()),
				gomponents.Attr("x-ref", "hiddenInput"), // Added x-ref for proper referencing
				gomponents.If(props.Required,
					gomponents.Group([]gomponents.Node{
						gomponents.Attr("data-validate-required", "true"),
						gomponents.Attr("required", ""),
					}),
				),
			),
			// Error message container
			html.Div(
				html.Class("h-5"), // Fixed height to avoid layout shift
				html.Div(
					html.Class("text-red-500 text-sm"),
					gomponents.Attr("x-show", fmt.Sprintf("errors['%s']", props.ID)),
					gomponents.Attr("x-text", fmt.Sprintf("errorMessages['%s']", props.ID)),
				),
			),
		),
	)
}
