package uiselect

import (
	"github.com/j-em/coachpad/i18n"
	gomponents "maragu.dev/gomponents"
)

// CategorySelectProps extends SelectProps with language support
type CategorySelectProps struct {
	SelectProps
	Lang             string
	IncludeAllOption bool // Whether to include "All Categories" option for filters
}

// CategorySelect renders a spending category dropdown using the custom Select component.
func CategorySelect(props CategorySelectProps) gomponents.Node {
	// Load locales for internationalization
	locales, err := i18n.LoadTemplateLocales("./templates/ui/uiselect/categoryselect.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Predefined category options with localized titles
	items := []SelectItem{}

	// Add "All Categories" or "Select Category" as first option
	if props.IncludeAllOption {
		items = append(items, SelectItem{Value: "", Title: locales["all_categories"], Disabled: false})
	} else {
		items = append(items, SelectItem{Value: "", Title: locales["select_category"], Disabled: false})
	}

	// Add all category options
	categoryItems := []SelectItem{
		{Value: "Equipment", Title: locales["equipment"], Disabled: false},
		{Value: "Travel", Title: locales["travel"], Disabled: false},
		{Value: "Facilities", Title: locales["facilities"], Disabled: false},
		{Value: "Food", Title: locales["food"], Disabled: false},
		{Value: "Medical", Title: locales["medical"], Disabled: false},
		{Value: "Referee Fees", Title: locales["referee_fees"], Disabled: false},
		{Value: "Venue Rental", Title: locales["venue_rental"], Disabled: false},
		{Value: "Insurance", Title: locales["insurance"], Disabled: false},
		{Value: "Marketing", Title: locales["marketing"], Disabled: false},
		{Value: "Training Materials", Title: locales["training_materials"], Disabled: false},
		{Value: "Tournament Entry", Title: locales["tournament_entry"], Disabled: false},
		{Value: "Uniforms", Title: locales["uniforms"], Disabled: false},
		{Value: "Transportation", Title: locales["transportation"], Disabled: false},
		{Value: "Maintenance", Title: locales["maintenance"], Disabled: false},
		{Value: "Administration", Title: locales["administration"], Disabled: false},
	}

	items = append(items, categoryItems...)

	props.SelectProps.Items = items
	if props.SelectProps.Placeholder == "" {
		if props.IncludeAllOption {
			props.SelectProps.Placeholder = locales["all_categories"]
		} else {
			props.SelectProps.Placeholder = locales["select_category"]
		}
	}
	if props.SelectProps.Label == "" {
		props.SelectProps.Label = locales["category"]
	}
	if props.SelectProps.ID == "" {
		props.SelectProps.ID = "category"
		props.SelectProps.XID = "$id('category')"
	}
	if props.SelectProps.Name == "" {
		props.SelectProps.Name = "category"
	}
	return Select(props.SelectProps)
}
