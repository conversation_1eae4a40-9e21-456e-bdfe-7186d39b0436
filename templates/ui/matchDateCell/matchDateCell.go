package matchdatecell

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/utils/matchutils"
	"github.com/jackc/pgx/v5/pgtype"
	"maragu.dev/gomponents"
	solid "maragu.dev/gomponents-heroicons/v3/solid"
	"maragu.dev/gomponents/html"
)

// MatchDateCellProps contains props for the match date cell
type MatchDateCellProps struct {
	Match db.GetMatchesBySeasonIdRow
	Lang  string
}

// MatchDateCell creates the match date input cell using the teleport datepicker component
func MatchDateCell(props MatchDateCellProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/matchDateCell/matchDateCell.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	uniqueId := fmt.Sprintf("match-%d", props.Match.ID)

	return html.Td(
		html.Class("px-6 py-4"),
		html.Div(
			html.Class("relative"),
			gomponents.Attr("x-data", fmt.Sprintf("Object.assign(teleportDatePickerComponent('%s', '%s'), { onDateChange() { $el.querySelector('input[name=matchDate]').value = this.datePickerValue; $el.querySelector('input[name=matchDate]').dispatchEvent(new Event('change', { bubbles: true })); } })", formatMatchDate(props.Match.MatchDate), uniqueId)),
			gomponents.Attr("x-init", "$watch('datePickerValue', () => { onDateChange(); })"),

			// Compact datepicker input for table cell
			html.Input(
				gomponents.Attr("x-ref", "datePickerInput"),
				gomponents.Attr("type", "text"),
				gomponents.Attr("@click", "openDatePicker()"),
				gomponents.Attr("x-model", "datePickerValue"),
				gomponents.Attr("x-on:keydown.escape", "closeDatePicker()"),
				html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 cursor-pointer"),
				gomponents.Attr("placeholder", "Select date"),
				gomponents.Attr("readonly", "true"),
				gomponents.Attr("data-testid", fmt.Sprintf("match-%d-date-picker", props.Match.ID)),
			),

			// Teleported datepicker popup - will be moved to portal container
			html.Template(
				gomponents.Attr("x-teleport", "#datepicker-portal"),
				html.Div(
					gomponents.Attr("x-show", "datePickerOpen"),
					gomponents.Attr("@click.away", "closeDatePicker()"),
					gomponents.Attr("data-teleport-id", uniqueId),
					html.Class("fixed max-w-lg p-4 antialiased bg-white border rounded-lg shadow border-neutral-200/70 z-50 dark:bg-neutral-800 dark:border-neutral-600"),
					gomponents.Attr("style", "display: none;"), // Initially hidden
					buildDatePickerPopupContent(locales),
				),
			),

			// Hidden input for HTMX form submission
			html.Input(
				html.Type("hidden"),
				html.Name("matchDate"),
				html.Value(formatMatchDate(props.Match.MatchDate)),
				gomponents.Attr("data-testid", fmt.Sprintf("match-%d-date", props.Match.ID)),
			),
		),
	)
}

// buildDatePickerPopupContent creates the datepicker popup content
func buildDatePickerPopupContent(locales map[string]string) gomponents.Node {
	return html.Div(
		html.Div(
			html.Class("flex items-center justify-between mb-2 gap-2"),
			// Year navigation left
			html.Button(
				html.Type("button"),
				gomponents.Attr("@click", "datePickerPreviousYear()"),
				gomponents.Attr("data-testid", "datepicker-prev-year"),
				html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
				solid.ChevronDoubleLeft(
					html.Class("inline-flex w-6 h-6 text-gray-400"),
				),
			),
			// Month/year display
			html.Div(
				html.Class("w-[145px] flex justify-center items-center gap-1"),
				html.Span(
					gomponents.Attr("x-text", "datePickerMonthNames[datePickerMonth]"),
					html.Class("text-lg font-bold text-gray-800 dark:text-white"),
					gomponents.Attr("data-testid", "datepicker-month-name"),
				),
				html.Span(
					gomponents.Attr("x-text", "datePickerYear"),
					html.Class("text-lg font-normal text-gray-600 dark:text-white cursor-pointer hover:text-blue-600"),
					gomponents.Attr("data-testid", "datepicker-year"),
					gomponents.Attr("@click", "datePickerToggleYearGrid()"),
				),
			),
			// Month navigation
			html.Button(
				html.Type("button"),
				gomponents.Attr("@click", "datePickerPreviousMonth()"),
				gomponents.Attr("data-testid", "datepicker-prev-month"),
				html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
				solid.ChevronLeft(
					html.Class("inline-flex w-6 h-6 text-gray-400"),
				),
			),
			html.Button(
				html.Type("button"),
				gomponents.Attr("@click", "datePickerNextMonth()"),
				gomponents.Attr("data-testid", "datepicker-next-month"),
				html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
				solid.ChevronRight(
					html.Class("inline-flex w-6 h-6 text-gray-400"),
				),
			),
			// Year navigation right
			html.Button(
				html.Type("button"),
				gomponents.Attr("@click", "datePickerNextYear()"),
				gomponents.Attr("data-testid", "datepicker-next-year"),
				html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
				solid.ChevronDoubleRight(
					html.Class("inline-flex w-6 h-6 text-gray-400"),
				),
			),
		),
		// Year grid or month/day view
		html.Template(
			gomponents.Attr("x-if", "datePickerShowYearGrid"),
			html.Div(
				html.Class("mb-3"),
				// Year grid navigation
				html.Div(
					html.Class("flex justify-between items-center mb-2"),
					html.Button(
						html.Type("button"),
						gomponents.Attr("@click", "datePickerPreviousYearGrid()"),
						gomponents.Attr("data-testid", "datepicker-prev-year-grid"),
						html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
						solid.ChevronLeft(
							html.Class("w-6 h-6 text-gray-400"),
						),
					),
					html.Span(
						html.Class("text-base font-semibold text-gray-700 dark:text-white"),
						gomponents.Attr("x-text", "datePickerYearGrid[0] + ' - ' + datePickerYearGrid[datePickerYearGrid.length-1]"),
						gomponents.Attr("data-testid", "datepicker-year-grid-range"),
					),
					html.Button(
						html.Type("button"),
						gomponents.Attr("@click", "datePickerNextYearGrid()"),
						gomponents.Attr("data-testid", "datepicker-next-year-grid"),
						html.Class("inline-flex p-1 transition duration-100 ease-in-out rounded-full cursor-pointer focus:outline-none focus:shadow-outline hover:bg-gray-100"),
						solid.ChevronRight(
							html.Class("w-6 h-6 text-gray-400"),
						),
					),
				),
				html.Div(
					html.Class("grid grid-cols-5 gap-2"),
					gomponents.El("template",
						gomponents.Attr("x-for", "year in datePickerYearGrid"),
						gomponents.Attr(":key", "'year-' + year"),
						html.Button(
							html.Type("button"),
							gomponents.Attr("@click", "datePickerSelectYear(year)"),
							html.Class("w-14 h-10 flex items-center justify-center rounded-md transition-colors duration-100 text-base focus:outline-none hover:bg-blue-100"),
							gomponents.Attr(":class", "{'bg-blue-600 text-white font-bold shadow': datePickerYear === year}"),
							gomponents.Attr(":data-testid", "'datepicker-year-' + year"),
							html.Span(gomponents.Attr("x-text", "year")),
						),
					),
				),
			),
		),
		html.Template(
			gomponents.Attr("x-if", "!datePickerShowYearGrid"),
			html.Div(
				html.Div(
					html.Class("grid grid-cols-7 mb-3"),
					gomponents.Map([]string{
						locales["sun"], locales["mon"], locales["tue"],
						locales["wed"], locales["thu"], locales["fri"], locales["sat"],
					}, func(day string) gomponents.Node {
						return html.Div(
							html.Class("px-0.5"),
							html.Div(
								gomponents.Text(day),
								html.Class("text-xs font-medium text-center text-gray-800"),
							),
						)
					}),
				),
				html.Div(
					html.Class("grid grid-cols-7 gap-1"),
					// Blank days
					gomponents.El("template",
						gomponents.Attr("x-for", "blankday in datePickerBlankDaysInMonth"),
						gomponents.Attr(":key", "'blank-' + blankday"),
						html.Div(),
					),
					// Actual days
					html.Template(gomponents.Attr("x-for", "day in datePickerDaysInMonth"),
						gomponents.Attr(":key", "day"),
						html.Button(
							html.Type("button"),
							html.Class("w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-100 text-sm focus:outline-none"),
							gomponents.Attr(":class", `{
                             'bg-blue-600 text-white font-bold shadow': datePickerIsSelectedDate(day),
                             'bg-gray-200 text-gray-900': datePickerIsToday(day) && !datePickerIsSelectedDate(day),
                             'hover:bg-blue-100': !datePickerIsSelectedDate(day) && !datePickerIsToday(day)
                           }`),
							gomponents.Attr("@click", "datePickerDayClicked(day)"),
							gomponents.Attr(":data-testid", "'datepicker-day-' + day"),
							html.Span(
								gomponents.Attr("x-text", "day"),
							),
						),
					),
				),
			),
		),
	)
}

// formatMatchDate converts pgtype.Date to string
func formatMatchDate(date pgtype.Date) string {
	return matchutils.FormatMatchDate(date, matchutils.DateFormatYMD)
}
