package templatesuilangselect

import (
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/uiselect"
	"maragu.dev/gomponents"
)

type LanguageSelectConfig struct {
	Name         string
	DefaultValue string // Optional: default selected language value
	ID           string
	Lang         string // Language for localization
	XModel       string // Optional Alpine.js x-model directive
}

func LanguageSelect(config LanguageSelectConfig) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/langselect/langselect.locales.json", config.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	return uiselect.Select(uiselect.SelectProps{
		ID:           config.ID,
		Class:        "w-full",
		Placeholder:  locales["select_language"],
		Name:         config.Name,
		DefaultValue: config.DefaultValue,
		XModel:       config.XModel,
		Items: []uiselect.SelectItem{
			{Value: "en", Title: locales["english"]},
			{Value: "fr", Title: locales["french"]},
		},
	})
}
