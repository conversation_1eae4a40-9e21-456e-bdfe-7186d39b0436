package templatesuibadge

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type BadgeProps struct {
	Text    string
	Variant string // "success", "info", "warning", "error"
	Size    string // "sm", "md", "lg"
}

func Badge(props BadgeProps) gomponents.Node {
	variant := props.Variant
	if variant == "" {
		variant = "info"
	}

	size := props.Size
	if size == "" {
		size = "md"
	}

	baseClasses := "inline-flex items-center font-medium rounded-full"

	var variantClasses string
	switch variant {
	case "success":
		variantClasses = "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
	case "info":
		variantClasses = "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
	case "warning":
		variantClasses = "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
	case "error":
		variantClasses = "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
	default:
		variantClasses = "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
	}

	var sizeClasses string
	switch size {
	case "sm":
		sizeClasses = "px-2 py-1 text-xs"
	case "md":
		sizeClasses = "px-2.5 py-0.5 text-sm"
	case "lg":
		sizeClasses = "px-3 py-1 text-base"
	default:
		sizeClasses = "px-2.5 py-0.5 text-sm"
	}

	return html.Span(
		html.Class(baseClasses+" "+variantClasses+" "+sizeClasses),
		gomponents.Text(props.Text),
	)
}
