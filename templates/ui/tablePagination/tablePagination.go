package tablepagination

import (
	"fmt"
	"net/url"
	"strconv"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/utils/pagination"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// TablePaginationProps contains props for the unified table pagination component
type TablePaginationProps struct {
	// Core pagination data
	Params     pagination.SortablePaginationParams
	TotalItems int
	TotalPages int

	// Display and behavior
	Lang       string
	BaseURL    string
	DataTestID string

	// Target configuration
	TargetID string // e.g., "players-table-content", "matches-table-content"

	// HTMX configuration
	HtmxInclude string // e.g., "[name='search'], [name='sort'], [name='dir']"

	// Custom query parameters (for specific table types)
	ExtraParams map[string]string // e.g., {"filter_today": "true", "season_id": "123"}
}

// TablePagination renders unified pagination controls for any table
func TablePagination(props TablePaginationProps) gomponents.Node {
	// Load locales - we'll use a generic pagination locales file
	locales, err := i18n.LoadTemplateLocales("./templates/ui/tablePagination/tablePagination.locales.json", props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Ensure required props have defaults
	if props.TargetID == "" {
		props.TargetID = "table-content"
	}
	if props.HtmxInclude == "" {
		props.HtmxInclude = "[name='search'], [name='sort'], [name='dir']"
	}

	targetSelector := "#" + props.TargetID
	startItem := (props.Params.Page-1)*props.Params.ItemsPerPage + 1
	endItem := min(props.Params.Page*props.Params.ItemsPerPage, props.TotalItems)

	return html.Div(
		html.Class("mt-4 flex justify-between items-center"),
		gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),

		// Left side: Pagination controls
		html.Div(
			html.Class("flex space-x-2"),
			html.Button(
				html.ID("prev-page-btn"),
				html.Class("px-4 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded text-sm"),
				gomponents.If(props.Params.Page == 1, html.Disabled()),
				gomponents.If(props.Params.Page == 1, html.Class("opacity-50 cursor-not-allowed")),
				htmx.Get(buildPaginationURL(props.BaseURL, props.Params, props.ExtraParams, props.Params.Page-1)),
				htmx.Target(targetSelector),
				htmx.PushURL("true"),
				gomponents.Text(locales["prev_page"]),
			),
			html.Button(
				html.ID("next-page-btn"),
				html.Class("px-4 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded text-sm"),
				gomponents.If(props.Params.Page >= props.TotalPages, html.Disabled()),
				gomponents.If(props.Params.Page >= props.TotalPages, html.Class("opacity-50 cursor-not-allowed")),
				htmx.Get(buildPaginationURL(props.BaseURL, props.Params, props.ExtraParams, props.Params.Page+1)),
				htmx.Target(targetSelector),
				htmx.PushURL("true"),
				gomponents.Text(locales["next_page"]),
			),
		),
		// Middle: Pagination information
		html.Div(
			html.ID("pagination-info"),
			html.Class("text-sm text-gray-700 dark:text-gray-200 flex items-center space-x-1"),
			html.Span(gomponents.Text(locales["showing"])),
			html.Span(gomponents.Text(strconv.Itoa(startItem))),
			html.Span(gomponents.Text("-")),
			html.Span(gomponents.Text(strconv.Itoa(endItem))),
			html.Span(gomponents.Text(locales["of"])),
			html.Span(gomponents.Text(strconv.Itoa(props.TotalItems))),
		),
		// Right side: Items per page selector
		html.Div(
			html.Class("flex items-center space-x-2"),
			html.Label(
				html.Class("text-sm text-gray-700 dark:text-gray-200"),
				html.For("items-per-page"),
				gomponents.Text(locales["items_per_page"]),
			),
			html.Select(
				html.ID("items-per-page"),
				html.Class("text-sm border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded px-2 py-1"),
				html.Name("per_page"),
				htmx.Get(props.BaseURL),
				htmx.Target(targetSelector),
				htmx.Include(props.HtmxInclude),
				htmx.Trigger("change"),
				buildPerPageOptions(props.Params.ItemsPerPage, locales),
			),
		),
	)
}

// buildPaginationURL creates URL with all pagination and custom parameters
func buildPaginationURL(baseURL string, params pagination.SortablePaginationParams, extraParams map[string]string, page int) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		// Fallback to simple string concatenation if URL parsing fails
		url := fmt.Sprintf("%s?page=%d&per_page=%d", baseURL, page, params.ItemsPerPage)
		if params.Sort != "" {
			url += fmt.Sprintf("&sort=%s&dir=%s", params.Sort, params.Direction)
		}
		if params.Search != "" {
			url += "&search=" + params.Search
		}
		for key, value := range extraParams {
			if value != "" {
				url += fmt.Sprintf("&%s=%s", key, value)
			}
		}
		return url
	}

	query := u.Query()
	query.Set("page", strconv.Itoa(page))
	query.Set("per_page", strconv.Itoa(params.ItemsPerPage))

	if params.Sort != "" {
		query.Set("sort", params.Sort)
		query.Set("dir", params.Direction)
	}

	if params.Search != "" {
		query.Set("search", params.Search)
	}

	// Add extra parameters
	for key, value := range extraParams {
		if value != "" {
			query.Set(key, value)
		}
	}

	u.RawQuery = query.Encode()
	return u.String()
}

// buildPerPageOptions creates per-page options with localization
func buildPerPageOptions(current int, locales map[string]string) gomponents.Node {
	options := []int{5, 10, 25, 50, 9001}
	return gomponents.Group(
		gomponents.Map(options, func(value int) gomponents.Node {
			text := strconv.Itoa(value)
			if value == 9001 {
				if locales["over_9000"] != "" {
					text = locales["over_9000"]
				} else {
					text = "OVER 9000!"
				}
			}
			return html.Option(
				html.Value(strconv.Itoa(value)),
				gomponents.If(value == current, html.Selected()),
				gomponents.Text(text),
			)
		}),
	)
}

// Helper function
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
