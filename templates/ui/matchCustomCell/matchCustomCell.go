package matchcustomcell

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// MatchCustomCellProps contains props for the match custom cell
type MatchCustomCellProps struct {
	Match        db.GetMatchesBySeasonIdRow
	Column       db.MatchCustomColumn
	CustomValues map[int32][]db.MatchCustomValue
}

// MatchCustomCell creates an individual custom column input cell
func MatchCustomCell(props MatchCustomCellProps) gomponents.Node {
	if !props.Column.IsActive.Bool {
		return gomponents.Text("")
	}

	value := getCustomValue(props.Match.ID, props.Column.ID, props.CustomValues)

	return html.Td(
		html.Class("px-6 py-4"),
		html.Input(
			html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
			gomponents.Attr("type", getInputType(props.Column.FieldType)),
			html.Value(value),
			html.Name(fmt.Sprintf("custom_%d", props.Column.ID)),
			htmx.Put(fmt.Sprintf("/app/matches/%d/custom-values", props.Match.ID)),
			htmx.Trigger("blur"),
			htmx.Vals(fmt.Sprintf(`{"column_id": %d}`, props.Column.ID)),
			htmx.Indicator("#global-saving-indicator"),
		),
	)
}

// getCustomValue retrieves custom value for a match and column
func getCustomValue(matchID, columnID int32, customValues map[int32][]db.MatchCustomValue) string {
	if values, exists := customValues[matchID]; exists {
		for _, value := range values {
			if value.ColumnID == columnID {
				if value.Value.Valid {
					return value.Value.String
				}
			}
		}
	}
	return ""
}

// getInputType returns HTML input type based on field type
func getInputType(fieldType string) string {
	switch fieldType {
	case "boolean":
		return "checkbox"
	case "number":
		return "number"
	case "date":
		return "date"
	default:
		return "text"
	}
}
