// package card provides reusable card component templates
package card

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// CardProps defines the properties available for the Card component
type CardProps struct {
	Title       string
	Content     gomponents.Node
	Footer      gomponents.Node
	Class       string
	HeaderClass string
	BodyClass   string
	FooterClass string
}

// Default Tailwind classes for cards
const (
	defaultCardClass        = "bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-visible border border-gray-200 dark:border-gray-700"
	defaultHeaderClass      = "px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700"
	defaultBodyClass        = "px-4 py-5 sm:p-6"
	defaultFooterClass      = "px-4 py-4 sm:px-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700"
	defaultHeaderTitleClass = "text-lg font-medium text-gray-900 dark:text-gray-100"
)

// Card renders a default card component with optional title, content and footer
func Card(props CardProps) gomponents.Node {
	cardClass := props.Class
	if cardClass == "" {
		cardClass = defaultCardClass
	}

	headerClass := props.HeaderClass
	if headerClass == "" {
		headerClass = defaultHeaderClass
	}

	bodyClass := props.BodyClass
	if bodyClass == "" {
		bodyClass = defaultBodyClass
	}

	footerClass := props.FooterClass
	if footerClass == "" {
		footerClass = defaultFooterClass
	}

	children := []gomponents.Node{}

	// Add header if title is provided
	if props.Title != "" {
		children = append(children, html.Div(
			html.Class(headerClass),
			html.H3(
				html.Class(defaultHeaderTitleClass),
				gomponents.Text(props.Title),
			),
		))
	}

	// Add body content
	children = append(children, html.Div(
		html.Class(bodyClass),
		props.Content,
	))

	// Add footer if provided
	if props.Footer != nil {
		children = append(children, html.Div(
			html.Class(footerClass),
			props.Footer,
		))
	}

	return html.Div(
		html.Class(cardClass),
		gomponents.Group(children),
	)
}

// SimpleCard creates a basic card with just content and styling
func SimpleCard(content gomponents.Node) gomponents.Node {
	return Card(CardProps{
		Content: content,
	})
}

// PrimaryCard creates a card with a distinctive primary color theme
func PrimaryCard(title string, content gomponents.Node) gomponents.Node {
	return Card(CardProps{
		Title:       title,
		Content:     content,
		Class:       defaultCardClass + " border-indigo-500 dark:border-indigo-400 border-t-4",
		HeaderClass: defaultHeaderClass + " bg-indigo-50 dark:bg-indigo-900/20",
	})
}

// WarningCard creates a card with a warning color theme
func WarningCard(title string, content gomponents.Node) gomponents.Node {
	return Card(CardProps{
		Title:       title,
		Content:     content,
		Class:       defaultCardClass + " border-yellow-500 dark:border-yellow-400 border-t-4",
		HeaderClass: defaultHeaderClass + " bg-yellow-50 dark:bg-yellow-900/20",
	})
}

// FullCard creates a card with title, content and footer
func FullCard(title string, content gomponents.Node, footer gomponents.Node) gomponents.Node {
	return Card(CardProps{
		Title:   title,
		Content: content,
		Footer:  footer,
	})
}
