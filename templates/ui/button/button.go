package button

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// Helper to get size classes
func getIconButtonSizeClass(size string) (buttonClass, iconClass string) {
	switch size {
	case "small":
		return "py-1 px-2 text-xs cursor-pointer", "w-4 h-4"
	case "large":
		return "py-3 px-6 text-lg cursor-pointer", "w-7 h-7"
	default:
		return "py-2 px-4 text-sm cursor-pointer", "w-5 h-5"
	}
}

type BaseButtonConfig struct {
	ID          string
	ButtonType  string
	Text        string
	Class       string
	Attrs       []gomponents.Node
	DataTestID  string // Optional data-testid attribute
	XDataTestID string // Optional x-bind:data-testid attribute
}

func BaseButton(config BaseButtonConfig) gomponents.Node {
	buttonConfig := BaseButtonConfig{
		ButtonType: config.ButtonType,
		Text:       config.Text,
		Class:      rootBaseButton + " " + config.Class,
	}

	mergedConfig := []gomponents.Node{
		html.Type(buttonConfig.ButtonType),
		html.Class(buttonConfig.Class),
		gomponents.Text(buttonConfig.Text),
	}
	if config.ID != "" {
		mergedConfig = append([]gomponents.Node{html.ID(config.ID)}, mergedConfig...)
	}
	if config.DataTestID != "" {
		mergedConfig = append(mergedConfig, gomponents.Attr("data-testid", config.DataTestID))
	}
	if config.XDataTestID != "" {
		mergedConfig = append(mergedConfig, gomponents.Attr("x-bind:data-testid", config.XDataTestID))
	}
	if config.Attrs != nil {
		mergedConfig = append(mergedConfig, config.Attrs...)
	}
	return html.Button(
		mergedConfig...,
	)
}

type ButtonLinkProps struct {
	Href  string
	Text  string
	Class string
}

func ButtonLink(props ButtonLinkProps) gomponents.Node {
	return html.A(
		html.Href(props.Href),
		html.Class(rootButtonLink+" "+props.Class),
		gomponents.Text(props.Text),
	)
}

type IconButtonConfig struct {
	ID         string
	DataTestID string
	ButtonType string
	Text       string
	Class      string
	Icon       gomponents.Node
	Attrs      []gomponents.Node
	Size       string // "small", "default", "large"
}

func IconButton(config IconButtonConfig) gomponents.Node {
	buttonSizeClass, iconSizeClass := getIconButtonSizeClass(config.Size)
	nodes := []gomponents.Node{
		html.ID(config.ID),
		gomponents.Attr("data-testid", config.DataTestID),
		html.Type(config.ButtonType),
		html.Class(rootIconButton + " " + config.Class + " " + buttonSizeClass),
		gomponents.Group([]gomponents.Node{
			html.Div(
				html.Class(iconSizeClass),
				config.Icon),
			gomponents.Text(" " + config.Text),
		}),
	}
	if config.Attrs != nil {
		nodes = append(nodes, config.Attrs...)
	}
	return html.Button(nodes...)
}

type ButtonStyle string

type IconButtonLinkConfig struct {
	ID         string
	DataTestID string
	Href       string
	Text       string
	Class      string
	Icon       gomponents.Node
	Attrs      []gomponents.Node
	Size       string // "small", "default", "large"
}

// IconButtonLink renders an anchor tag styled as a button, with an icon and text.
func IconButtonLink(config IconButtonLinkConfig) gomponents.Node {
	buttonSizeClass, iconSizeClass := getIconButtonSizeClass(config.Size)
	nodes := []gomponents.Node{
		html.ID(config.ID),
		gomponents.Attr("data-testid", config.DataTestID),
		html.Href(config.Href),
		html.Class(rootIconButtonLink + " " + config.Class + " " + buttonSizeClass),
		html.Div(
			html.Class(iconSizeClass),
			config.Icon),
		gomponents.Text(" " + config.Text),
	}
	if config.Attrs != nil {
		nodes = append(config.Attrs, nodes...)
	}
	return html.A(nodes...)
}

const (
	PrimaryStyle       ButtonStyle = "primary"
	SecondaryStyle     ButtonStyle = "secondary"
	SecondaryLinkStyle ButtonStyle = "secondary-link"
	baseClasses        string      = "cursor-pointer flex items-center gap-2 justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 [&>svg]:w-5 [&>svg]:h-5 cursor-pointer"

	// Root classes for button variations
	rootBaseButton     string = "coachpad-base-button"
	rootButtonLink     string = "coachpad-button-link"
	rootIconButton     string = "coachpad-icon-button"
	rootIconButtonLink string = "coachpad-icon-button-link"
)

func getButtonClass(style ButtonStyle) string {
	switch style {
	case PrimaryStyle:
		return baseClasses + " border-transparent text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600"
	case SecondaryStyle:
		return baseClasses + " border-gray-300 text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:text-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
	case SecondaryLinkStyle:
		// More link-like, less button-like, but still styled for consistency
		return baseClasses + " border-none text-indigo-700 bg-transparent hover:bg-indigo-50 underline px-2 py-1 shadow-none dark:text-indigo-400 dark:hover:bg-indigo-900"
	default:
		return baseClasses
	}
}

func PrimaryButton(config BaseButtonConfig) gomponents.Node {
	config.Class = getButtonClass(PrimaryStyle) + " " + config.Class
	return BaseButton(config)
}

func SecondaryButton(config BaseButtonConfig) gomponents.Node {
	config.Class = getButtonClass(SecondaryStyle) + " " + config.Class
	return BaseButton(config)
}

func PrimaryButtonLink(props ButtonLinkProps) gomponents.Node {
	class := getButtonClass(PrimaryStyle)
	if props.Class != "" {
		class = class + " " + props.Class
	}
	return ButtonLink(ButtonLinkProps{
		Href:  props.Href,
		Text:  props.Text,
		Class: class,
	})
}

func SecondaryButtonLink(href, text string) gomponents.Node {
	return ButtonLink(ButtonLinkProps{
		Href:  href,
		Text:  text,
		Class: getButtonClass(SecondaryStyle),
	})
}

// PrimaryIconButtonLink returns a primary-styled icon button link, merging the provided config with the primary style.
func PrimaryIconButtonLink(customConfig IconButtonLinkConfig) gomponents.Node {
	iconButtonLinkProps := IconButtonLinkConfig{
		ID:         customConfig.ID,
		DataTestID: customConfig.DataTestID,
		Href:       customConfig.Href,
		Text:       customConfig.Text,
		Class:      getButtonClass(PrimaryStyle) + " " + customConfig.Class,
		Icon:       customConfig.Icon,
		Attrs:      customConfig.Attrs,
		Size:       customConfig.Size,
	}
	return IconButtonLink(iconButtonLinkProps)
}

// SecondaryIconButtonLink returns a secondary-styled icon button link, merging the provided config with the secondary style.
func SecondaryIconButtonLink(customConfig IconButtonLinkConfig) gomponents.Node {
	iconButtonLinkProps := IconButtonLinkConfig{
		ID:         customConfig.ID,
		DataTestID: customConfig.DataTestID,
		Href:       customConfig.Href,
		Text:       customConfig.Text,
		Class:      getButtonClass(SecondaryStyle) + " " + customConfig.Class,
		Icon:       customConfig.Icon,
		Attrs:      customConfig.Attrs,
		Size:       customConfig.Size,
	}
	return IconButtonLink(iconButtonLinkProps)
}

// PrimaryIconButton returns a primary-styled icon button, merging the provided config with the primary style.
func PrimaryIconButton(customConfig IconButtonConfig) gomponents.Node {
	iconButtonProps := IconButtonConfig{
		ID:         customConfig.ID,
		DataTestID: customConfig.DataTestID,
		ButtonType: customConfig.ButtonType,
		Text:       customConfig.Text,
		Class:      getButtonClass(PrimaryStyle) + " " + customConfig.Class,
		Icon:       customConfig.Icon,
		Attrs:      customConfig.Attrs,
		Size:       customConfig.Size,
	}
	return IconButton(iconButtonProps)
}

// SecondaryIconButton returns a secondary-styled icon button, merging the provided config with the secondary style.
func SecondaryIconButton(config IconButtonConfig) gomponents.Node {
	iconButtonProps := IconButtonConfig{
		ID:         config.ID,
		DataTestID: config.DataTestID,
		ButtonType: config.ButtonType,
		Text:       config.Text,
		Class:      getButtonClass(SecondaryStyle) + " " + config.Class,
		Icon:       config.Icon,
		Attrs:      config.Attrs,
		Size:       config.Size,
	}
	return IconButton(iconButtonProps)
}
