package matchrow

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/ui/matchCustomCell"
	"github.com/j-em/coachpad/templates/ui/matchDateCell"
	"github.com/j-em/coachpad/templates/ui/matchGroupCell"
	"github.com/j-em/coachpad/templates/ui/matchPlayerCell"
	"github.com/j-em/coachpad/templates/ui/matchPointsCell"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// MatchRowProps contains props for the match row
type MatchRowProps struct {
	Match         db.GetMatchesBySeasonIdRow
	CustomColumns []db.MatchCustomColumn
	CustomValues  map[int32][]db.MatchCustomValue
	Lang          string
}

// MatchRow creates a table row for a match using specialized cell components
func MatchRow(props MatchRowProps) gomponents.Node {
	return html.Tr(
		html.ID(fmt.Sprintf("match-row-%d", props.Match.ID)),

		// Centralized HTMX attributes for all match field updates
		htmx.Patch(fmt.Sprintf("/app/matches/%d", props.Match.ID)),
		htmx.Trigger("blur from:input, change from:input[name='matchDate']"),
		htmx.Include("closest tr"),
		htmx.Indicator("#global-saving-indicator"),
		htmx.Target(fmt.Sprintf("#match-row-%d", props.Match.ID)),
		htmx.Swap("outerHTML"),
		htmx.Sync("this:replace"),

		// Each cell is now a focused, testable component
		buildHiddenMetadataCell(props.Match),
		matchdatecell.MatchDateCell(matchdatecell.MatchDateCellProps{
			Match: props.Match,
			Lang:  props.Lang,
		}),
		matchplayercell.MatchPlayerCell(matchplayercell.MatchPlayerCellProps{
			Match:        props.Match,
			PlayerNumber: 1,
			PlayerName:   props.Match.Player1Name.String,
			PlayerID:     props.Match.PlayerId1,
		}),
		matchpointscell.MatchPointsCell(matchpointscell.MatchPointsCellProps{
			Match:        props.Match,
			PlayerNumber: 1,
			Points:       props.Match.PlayerId1Points,
		}),
		matchplayercell.MatchPlayerCell(matchplayercell.MatchPlayerCellProps{
			Match:        props.Match,
			PlayerNumber: 2,
			PlayerName:   props.Match.Player2Name.String,
			PlayerID:     props.Match.PlayerId2,
		}),
		matchpointscell.MatchPointsCell(matchpointscell.MatchPointsCellProps{
			Match:        props.Match,
			PlayerNumber: 2,
			Points:       props.Match.PlayerId2Points,
		}),
		matchgroupcell.MatchGroupCell(matchgroupcell.MatchGroupCellProps{
			Match: props.Match,
		}),

		// Custom columns - each gets its own component instance
		gomponents.Group(
			gomponents.Map(props.CustomColumns, func(column db.MatchCustomColumn) gomponents.Node {
				return matchcustomcell.MatchCustomCell(matchcustomcell.MatchCustomCellProps{
					Match:        props.Match,
					Column:       column,
					CustomValues: props.CustomValues,
				})
			}),
		),
	)
}

// buildHiddenMetadataCell creates the hidden form inputs cell
func buildHiddenMetadataCell(match db.GetMatchesBySeasonIdRow) gomponents.Node {
	return html.Td(
		html.Class("hidden"),
		html.Input(
			html.Type("hidden"),
			html.Name("id"),
			html.Value(strconv.Itoa(int(match.ID))),
			gomponents.Attr("data-testid", fmt.Sprintf("match-id-%d", match.ID)),
		),
		html.Input(
			html.Type("hidden"),
			html.Name("seasonId"),
			html.Value(strconv.Itoa(int(match.SeasonID.Int32))),
		),
		// Only include player IDs if they are valid (not null)
		gomponents.If(match.PlayerId1.Valid,
			html.Input(
				html.Type("hidden"),
				html.Name("player_id1"),
				html.Value(strconv.Itoa(int(match.PlayerId1.Int32))),
			),
		),
		gomponents.If(match.PlayerId2.Valid,
			html.Input(
				html.Type("hidden"),
				html.Name("player_id2"),
				html.Value(strconv.Itoa(int(match.PlayerId2.Int32))),
			),
		),
		html.Input(
			html.Type("hidden"),
			html.Name("isActive"),
			html.Value(strconv.FormatBool(match.IsActive)),
		),
	)
}
