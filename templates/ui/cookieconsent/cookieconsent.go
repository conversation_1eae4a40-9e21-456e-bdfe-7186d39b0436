package cookieconsent

import (
	"github.com/j-em/coachpad/templates/ui/modal"
	. "maragu.dev/gomponents"
	. "maragu.dev/gomponents/html"
)

type CookieConsentConfig struct {
	ModalID    string
	Title      string
	Message    string
	AcceptText string
	RejectText string
}

// CookieConsentModal creates a modal for obtaining user consent for cookies
func CookieConsentModal(cfg CookieConsentConfig) Node {
	if cfg.ModalID == "" {
		cfg.ModalID = "cookieConsent"
	}
	if cfg.Title == "" {
		cfg.Title = "Cookie Consent"
	}
	if cfg.Message == "" {
		cfg.Message = "We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking 'Accept', you consent to our use of cookies."
	}
	if cfg.AcceptText == "" {
		cfg.AcceptText = "Accept"
	}
	if cfg.RejectText == "" {
		cfg.RejectText = "Reject"
	}

	content := Div(
		Class("p-4 space-y-4"),
		P(
			Text(cfg.Message),
			Class("text-gray-700 dark:text-gray-300"),
		),
		Div(
			Class("flex justify-end space-x-3 pt-4 border-t dark:border-gray-700"),
			Button(
				Attr("@click", cfg.ModalID+"_modalOpen = false; localStorage.setItem('cookieConsent', 'true')"),
				Text(cfg.AcceptText),
				Class("px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"),
			),
		),
	)

	return Div(
		Attr("x-data", "cookieConsent"),
		modal.Modal(modal.ModalConfig{
			ModalID:      cfg.ModalID,
			Title:        cfg.Title,
			Content:      content,
			IsUnclosable: true,
		}),
	)
}
