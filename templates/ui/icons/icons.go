package icons

import (
	"maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
	"maragu.dev/gomponents/html"
)

// Individual Type-Safe Icon Functions
// These functions provide a clean, typed API for all Heroicons used in the codebase

// Spinner returns a standard animated spinner icon (Tailwind-compatible)
func Spinner(attrs ...gomponents.Node) gomponents.Node {
	nodes := []gomponents.Node{
		gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
		gomponents.Attr("fill", "none"),
		gomponents.Attr("viewBox", "0 0 24 24"),
		gomponents.El("circle",
			gomponents.Attr("class", "opacity-25"),
			gomponents.Attr("cx", "12"),
			gomponents.Attr("cy", "12"),
			gomponents.Attr("r", "10"),
			gomponents.Attr("stroke", "currentColor"),
			gomponents.Attr("stroke-width", "4"),
		),
		gomponents.El("path",
			gomponents.Attr("class", "opacity-75"),
			gomponents.Attr("fill", "currentColor"),
			gomponents.Attr("d", "M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"),
		),
	}
	nodes = append(nodes, attrs...)
	return gomponents.El("svg", nodes...)
}

func ArrowLeft(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ArrowLeft(attrs...)
}

func ArrowRight(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ArrowRight(attrs...)
}

func Bars4(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Bars4(attrs...)
}

func Bell(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Bell(attrs...)
}

func Calendar(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Calendar(attrs...)
}

func ChatBubbleBottomCenterText(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ChatBubbleBottomCenterText(attrs...)
}

func Check(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Check(attrs...)
}

func CheckCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.CheckCircle(attrs...)
}

func ChevronDown(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ChevronDown(attrs...)
}

func Cog(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Cog(attrs...)
}

func CreditCard(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.CreditCard(attrs...)
}

func ArrowDownTray(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ArrowDownTray(attrs...)
}

func DocumentText(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.DocumentText(attrs...)
}

func EllipsisHorizontalCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.EllipsisHorizontalCircle(attrs...)
}

func EllipsisVertical(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.EllipsisVertical(attrs...)
}

func ExclamationCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ExclamationCircle(attrs...)
}

func Home(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Home(attrs...)
}

func InformationCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.InformationCircle(attrs...)
}

func Link(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Link(attrs...)
}

func Megaphone(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Megaphone(attrs...)
}

func PaperAirplane(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.PaperAirplane(attrs...)
}

func Pencil(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Pencil(attrs...)
}

func PencilSquare(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.PencilSquare(attrs...)
}

func Plus(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Plus(attrs...)
}

func PlusCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.PlusCircle(attrs...)
}

func Printer(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Printer(attrs...)
}

func ShieldExclamation(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.ShieldExclamation(attrs...)
}

func Trash(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Trash(attrs...)
}

func Trophy(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.Trophy(attrs...)
}

func User(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.User(attrs...)
}

func UserCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.UserCircle(attrs...)
}

func UserGroup(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.UserGroup(attrs...)
}

func WrenchScrewdriver(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.WrenchScrewdriver(attrs...)
}

func XCircle(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.XCircle(attrs...)
}

func XMark(attrs ...gomponents.Node) gomponents.Node {
	return heroicons.XMark(attrs...)
}

// Legacy Helper Functions (kept for backward compatibility)

// StandardIcon wraps an icon with default size and class
func StandardIcon(icon gomponents.Node, extraClasses ...string) gomponents.Node {
	classes := "h-5 w-5"
	if len(extraClasses) > 0 {
		classes += " " + extraClasses[0]
	}
	return html.Span(
		html.Class(classes),
		icon,
	)
}

// ErrorIcon returns a standard error icon
func ErrorIcon() gomponents.Node {
	return StandardIcon(ExclamationCircle(), "text-red-500")
}

// SuccessIcon returns a standard success icon
func SuccessIcon() gomponents.Node {
	return StandardIcon(CheckCircle(), "text-green-500")
}

// InfoIcon returns a standard info icon
func InfoIcon() gomponents.Node {
	return StandardIcon(InformationCircle(), "text-blue-500")
}

// WarningIcon returns a standard warning icon
func WarningIcon() gomponents.Node {
	return StandardIcon(ExclamationCircle(), "text-yellow-500")
}

// CrownIcon returns a crown icon to indicate winner
func CrownIcon() gomponents.Node {
	return html.Span(
		html.Class("h-4 w-4 text-yellow-500"),
		gomponents.El("svg",
			gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
			gomponents.Attr("fill", "currentColor"),
			gomponents.Attr("viewBox", "0 0 24 24"),
			gomponents.El("path",
				gomponents.Attr("d", "M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5z"),
			),
		),
	)
}
