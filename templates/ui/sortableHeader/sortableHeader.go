package sortableheader

import (
	"fmt"

	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// SortableHeaderProps contains props for a sortable table header
type SortableHeaderProps struct {
	Field        string
	Title        string
	CurrentSort  string
	Direction    string
	BaseURL      string
	Search       string
	FilterToday  bool
	ItemsPerPage int
}

// SortableHeader creates a sortable table header
func SortableHeader(props SortableHeaderProps) gomponents.Node {
	isCurrentSort := props.CurrentSort == props.Field
	direction := "asc"
	if isCurrentSort && props.Direction == "asc" {
		direction = "desc"
	}

	queryParams := fmt.Sprintf("?sort=%s&dir=%s", props.Field, direction)
	if props.Search != "" {
		queryParams += "&search=" + props.Search
	}
	if props.FilterToday {
		queryParams += "&filter_today=true"
	}
	queryParams += fmt.Sprintf("&per_page=%d", props.ItemsPerPage)

	sortIcon := ""
	if isCurrentSort {
		if props.Direction == "asc" {
			sortIcon = " ↑"
		} else {
			sortIcon = " ↓"
		}
	}

	return html.Th(
		gomponents.Attr("scope", "col"),
		html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"),
		html.Button(
			html.Class("hover:text-gray-700 dark:hover:text-gray-100"),
			htmx.Get(props.BaseURL+queryParams),
			htmx.Target("#matches-table-content"),
			htmx.PushURL("true"),
			gomponents.Text(props.Title+sortIcon),
		),
	)
}
