# Unified Table Search Bar Component

A standardized, configurable search bar component for all tables in the Coachpad application.

## Features

- **Standardized 500ms delay** for search input to improve performance
- **Unified HTMX patterns** for consistent behavior across all tables
- **Configurable filters** including checkboxes, selects, and date inputs
- **Consistent styling** with dark mode support
- **Localization support** with fallbacks
- **Backward compatibility** with existing test IDs
- **Extensible design** for future filter types

## Basic Usage

```go
import tablesearchbar "github.com/j-em/coachpad/templates/ui/tableSearchBar"

// Simple search bar with just text input
searchBar := tablesearchbar.TableSearchBar(tablesearchbar.TableSearchBarProps{
    SearchValue:      params.Search,
    SearchDataTestID: "my-search-bar",
    BaseURL:          "/app/my-table",
    TargetID:         "my-table-content",
    HiddenInputs: map[string]string{
        "page":     strconv.Itoa(params.Page),
        "per_page": strconv.Itoa(params.ItemsPerPage),
        "sort":     params.Sort,
        "dir":      params.Direction,
    },
    Lang: lang,
})
```

## Configuration Options

### Core Properties

- `SearchValue`: Current search term
- `SearchDataTestID`: Test ID for the search input (for E2E testing)
- `BaseURL`: HTMX endpoint URL
- `TargetID`: Target element ID for HTMX updates
- `Lang`: Language code for localization

### Optional Properties

- `SearchPlaceholder`: Custom placeholder text (defaults to localized "Search...")
- `SearchName`: Input name attribute (defaults to "search")
- `ShowSavingIndicator`: Shows spinning indicator during HTMX requests
- `LocalesPath`: Custom path to locales file
- `TriggerOverride`: Custom HTMX trigger pattern
- `HtmxInclude`: Custom HTMX include pattern

### Filter Configuration

Add filters using the `Filters` array:

```go
Filters: []tablesearchbar.FilterOption{
    // Checkbox filter
    {
        Type:       tablesearchbar.FilterTypeCheckbox,
        Name:       "filter_active",
        Label:      "Show Active Only",
        Value:      "true",
        Checked:    isActiveFilterOn,
        DataTestID: "active-filter",
    },
    // Select dropdown filter
    {
        Type:  tablesearchbar.FilterTypeSelect,
        Name:  "category",
        Label: "Category",
        Value: selectedCategory,
        Options: []tablesearchbar.SelectOption{
            {Value: "", Label: "All Categories"},
            {Value: "type1", Label: "Type 1"},
            {Value: "type2", Label: "Type 2"},
        },
        DataTestID: "category-filter",
    },
    // Date filter
    {
        Type:       tablesearchbar.FilterTypeDate,
        Name:       "start_date",
        Label:      "Start Date",
        Value:      startDate,
        DataTestID: "start-date-filter",
    },
},
```

## Localization

The component supports localization through JSON files. Add these entries to your locales file:

```json
{
  "en": {
    "search_placeholder": "Search...",
    "saving": "Saving...",
    "show_todays_matches": "Show Today's Matches"
  },
  "fr": {
    "search_placeholder": "Rechercher...",
    "saving": "Enregistrement...",
    "show_todays_matches": "Afficher les matchs d'aujourd'hui"
  }
}
```

## Migration Guide

To migrate existing search bars:

1. **Update imports**:
   ```go
   import tablesearchbar "github.com/j-em/coachpad/templates/ui/tableSearchBar"
   ```

2. **Replace search bar function**:
   ```go
   // OLD
   func MySearchBar(params MyTableParams, lang string) gomponents.Node {
       // ... old implementation
   }
   
   // NEW
   func MySearchBar(params MyTableParams, lang string) gomponents.Node {
       return tablesearchbar.TableSearchBar(tablesearchbar.TableSearchBarProps{
           SearchValue:      params.Search,
           SearchDataTestID: "my-search-bar", // Keep same for E2E tests
           BaseURL:          "/app/my-table",
           TargetID:         "my-table-content",
           HiddenInputs: map[string]string{
               "page":     strconv.Itoa(params.Page),
               "per_page": strconv.Itoa(params.ItemsPerPage),
               "sort":     params.Sort,
               "dir":      params.Direction,
           },
           Lang: lang,
       })
   }
   ```

3. **Add required locales** to your existing locales file

4. **Test** that existing functionality still works

## Current Migration Status

- ✅ **Teams table**: Migrated to use unified component
- ✅ **Players table**: Migrated with saving indicator
- ✅ **Matches table**: Migrated with "today" checkbox filter
- 🔄 **Old search bar files**: Kept for backward compatibility, will be removed in future

## Default Behavior

- **Search delay**: 500ms after user stops typing
- **Filter triggers**: Immediate on change (checkbox, select, date)
- **HTMX sync**: `this:replace` to prevent duplicate requests
- **Include pattern**: `closest form` to capture all form inputs
- **Target pattern**: Replaces entire table content

## Extending the Component

To add new filter types:

1. Add new `FilterType` constant
2. Update `buildDefaultTrigger()` function
3. Add new render function (e.g., `renderRangeFilter()`)
4. Update `renderFilters()` switch statement

## Testing

All existing E2E tests should continue to work as the component preserves existing `data-testid` attributes:

- `teams-search-bar`
- `players-search-bar` 
- `match-search-input`

## Benefits

1. **Consistency**: All tables have identical search behavior
2. **Maintainability**: Single source of truth for search functionality
3. **Performance**: Standardized 500ms delay and optimized HTMX patterns
4. **Accessibility**: Consistent keyboard navigation and screen reader support
5. **Localization**: Unified translation system
6. **Testing**: Standardized test patterns
7. **Future-proof**: Easy to extend with new filter types
