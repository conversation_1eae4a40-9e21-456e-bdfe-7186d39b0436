package tablesearchbar

import (
	"fmt"
	"strings"

	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// FilterType represents different types of filters
type FilterType string

const (
	FilterTypeCheckbox FilterType = "checkbox"
	FilterTypeSelect   FilterType = "select"
	FilterTypeDate     FilterType = "date"
)

// FilterOption represents a filter configuration
type FilterOption struct {
	Type       FilterType
	Name       string
	Label      string
	Value      string
	Checked    bool
	Options    []SelectOption // For select filters
	DataTestID string
}

// SelectOption represents an option in a select filter
type SelectOption struct {
	Value string
	Label string
}

// TableSearchBarProps contains props for the unified search bar
type TableSearchBarProps struct {
	// Search configuration
	SearchValue       string
	SearchPlaceholder string
	SearchDataTestID  string
	SearchName        string // defaults to "search"

	// HTMX configuration
	BaseURL         string
	TargetID        string
	HtmxInclude     string // Standard include pattern for hidden inputs
	TriggerOverride string // Optional override for trigger pattern

	// Filter configuration
	Filters []FilterOption

	// Hidden inputs to preserve state
	HiddenInputs map[string]string

	// Display configuration
	ShowSavingIndicator bool
	Lang                string

	// Localization override
	LocalesPath string // Optional custom locales path
}

// TableSearchBar renders a unified search bar with configurable filters
func TableSearchBar(props TableSearchBarProps) gomponents.Node {
	// Load locales
	localesPath := props.LocalesPath
	if localesPath == "" {
		localesPath = "./templates/ui/tableSearchBar/tableSearchBar.locales.json"
	}

	locales, err := i18n.LoadTemplateLocales(localesPath, props.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Default values
	searchName := props.SearchName
	if searchName == "" {
		searchName = "search"
	}

	searchPlaceholder := props.SearchPlaceholder
	if searchPlaceholder == "" {
		searchPlaceholder = locales["search_placeholder"]
	}

	trigger := props.TriggerOverride
	if trigger == "" {
		trigger = buildDefaultTrigger(props.Filters)
	}

	return html.Form(
		html.Class("mb-4 flex flex-wrap justify-between items-center gap-4"),

		// Centralized HTMX attributes
		htmx.Get(props.BaseURL),
		htmx.Target(fmt.Sprintf("#%s", props.TargetID)),
		htmx.Trigger(trigger),
		htmx.Include("closest form"),
		htmx.Sync("this:replace"),

		// Saving indicator (if enabled)
		gomponents.If(props.ShowSavingIndicator, renderSavingIndicator(locales)),

		// Filters section
		gomponents.If(len(props.Filters) > 0, renderFilters(props.Filters, locales)),

		// Search input
		renderSearchInput(props, locales, searchName, searchPlaceholder),

		// Hidden inputs
		renderHiddenInputs(props.HiddenInputs),
	)
}

// buildDefaultTrigger creates the default trigger pattern based on filters
func buildDefaultTrigger(filters []FilterOption) string {
	var triggers []string

	// Add search input trigger
	triggers = append(triggers, "input changed delay:500ms from:input[name='search']")

	// Add filter triggers
	for _, filter := range filters {
		switch filter.Type {
		case FilterTypeCheckbox:
			triggers = append(triggers, fmt.Sprintf("change from:input[name='%s']", filter.Name))
		case FilterTypeSelect:
			triggers = append(triggers, fmt.Sprintf("change from:select[name='%s']", filter.Name))
		case FilterTypeDate:
			triggers = append(triggers, fmt.Sprintf("change from:input[name='%s']", filter.Name))
		}
	}

	return strings.Join(triggers, ", ")
}

// renderSavingIndicator renders the global saving indicator
func renderSavingIndicator(locales map[string]string) gomponents.Node {
	return html.Div(
		html.Class("mb-4 text-sm text-gray-600 dark:text-gray-300 items-center htmx-indicator"),
		html.ID("global-saving-indicator"),
		gomponents.Attr("data-testid", "global-saving-indicator"),
		gomponents.El("svg",
			html.Class("animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500 dark:text-blue-400"),
			gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
			gomponents.Attr("fill", "none"),
			gomponents.Attr("viewBox", "0 0 24 24"),
			gomponents.El("circle",
				html.Class("opacity-25"),
				gomponents.Attr("cx", "12"),
				gomponents.Attr("cy", "12"),
				gomponents.Attr("r", "10"),
				gomponents.Attr("stroke", "currentColor"),
				gomponents.Attr("stroke-width", "4"),
			),
		),
		gomponents.Text(locales["saving"]),
	)
}

// renderFilters renders all configured filters
func renderFilters(filters []FilterOption, locales map[string]string) gomponents.Node {
	var filterNodes []gomponents.Node

	for _, filter := range filters {
		switch filter.Type {
		case FilterTypeCheckbox:
			filterNodes = append(filterNodes, renderCheckboxFilter(filter, locales))
		case FilterTypeSelect:
			filterNodes = append(filterNodes, renderSelectFilter(filter, locales))
		case FilterTypeDate:
			filterNodes = append(filterNodes, renderDateFilter(filter, locales))
		}
	}

	return html.Div(
		html.Class("flex flex-wrap items-center gap-4"),
		gomponents.Group(filterNodes),
	)
}

// renderCheckboxFilter renders a checkbox filter
func renderCheckboxFilter(filter FilterOption, locales map[string]string) gomponents.Node {
	label := filter.Label
	if label == "" && filter.Name == "filter_today" {
		label = locales["show_todays_matches"]
	}

	return html.Div(
		html.Class("flex items-center"),
		html.Label(
			html.Class("inline-flex items-center"),
			html.Input(
				html.Type("checkbox"),
				html.Class("form-checkbox h-5 w-5 text-gray-600 dark:text-gray-300 rounded mr-2"),
				html.Name(filter.Name),
				html.Value(filter.Value),
				gomponents.If(filter.Checked, html.Checked()),
				gomponents.If(filter.DataTestID != "", gomponents.Attr("data-testid", filter.DataTestID)),
			),
			html.Span(
				html.Class("ml-2 text-gray-700 dark:text-gray-200"),
				gomponents.Text(label),
			),
		),
	)
}

// renderSelectFilter renders a select filter
func renderSelectFilter(filter FilterOption, locales map[string]string) gomponents.Node {
	var options []gomponents.Node

	for _, option := range filter.Options {
		options = append(options, html.Option(
			html.Value(option.Value),
			gomponents.If(option.Value == filter.Value, html.Selected()),
			gomponents.Text(option.Label),
		))
	}

	return html.Div(
		html.Class("flex flex-col"),
		gomponents.If(filter.Label != "", html.Label(
			html.Class("block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300"),
			gomponents.Text(filter.Label),
		)),
		html.Select(
			html.Class("border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"),
			html.Name(filter.Name),
			gomponents.If(filter.DataTestID != "", gomponents.Attr("data-testid", filter.DataTestID)),
			gomponents.Group(options),
		),
	)
}

// renderDateFilter renders a date input filter
func renderDateFilter(filter FilterOption, locales map[string]string) gomponents.Node {
	return html.Div(
		html.Class("flex flex-col"),
		gomponents.If(filter.Label != "", html.Label(
			html.Class("block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300"),
			gomponents.Text(filter.Label),
		)),
		html.Input(
			html.Type("date"),
			html.Class("border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"),
			html.Name(filter.Name),
			html.Value(filter.Value),
			gomponents.If(filter.DataTestID != "", gomponents.Attr("data-testid", filter.DataTestID)),
		),
	)
}

// renderSearchInput renders the search input with icon
func renderSearchInput(props TableSearchBarProps, locales map[string]string, searchName, searchPlaceholder string) gomponents.Node {
	return html.Div(
		html.Class("relative"),
		html.Input(
			html.Type("text"),
			html.Class("border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-md px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"),
			html.Value(props.SearchValue),
			gomponents.Attr("placeholder", searchPlaceholder),
			gomponents.If(props.SearchDataTestID != "", gomponents.Attr("data-testid", props.SearchDataTestID)),
			html.Name(searchName),
		),
		html.Div(
			html.Class("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"),
			gomponents.El("svg",
				html.Class("h-5 w-5"),
				gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
				gomponents.Attr("fill", "none"),
				gomponents.Attr("viewBox", "0 0 24 24"),
				gomponents.Attr("stroke", "currentColor"),
				gomponents.El("path",
					gomponents.Attr("stroke-linecap", "round"),
					gomponents.Attr("stroke-linejoin", "round"),
					gomponents.Attr("stroke-width", "2"),
					gomponents.Attr("d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"),
				),
			),
		),
	)
}

// renderHiddenInputs renders all hidden inputs to preserve state
func renderHiddenInputs(hiddenInputs map[string]string) gomponents.Node {
	var inputs []gomponents.Node

	for name, value := range hiddenInputs {
		inputs = append(inputs, html.Input(
			html.Type("hidden"),
			html.Name(name),
			html.Value(value),
		))
	}

	return gomponents.Group(inputs)
}
