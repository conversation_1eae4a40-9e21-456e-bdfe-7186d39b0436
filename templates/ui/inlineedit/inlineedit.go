package inlineedit

import (
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type InlineEditProps struct {
	ID          string
	Value       string
	EndpointID  string
	EndpointURL string
	DataTestID  string
	Format      string // optional format for display (e.g., "2006-01-02" for dates)
	Type        string // "text", "date", "number", etc.
}

func InlineEdit(props InlineEditProps) gomponents.Node {
	displayValue := props.Value
	if props.Format != "" && props.Type == "date" {
		// Value is already formatted from the handler
		displayValue = props.Value
	}

	return html.Span(
		html.Class("inline-edit cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded transition-colors"),
		gomponents.Attr("data-id", props.EndpointID),
		gomponents.Attr("hx-put", props.EndpointURL),
		gomponents.Attr("hx-trigger", "blur changed delay:500ms"),
		gomponents.Attr("hx-target", "this"),
		gomponents.Attr("hx-swap", "outerHTML"),
		gomponents.Attr("contenteditable", "true"),
		gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),
		gomponents.Text(displayValue),
	)
}
