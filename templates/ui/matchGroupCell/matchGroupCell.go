package matchgroupcell

import (
	"strconv"

	"github.com/j-em/coachpad/db"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// MatchGroupCellProps contains props for the match group cell
type MatchGroupCellProps struct {
	Match db.GetMatchesBySeasonIdRow
}

// MatchGroupCell creates the match group input cell
func MatchGroupCell(props MatchGroupCellProps) gomponents.Node {
	return html.Td(
		html.Class("px-6 py-4"),
		html.Input(
			html.Type("number"),
			html.Class("text-sm border-b border-transparent hover:border-gray-300 dark:hover:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none w-full px-2 py-1 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"),
			html.Value(strconv.Itoa(int(props.Match.MatchGroup))),
			html.Name("matchGroup"),
		),
	)
}
