package infobubble

import (
	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
	"maragu.dev/gomponents/html"
)

type InfoBubbleProps struct {
	Text       string
	Title      string
	ID         string
	Class      string
	Lang       string
	LocaleKey  string // For when using locale files
	TitleKey   string // For title localization
	DataTestID string
}

func InfoBubble(props InfoBubbleProps) gomponents.Node {
	text := props.Text
	title := props.Title

	// If LocaleKey is provided, load from locales
	if props.LocaleKey != "" || props.TitleKey != "" {
		locales, err := i18n.LoadTemplateLocales("./templates/ui/infobubble/infobubble.locales.json", props.Lang)
		if err == nil {
			if props.LocaleKey != "" {
				if localizedText, exists := locales[props.LocaleKey]; exists {
					text = localizedText
				}
			}
			if props.TitleKey != "" {
				if localizedTitle, exists := locales[props.TitleKey]; exists {
					title = localizedTitle
				}
			}
		}
	}

	return html.Div(
		html.Class("flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md "+props.Class),
		gomponents.If(props.ID != "", html.ID(props.ID)),
		gomponents.If(props.DataTestID != "", gomponents.Attr("data-testid", props.DataTestID)),

		// Info icon
		html.Div(
			html.Class("flex-shrink-0 mt-0.5"),
			heroicons.InformationCircle(
				html.Class("w-4 h-4 text-blue-500"),
			),
		),

		// Text content
		html.Div(
			html.Class("flex-1"),
			gomponents.If(title != "",
				html.H4(
					html.Class("text-sm font-medium text-gray-900 dark:text-gray-100 mb-1"),
					gomponents.Text(title),
				),
			),
			html.P(
				html.Class("text-sm text-gray-600 dark:text-gray-400 leading-relaxed"),
				gomponents.Text(text),
			),
		),
	)
}
