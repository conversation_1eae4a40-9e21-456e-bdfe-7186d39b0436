package matchDisplay

import (
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/utils/matchutils"
	"github.com/jackc/pgx/v5/pgtype"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// MatchDisplayProps contains props for match display components (basic matches without names)
type MatchDisplayProps struct {
	Matches []db.Match
	Locales map[string]string
	Format  MatchDisplayFormat
}

// MatchDisplayWithNamesProps contains props for match display with player names
type MatchDisplayWithNamesProps struct {
	Matches []db.GetMatchesBySeasonIdRow
	Locales map[string]string
	Format  MatchDisplayFormat
}

// MatchDisplayFormat specifies how matches should be displayed
type MatchDisplayFormat struct {
	ShowDate     bool
	ShowPlayers  bool
	ShowGroup    bool
	ShowScore    bool
	DateFormat   matchutils.DateFormat
	TableClasses string
	RowClasses   string
	CellClasses  string
}

// DefaultTableFormat provides a standard table format for matches
var DefaultTableFormat = MatchDisplayFormat{
	ShowDate:     true,
	ShowPlayers:  true,
	ShowGroup:    true,
	ShowScore:    false,
	DateFormat:   matchutils.DateFormatYMD,
	TableClasses: "min-w-full",
	RowClasses:   "border-t",
	CellClasses:  "px-4 py-2",
}

// CompactTableFormat provides a compact table format for matches
var CompactTableFormat = MatchDisplayFormat{
	ShowDate:     true,
	ShowPlayers:  true,
	ShowGroup:    false,
	ShowScore:    false,
	DateFormat:   matchutils.DateFormatYMD,
	TableClasses: "min-w-full text-sm",
	RowClasses:   "border-t",
	CellClasses:  "px-2 py-1",
}

// MatchTable renders a table of basic matches (without player names)
func MatchTable(props MatchDisplayProps) gomponents.Node {
	if props.Matches == nil || len(props.Matches) == 0 {
		return html.Div(
			html.Class("p-6 text-center text-gray-500"),
			gomponents.Text(props.Locales["no_upcoming_matches"]),
		)
	}

	// Build table rows
	rows := []gomponents.Node{}

	// Table header
	headerRow := buildTableHeader(props.Format, props.Locales)
	rows = append(rows, html.Tr(
		html.Class("bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
		gomponents.Group(headerRow),
	))

	// Data rows
	for _, match := range props.Matches {
		rows = append(rows, MatchTableRowBasic(match, props.Locales, props.Format))
	}

	return html.Table(
		html.Class(props.Format.TableClasses),
		html.TBody(
			gomponents.Group(rows),
		),
	)
}

// MatchTableWithNames renders a table of matches with player names
func MatchTableWithNames(props MatchDisplayWithNamesProps) gomponents.Node {
	if props.Matches == nil || len(props.Matches) == 0 {
		return html.Div(
			html.Class("p-6 text-center text-gray-500"),
			gomponents.Text(props.Locales["no_upcoming_matches"]),
		)
	}

	// Build table rows
	rows := []gomponents.Node{}

	// Table header
	headerRow := buildTableHeader(props.Format, props.Locales)
	rows = append(rows, html.Tr(
		html.Class("bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"),
		gomponents.Group(headerRow),
	))

	// Data rows
	for _, match := range props.Matches {
		rows = append(rows, MatchTableRowWithNames(match, props.Locales, props.Format))
	}

	return html.Table(
		html.Class(props.Format.TableClasses),
		html.TBody(
			gomponents.Group(rows),
		),
	)
}

// buildTableHeader builds the table header row
func buildTableHeader(format MatchDisplayFormat, locales map[string]string) []gomponents.Node {
	headerRow := []gomponents.Node{}

	if format.ShowDate {
		headerRow = append(headerRow, html.Th(
			html.Class(format.CellClasses+" text-left"),
			gomponents.Text(locales["date"]),
		))
	}
	if format.ShowPlayers {
		headerRow = append(headerRow, html.Th(
			html.Class(format.CellClasses+" text-left"),
			gomponents.Text(locales["match"]),
		))
	}
	if format.ShowScore {
		headerRow = append(headerRow, html.Th(
			html.Class(format.CellClasses+" text-center"),
			gomponents.Text(locales["score"]),
		))
	}
	if format.ShowGroup {
		headerRow = append(headerRow, html.Th(
			html.Class(format.CellClasses+" text-left"),
			gomponents.Text(locales["group"]),
		))
	}

	return headerRow
}

// MatchTableRowBasic renders a single match row (for basic matches without names)
func MatchTableRowBasic(match db.Match, locales map[string]string, format MatchDisplayFormat) gomponents.Node {
	cells := []gomponents.Node{}

	if format.ShowDate {
		dateText := matchutils.FormatMatchDateWithFallback(match.MatchDate, format.DateFormat, locales["na"])
		cells = append(cells, html.Td(
			html.Class(format.CellClasses),
			gomponents.Text(dateText),
		))
	}

	if format.ShowPlayers {
		// For basic matches, we only have player IDs, no names
		player1ID := matchutils.FormatPlayerName(pgtype.Text{}, match.PlayerId1, locales)
		player2ID := matchutils.FormatPlayerName(pgtype.Text{}, match.PlayerId2, locales)

		matchText := ""
		if matchFormat, ok := locales["match_format"]; ok {
			matchText = fmt.Sprintf(matchFormat, player1ID, player2ID)
		} else {
			matchText = player1ID + " vs " + player2ID
		}

		cells = append(cells, html.Td(
			html.Class(format.CellClasses),
			gomponents.Text(matchText),
		))
	}

	if format.ShowScore {
		scoreText := matchutils.FormatScoreWithFallback(match.PlayerId1Points, match.PlayerId2Points, locales["na"])
		cells = append(cells, html.Td(
			html.Class(format.CellClasses+" text-center"),
			gomponents.Text(scoreText),
		))
	}

	if format.ShowGroup {
		groupText := matchutils.FormatMatchGroup(match.MatchGroup)
		cells = append(cells, html.Td(
			html.Class(format.CellClasses),
			gomponents.Text(groupText),
		))
	}

	return html.Tr(
		html.Class(format.RowClasses),
		gomponents.Group(cells),
	)
}

// MatchTableRowWithNames renders a single match row (for matches with player names)
func MatchTableRowWithNames(match db.GetMatchesBySeasonIdRow, locales map[string]string, format MatchDisplayFormat) gomponents.Node {
	cells := []gomponents.Node{}

	if format.ShowDate {
		dateText := matchutils.FormatMatchDateWithFallback(match.MatchDate, format.DateFormat, locales["na"])
		cells = append(cells, html.Td(
			html.Class(format.CellClasses),
			gomponents.Text(dateText),
		))
	}

	if format.ShowPlayers {
		player1Name := matchutils.FormatPlayerName(match.Player1Name, match.PlayerId1, locales)
		player2Name := matchutils.FormatPlayerName(match.Player2Name, match.PlayerId2, locales)

		matchText := ""
		if matchFormat, ok := locales["match_format"]; ok {
			matchText = fmt.Sprintf(matchFormat, player1Name, player2Name)
		} else {
			matchText = player1Name + " vs " + player2Name
		}

		cells = append(cells, html.Td(
			html.Class(format.CellClasses),
			gomponents.Text(matchText),
		))
	}

	if format.ShowScore {
		scoreText := matchutils.FormatScoreWithFallback(match.PlayerId1Points, match.PlayerId2Points, locales["na"])
		cells = append(cells, html.Td(
			html.Class(format.CellClasses+" text-center"),
			gomponents.Text(scoreText),
		))
	}

	if format.ShowGroup {
		groupText := matchutils.FormatMatchGroup(match.MatchGroup)
		cells = append(cells, html.Td(
			html.Class(format.CellClasses),
			gomponents.Text(groupText),
		))
	}

	return html.Tr(
		html.Class(format.RowClasses),
		gomponents.Group(cells),
	)
}
