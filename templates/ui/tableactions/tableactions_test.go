package tableactions

import (
	"strings"
	"testing"
)

func TestSingleActionButton_Delete(t *testing.T) {
	config := SingleActionConfig{
		ID:         "test-delete-btn",
		DataTestID: "test-delete-button",
		Action:     ActionDelete,
		EntityID:   123,
		EntityName: "Test Entity",
		HxEndpoint: "/api/test/123",
		HxTarget:   "#test-row-123",
		HxSwap:     "outerHTML",
		Size:       SizeDefault,
		Lang:       "en",
	}

	result := SingleActionButton(config)

	// Convert to string to test the output
	var builder strings.Builder
	err := result.Render(&builder)
	if err != nil {
		t.Fatalf("Failed to render button: %v", err)
	}

	output := builder.String()

	// Test basic structure
	if !strings.Contains(output, `id="test-delete-btn"`) {
		t.Error("Expected button to have correct ID")
	}

	if !strings.Contains(output, `data-testid="test-delete-button"`) {
		t.Error("Expected button to have data-testid attribute")
	}

	if !strings.Contains(output, `hx-delete="/api/test/123"`) {
		t.<PERSON>rror("Expected button to have hx-delete attribute")
	}

	if !strings.Contains(output, `hx-target="#test-row-123"`) {
		t.Error("Expected button to have hx-target attribute")
	}

	if !strings.Contains(output, `hx-swap="outerHTML"`) {
		t.Error("Expected button to have hx-swap attribute")
	}

	// Test delete styling
	if !strings.Contains(output, "text-red-600") {
		t.Error("Expected delete button to have red styling")
	}
}

func TestSingleActionButton_Edit(t *testing.T) {
	config := SingleActionConfig{
		Action:     ActionEdit,
		EntityID:   456,
		HxEndpoint: "/api/test/456/edit",
		Size:       SizeSmall,
		Lang:       "en",
	}

	result := SingleActionButton(config)

	var builder strings.Builder
	err := result.Render(&builder)
	if err != nil {
		t.Fatalf("Failed to render button: %v", err)
	}

	output := builder.String()

	// Test edit uses GET by default
	if !strings.Contains(output, `hx-get="/api/test/456/edit"`) {
		t.Error("Expected edit button to use hx-get")
	}

	// Test edit styling
	if !strings.Contains(output, "text-blue-600") {
		t.Error("Expected edit button to have blue styling")
	}

	// Test small size
	if !strings.Contains(output, "p-1") {
		t.Error("Expected small button to have p-1 padding")
	}
}

func TestMultiActionDropdown(t *testing.T) {
	config := MultiActionConfig{
		ID:         "test-actions",
		DataTestID: "test-actions-dropdown",
		EntityID:   789,
		EntityName: "Test Item",
		Size:       SizeDefault,
		Lang:       "en",
		Actions: []ActionItem{
			{
				Type:       ActionEdit,
				HxEndpoint: "/api/test/789/edit",
				HxTarget:   "#edit-form",
				DataTestID: "test-edit-789",
			},
			{
				Type:       ActionDelete,
				HxEndpoint: "/api/test/789",
				HxTarget:   "#test-row-789",
				HxSwap:     "outerHTML",
				DataTestID: "test-delete-789",
			},
		},
	}

	result := MultiActionDropdown(config)

	var builder strings.Builder
	err := result.Render(&builder)
	if err != nil {
		t.Fatalf("Failed to render dropdown: %v", err)
	}

	output := builder.String()

	// Test dropdown trigger
	if !strings.Contains(output, `id="test-actions"`) {
		t.Error("Expected dropdown to have correct ID")
	}

	if !strings.Contains(output, `data-testid="test-actions-dropdown"`) {
		t.Error("Expected dropdown trigger to have data-testid")
	}

	// Test Alpine.js attributes
	if !strings.Contains(output, `x-data="{ open: false }"`) {
		t.Error("Expected dropdown to have Alpine.js data")
	}

	if !strings.Contains(output, `@click="open = !open"`) {
		t.Error("Expected dropdown trigger to toggle open state")
	}

	// Test action items
	if !strings.Contains(output, `data-testid="test-edit-789"`) {
		t.Error("Expected edit action to have correct data-testid")
	}

	if !strings.Contains(output, `data-testid="test-delete-789"`) {
		t.Error("Expected delete action to have correct data-testid")
	}

	if !strings.Contains(output, `hx-get="/api/test/789/edit"`) {
		t.Error("Expected edit action to use hx-get")
	}

	if !strings.Contains(output, `hx-delete="/api/test/789"`) {
		t.Error("Expected delete action to use hx-delete")
	}
}

func TestGetActionStyling(t *testing.T) {
	tests := []struct {
		action   ActionType
		expected string
	}{
		{ActionDelete, "text-red-600"},
		{ActionEdit, "text-blue-600"},
		{ActionView, "text-gray-600"},
		{ActionClone, "text-green-600"},
		{ActionExport, "text-purple-600"},
		{ActionToggle, "text-yellow-600"},
		{ActionShare, "text-indigo-600"},
	}

	for _, tt := range tests {
		t.Run(string(tt.action), func(t *testing.T) {
			result := getActionStyling(tt.action)
			if !strings.Contains(result, tt.expected) {
				t.Errorf("Expected styling for %s to contain %s, got %s", tt.action, tt.expected, result)
			}
		})
	}
}

func TestGetSizeClasses(t *testing.T) {
	// Test small size
	buttonClass, iconClass := getSizeClasses(SizeSmall)
	if buttonClass != "p-1" {
		t.Errorf("Expected small button class to be 'p-1', got '%s'", buttonClass)
	}
	if iconClass != "w-3 h-3" {
		t.Errorf("Expected small icon class to be 'w-3 h-3', got '%s'", iconClass)
	}

	// Test default size
	buttonClass, iconClass = getSizeClasses(SizeDefault)
	if buttonClass != "p-2" {
		t.Errorf("Expected default button class to be 'p-2', got '%s'", buttonClass)
	}
	if iconClass != "w-4 h-4" {
		t.Errorf("Expected default icon class to be 'w-4 h-4', got '%s'", iconClass)
	}
}

func TestGetDefaultConfirmMessage(t *testing.T) {
	// Test with delete action
	msg := getDefaultConfirmMessage(ActionDelete, "Test Item", "en")
	expected := "Are you sure you want to delete Test Item? This action cannot be undone."
	if msg != expected {
		t.Errorf("Expected confirm message '%s', got '%s'", expected, msg)
	}

	// Test with non-delete action
	msg = getDefaultConfirmMessage(ActionEdit, "Test Item", "en")
	if msg != "" {
		t.Errorf("Expected empty confirm message for non-delete action, got '%s'", msg)
	}
}

func TestSingleActionButton_DisabledState(t *testing.T) {
	config := SingleActionConfig{
		Action:   ActionDelete,
		EntityID: 123,
		Size:     SizeDefault,
		Lang:     "en",
		Disabled: true,
	}

	result := SingleActionButton(config)

	var builder strings.Builder
	err := result.Render(&builder)
	if err != nil {
		t.Fatalf("Failed to render button: %v", err)
	}

	output := builder.String()

	if !strings.Contains(output, `disabled="true"`) {
		t.Error("Expected disabled button to have disabled attribute")
	}

	if !strings.Contains(output, "opacity-50") {
		t.Error("Expected disabled button to have opacity-50 class")
	}

	if !strings.Contains(output, "cursor-not-allowed") {
		t.Error("Expected disabled button to have cursor-not-allowed class")
	}
}

func TestMultiActionDropdown_WithHref(t *testing.T) {
	config := MultiActionConfig{
		EntityID:   123,
		EntityName: "Test Item",
		Size:       SizeDefault,
		Lang:       "en",
		Actions: []ActionItem{
			{
				Type:       ActionView,
				Href:       "/test/123",
				DataTestID: "test-view-123",
			},
		},
	}

	result := MultiActionDropdown(config)

	var builder strings.Builder
	err := result.Render(&builder)
	if err != nil {
		t.Fatalf("Failed to render dropdown: %v", err)
	}

	output := builder.String()

	// Test that href creates an anchor tag
	if !strings.Contains(output, `href="/test/123"`) {
		t.Error("Expected action item with href to create anchor tag")
	}

	if !strings.Contains(output, `data-testid="test-view-123"`) {
		t.Error("Expected action item to have correct data-testid")
	}
}
