package tableactions

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

// ActionType defines the type of action for consistent styling and behavior
type ActionType string

const (
	ActionDelete ActionType = "delete"
	ActionEdit   ActionType = "edit"
	ActionView   ActionType = "view"
	ActionClone  ActionType = "clone"
	ActionExport ActionType = "export"
	ActionToggle ActionType = "toggle"
	ActionShare  ActionType = "share"
)

// Size variants for different table densities
type Size string

const (
	SizeSmall   Size = "small"
	SizeDefault Size = "default"
)

// SingleActionConfig configures a single action button
type SingleActionConfig struct {
	ID         string
	DataTestID string
	Action     ActionType
	EntityID   int32
	EntityName string
	HxEndpoint string
	HxTarget   string
	HxSwap     string
	HxConfirm  string
	Size       Size
	Lang       string
	Disabled   bool
	ExtraAttrs []gomponents.Node
}

// ActionItem represents an individual action in a dropdown
type ActionItem struct {
	Type       ActionType
	Text       string
	Icon       gomponents.Node
	HxEndpoint string
	HxTarget   string
	HxSwap     string
	HxConfirm  string
	DataTestID string
	Disabled   bool
	Href       string
	OnClick    string
	ExtraAttrs []gomponents.Node
}

// MultiActionConfig configures a dropdown with multiple actions
type MultiActionConfig struct {
	ID         string
	DataTestID string
	EntityID   int32
	EntityName string
	Actions    []ActionItem
	Size       Size
	Lang       string
	Position   string // "bottom-end", "bottom-start", etc.
	ExtraAttrs []gomponents.Node
}

// getActionStyling returns the CSS classes for each action type
func getActionStyling(action ActionType) string {
	switch action {
	case ActionDelete:
		return "text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-600"
	case ActionEdit:
		return "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-600"
	case ActionView:
		return "text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-600"
	case ActionClone:
		return "text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-600"
	case ActionExport:
		return "text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-600"
	case ActionToggle:
		return "text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-600"
	case ActionShare:
		return "text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-600"
	default:
		return "text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-600"
	}
}

// getActionIcon returns the default icon for each action type
func getActionIcon(action ActionType) gomponents.Node {
	switch action {
	case ActionDelete:
		return icons.Trash()
	case ActionEdit:
		return icons.PencilSquare()
	case ActionView:
		return icons.InformationCircle() // Using InformationCircle as substitute for Eye
	case ActionClone:
		return icons.DocumentText() // Using DocumentText as substitute for DocumentDuplicate
	case ActionExport:
		return icons.ArrowDownTray()
	case ActionToggle:
		return icons.ArrowLeft() // Using ArrowLeft as substitute for ArrowPath
	case ActionShare:
		return icons.PaperAirplane() // Using PaperAirplane as substitute for Share
	default:
		return icons.EllipsisVertical()
	}
}

// getSizeClasses returns size-specific CSS classes
func getSizeClasses(size Size) (buttonClass, iconClass string) {
	switch size {
	case SizeSmall:
		return "p-1", "w-3 h-3"
	default:
		return "p-2", "w-4 h-4"
	}
}

// getDefaultConfirmMessage returns a localized confirmation message
func getDefaultConfirmMessage(action ActionType, entityName, lang string) string {
	locales, err := i18n.LoadTemplateLocales("./templates/ui/tableactions/tableactions.locales.json", lang)
	if err != nil {
		locales = map[string]string{}
	}

	switch action {
	case ActionDelete:
		if confirmMsg, exists := locales["confirm_delete"]; exists {
			return fmt.Sprintf(confirmMsg, entityName)
		}
		return fmt.Sprintf("Are you sure you want to delete %s? This action cannot be undone.", entityName)
	default:
		return ""
	}
}

// SingleActionButton creates a single action button
func SingleActionButton(config SingleActionConfig) gomponents.Node {
	// Load locales for default text
	locales, err := i18n.LoadTemplateLocales("./templates/ui/tableactions/tableactions.locales.json", config.Lang)
	if err != nil {
		locales = map[string]string{}
	}

	// Get default text if not provided
	actionText := ""
	switch config.Action {
	case ActionDelete:
		actionText = locales["action_delete"]
		if actionText == "" {
			actionText = "Delete"
		}
	case ActionEdit:
		actionText = locales["action_edit"]
		if actionText == "" {
			actionText = "Edit"
		}
	case ActionView:
		actionText = locales["action_view"]
		if actionText == "" {
			actionText = "View"
		}
	case ActionClone:
		actionText = locales["action_clone"]
		if actionText == "" {
			actionText = "Clone"
		}
	case ActionExport:
		actionText = locales["action_export"]
		if actionText == "" {
			actionText = "Export"
		}
	case ActionToggle:
		actionText = locales["action_toggle"]
		if actionText == "" {
			actionText = "Toggle"
		}
	case ActionShare:
		actionText = locales["action_share"]
		if actionText == "" {
			actionText = "Share"
		}
	}

	// Get styling and size classes
	actionStyling := getActionStyling(config.Action)
	buttonClass, iconClass := getSizeClasses(config.Size)

	// Prepare attributes
	attrs := []gomponents.Node{
		html.Type("button"),
		html.Class(fmt.Sprintf("inline-flex items-center justify-center rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 %s %s", buttonClass, actionStyling)),
		gomponents.Attr("aria-label", actionText),
		gomponents.Attr("title", actionText),
	}

	// Add ID and test ID
	if config.ID != "" {
		attrs = append(attrs, html.ID(config.ID))
	}
	if config.DataTestID != "" {
		attrs = append(attrs, gomponents.Attr("data-testid", config.DataTestID))
	}

	// Add HTMX attributes
	if config.HxEndpoint != "" {
		// Determine HTMX method based on action type
		switch config.Action {
		case ActionDelete:
			attrs = append(attrs, htmx.Delete(config.HxEndpoint))
		default:
			attrs = append(attrs, htmx.Get(config.HxEndpoint))
		}
	}

	if config.HxTarget != "" {
		attrs = append(attrs, htmx.Target(config.HxTarget))
	}

	if config.HxSwap != "" {
		attrs = append(attrs, htmx.Swap(config.HxSwap))
	}

	// Add confirmation for destructive actions
	confirmMsg := config.HxConfirm
	if confirmMsg == "" && config.Action == ActionDelete {
		confirmMsg = getDefaultConfirmMessage(config.Action, config.EntityName, config.Lang)
	}
	if confirmMsg != "" {
		attrs = append(attrs, htmx.Confirm(confirmMsg))
	}

	// Add loading indicator
	attrs = append(attrs, htmx.Indicator("#global-saving-indicator"))

	// Add disabled state
	if config.Disabled {
		attrs = append(attrs,
			gomponents.Attr("disabled", "true"),
			html.Class("opacity-50 cursor-not-allowed"),
		)
	}

	// Add extra attributes
	attrs = append(attrs, config.ExtraAttrs...)

	// Add icon
	attrs = append(attrs, html.Div(
		html.Class(iconClass),
		getActionIcon(config.Action),
	))

	return html.Button(attrs...)
}

// MultiActionDropdown creates a dropdown with multiple actions
func MultiActionDropdown(config MultiActionConfig) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/ui/tableactions/tableactions.locales.json", config.Lang)
	if err != nil {
		locales = map[string]string{}
	}

	// Get size classes
	buttonClass, iconClass := getSizeClasses(config.Size)

	// Dropdown trigger button
	triggerID := config.ID
	if triggerID == "" {
		triggerID = fmt.Sprintf("actions-dropdown-%d", config.EntityID)
	}

	dropdownID := fmt.Sprintf("%s-menu", triggerID)

	// Position classes
	positionClass := "bottom-end"
	if config.Position != "" {
		positionClass = config.Position
	}

	actionsMenuText := locales["actions_menu"]
	if actionsMenuText == "" {
		actionsMenuText = "Actions menu"
	}

	return html.Div(
		html.Class("relative"),
		gomponents.Attr("x-data", "{ open: false }"),

		// Trigger button
		html.Button(
			html.Type("button"),
			html.ID(triggerID),
			html.Class(fmt.Sprintf("inline-flex items-center justify-center rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 %s", buttonClass)),
			gomponents.Attr("@click", "open = !open"),
			gomponents.Attr("@click.away", "open = false"),
			gomponents.Attr("aria-label", actionsMenuText),
			gomponents.Attr("aria-expanded", "false"),
			gomponents.Attr("aria-haspopup", "true"),
			gomponents.If(config.DataTestID != "", gomponents.Attr("data-testid", config.DataTestID)),

			html.Div(
				html.Class(iconClass),
				icons.EllipsisVertical(),
			),
		),

		// Dropdown menu
		html.Div(
			html.ID(dropdownID),
			html.Class("absolute z-50 mt-1 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-600"),
			gomponents.Attr("x-show", "open"),
			gomponents.Attr("x-transition:enter", "transition ease-out duration-100"),
			gomponents.Attr("x-transition:enter-start", "transform opacity-0 scale-95"),
			gomponents.Attr("x-transition:enter-end", "transform opacity-100 scale-100"),
			gomponents.Attr("x-transition:leave", "transition ease-in duration-75"),
			gomponents.Attr("x-transition:leave-start", "transform opacity-100 scale-100"),
			gomponents.Attr("x-transition:leave-end", "transform opacity-0 scale-95"),
			gomponents.Attr("x-cloak", ""),

			// Position the dropdown
			gomponents.If(positionClass == "bottom-end", html.Class("right-0")),
			gomponents.If(positionClass == "bottom-start", html.Class("left-0")),
			gomponents.If(positionClass == "top-end", html.Class("right-0 bottom-full mb-1")),
			gomponents.If(positionClass == "top-start", html.Class("left-0 bottom-full mb-1")),

			html.Div(
				html.Class("py-1"),
				gomponents.Group(
					gomponents.Map(config.Actions, func(action ActionItem) gomponents.Node {
						return buildActionItem(action, config.EntityID, config.EntityName, config.Lang)
					}),
				),
			),
		),
	)
}

// buildActionItem creates an individual action item in the dropdown
func buildActionItem(action ActionItem, entityID int32, entityName, lang string) gomponents.Node {
	actionStyling := getActionStyling(action.Type)

	// Use provided text or get default
	text := action.Text
	if text == "" {
		locales, _ := i18n.LoadTemplateLocales("./templates/ui/tableactions/tableactions.locales.json", lang)
		switch action.Type {
		case ActionDelete:
			text = locales["action_delete"]
			if text == "" {
				text = "Delete"
			}
		case ActionEdit:
			text = locales["action_edit"]
			if text == "" {
				text = "Edit"
			}
		case ActionView:
			text = locales["action_view"]
			if text == "" {
				text = "View"
			}
		case ActionClone:
			text = locales["action_clone"]
			if text == "" {
				text = "Clone"
			}
		case ActionExport:
			text = locales["action_export"]
			if text == "" {
				text = "Export"
			}
		case ActionToggle:
			text = locales["action_toggle"]
			if text == "" {
				text = "Toggle"
			}
		case ActionShare:
			text = locales["action_share"]
			if text == "" {
				text = "Share"
			}
		}
	}

	// Use provided icon or get default
	icon := action.Icon
	if icon == nil {
		icon = getActionIcon(action.Type)
	}

	// Base classes for menu items
	baseClasses := "group flex items-center px-4 py-2 text-sm transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left"
	classes := fmt.Sprintf("%s %s", baseClasses, actionStyling)

	if action.Disabled {
		classes += " opacity-50 cursor-not-allowed"
	}

	// Prepare attributes
	attrs := []gomponents.Node{
		html.Class(classes),
	}

	// Add test ID
	if action.DataTestID != "" {
		attrs = append(attrs, gomponents.Attr("data-testid", action.DataTestID))
	}

	// Handle different action types
	if action.Href != "" {
		// Link action
		attrs = append(attrs, html.Href(action.Href))
		attrs = append(attrs, buildActionContent(icon, text))
		return html.A(attrs...)
	} else {
		// Button action
		attrs = append(attrs, html.Type("button"))

		// Add HTMX attributes
		if action.HxEndpoint != "" {
			switch action.Type {
			case ActionDelete:
				attrs = append(attrs, htmx.Delete(action.HxEndpoint))
			default:
				attrs = append(attrs, htmx.Get(action.HxEndpoint))
			}
		}

		if action.HxTarget != "" {
			attrs = append(attrs, htmx.Target(action.HxTarget))
		}

		if action.HxSwap != "" {
			attrs = append(attrs, htmx.Swap(action.HxSwap))
		}

		// Add confirmation for destructive actions
		confirmMsg := action.HxConfirm
		if confirmMsg == "" && action.Type == ActionDelete {
			confirmMsg = getDefaultConfirmMessage(action.Type, entityName, lang)
		}
		if confirmMsg != "" {
			attrs = append(attrs, htmx.Confirm(confirmMsg))
		}

		// Add loading indicator
		attrs = append(attrs, htmx.Indicator("#global-saving-indicator"))

		// Add onClick handler
		if action.OnClick != "" {
			attrs = append(attrs, gomponents.Attr("@click", action.OnClick))
		}

		// Add disabled state
		if action.Disabled {
			attrs = append(attrs, gomponents.Attr("disabled", "true"))
		}

		// Add extra attributes
		attrs = append(attrs, action.ExtraAttrs...)

		// Close dropdown after click
		attrs = append(attrs, gomponents.Attr("@click", "open = false"))

		attrs = append(attrs, buildActionContent(icon, text))
		return html.Button(attrs...)
	}
}

// buildActionContent creates the icon and text content for an action
func buildActionContent(icon gomponents.Node, text string) gomponents.Node {
	return gomponents.Group([]gomponents.Node{
		html.Div(
			html.Class("w-4 h-4 mr-3 flex-shrink-0"),
			icon,
		),
		html.Span(
			html.Class("truncate"),
			gomponents.Text(text),
		),
	})
}
