# Table Actions Component Usage Examples

This document provides comprehensive examples of how to use the unified table actions component.

## Basic Usage

### Single Action Button

Use `SingleActionButton` for simple actions like delete buttons on dense tables:

```go
// Simple delete button
tableactions.SingleActionButton(tableactions.SingleActionConfig{
    ID:         fmt.Sprintf("delete-item-%d", item.ID),
    DataTestID: fmt.Sprintf("delete-item-%d", item.ID),
    Action:     tableactions.ActionDelete,
    EntityID:   item.ID,
    EntityName: item.Name,
    HxEndpoint: fmt.Sprintf("/api/items/%d", item.ID),
    HxTarget:   fmt.Sprintf("#item-row-%d", item.ID),
    HxSwap:     "outerHTML",
    Size:       tableactions.SizeSmall,
    Lang:       lang,
})
```

### Multi-Action Dropdown

Use `MultiActionDropdown` for multiple actions on main entity tables:

```go
// Complete dropdown with multiple actions
tableactions.MultiActionDropdown(tableactions.MultiActionConfig{
    ID:         fmt.Sprintf("team-%d-actions", team.ID),
    DataTestID: fmt.Sprintf("team-%d-actions-dropdown", team.ID),
    EntityID:   team.ID,
    EntityName: team.Name,
    Size:       tableactions.SizeDefault,
    Lang:       lang,
    Actions: []tableactions.ActionItem{
        {
            Type:       tableactions.ActionEdit,
            HxEndpoint: fmt.Sprintf("/app/teams/%d/edit", team.ID),
            HxTarget:   "#editTeamFormContainer",
            DataTestID: fmt.Sprintf("team-%d-edit", team.ID),
        },
        {
            Type:       tableactions.ActionView,
            Href:       fmt.Sprintf("/app/teams/%d", team.ID),
            DataTestID: fmt.Sprintf("team-%d-view", team.ID),
        },
        {
            Type:       tableactions.ActionDelete,
            HxEndpoint: fmt.Sprintf("/app/teams/%d", team.ID),
            HxTarget:   fmt.Sprintf("#team-row-%d", team.ID),
            HxSwap:     "outerHTML",
            DataTestID: fmt.Sprintf("team-%d-delete", team.ID),
        },
    },
})
```

## Real-World Integration Examples

### 1. Teams Table (Updated)

Replace the existing `TeamRowDropdown` with the new unified component:

```go
// Before (in teamsTable.go)
html.Td(
    html.Class("px-6 py-4 w-20"),
    TeamRowDropdown(team, lang),
),

// After
html.Td(
    html.Class("px-6 py-4 w-20"),
    tableactions.MultiActionDropdown(tableactions.MultiActionConfig{
        ID:         fmt.Sprintf("team-%d-actions", team.ID),
        DataTestID: fmt.Sprintf("team-%d-actions-dropdown", team.ID),
        EntityID:   team.ID,
        EntityName: team.Name,
        Size:       tableactions.SizeDefault,
        Lang:       lang,
        Actions: []tableactions.ActionItem{
            {
                Type:       tableactions.ActionDelete,
                HxEndpoint: fmt.Sprintf("/app/teams/%d", team.ID),
                HxTarget:   fmt.Sprintf("#team-row-%d", team.ID),
                HxSwap:     "outerHTML",
                DataTestID: fmt.Sprintf("team-%d-delete", team.ID),
            },
        },
    }),
),
```

### 2. Custom Columns Tables (Future Migration)

For Alpine.js based tables, you would need to create HTMX endpoints to replace the JavaScript calls:

```go
// Instead of Alpine.js deleteColumn(column) calls
html.Td(
    html.Class("px-6 py-4"),
    tableactions.SingleActionButton(tableactions.SingleActionConfig{
        ID:         fmt.Sprintf("delete-column-%d", column.ID),
        DataTestID: fmt.Sprintf("delete-custom-column-%d", column.ID),
        Action:     tableactions.ActionDelete,
        EntityID:   column.ID,
        EntityName: column.Name,
        HxEndpoint: fmt.Sprintf("/app/settings/custom-player-columns/%d", column.ID),
        HxTarget:   fmt.Sprintf("#custom-column-row-%d", column.ID),
        HxSwap:     "outerHTML",
        Size:       tableactions.SizeSmall,
        Lang:       lang,
    }),
),
```

### 3. Players Table (Adding Actions)

Add action column to players table:

```go
// In PlayersTable function, add to table headers:
html.Th(
    gomponents.Attr("scope", "col"),
    html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20"),
    gomponents.Text(locales["table_header_actions"]),
),

// In PlayerRow function, add to table cells:
html.Td(
    html.Class("px-6 py-4 w-20"),
    tableactions.MultiActionDropdown(tableactions.MultiActionConfig{
        ID:         fmt.Sprintf("player-%d-actions", player.ID),
        DataTestID: fmt.Sprintf("player-%d-actions-dropdown", player.ID),
        EntityID:   player.ID,
        EntityName: player.Name,
        Size:       tableactions.SizeDefault,
        Lang:       lang,
        Actions: []tableactions.ActionItem{
            {
                Type:       tableactions.ActionView,
                Href:       fmt.Sprintf("/app/players/%d", player.ID),
                DataTestID: fmt.Sprintf("player-%d-view", player.ID),
            },
            {
                Type:       tableactions.ActionDelete,
                HxEndpoint: fmt.Sprintf("/app/players/%d", player.ID),
                HxTarget:   fmt.Sprintf("#player-row-%d", player.ID),
                HxSwap:     "outerHTML",
                DataTestID: fmt.Sprintf("player-%d-delete", player.ID),
            },
        },
    }),
),
```

### 4. API Keys Table

```go
tableactions.MultiActionDropdown(tableactions.MultiActionConfig{
    EntityID:   apiKey.ID,
    EntityName: apiKey.Name,
    Size:       tableactions.SizeDefault,
    Lang:       lang,
    Actions: []tableactions.ActionItem{
        {
            Type:       tableactions.ActionView,
            Text:       locales["view_key"], // Custom text
            HxEndpoint: fmt.Sprintf("/app/api-keys/%d/view", apiKey.ID),
            HxTarget:   "#keyViewModal",
            DataTestID: fmt.Sprintf("api-key-%d-view", apiKey.ID),
        },
        {
            Type:       tableactions.ActionClone,
            Text:       locales["regenerate"], // Custom text
            HxEndpoint: fmt.Sprintf("/app/api-keys/%d/regenerate", apiKey.ID),
            HxTarget:   fmt.Sprintf("#api-key-row-%d", apiKey.ID),
            HxSwap:     "outerHTML",
            DataTestID: fmt.Sprintf("api-key-%d-regenerate", apiKey.ID),
        },
        {
            Type:       tableactions.ActionDelete,
            HxEndpoint: fmt.Sprintf("/app/api-keys/%d", apiKey.ID),
            HxTarget:   fmt.Sprintf("#api-key-row-%d", apiKey.ID),
            HxSwap:     "outerHTML",
            DataTestID: fmt.Sprintf("api-key-%d-delete", apiKey.ID),
        },
    },
})
```

## Advanced Features

### Custom Confirmation Messages

```go
tableactions.ActionItem{
    Type:       tableactions.ActionDelete,
    HxEndpoint: "/api/dangerous-action",
    HxConfirm:  "This will permanently delete all data. Are you absolutely sure?",
    // ... other properties
}
```

### Custom Icons and Text

```go
tableactions.ActionItem{
    Type:       tableactions.ActionExport,
    Text:       "Download CSV", // Custom text
    Icon:       icons.ArrowDownTray(), // Custom icon
    // ... other properties
}
```

### Disabled Actions

```go
tableactions.ActionItem{
    Type:     tableactions.ActionDelete,
    Disabled: !user.CanDelete(item),
    // ... other properties
}
```

### Position Control

```go
tableactions.MultiActionConfig{
    // ... other properties
    Position: "top-end", // Dropdown opens upward
}
```

## Action Types

All available action types with their default styling:

- `ActionDelete` - Red styling, uses DELETE HTTP method, auto-confirmation
- `ActionEdit` - Blue styling, uses GET HTTP method
- `ActionView` - Gray styling, uses GET HTTP method or href
- `ActionClone` - Green styling, uses GET HTTP method
- `ActionExport` - Purple styling, uses GET HTTP method
- `ActionToggle` - Yellow styling, uses GET HTTP method
- `ActionShare` - Indigo styling, uses GET HTTP method

## Size Variants

- `SizeSmall` - Compact for dense tables (`p-1`, `w-3 h-3` icons)
- `SizeDefault` - Standard for main tables (`p-2`, `w-4 h-4` icons)

## Data Test IDs

Standard patterns for testing:

- Single actions: `{entity}-{id}-{action}` (e.g., `team-123-delete`)
- Dropdown triggers: `{entity}-{id}-actions-dropdown`
- Dropdown items: `{entity}-{id}-{action}`

## HTMX Integration

The component automatically:
- Uses appropriate HTTP methods (DELETE for delete actions, GET for others)
- Adds loading indicators (`#global-saving-indicator`)
- Adds confirmation dialogs for delete actions
- Handles target and swap attributes for seamless updates

## Localization

The component supports full i18n with automatic fallbacks:
- Loads from `./templates/ui/tableactions/tableactions.locales.json`
- Supports English and French by default
- Falls back to English text if translations are missing

## Testing

Use the standardized data-testid patterns for E2E testing:

```typescript
// Playwright test examples
await page.getByTestId('team-123-actions-dropdown').click();
await page.getByTestId('team-123-delete').click();
await page.getByRole('button', { name: 'OK' }).click(); // Confirmation dialog
await expect(page.getByTestId('team-row-123')).not.toBeVisible();
```

## Migration Strategy

1. **Phase 1**: Update Teams Table (✅ Complete)
2. **Phase 2**: Update Custom Columns Tables (requires HTMX endpoints)
3. **Phase 3**: Add actions to Players Table
4. **Phase 4**: Update API Keys and other tables
5. **Phase 5**: Remove old dropdown components

## Benefits

1. **Consistency**: All tables use the same action patterns
2. **Accessibility**: Proper ARIA labels and keyboard navigation
3. **Maintainability**: Single source of truth for action styling
4. **Performance**: Optimized HTMX integration
5. **Localization**: Full i18n support
6. **Testing**: Standardized test patterns
7. **Future-proof**: Easy to add new action types
