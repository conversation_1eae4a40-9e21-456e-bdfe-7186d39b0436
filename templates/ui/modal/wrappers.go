package modal

import (
	"fmt"

	. "maragu.dev/gomponents"
	. "maragu.dev/gomponents/html"
)

// ServerModalConfig holds configuration for server-controlled modals
type ServerModalConfig struct {
	ModalID      string
	Title        string
	ContentURL   string
	Trigger      string
	SwapTarget   string
	SwapMethod   string
	IsUnclosable bool
	DataTestID   string
}

// ClientModalConfig holds configuration for client-controlled modals
type ClientModalConfig struct {
	ModalID      string
	Title        string
	Content      Node
	IsUnclosable bool
	DataTestID   string
}

// ServerModal creates a modal that loads content via HTMX
// This pattern is used when modal content needs to be loaded dynamically from the server
// The modal starts closed and opens when triggered, then loads content via HTMX
func ServerModal(config ServerModalConfig) Node {
	// Set defaults
	if config.SwapTarget == "" {
		config.SwapTarget = "#modal-body-container"
	}
	if config.SwapMethod == "" {
		config.SwapMethod = "innerHTML"
	}
	if config.Trigger == "" {
		config.Trigger = "click"
	}

	modalID := config.ModalID

	return Div(
		Attr("x-data", fmt.Sprintf("{ %s_modalOpen: false }", modalID)),
		Attr("x-init", fmt.Sprintf("setTimeout(() => { %s_modalOpen = true }, 0)", modalID)),
		If(config.DataTestID != "", Attr("data-testid", config.DataTestID)),

		// Server-controlled modals typically use HTMX to load content
		Div(
			Attr("hx-get", config.ContentURL),
			Attr("hx-target", config.SwapTarget),
			Attr("hx-swap", config.SwapMethod),
			Attr("hx-trigger", config.Trigger),
			// The modal component will be rendered by the server response
		),

		Modal(ModalConfig{
			ModalID:      modalID,
			Title:        config.Title,
			Content:      LoadingContent(), // Show loading content while HTMX loads
			IsUnclosable: config.IsUnclosable,
		}),
	)
}

// ClientModal creates a pre-rendered modal controlled by Alpine.js
// This pattern is used when modal content is static and known at render time
// The modal content is included in the initial HTML payload
func ClientModal(config ClientModalConfig) Node {
	modalID := config.ModalID

	return Div(
		Attr("x-data", fmt.Sprintf("{ %s_modalOpen: false }", modalID)),
		If(config.DataTestID != "", Attr("data-testid", config.DataTestID)),

		Modal(ModalConfig{
			ModalID:      modalID,
			Title:        config.Title,
			Content:      config.Content,
			IsUnclosable: config.IsUnclosable,
		}),
	)
}

// ClientModalWithAutoOpen creates a pre-rendered modal that opens immediately
// This is for modals that should display as soon as they're rendered (like the feedback modal)
func ClientModalWithAutoOpen(config ClientModalConfig) Node {
	modalID := config.ModalID

	return Div(
		Attr("x-data", fmt.Sprintf("{ %s_modalOpen: false }", modalID)),
		Attr("x-init", fmt.Sprintf("$nextTick(() => { %s_modalOpen = true })", modalID)),
		If(config.DataTestID != "", Attr("data-testid", config.DataTestID)),

		Modal(ModalConfig{
			ModalID:      modalID,
			Title:        config.Title,
			Content:      config.Content,
			IsUnclosable: config.IsUnclosable,
		}),
	)
}

// ClientModalWithTrigger creates a client-controlled modal with a built-in trigger button
// This is a convenience wrapper for simple cases where you need both the modal and trigger
func ClientModalWithTrigger(config ClientModalConfig, triggerText string, triggerClass string) Node {
	modalID := config.ModalID

	return Div(
		Attr("x-data", fmt.Sprintf("{ %s_modalOpen: false }", modalID)),
		If(config.DataTestID != "", Attr("data-testid", config.DataTestID+"-container")),

		// Trigger button
		Button(
			Type("button"),
			Class(triggerClass),
			Attr("@click", fmt.Sprintf("%s_modalOpen = true", modalID)),
			If(config.DataTestID != "", Attr("data-testid", config.DataTestID+"-trigger")),
			Text(triggerText),
		),

		// Modal
		Modal(ModalConfig{
			ModalID:      modalID,
			Title:        config.Title,
			Content:      config.Content,
			IsUnclosable: config.IsUnclosable,
		}),
	)
}

// ServerModalWithAutoOpen creates a server-controlled modal that opens immediately
// This pattern is commonly used for modals that should display as soon as they're rendered
func ServerModalWithAutoOpen(config ServerModalConfig, contentURL string) Node {
	modalID := config.ModalID

	return Div(
		Attr("x-data", fmt.Sprintf("{ %s_modalOpen: false }", modalID)),
		Attr("x-init", fmt.Sprintf("setTimeout(() => { %s_modalOpen = true }, 0)", modalID)),
		If(config.DataTestID != "", Attr("data-testid", config.DataTestID)),

		// Auto-load content when modal is initialized
		Div(
			Attr("hx-get", contentURL),
			Attr("hx-target", "#modal-body-container"),
			Attr("hx-swap", "innerHTML"),
			Attr("hx-trigger", "load"),
		),

		Modal(ModalConfig{
			ModalID:      modalID,
			Title:        config.Title,
			Content:      LoadingContent(),
			IsUnclosable: config.IsUnclosable,
		}),
	)
}

// FormModal creates a modal specifically designed for forms with HTMX
// This handles common form patterns like toast notifications and closing on success
func FormModal(config ServerModalConfig, formContent Node) Node {
	modalID := config.ModalID

	return Div(
		Attr("x-data", fmt.Sprintf("{ %s_modalOpen: false }", modalID)),
		Attr("x-init", fmt.Sprintf("setTimeout(() => { %s_modalOpen = true }, 0)", modalID)),
		If(config.DataTestID != "", Attr("data-testid", config.DataTestID)),

		Modal(ModalConfig{
			ModalID: modalID,
			Title:   config.Title,
			Content: Form(
				// Common HTMX attributes for form modals
				Attr("hx-target", "#toast-body-container"),
				Attr("hx-swap", "afterbegin"),
				Attr("@htmx:after-request", fmt.Sprintf("if(event.detail.successful) { %s_modalOpen = false }", modalID)),
				formContent,
			),
			IsUnclosable: config.IsUnclosable,
		}),
	)
}

// LoadingContent provides a standard loading indicator for server-controlled modals
func LoadingContent() Node {
	return Div(
		Class("flex items-center justify-center py-8"),
		Div(
			Class("animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"),
		),
		Div(
			Class("ml-3 text-gray-600 dark:text-gray-400"),
			Text("Loading..."),
		),
	)
}

// ConfirmationModal creates a standardized confirmation modal
// Useful for delete confirmations, destructive actions, etc.
func ConfirmationModal(modalID, title, message, confirmText, cancelText string, confirmAction Node) Node {
	return Modal(ModalConfig{
		ModalID: modalID,
		Title:   title,
		Content: Div(
			Class("space-y-4"),
			P(
				Class("text-gray-700 dark:text-gray-300"),
				Text(message),
			),
			Div(
				Class("flex justify-end space-x-3 pt-4"),
				Button(
					Type("button"),
					Class("px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-700"),
					Attr("@click", fmt.Sprintf("%s_modalOpen = false", modalID)),
					Text(cancelText),
				),
				confirmAction, // This should be a button with the actual action (HTMX post/delete/etc)
			),
		),
		IsUnclosable: false,
	})
}
