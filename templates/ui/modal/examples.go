package modal

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	. "maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	. "maragu.dev/gomponents/html"
)

// Example showing how to refactor existing modals using the new wrapper patterns

// ===== SERVER-CONTROLLED MODAL EXAMPLES =====

// Example 1: Settings modal that loads configuration from server
func ExampleSettingsModalServer(lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/example.locales.json", lang)

	return ServerModalWithAutoOpen(ServerModalConfig{
		ModalID:      "settingsModal",
		Title:        locales["settings_title"],
		IsUnclosable: false,
		DataTestID:   "settings-modal",
	}, "/app/settings/modal-content")
}

// Example 2: Player selection modal that loads players from server
func ExamplePlayerSelectModalServer(seasonID int32, lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/example.locales.json", lang)

	return ServerModal(ServerModalConfig{
		ModalID:    "playerSelectModal",
		Title:      locales["select_players"],
		ContentURL: "/app/players/select-modal",
		Trigger:    "load",
		SwapTarget: "#modal-body-container",
		SwapMethod: "innerHTML",
		DataTestID: "player-select-modal",
	})
}

// Example 3: Form modal for creating a new season
func ExampleNewSeasonFormModal(lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/example.locales.json", lang)

	formContent := Div(
		Class("space-y-4"),
		Div(
			Label(
				For("season-name"),
				Class("block text-sm font-medium text-gray-700 dark:text-gray-300"),
				Text(locales["season_name"]),
			),
			Input(
				Type("text"),
				ID("season-name"),
				Name("name"),
				Required(),
				Class("mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"),
			),
		),
		Div(
			Class("flex justify-end space-x-3 pt-4"),
			button.SecondaryButton(button.BaseButtonConfig{
				Text:       locales["cancel"],
				ButtonType: "button",
				Attrs: []Node{
					Attr("@click", "newSeasonModal_modalOpen = false"),
				},
			}),
			button.PrimaryButton(button.BaseButtonConfig{
				Text:       locales["create_season"],
				ButtonType: "submit",
				DataTestID: "create-season-btn",
			}),
		),
	)

	return FormModal(ServerModalConfig{
		ModalID:    "newSeasonModal",
		Title:      locales["create_new_season"],
		DataTestID: "new-season-modal",
	}, formContent)
}

// ===== CLIENT-CONTROLLED MODAL EXAMPLES =====

// Example 4: Help modal with static content
func ExampleHelpModalClient(lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/example.locales.json", lang)

	helpContent := Div(
		Class("space-y-4"),
		H4(
			Class("text-lg font-semibold text-gray-900 dark:text-white"),
			Text(locales["getting_started"]),
		),
		P(
			Class("text-gray-700 dark:text-gray-300"),
			Text(locales["help_description"]),
		),
		Ul(
			Class("list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300"),
			Li(Text(locales["help_step_1"])),
			Li(Text(locales["help_step_2"])),
			Li(Text(locales["help_step_3"])),
		),
		Div(
			Class("flex justify-end pt-4"),
			button.PrimaryButton(button.BaseButtonConfig{
				Text:       locales["got_it"],
				ButtonType: "button",
				Attrs: []Node{
					Attr("@click", "helpModal_modalOpen = false"),
				},
			}),
		),
	)

	return ClientModal(ClientModalConfig{
		ModalID:    "helpModal",
		Title:      locales["help_title"],
		Content:    helpContent,
		DataTestID: "help-modal",
	})
}

// Example 5: About modal with trigger button
func ExampleAboutModalWithTrigger(lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/example.locales.json", lang)

	aboutContent := Div(
		Class("space-y-4"),
		P(
			Class("text-gray-700 dark:text-gray-300"),
			Text(locales["app_description"]),
		),
		P(
			Class("text-sm text-gray-500 dark:text-gray-400"),
			Text(locales["version_info"]),
		),
	)

	return ClientModalWithTrigger(
		ClientModalConfig{
			ModalID:    "aboutModal",
			Title:      locales["about_app"],
			Content:    aboutContent,
			DataTestID: "about-modal",
		},
		locales["about"],                        // trigger text
		"text-sm text-blue-600 hover:underline", // trigger class
	)
}

// Example 6: Confirmation modal for destructive actions
func ExampleDeleteConfirmationModal(itemName string, deleteURL string, lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/example.locales.json", lang)

	confirmButton := button.PrimaryIconButton(button.IconButtonConfig{
		Text:       locales["delete"],
		ButtonType: "button",
		Icon:       icons.Trash(Class("w-4 h-4")),
		Class:      "bg-red-600 hover:bg-red-700 border-red-600 focus:ring-red-500",
		DataTestID: "confirm-delete-btn",
		Attrs: []Node{
			htmx.Delete(deleteURL),
			htmx.Target("#toast-body-container"),
			htmx.Swap("afterbegin"),
			Attr("@htmx:after-request", "if(event.detail.successful) { deleteConfirmModal_modalOpen = false }"),
		},
	})

	// Wrap ConfirmationModal with Alpine.js state management
	return Div(
		Attr("x-data", "{ deleteConfirmModal_modalOpen: false }"),

		// Trigger button (example - consumer would implement this)
		Button(
			Type("button"),
			Class("bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"),
			Attr("@click", "deleteConfirmModal_modalOpen = true"),
			Text("Delete Item"),
		),

		// The actual modal
		ConfirmationModal(
			"deleteConfirmModal",
			locales["confirm_delete"],
			locales["delete_warning"]+" \""+itemName+"\"?",
			locales["delete"],
			locales["cancel"],
			confirmButton,
		),
	)
}

// ===== REFACTORING EXAMPLES =====

// Before: Using the raw modal component
func FeedbackModalOld(lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/app/feedback/feedbackModal.locales.json", lang)

	return Div(
		Attr("x-data", "{feedbackModal_modalOpen: false}"),
		Attr("x-init", "$nextTick(() => { feedbackModal_modalOpen = true })"),
		Modal(ModalConfig{
			ModalID:      "feedbackModal",
			Title:        locales["feedback_title"],
			Content:      feedbackFormContent(lang),
			IsUnclosable: false,
		}),
	)
}

// After: Using the new client modal wrapper
func FeedbackModalNew(lang string) Node {
	locales, _ := i18n.LoadTemplateLocales("./templates/app/feedback/feedbackModal.locales.json", lang)

	return ClientModal(ClientModalConfig{
		ModalID:    "feedbackModal",
		Title:      locales["feedback_title"],
		Content:    feedbackFormContent(lang),
		DataTestID: "feedback-modal",
	})
}

// Dummy function for example
func feedbackFormContent(lang string) Node {

	fmt.Println(lang)
	return Div(Text("Feedback form content here"))
}

// ===== PATTERN SELECTION GUIDE =====

/*
When to use ServerModal:
- Modal content needs to be loaded from server
- Content depends on dynamic data
- Large or complex content that shouldn't be in initial payload
- Forms that need server-side validation

When to use ClientModal:
- Static content known at render time
- Simple forms with client-side validation
- Content that's lightweight and can be pre-rendered
- Modals that need to work offline

When to use FormModal:
- Any modal containing a form that submits to server
- Automatically handles toast notifications
- Closes modal on successful submission

When to use ConfirmationModal:
- Delete confirmations
- Destructive actions
- Simple yes/no decisions
*/
