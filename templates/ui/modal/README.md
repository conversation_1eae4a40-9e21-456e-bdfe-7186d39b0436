# Modal Wrapper Components

This package provides clear wrapper components that make the modal patterns explicit in the Coachpad application. Instead of manually implementing server-controlled and client-controlled modal patterns, use these standardized wrappers.

## Quick Reference

| Pattern | Use Case | Wrapper Function |
|---------|----------|------------------|
| Server-controlled | Dynamic content from server | `ServerModal()` |
| Client-controlled | Static content, pre-rendered | `ClientModal()` |
| Client-controlled (auto-open) | Static content, opens immediately | `ClientModalWithAutoOpen()` |
| Form modal | Forms with HTMX submission | `FormModal()` |
| Confirmation | Delete/destructive actions | `ConfirmationModal()` |

## Pattern Comparison

### Before (Manual Implementation)
```go
// Hard to distinguish patterns, repetitive boilerplate
func SomeModal() Node {
    return Div(
        Attr("x-data", "{someModal_modalOpen: false}"),
        Attr("x-init", "setTimeout(() => { someModal_modalOpen = true }, 0)"),
        modal.Modal(modal.ModalConfig{
            ModalID: "someModal",
            Title:   "Some Title",
            Content: someContent,
        }),
    )
}
```

### After (Wrapper Components)
```go
// Pattern is immediately clear from function name
func SomeModal() Node {
    return ClientModal(ClientModalConfig{
        ModalID: "someModal",
        Title:   "Some Title", 
        Content: someContent,
    })
}
```

## Available Wrappers

### 1. ServerModal - Dynamic Content Loading

Use when modal content needs to be loaded from the server via HTMX.

```go
ServerModal(ServerModalConfig{
    ModalID:     "settingsModal",
    Title:       "Settings",
    ContentURL:  "/app/settings/modal-content", 
    Trigger:     "click",          // HTMX trigger
    SwapTarget:  "#modal-body-container",
    SwapMethod:  "innerHTML",
    DataTestID:  "settings-modal",
})
```

**When to use:**
- Content depends on dynamic server data
- Large/complex content that shouldn't be in initial payload
- Forms requiring server-side validation

### 2. ClientModal - Pre-rendered Content

Use when modal content is static and known at render time.

```go
ClientModal(ClientModalConfig{
    ModalID:    "helpModal",
    Title:      "Help",
    Content:    helpContent, // Pre-rendered content
    DataTestID: "help-modal",
})
```

**When to use:**
- Static content known at render time
- Simple forms with client-side validation
- Lightweight content that can be pre-rendered
- Modals that need to work offline

### 2a. ClientModalWithAutoOpen - Pre-rendered Content (Auto-Opening)

Use when modal content is static and should open immediately when rendered.

```go
ClientModalWithAutoOpen(ClientModalConfig{
    ModalID:    "feedbackModal",
    Title:      "Feedback", 
    Content:    feedbackContent, // Pre-rendered content
    DataTestID: "feedback-modal",
})
```

**When to use:**
- Same as ClientModal, but modal should open immediately
- Modals triggered by navigation or specific page loads
- Feedback forms, welcome messages, etc.

### 3. FormModal - HTMX Form Handling

Use for forms that submit to the server with standardized patterns.

```go
FormModal(ServerModalConfig{
    ModalID:    "createUserModal", 
    Title:      "Create User",
    DataTestID: "create-user-modal",
}, formContent)
```

**Automatically handles:**
- Toast notification targeting (`#toast-body-container`)
- Modal closure on successful submission
- Standard HTMX form patterns

### 4. ConfirmationModal - Destructive Actions

Use for delete confirmations and other destructive actions.

```go
// Consumer must wrap ConfirmationModal with Alpine.js state management
Div(
    Attr("x-data", "{ deleteModal_modalOpen: false }"),
    
    // Trigger button
    Button(
        Type("button"),
        Attr("@click", "deleteModal_modalOpen = true"),
        Text("Delete Item"),
    ),
    
    // The actual modal
    ConfirmationModal(
        "deleteModal",                    // modalID
        "Confirm Delete",                 // title  
        "Are you sure you want to delete this item?", // message
        "Delete",                         // confirm button text
        "Cancel",                         // cancel button text
        confirmActionButton,              // action button with HTMX
    ),
)
```

### 5. Convenience Wrappers

#### ClientModalWithTrigger
Includes both modal and trigger button:

```go
ClientModalWithTrigger(
    ClientModalConfig{
        ModalID: "aboutModal",
        Title:   "About",
        Content: aboutContent,
    },
    "About App",                      // trigger text
    "text-blue-600 hover:underline",  // trigger classes
)
```

#### ServerModalWithAutoOpen
Server modal that opens immediately when rendered:

```go
ServerModalWithAutoOpen(ServerModalConfig{
    ModalID: "notificationsModal",
    Title:   "Notifications", 
}, "/app/notifications/modal-content")
```

## Migration Guide

### Identifying Current Patterns

**Server-controlled pattern indicators:**
- Has `hx-get` or similar HTMX attributes
- Loads content via HTMX after modal opens
- Content URL in HTMX attributes

**Client-controlled pattern indicators:**  
- All content pre-rendered in initial HTML
- No HTMX content loading
- Static content known at render time

### Migration Steps

1. **Identify the pattern** your existing modal uses
2. **Replace manual implementation** with appropriate wrapper
3. **Update configuration** to use new config structs
4. **Test** that behavior remains the same

### Example Migration

```go
// Before
func NotificationsModal() Node {
    return Div(
        Attr("x-data", "{notificationsModal_modalOpen: false}"),
        Attr("x-init", "setTimeout(() => { notificationsModal_modalOpen = true }, 0)"),
        modal.Modal(modal.ModalConfig{
            ModalID: "notificationsModal", 
            Title:   "Notifications",
            Content: loadingIndicator(),
        }),
        Div(
            Attr("hx-get", "/app/notifications/content"),
            Attr("hx-target", "#modal-body-container"),
            Attr("hx-trigger", "load"),
        ),
    )
}

// After  
func NotificationsModal() Node {
    return ServerModalWithAutoOpen(ServerModalConfig{
        ModalID:    "notificationsModal",
        Title:      "Notifications",
        DataTestID: "notifications-modal",
    }, "/app/notifications/content")
}
```

## Best Practices

1. **Use appropriate wrapper** - Choose based on content loading pattern
2. **Include DataTestID** - Always provide for E2E testing
3. **Follow naming convention** - Use descriptive modalID names
4. **Leverage defaults** - Wrappers provide sensible defaults
5. **Consistent patterns** - Same pattern throughout the app

## Testing

All wrapper components automatically include `data-testid` attributes when configured:

```go
// This modal can be tested with: [data-testid="user-settings-modal"]
ClientModal(ClientModalConfig{
    ModalID:    "userSettings", 
    DataTestID: "user-settings-modal",
    // ...
})
```

## Error Handling

- **Missing ContentURL**: ServerModal requires ContentURL
- **Empty ModalID**: All wrappers require unique ModalID
- **Content conflicts**: Don't mix pre-rendered content with HTMX loading

See `examples.go` for complete working examples of each pattern.
