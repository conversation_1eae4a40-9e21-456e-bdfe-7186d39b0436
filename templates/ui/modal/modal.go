package modal

import (
	. "maragu.dev/gomponents"
	. "maragu.dev/gomponents/html"
)

// ModalConfig holds configuration for the Modal component.
type ModalConfig struct {
	ModalID      string
	Title        string
	Content      Node
	IsOpen       bool
	IsUnclosable bool
}

// Modal creates the content part of a modal component identified by ModalID, using AlpineJS for interactivity.
// The trigger for this modal should be defined externally, setting the 'ModalID_modalOpen' variable to true.
// If IsUnclosable is true, the modal cannot be closed by clicking the overlay, pressing escape, or using a close button.
func Modal(cfg ModalConfig) Node {
	modalID := cfg.ModalID
	title := cfg.Title
	content := cfg.Content
	isUnclosable := cfg.IsUnclosable
	return Div(
		If(!isUnclosable, Attr("@keydown.escape.window", modalID+"_modalOpen = false")),
		// Add event listener for the custom global event to close all modals
		Attr("@close-all-modals.window", modalID+"_modalOpen = false"),
		Class("relative z-50 w-auto h-auto"),

		// Modal Content with Teleport to body
		Template(
			Attr("x-teleport", "#modal-body-container"),
			Div(
				ID(modalID),
				Attr("x-show", modalID+"_modalOpen"),
				Attr("data-testid", modalID+"_modal"),
				Class("fixed top-0 left-0 z-[99] flex items-center justify-center w-screen h-screen"),
				Attr("x-cloak"),

				// Overlay Background
				Div(
					Attr("x-show", modalID+"_modalOpen"),
					Attr("x-transition:enter", "ease-out duration-300"),
					Attr("x-transition:enter-start", "opacity-0"),
					Attr("x-transition:enter-end", "opacity-100"),
					Attr("x-transition:leave", "ease-in duration-300"),
					Attr("x-transition:leave-start", "opacity-100"),
					Attr("x-transition:leave-end", "opacity-0"),
					If(!isUnclosable, Attr("@click", modalID+"_modalOpen = false")),
					Class("absolute inset-0 w-full h-full bg-black/10 dark:bg-gray-900/10 backdrop-blur-md"),
				),

				// Modal Content Box
				Div(
					Attr("x-show", modalID+"_modalOpen"),
					Attr("x-trap.inert.noscroll", modalID+"_modalOpen"),
					Attr("x-transition:enter", "ease-out duration-300"),
					Attr("x-transition:enter-start", "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"),
					Attr("x-transition:enter-end", "opacity-100 translate-y-0 sm:scale-100"),
					Attr("x-transition:leave", "ease-in duration-200"),
					Attr("x-transition:leave-start", "opacity-100 translate-y-0 sm:scale-100"),
					Attr("x-transition:leave-end", "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"),
					Attr("x-init", "$nextTick(() => htmx.process($el))"),
					Class("relative w-full py-6 bg-white px-7 sm:max-w-lg sm:rounded-lg dark:bg-gray-800 dark:text-white"),

					// Modal Header with Title and Close Button
					Div(
						Class("flex items-center justify-between pb-2"),
						H3(
							Class("text-lg font-semibold dark:text-gray-300"),
							Text(title),
						),
						If(!isUnclosable,
							Button(
								Attr("@click", modalID+"_modalOpen = false"),
								Attr("data-testid", modalID+"_close-button"),
								Class("absolute top-0 right-0 flex items-center justify-center w-8 h-8 mt-5 mr-5 text-gray-600 rounded-full hover:text-gray-800 hover:bg-gray-50 cursor-pointer transition-colors duration-150"),
								Raw(`<svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>`),
							),
						),
					),

					// Modal Body Content
					Div(
						Class("relative w-auto"),
						content,
					),
				),
			),
		),
	)
}
