package templatescomponents

import (
	"strconv"

	gomponents "maragu.dev/gomponents"
	html "maragu.dev/gomponents/html"
)

type SwitchProps struct {
	Checked bool
	Name    string
	Class   string
	Label   string
}

func SwitchComponent(props SwitchProps) gomponents.Node {
	return html.Div(
		html.Class("flex items-center justify-center space-x-2"),
		gomponents.Attr("x-data", "{ switchOn: "+strconv.FormatBool(props.Checked)+", switchId: $id('switch') }"),

		html.Input(
			html.Type("checkbox"),
			html.Class("hidden"),
			gomponents.Attr(":id", "switchId"),
			gomponents.Attr(":name", "'"+props.Name+"'"),
			gomponents.Attr(":checked", "switchOn"),
		),

		html.Button(
			html.Type("button"),
			html.Class("relative inline-flex h-6 py-0.5 ml-4 focus:outline-none rounded-full w-10 focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400"),
			gomponents.Attr("x-ref", "switchButton"),
			gomponents.Attr("@click", "switchOn = !switchOn"),
			gomponents.Attr(":class", "switchOn ? 'bg-blue-600' : 'bg-neutral-200'"),
			html.Span(
				html.Class("w-5 h-5 duration-200 ease-in-out bg-white rounded-full shadow-md"),
				gomponents.Attr(":class", "switchOn ? 'translate-x-[18px]' : 'translate-x-0.5'"),
			),
		),

		html.Label(
			html.Class("text-sm select-none cursor-pointer "+props.Class),
			gomponents.Attr(":for", "switchId"),
			gomponents.Attr("@click", "$refs.switchButton.click(); $refs.switchButton.focus()"),
			gomponents.Attr(":class", "{ 'text-blue-600': switchOn, 'text-gray-400': !switchOn }"),
			gomponents.Text(props.Label),
		),
	)
}
