package templates

import (
	"context"
	"log"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/middleware"
	templatescomponents "github.com/j-em/coachpad/templates/components"
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/infobox"
	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func Landing(c echo.Context, stytchClient *stytchapi.API, queries *db.Queries) gomponents.Node {
	// Get the current language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/landing.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Load header locales for signin/signup buttons
	headerLocales, err := i18n.LoadTemplateLocales("./templates/components/public_header.locales.json", lang)
	if err != nil {
		headerLocales = make(map[string]string)
	}

	// Check if user is already authenticated
	userID, err := middleware.GetOptionalUserID(c, stytchClient)
	if err != nil {
		log.Printf("Error checking authentication on landing page: %v", err)
	}

	var userInfoBox gomponents.Node
	if userID != 0 {
		// User is authenticated, fetch user details and show info box
		user, err := queries.GetUserByID(context.Background(), userID)
		if err != nil {
			log.Printf("Error fetching user details: %v", err)
		} else {
			userInfoBox = infobox.InfoBox(infobox.InfoBoxConfig{
				ID:          "landing-user-info",
				UserName:    user.Name,
				Style:       "info",
				Lang:        lang,
				DataTestID:  "landing-user-info-box",
				Dismissible: true,
			})
		}
	}

	// Check if user account was deleted
	deleted := c.QueryParam("deleted") == "true"

	var mainContent gomponents.Node
	if deleted {
		// Show account deletion success message
		mainContent = html.Main(
			html.Class("flex flex-col items-center justify-center min-h-[500px] px-4"),
			html.Div(
				html.Class("max-w-md text-center bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-8"),
				gomponents.Attr("data-testid", "deletion-success-message"),
				html.H1(
					html.Class("text-2xl font-bold text-green-800 dark:text-green-200 mb-4"),
					gomponents.Text(locales["account_deleted_title"]),
				),
				html.P(
					html.Class("text-green-700 dark:text-green-300"),
					gomponents.Text(locales["account_deleted_message"]),
				),
			),
		)
	} else {
		// Show normal landing page content
		mainContent = html.Main(
			html.Class("flex flex-col gap-20"),
			HeroSection(locales, nil),
			html.Div(
				html.Class("flex flex-col gap-16"),
				FeatureSection(locales["player_management_title"], "/images/phone_in_city.jpg", []string{
					locales["player_management_item1"],
					locales["player_management_item2"],
					locales["player_management_item3"],
					locales["player_management_item4"],
				}, locales["player_management_alt"], false),
				FeatureSection(locales["season_overview_title"], "/images/board.jpg", []string{
					locales["season_overview_item1"],
					locales["season_overview_item2"],
					locales["season_overview_item3"],
					locales["season_overview_item4"],
				}, locales["season_overview_alt"], true),
			),
			PricingSection(locales),
			ScheduleMatchesSection(locales),
			CustomizationSection(locales),
		)
	}

	return layouts.HTMLLayout(locales["page_title"], html.Div(
		html.Class("overflow-y-auto min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300"),
		templatescomponents.PublicHeader(lang, html.Div(
			html.Class("flex items-center gap-3 flex-col md:flex-row w-full md:w-auto"),
			// Display user info box if provided (first)
			gomponents.If(userInfoBox != nil,
				html.Div(
					html.Class("w-full md:w-auto"),
					userInfoBox,
				),
			),
			// Then the buttons
			templatescomponents.PublicHeaderButton(headerLocales["sign_in"], "/signin"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_up"], "/signup"),
		)),
		mainContent,
	))
}

func HeroSection(locales map[string]string, userInfoBox gomponents.Node) gomponents.Node {
	return html.Section(
		html.Class("relative min-h-[600px] flex items-center justify-center bg-black/70 dark:bg-black/80 overflow-hidden"),
		html.Img(
			html.Src("/images/bowling.jpg"),
			html.Alt(locales["bowling_alt"]),
			html.Class("absolute inset-0 w-full h-full object-cover opacity-60"),
		),
		html.Div(
			html.Class("relative z-10 flex flex-col items-center justify-center text-center w-full py-24 px-4"),
			html.H1(
				html.Class("text-5xl md:text-7xl font-extrabold mb-6 text-white drop-shadow-lg"),
				gomponents.Text(locales["hero_title"]),
			),
			html.P(
				html.Class("text-xl md:text-2xl mb-10 text-white/90 max-w-2xl mx-auto"),
				gomponents.Text(locales["hero_subtitle"]),
			),
			button.SecondaryButtonLink("/signup", locales["get_started"]),
		),
	)
}

func FeatureSection(title string, imageSrc string, items []string, altText string, reverse bool) gomponents.Node {
	imgCol := html.Div(
		html.Class("flex justify-center items-center"),
		html.Img(
			html.Src(imageSrc),
			html.Alt(altText),
			html.Class("rounded-xl shadow-lg w-full max-w-md dark:shadow-gray-800"),
		),
	)
	textCol := html.Div(
		html.Class("flex flex-col justify-center gap-4 px-2 md:px-8"),
		html.H3(
			html.Class("text-3xl font-bold mb-2 text-primary-900 dark:text-primary-100"),
			gomponents.Text(title),
		),
		html.Ul(
			html.Class("list-disc pl-6 text-lg text-primary-800 dark:text-primary-200 space-y-2"),
			gomponents.Group(func() []gomponents.Node {
				var nodes []gomponents.Node
				for _, item := range items {
					nodes = append(nodes, html.Li(gomponents.Text(item)))
				}
				return nodes
			}()),
		),
	)
	var content []gomponents.Node
	if reverse {
		content = []gomponents.Node{textCol, imgCol}
	} else {
		content = []gomponents.Node{imgCol, textCol}
	}
	// Unpack content slice into variadic arguments for html.Div
	children := make([]gomponents.Node, 0, len(content)+1)
	children = append(children, html.Class("container mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center"))
	children = append(children, content...)
	return html.Section(
		html.Class("py-12 bg-white/90 dark:bg-gray-800 transition-colors duration-300"),
		html.Div(
			children...,
		),
	)
}

func ScheduleMatchesSection(locales map[string]string) gomponents.Node {
	return html.Section(
		html.Class("py-12 bg-gray-100 dark:bg-gray-900 transition-colors duration-300"),
		html.Div(
			html.Class("container mx-auto grid grid-cols-1 md:grid-cols-3 gap-12 items-center"),
			html.Div(
				html.Img(
					html.Src("/images/coach.jpg"),
					html.Alt(locales["coach_alt"]),
					html.Class("rounded-xl shadow-lg w-full max-w-xs mx-auto dark:shadow-gray-800"),
				),
			),
			html.Div(
				html.Class("md:col-span-2 flex flex-col gap-4 px-2 md:px-8"),
				html.H3(
					html.Class("text-3xl font-bold mb-2 text-primary-900 dark:text-primary-100"),
					gomponents.Text(locales["schedule_matches_title"]),
				),
				html.P(
					html.Class("text-xl text-primary-800 dark:text-primary-200"),
					gomponents.Text(locales["schedule_matches_subtitle"]),
				),
				html.Ul(
					html.Class("list-disc mt-4 pl-6 text-lg text-primary-800 dark:text-primary-200 space-y-2"),
					html.Li(gomponents.Text(locales["schedule_matches_item1"])),
					html.Li(gomponents.Text(locales["schedule_matches_item2"])),
					html.Li(gomponents.Text(locales["schedule_matches_item3"])),
					html.Li(gomponents.Text(locales["schedule_matches_item4"])),
				),
			),
		),
	)
}

func PricingSection(locales map[string]string) gomponents.Node {
	return html.Section(
		html.Class("py-16 bg-white dark:bg-gray-800 transition-colors duration-300"),
		html.Div(
			html.Class("container mx-auto px-4"),
			html.Div(
				html.Class("text-center mb-12"),
				html.H2(
					html.Class("text-4xl font-bold text-gray-900 dark:text-white mb-4"),
					gomponents.Text(locales["pricing_title"]),
				),
				html.P(
					html.Class("text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"),
					gomponents.Text(locales["pricing_subtitle"]),
				),
			),
			html.Div(
				html.Class("grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto"),
				PricingCard(PricingCardConfig{
					Title:    locales["pricing_free_title"],
					Price:    locales["pricing_free_price"],
					Features: getFreeFeatures(locales),
				}),
				PricingCard(PricingCardConfig{
					Title:    locales["pricing_pro_title"],
					Price:    locales["pricing_pro_price"],
					Features: getProFeatures(locales),
				}),
			),
		),
	)
}

type PricingCardConfig struct {
	Title    string
	Price    string
	Features []string
}

func PricingCard(config PricingCardConfig) gomponents.Node {
	return html.Div(
		html.Class("bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8 border border-gray-200 dark:border-gray-700"),
		html.Div(
			html.Class("text-center mb-6"),
			html.H3(
				html.Class("text-2xl font-bold text-gray-900 dark:text-white mb-2"),
				gomponents.Text(config.Title),
			),
			html.P(
				html.Class("text-3xl font-bold text-gray-900 dark:text-white"),
				gomponents.Text(config.Price),
			),
		),
		html.Ul(
			html.Class("space-y-4"),
			gomponents.Group(renderPricingFeatures(config.Features)),
		),
	)
}

func renderPricingFeatures(features []string) []gomponents.Node {
	var nodes []gomponents.Node
	for _, feature := range features {
		nodes = append(nodes, html.Li(
			html.Class("flex items-center text-gray-700 dark:text-gray-300"),
			html.Span(
				html.Class("w-5 h-5 text-green-500 dark:text-green-400 mr-3 flex-shrink-0"),
				icons.Check(),
			),
			gomponents.Text(feature),
		))
	}
	return nodes
}

func getFreeFeatures(locales map[string]string) []string {
	return []string{
		locales["pricing_free_players"],
		locales["pricing_free_seasons"],
		locales["pricing_free_matches"],
		locales["pricing_free_stats"],
	}
}

func getProFeatures(locales map[string]string) []string {
	return []string{
		locales["pricing_pro_players"],
		locales["pricing_pro_seasons"],
		locales["pricing_pro_matches"],
		locales["pricing_pro_stats"],
		locales["pricing_pro_custom_columns"],
		locales["pricing_pro_export"],
		locales["pricing_pro_priority_support"],
	}
}

func CustomizationSection(locales map[string]string) gomponents.Node {
	return html.Section(
		html.Class("py-12 bg-primary text-primary-foreground dark:bg-gray-800 dark:text-primary-100 transition-colors duration-300"),
		html.Div(
			html.Class("container mx-auto flex flex-col md:flex-row gap-12 items-center px-4"),
			html.Div(
				html.Class("flex-1 flex flex-col gap-4"),
				html.H3(
					html.Class("text-3xl font-bold mb-2"),
					gomponents.Text(locales["customization_title"]),
				),
				html.P(
					html.Class("text-xl"),
					gomponents.Text(locales["customization_subtitle"]),
				),
				html.Ul(
					html.Class("list-disc mt-4 pl-6 text-lg space-y-2"),
					html.Li(gomponents.Text(locales["customization_item1"])),
					html.Li(gomponents.Text(locales["customization_item2"])),
					html.Li(gomponents.Text(locales["customization_item3"])),
				),
			),
			html.Div(
				html.Class("flex-1 flex justify-center items-center"),
				html.Img(
					html.Src("/images/cogwheel.jpg"),
					html.Alt(locales["customization_alt"]),
					html.Class("rounded-xl shadow-lg w-full max-w-md dark:shadow-gray-800"),
				),
			),
		),
	)
}
