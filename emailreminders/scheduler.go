package emailreminders

import (
	"context"
	"fmt"
	"log"
	"math"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/jackc/pgx/v5/pgtype"
)

// EmailScheduler manages the scheduling and processing of email reminders
type EmailScheduler struct {
	db           *db.Queries
	emailService *EmailReminderService
	config       SchedulerConfig
	ticker       *time.Ticker
	stopChan     chan struct{}
	isRunning    bool
	mutex        sync.RWMutex
	metrics      SchedulerMetrics
}

// SchedulerConfig contains configuration for the email scheduler
type SchedulerConfig struct {
	CheckInterval   time.Duration // How often to check for pending reminders
	BatchSize       int           // Max reminders to process per batch
	MaxRetries      int           // Max retry attempts for failed emails
	RetryBackoff    time.Duration // Base delay between retries
	DeadLetterAfter time.Duration // When to move to dead letter queue
	MaxConcurrent   int           // Max concurrent email processing
}

// SchedulerMetrics tracks scheduler performance
type SchedulerMetrics struct {
	RemindersProcessed    int64
	RemindersSent         int64
	RemindersFailed       int64
	RemindersSkipped      int64
	DeadLetterCount       int64
	LastProcessedAt       time.Time
	AverageProcessingTime time.Duration
}

// NewEmailScheduler creates a new email scheduler
func NewEmailScheduler(database *db.Queries, emailService *EmailReminderService) (*EmailScheduler, error) {
	config := SchedulerConfig{
		CheckInterval:   30 * time.Second,
		BatchSize:       50,
		MaxRetries:      3,
		RetryBackoff:    5 * time.Minute,
		DeadLetterAfter: 24 * time.Hour,
		MaxConcurrent:   3,
	}

	// Override defaults with environment variables
	if intervalStr := os.Getenv("EMAIL_SCHEDULER_CHECK_INTERVAL"); intervalStr != "" {
		if interval, err := time.ParseDuration(intervalStr); err == nil {
			config.CheckInterval = interval
		}
	}

	if batchSizeStr := os.Getenv("EMAIL_SCHEDULER_BATCH_SIZE"); batchSizeStr != "" {
		if batchSize, err := strconv.Atoi(batchSizeStr); err == nil {
			config.BatchSize = batchSize
		}
	}

	if maxRetriesStr := os.Getenv("EMAIL_SCHEDULER_MAX_RETRIES"); maxRetriesStr != "" {
		if maxRetries, err := strconv.Atoi(maxRetriesStr); err == nil {
			config.MaxRetries = maxRetries
		}
	}

	if retryBackoffStr := os.Getenv("EMAIL_SCHEDULER_RETRY_BACKOFF"); retryBackoffStr != "" {
		if retryBackoff, err := time.ParseDuration(retryBackoffStr); err == nil {
			config.RetryBackoff = retryBackoff
		}
	}

	if maxConcurrentStr := os.Getenv("EMAIL_SCHEDULER_MAX_CONCURRENT"); maxConcurrentStr != "" {
		if maxConcurrent, err := strconv.Atoi(maxConcurrentStr); err == nil {
			config.MaxConcurrent = maxConcurrent
		}
	}

	return &EmailScheduler{
		db:           database,
		emailService: emailService,
		config:       config,
		stopChan:     make(chan struct{}),
		metrics:      SchedulerMetrics{},
	}, nil
}

// Start begins the email scheduler
func (s *EmailScheduler) Start(ctx context.Context) error {
	s.mutex.Lock()
	if s.isRunning {
		s.mutex.Unlock()
		return fmt.Errorf("scheduler already running")
	}
	s.isRunning = true
	s.mutex.Unlock()

	if !s.emailService.IsEnabled() {
		log.Println("Email scheduler disabled because email reminders are disabled")
		return nil
	}

	log.Printf("Starting email scheduler with %v check interval", s.config.CheckInterval)

	s.ticker = time.NewTicker(s.config.CheckInterval)
	s.stopChan = make(chan struct{})

	go func() {
		defer s.ticker.Stop()

		for {
			select {
			case <-s.ticker.C:
				if err := s.processPendingReminders(ctx); err != nil {
					log.Printf("Error processing reminders: %v", err)
				}
			case <-s.stopChan:
				log.Println("Email scheduler stopped")
				return
			case <-ctx.Done():
				log.Println("Email scheduler context cancelled")
				return
			}
		}
	}()

	return nil
}

// Stop stops the email scheduler
func (s *EmailScheduler) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return
	}

	close(s.stopChan)
	s.isRunning = false
}

// IsRunning returns whether the scheduler is currently running
func (s *EmailScheduler) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// GetMetrics returns current scheduler metrics
func (s *EmailScheduler) GetMetrics() SchedulerMetrics {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.metrics
}

// processPendingReminders fetches and processes pending email reminders
func (s *EmailScheduler) processPendingReminders(ctx context.Context) error {
	startTime := time.Now()

	// Get reminders that are due to be sent
	reminders, err := s.db.GetPendingEmailReminders(ctx, db.GetPendingEmailRemindersParams{
		ScheduledAt: pgtype.Timestamp{Time: time.Now(), Valid: true},
		Limit:       int32(s.config.BatchSize),
	})
	if err != nil {
		return fmt.Errorf("failed to fetch pending reminders: %w", err)
	}

	if len(reminders) == 0 {
		return nil // Nothing to process
	}

	log.Printf("Processing %d pending email reminders", len(reminders))

	// Process reminders concurrently but with rate limiting
	semaphore := make(chan struct{}, s.config.MaxConcurrent)
	var wg sync.WaitGroup

	for _, reminder := range reminders {
		wg.Add(1)
		go func(r db.GetPendingEmailRemindersRow) {
			defer wg.Done()
			semaphore <- struct{}{}        // Acquire
			defer func() { <-semaphore }() // Release

			if err := s.processReminder(ctx, r); err != nil {
				log.Printf("Failed to process reminder %d: %v", r.ID, err)
				s.incrementMetric("failed")
			} else {
				s.incrementMetric("processed")
			}
		}(reminder)
	}

	wg.Wait()

	// Update metrics
	processingTime := time.Since(startTime)
	s.mutex.Lock()
	s.metrics.LastProcessedAt = time.Now()
	s.metrics.AverageProcessingTime = processingTime
	s.mutex.Unlock()

	return nil
}

// processReminder processes a single email reminder
func (s *EmailScheduler) processReminder(ctx context.Context, reminder db.GetPendingEmailRemindersRow) error {
	// Get player info to check if notifications are enabled
	player, err := s.getPlayerByID(ctx, reminder.PlayerID)
	if err != nil {
		return s.markReminderFailed(ctx, reminder.ID, fmt.Errorf("failed to get player: %w", err))
	}

	if !player.EmailNotificationsEnabled {
		return s.markReminderSkipped(ctx, reminder.ID, "notifications disabled")
	}

	if !player.Email.Valid || player.Email.String == "" {
		return s.markReminderSkipped(ctx, reminder.ID, "no email address")
	}

	// Check user preferences for this reminder type
	if !s.emailService.isReminderTypeEnabled(player.EmailReminderPreferences, reminder.ReminderType) {
		return s.markReminderSkipped(ctx, reminder.ID, "reminder type disabled")
	}

	// Build email variables
	emailReminder := db.EmailReminder{
		ID:           reminder.ID,
		PlayerID:     reminder.PlayerID,
		ReminderType: reminder.ReminderType,
		ScheduledAt:  reminder.ScheduledAt,
		MatchID:      reminder.MatchID,
		SeasonID:     reminder.SeasonID,
	}

	variables, err := s.emailService.buildEmailVariables(ctx, emailReminder)
	if err != nil {
		return s.markReminderFailed(ctx, reminder.ID, fmt.Errorf("failed to build email variables: %w", err))
	}

	// Create and send email job
	emailJob := EmailJob{
		ReminderID:  reminder.ID,
		PlayerEmail: player.Email.String,
		Template:    s.emailService.getTemplateForReminderType(reminder.ReminderType),
		Variables:   variables,
		ScheduledAt: reminder.ScheduledAt.Time,
	}

	if err := s.emailService.SendEmail(ctx, emailJob); err != nil {
		return s.handleEmailFailure(ctx, reminder, err)
	}

	// Mark as successfully sent
	if err := s.db.MarkReminderSent(ctx, db.MarkReminderSentParams{
		ID:     reminder.ID,
		SentAt: pgtype.Timestamp{Time: time.Now(), Valid: true},
	}); err != nil {
		log.Printf("Failed to mark reminder %d as sent: %v", reminder.ID, err)
		return err
	}

	s.incrementMetric("sent")
	return nil
}

// handleEmailFailure handles email sending failures with retry logic
func (s *EmailScheduler) handleEmailFailure(ctx context.Context, reminder db.GetPendingEmailRemindersRow, err error) error {
	newRetryCount := reminder.RetryCount.Int32 + 1

	// Check if we should retry or give up
	if int(newRetryCount) >= s.config.MaxRetries {
		// Move to dead letter queue
		return s.moveToDeadLetterQueue(ctx, reminder.ID, err)
	}

	// Calculate next retry time with exponential backoff
	backoffDuration := time.Duration(math.Pow(2, float64(newRetryCount))) * s.config.RetryBackoff
	nextRetryAt := time.Now().Add(backoffDuration)

	// Update reminder for retry
	return s.db.ScheduleReminderRetry(ctx, db.ScheduleReminderRetryParams{
		ID:          reminder.ID,
		RetryCount:  pgtype.Int4{Int32: newRetryCount, Valid: true},
		NextRetryAt: pgtype.Timestamp{Time: nextRetryAt, Valid: true},
		LastError:   pgtype.Text{String: err.Error(), Valid: true},
	})
}

// markReminderFailed marks a reminder as failed
func (s *EmailScheduler) markReminderFailed(ctx context.Context, reminderID int32, err error) error {
	return s.db.MarkReminderSkipped(ctx, db.MarkReminderSkippedParams{
		ID:        reminderID,
		LastError: pgtype.Text{String: err.Error(), Valid: true},
	})
}

// markReminderSkipped marks a reminder as skipped
func (s *EmailScheduler) markReminderSkipped(ctx context.Context, reminderID int32, reason string) error {
	s.incrementMetric("skipped")
	return s.db.MarkReminderSkipped(ctx, db.MarkReminderSkippedParams{
		ID:        reminderID,
		LastError: pgtype.Text{String: reason, Valid: true},
	})
}

// moveToDeadLetterQueue moves a failed reminder to the dead letter queue
func (s *EmailScheduler) moveToDeadLetterQueue(ctx context.Context, reminderID int32, err error) error {
	s.incrementMetric("dead_letter")
	return s.db.MoveReminderToDeadLetter(ctx, db.MoveReminderToDeadLetterParams{
		ID:            reminderID,
		FailureReason: err.Error(),
	})
}

// getPlayerByID gets a player by ID
func (s *EmailScheduler) getPlayerByID(ctx context.Context, playerID int32) (db.Player, error) {
	return s.db.GetPlayerByID(ctx, playerID)
}

// incrementMetric safely increments a metric counter
func (s *EmailScheduler) incrementMetric(metricType string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	switch metricType {
	case "processed":
		s.metrics.RemindersProcessed++
	case "sent":
		s.metrics.RemindersSent++
	case "failed":
		s.metrics.RemindersFailed++
	case "skipped":
		s.metrics.RemindersSkipped++
	case "dead_letter":
		s.metrics.DeadLetterCount++
	}
}
