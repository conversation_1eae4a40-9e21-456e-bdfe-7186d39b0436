package emailreminders

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/mailgun"
	"github.com/jackc/pgx/v5/pgtype"
)

// EmailReminderService handles email reminder sending and management
type EmailReminderService struct {
	mailgunClient *mailgun.MailgunClient
	db            *db.Queries
	isEnabled     bool
	workerCount   int
	jobQueue      chan EmailJob
	stopChan      chan struct{}
}

// EmailJob represents a single email job to be processed
type EmailJob struct {
	ReminderID  int32
	PlayerEmail string
	Template    string
	Variables   map[string]string
	ScheduledAt time.Time
}

// EmailReminderPreferences represents the player's email preferences
type EmailReminderPreferences struct {
	Match24h        bool `json:"match_24h"`
	Match2h         bool `json:"match_2h"`
	ScheduleUpdates bool `json:"schedule_updates"`
	Results         bool `json:"results"`
}

// NewEmailReminderService creates a new email reminder service
func NewEmailReminderService(mailgunClient *mailgun.MailgunClient, database *db.Queries) (*EmailReminderService, error) {
	enabledStr := os.Getenv("EMAIL_REMINDERS_ENABLED")
	enabled, err := strconv.ParseBool(enabledStr)
	if err != nil {
		enabled = false // Default to disabled
	}

	workerCountStr := os.Getenv("EMAIL_WORKER_COUNT")
	workerCount, err := strconv.Atoi(workerCountStr)
	if err != nil {
		workerCount = 3 // Default to 3 workers
	}

	return &EmailReminderService{
		mailgunClient: mailgunClient,
		db:            database,
		isEnabled:     enabled,
		workerCount:   workerCount,
		jobQueue:      make(chan EmailJob, 100), // Buffer for 100 jobs
		stopChan:      make(chan struct{}),
	}, nil
}

// IsEnabled returns whether email reminders are enabled
func (s *EmailReminderService) IsEnabled() bool {
	return s.isEnabled && s.mailgunClient.IsEmailConfirmationEnabled()
}

// StartWorkers starts the background workers for processing email jobs
func (s *EmailReminderService) StartWorkers(ctx context.Context) {
	if !s.IsEnabled() {
		log.Println("Email reminders are disabled")
		return
	}

	log.Printf("Starting %d email reminder workers", s.workerCount)

	for i := 0; i < s.workerCount; i++ {
		go s.emailWorker(ctx, i)
	}
}

// StopWorkers stops all email workers
func (s *EmailReminderService) StopWorkers() {
	close(s.stopChan)
}

// emailWorker processes email jobs from the queue
func (s *EmailReminderService) emailWorker(ctx context.Context, workerID int) {
	log.Printf("Email worker %d started", workerID)

	for {
		select {
		case job := <-s.jobQueue:
			if err := s.processEmailJob(ctx, job); err != nil {
				log.Printf("Worker %d failed to process email job %d: %v", workerID, job.ReminderID, err)
			}
		case <-s.stopChan:
			log.Printf("Email worker %d stopped", workerID)
			return
		case <-ctx.Done():
			log.Printf("Email worker %d context cancelled", workerID)
			return
		}
	}
}

// processEmailJob sends the actual email
func (s *EmailReminderService) processEmailJob(ctx context.Context, job EmailJob) error {
	log.Printf("Processing email job %d for %s", job.ReminderID, job.PlayerEmail)

	emailParams := mailgun.EmailParams{
		Recipient: job.PlayerEmail,
		Subject:   s.getSubjectForTemplate(job.Template),
		Template:  job.Template,
		Variables: job.Variables,
	}

	if err := s.mailgunClient.SendEmail(ctx, emailParams); err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	log.Printf("Successfully sent email for reminder %d", job.ReminderID)
	return nil
}

// SendEmail queues an email job for processing
func (s *EmailReminderService) SendEmail(ctx context.Context, job EmailJob) error {
	if !s.IsEnabled() {
		return fmt.Errorf("email reminders are disabled")
	}

	select {
	case s.jobQueue <- job:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	default:
		return fmt.Errorf("email queue is full")
	}
}

// getSubjectForTemplate returns the email subject based on the template
func (s *EmailReminderService) getSubjectForTemplate(template string) string {
	subjects := map[string]string{
		"match-reminder-24h": "Match Reminder - Tomorrow",
		"match-reminder-2h":  "Match Reminder - In 2 Hours",
		"schedule-update":    "Schedule Update",
		"match-results":      "Match Results",
		"season-welcome":     "Welcome to New Season",
		"season-summary":     "Season Summary",
	}

	if subject, exists := subjects[template]; exists {
		return subject
	}
	return "Reminder"
}

// getTemplateForReminderType returns the Mailgun template name for a reminder type
func (s *EmailReminderService) getTemplateForReminderType(reminderType string) string {
	templates := map[string]string{
		"match_24h":       "match-reminder-24h",
		"match_2h":        "match-reminder-2h",
		"schedule_update": "schedule-update",
		"match_results":   "match-results",
		"season_welcome":  "season-welcome",
		"season_summary":  "season-summary",
	}

	if template, exists := templates[reminderType]; exists {
		return template
	}
	return "default-reminder"
}

// isReminderTypeEnabled checks if a specific reminder type is enabled for a player
func (s *EmailReminderService) isReminderTypeEnabled(preferencesJSON []byte, reminderType string) bool {
	if len(preferencesJSON) == 0 {
		// Default preferences if none set
		return true
	}

	var prefs EmailReminderPreferences
	if err := json.Unmarshal(preferencesJSON, &prefs); err != nil {
		log.Printf("Failed to unmarshal email preferences: %v", err)
		return true // Default to enabled if parsing fails
	}

	switch reminderType {
	case "match_24h":
		return prefs.Match24h
	case "match_2h":
		return prefs.Match2h
	case "schedule_update":
		return prefs.ScheduleUpdates
	case "match_results":
		return prefs.Results
	default:
		return true
	}
}

// buildEmailVariables creates template variables for different reminder types
func (s *EmailReminderService) buildEmailVariables(ctx context.Context, reminder db.EmailReminder) (map[string]string, error) {
	variables := make(map[string]string)

	// Get player info
	player, err := s.db.GetPlayerByID(ctx, reminder.PlayerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get player: %w", err)
	}

	variables["player_name"] = player.Name
	variables["reminder_type"] = reminder.ReminderType

	// Add match-specific variables if this is a match reminder
	if reminder.MatchID.Valid {
		match, err := s.db.GetMatch(ctx, reminder.MatchID.Int32)
		if err != nil {
			return nil, fmt.Errorf("failed to get match: %w", err)
		}

		if match.MatchDate.Valid {
			variables["match_date"] = match.MatchDate.Time.Format("Monday, January 2, 2006")
			variables["match_time"] = match.MatchDate.Time.Format("3:04 PM")
		}

		// Get player names for the match
		if match.PlayerId1.Valid {
			player1, err := s.db.GetPlayerByID(ctx, match.PlayerId1.Int32)
			if err == nil {
				variables["player1_name"] = player1.Name
			}
		}
		if match.PlayerId2.Valid {
			player2, err := s.db.GetPlayerByID(ctx, match.PlayerId2.Int32)
			if err == nil {
				variables["player2_name"] = player2.Name
			}
		}
	}

	// Add season-specific variables if this is a season reminder
	if reminder.SeasonID.Valid {
		season, err := s.db.GetSeasonByID(ctx, reminder.SeasonID.Int32)
		if err != nil {
			return nil, fmt.Errorf("failed to get season: %w", err)
		}

		variables["season_name"] = season.Name
		variables["season_type"] = season.SeasonType
	}

	return variables, nil
}

// ScheduleMatchReminders schedules email reminders for a match
func (s *EmailReminderService) ScheduleMatchReminders(ctx context.Context, match db.Match, players []db.Player) error {
	if !s.IsEnabled() {
		return nil // Skip if disabled
	}

	if !match.MatchDate.Valid {
		return fmt.Errorf("match has no valid date")
	}

	matchDate := match.MatchDate.Time

	for _, player := range players {
		if !player.EmailNotificationsEnabled {
			continue
		}

		if !player.Email.Valid || player.Email.String == "" {
			continue
		}

		// Check if player has email preferences that allow reminders
		if !s.isReminderTypeEnabled(player.EmailReminderPreferences, "match_24h") &&
			!s.isReminderTypeEnabled(player.EmailReminderPreferences, "match_2h") {
			continue
		}

		// Schedule 24-hour reminder
		if s.isReminderTypeEnabled(player.EmailReminderPreferences, "match_24h") {
			reminder24h := db.ScheduleEmailReminderParams{
				PlayerID:     player.ID,
				ReminderType: "match_24h",
				ScheduledAt:  pgtype.Timestamp{Time: matchDate.Add(-24 * time.Hour), Valid: true},
				MatchID:      pgtype.Int4{Int32: match.ID, Valid: true},
				SeasonID:     match.SeasonID,
			}

			if _, err := s.db.ScheduleEmailReminder(ctx, reminder24h); err != nil {
				log.Printf("Failed to schedule 24h reminder for player %d: %v", player.ID, err)
			}
		}

		// Schedule 2-hour reminder
		if s.isReminderTypeEnabled(player.EmailReminderPreferences, "match_2h") {
			reminder2h := db.ScheduleEmailReminderParams{
				PlayerID:     player.ID,
				ReminderType: "match_2h",
				ScheduledAt:  pgtype.Timestamp{Time: matchDate.Add(-2 * time.Hour), Valid: true},
				MatchID:      pgtype.Int4{Int32: match.ID, Valid: true},
				SeasonID:     match.SeasonID,
			}

			if _, err := s.db.ScheduleEmailReminder(ctx, reminder2h); err != nil {
				log.Printf("Failed to schedule 2h reminder for player %d: %v", player.ID, err)
			}
		}
	}

	return nil
}
