#!/bin/bash

CONTAINER_NAME=postgres
VOLUME_NAME=postgres_data

# Stop and remove the container if it exists
echo "Stopping PostgreSQL container..."
if podman rm -f $CONTAINER_NAME 2>/dev/null; then
    echo "PostgreSQL container stopped and removed"
else
    echo "No PostgreSQL container found to stop"
fi

# Remove the volume
echo "Removing PostgreSQL volume..."
if podman volume rm -f $VOLUME_NAME 2>/dev/null; then
    echo "PostgreSQL volume removed successfully"
else
    echo "No PostgreSQL volume found to remove (or volume is in use)"
fi

echo "PostgreSQL volume data cleared successfully!"