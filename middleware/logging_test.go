package middleware

import (
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/j-em/coachpad/pkg/logger"
	"github.com/labstack/echo/v4"
)

func TestRequestMetrics_ToLogAttrs(t *testing.T) {
	tests := []struct {
		name     string
		metrics  RequestMetrics
		expected map[string]interface{}
	}{
		{
			name: "basic metrics without user agent and error",
			metrics: RequestMetrics{
				Duration:     100 * time.Millisecond,
				Status:       200,
				Method:       "GET",
				Path:         "/test",
				UserID:       123,
				IP:           "***********",
				ResponseSize: 1024,
				RequestSize:  512,
			},
			expected: map[string]interface{}{
				"duration_ms":   int64(100),
				"status":        200,
				"method":        "GET",
				"path":          "/test",
				"user_id":       int32(123),
				"ip":            "***********",
				"response_size": int64(1024),
				"request_size":  int64(512),
			},
		},
		{
			name: "metrics with user agent and error type",
			metrics: RequestMetrics{
				Duration:     50 * time.Millisecond,
				Status:       400,
				Method:       "POST",
				Path:         "/api/test",
				UserID:       456,
				UserAgent:    "Mozilla/5.0",
				IP:           "********",
				ResponseSize: 256,
				RequestSize:  128,
				ErrorType:    "validation",
			},
			expected: map[string]interface{}{
				"duration_ms":   int64(50),
				"status":        400,
				"method":        "POST",
				"path":          "/api/test",
				"user_id":       int32(456),
				"user_agent":    "Mozilla/5.0",
				"ip":            "********",
				"response_size": int64(256),
				"request_size":  int64(128),
				"error_type":    "validation",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attrs := tt.metrics.ToLogAttrs()

			// Convert slice to map for easier testing
			attrMap := make(map[string]interface{})
			for i := 0; i < len(attrs); i += 2 {
				key := attrs[i].(string)
				value := attrs[i+1]
				attrMap[key] = value
			}

			for key, expectedValue := range tt.expected {
				if actualValue, exists := attrMap[key]; !exists {
					t.Errorf("Expected key %s not found in attributes", key)
				} else if actualValue != expectedValue {
					t.Errorf("For key %s, expected %v, got %v", key, expectedValue, actualValue)
				}
			}

			// Check that no unexpected keys are present
			for key := range attrMap {
				if _, expected := tt.expected[key]; !expected {
					t.Errorf("Unexpected key %s found in attributes", key)
				}
			}
		})
	}
}

func TestClassifyError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		status   int
		expected string
	}{
		{
			name:     "no error",
			err:      nil,
			status:   200,
			expected: "",
		},
		{
			name:     "validation error - bad request",
			err:      echo.NewHTTPError(http.StatusBadRequest, "invalid input"),
			status:   400,
			expected: "validation",
		},
		{
			name:     "validation error - unprocessable entity",
			err:      echo.NewHTTPError(http.StatusUnprocessableEntity, "validation failed"),
			status:   422,
			expected: "validation",
		},
		{
			name:     "business error - unauthorized",
			err:      echo.NewHTTPError(http.StatusUnauthorized, "unauthorized"),
			status:   401,
			expected: "business",
		},
		{
			name:     "business error - forbidden",
			err:      echo.NewHTTPError(http.StatusForbidden, "forbidden"),
			status:   403,
			expected: "business",
		},
		{
			name:     "business error - not found",
			err:      echo.NewHTTPError(http.StatusNotFound, "not found"),
			status:   404,
			expected: "business",
		},
		{
			name:     "business error - conflict",
			err:      echo.NewHTTPError(http.StatusConflict, "conflict"),
			status:   409,
			expected: "business",
		},
		{
			name:     "system error - internal server error",
			err:      echo.NewHTTPError(http.StatusInternalServerError, "internal error"),
			status:   500,
			expected: "system",
		},
		{
			name:     "system error - service unavailable",
			err:      echo.NewHTTPError(http.StatusServiceUnavailable, "service unavailable"),
			status:   503,
			expected: "system",
		},
		{
			name:     "system error - successful status with error",
			err:      echo.NewHTTPError(http.StatusOK, "unexpected error"),
			status:   200,
			expected: "system",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := classifyError(tt.err, tt.status)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestExtractUserID(t *testing.T) {
	tests := []struct {
		name     string
		userID   interface{}
		expected int32
	}{
		{
			name:     "valid user ID",
			userID:   int32(123),
			expected: 123,
		},
		{
			name:     "no user ID in context",
			userID:   nil,
			expected: 0,
		},
		{
			name:     "invalid user ID type",
			userID:   "123",
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			if tt.userID != nil {
				c.Set(UserIDKey, tt.userID)
			}

			result := extractUserID(c)
			if result != tt.expected {
				t.Errorf("Expected %d, got %d", tt.expected, result)
			}
		})
	}
}

func TestCreateRequestLoggingMiddleware(t *testing.T) {
	// Create a simple logger for testing
	appLogger := logger.New(logger.Config{
		Level:    slog.LevelInfo,
		Format:   logger.FormatJSON,
		Output:   logger.OutputConsole,
		FilePath: "",
	})

	// Test that middleware doesn't crash and processes requests
	t.Run("middleware processes request", func(t *testing.T) {
		os.Setenv("VERBOSE_REQUEST_LOGS", "false")
		defer os.Unsetenv("VERBOSE_REQUEST_LOGS")

		middleware := CreateRequestLoggingMiddleware(appLogger)

		e := echo.New()
		req := httptest.NewRequest(http.MethodPost, "/api/test", strings.NewReader("test body"))
		req.Header.Set("Content-Length", "9")
		req.Header.Set("User-Agent", "test-agent")
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		// Set a user ID
		c.Set(UserIDKey, int32(123))

		// Create a handler that writes some response
		handler := func(c echo.Context) error {
			return c.String(http.StatusOK, "success")
		}

		err := middleware(handler)(c)
		if err != nil {
			t.Fatalf("Middleware returned error: %v", err)
		}

		// Verify response
		if rec.Code != http.StatusOK {
			t.Errorf("Expected status 200, got %d", rec.Code)
		}

		if rec.Body.String() != "success" {
			t.Errorf("Expected body 'success', got %s", rec.Body.String())
		}
	})

	// Test that middleware handles errors properly
	t.Run("middleware handles errors", func(t *testing.T) {
		middleware := CreateRequestLoggingMiddleware(appLogger)

		e := echo.New()
		req := httptest.NewRequest(http.MethodPost, "/api/test", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		// Create a handler that returns an error
		handler := func(c echo.Context) error {
			return echo.NewHTTPError(http.StatusBadRequest, "validation failed")
		}

		err := middleware(handler)(c)
		if err == nil {
			t.Error("Expected error from handler")
		}

		// Verify that the error is properly returned
		httpErr, ok := err.(*echo.HTTPError)
		if !ok {
			t.Error("Expected HTTP error")
		}

		if httpErr.Code != http.StatusBadRequest {
			t.Errorf("Expected status 400, got %d", httpErr.Code)
		}
	})
}
