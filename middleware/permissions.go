package middleware

import (
	"context"
	"net/http"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/labstack/echo/v4"
)

// PermissionLevel represents the different levels of permissions
type PermissionLevel int

const (
	PermissionViewer PermissionLevel = iota
	PermissionManager
	PermissionAdmin
	PermissionOwner
)

// Context keys for permission-related data
const (
	UserPermissionLevelKey = "userPermissionLevel"
	SeasonIDKey            = "seasonID"
)

// PermissionMiddleware handles season-level permission checks
type PermissionMiddleware struct {
	Queries *db.Queries
}

// NewPermissionMiddleware creates a new permission middleware instance
func NewPermissionMiddleware(queries *db.Queries) *PermissionMiddleware {
	return &PermissionMiddleware{
		Queries: queries,
	}
}

// RequireSeasonPermission returns middleware that checks if user has required permission level for a season
func (pm *PermissionMiddleware) RequireSeasonPermission(level PermissionLevel) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID := c.Get(UserIDKey).(int32)
			seasonIDStr := c.Param("id")
			if seasonIDStr == "" {
				return echo.NewHTTPError(http.StatusBadRequest, "Season ID is required")
			}

			seasonID, err := strconv.Atoi(seasonIDStr)
			if err != nil {
				return echo.NewHTTPError(http.StatusBadRequest, "Invalid season ID")
			}

			hasPermission, userLevel, err := pm.checkSeasonPermission(
				c.Request().Context(),
				userID,
				int32(seasonID),
				level,
			)

			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "Permission check failed")
			}

			if !hasPermission {
				return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
			}

			// Store permission context for handlers
			c.Set(UserPermissionLevelKey, userLevel)
			c.Set(SeasonIDKey, int32(seasonID))

			return next(c)
		}
	}
}

// checkSeasonPermission checks if user has required permission level for a season
func (pm *PermissionMiddleware) checkSeasonPermission(ctx context.Context, userID, seasonID int32, required PermissionLevel) (bool, PermissionLevel, error) {
	// Use the CheckSeasonAccess query to get user's permission level
	result, err := pm.Queries.CheckSeasonAccess(ctx, db.CheckSeasonAccessParams{
		ID:     seasonID,
		UserID: userID,
	})

	if err != nil {
		// User has no access to this season
		return false, 0, nil
	}

	// result is already PermissionLevelEnum, no conversion needed
	userLevel := parsePermissionLevel(result)
	return userLevel >= required, userLevel, nil
}

// parsePermissionLevel converts database enum to PermissionLevel
func parsePermissionLevel(dbLevel db.PermissionLevelEnum) PermissionLevel {
	switch dbLevel {
	case db.PermissionLevelEnumViewer:
		return PermissionViewer
	case db.PermissionLevelEnumManager:
		return PermissionManager
	case db.PermissionLevelEnumAdmin:
		return PermissionAdmin
	case db.PermissionLevelEnumOwner:
		return PermissionOwner
	default:
		return PermissionViewer
	}
}

// CanPerform checks if a permission level can perform an action requiring another level
func (p PermissionLevel) CanPerform(required PermissionLevel) bool {
	return p >= required
}

// GetUserPermissionLevel retrieves the user's permission level from context
func GetUserPermissionLevel(c echo.Context) PermissionLevel {
	level, ok := c.Get(UserPermissionLevelKey).(PermissionLevel)
	if !ok {
		return PermissionViewer
	}
	return level
}

// GetSeasonID retrieves the season ID from context
func GetSeasonID(c echo.Context) int32 {
	seasonID, ok := c.Get(SeasonIDKey).(int32)
	if !ok {
		return 0
	}
	return seasonID
}

// String returns the string representation of permission level
func (p PermissionLevel) String() string {
	switch p {
	case PermissionViewer:
		return "viewer"
	case PermissionManager:
		return "manager"
	case PermissionAdmin:
		return "admin"
	case PermissionOwner:
		return "owner"
	default:
		return "viewer"
	}
}
