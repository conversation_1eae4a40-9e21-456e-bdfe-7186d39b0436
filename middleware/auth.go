package middleware

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/sessions"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
)

// UserIDKey is the context key for user ID
const UserIDKey = "userID"

// StytchJWTConfig holds the configuration for the Stytch JWT middleware
type StytchJWTConfig struct {
	StytchClient *stytchapi.API
}

// StytchJWT returns a middleware that validates JWT tokens with Stytch
// and sets the user ID in the context if valid
func StytchJWT(config StytchJWTConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Extract token from Authorization header
			auth := c.Request().Header.Get("Authorization")
			if auth == "" {
				// Also check cookies if header is not present
				cookie, err := c.<PERSON>ie("token")
				if err != nil || cookie.Value == "" {
					return echo.NewHTTPError(http.StatusUnauthorized, "missing authentication")
				}
				auth = cookie.Value
			} else {
				parts := strings.SplitN(auth, " ", 2)
				if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
					return echo.NewHTTPError(http.StatusUnauthorized, "invalid authorization header format")
				}
				auth = parts[1]
			}

			// Validate JWT token with Stytch
			authParams := &sessions.AuthenticateParams{
				SessionJWT: auth,
			}

			resp, err := config.StytchClient.Sessions.Authenticate(c.Request().Context(), authParams)
			if err != nil {
				log.Printf("Stytch JWT authentication failed:")
				log.Printf("  Error: %v", err)
				log.Printf("  Token length: %d characters", len(auth))
				log.Printf("  Token prefix: %s...", func() string {
					if len(auth) > 20 {
						return auth[:20]
					}
					return auth
				}())
				log.Printf("  Request path: %s", c.Request().URL.Path)
				log.Printf("  Request method: %s", c.Request().Method)

				// Check for common error types
				errStr := err.Error()
				if strings.Contains(errStr, "404") {
					log.Printf("  404 error suggests JWKS endpoint issues or invalid project configuration")
				} else if strings.Contains(errStr, "expired") {
					log.Printf("  Token appears to be expired")
				} else if strings.Contains(errStr, "invalid") {
					log.Printf("  Token appears to be invalid or malformed")
				}

				return echo.NewHTTPError(http.StatusUnauthorized, "invalid or expired token")
			}

			// Set the user ID in the context, ensuring it's converted from float64 to int32
			userId, ok := resp.User.TrustedMetadata["userId"]
			if !ok {
				log.Printf("Stytch user metadata issues:")
				log.Printf("  userId not found in trusted metadata")
				log.Printf("  Available trusted metadata keys: %v", func() []string {
					keys := make([]string, 0, len(resp.User.TrustedMetadata))
					for k := range resp.User.TrustedMetadata {
						keys = append(keys, k)
					}
					return keys
				}())
				log.Printf("  User ID from Stytch: %s", resp.User.UserID)
				return echo.NewHTTPError(http.StatusInternalServerError, "userId not found in trusted metadata")
			}

			// Convert userId from float64 to int32 (Stytch always returns float64 from JSON)
			floatUserId, ok := userId.(float64)
			if !ok {
				log.Printf("Stytch userId conversion error:")
				log.Printf("  userId type: %T", userId)
				log.Printf("  userId value: %v", userId)
				return echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("userId is not a float64, got %T", userId))
			}
			intUserId := int32(floatUserId)

			c.Set(UserIDKey, intUserId)

			// Continue with the next handler
			return next(c)
		}
	}
}

// GetUserID retrieves the user ID from the context
func GetUserID(c echo.Context) int32 {
	userID, ok := c.Get(UserIDKey).(int32)
	if !ok {
		return 0
	}
	return userID
}

// GetOptionalUserID checks if a user is authenticated without requiring authentication
// Returns user ID if authenticated, 0 if not authenticated, error for validation issues
func GetOptionalUserID(c echo.Context, stytchClient *stytchapi.API) (int32, error) {
	var token string

	// Try to get token from cookie first
	cookie, err := c.Cookie("token")
	if err != nil || cookie.Value == "" {
		// Also check Authorization header
		auth := c.Request().Header.Get("Authorization")
		if auth == "" {
			return 0, nil // No token found, user not authenticated
		}
		parts := strings.SplitN(auth, " ", 2)
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			return 0, nil // Invalid header format, treat as not authenticated
		}
		token = parts[1]
	} else {
		token = cookie.Value
	}

	// Validate JWT token with Stytch
	authParams := &sessions.AuthenticateParams{
		SessionJWT: token,
	}

	resp, err := stytchClient.Sessions.Authenticate(c.Request().Context(), authParams)
	if err != nil {
		log.Printf("Stytch optional authentication failed:")
		log.Printf("  Error: %v", err)
		log.Printf("  Token length: %d characters", len(token))
		log.Printf("  Request path: %s", c.Request().URL.Path)

		// Check for common error types
		errStr := err.Error()
		if strings.Contains(errStr, "404") {
			log.Printf("  404 error suggests JWKS endpoint issues or invalid project configuration")
		}

		return 0, nil // Invalid token, treat as not authenticated
	}

	// Extract user ID from trusted metadata
	userId, ok := resp.User.TrustedMetadata["userId"]
	if !ok {
		log.Printf("Optional auth - userId not found in trusted metadata")
		log.Printf("  Available trusted metadata keys: %v", func() []string {
			keys := make([]string, 0, len(resp.User.TrustedMetadata))
			for k := range resp.User.TrustedMetadata {
				keys = append(keys, k)
			}
			return keys
		}())
		return 0, nil // No userId in metadata, treat as not authenticated
	}

	// Convert userId from float64 to int32
	floatUserId, ok := userId.(float64)
	if !ok {
		log.Printf("Optional auth - userId conversion error:")
		log.Printf("  userId type: %T", userId)
		log.Printf("  userId value: %v", userId)
		return 0, nil // Invalid userId format, treat as not authenticated
	}

	return int32(floatUserId), nil
}
