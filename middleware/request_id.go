package middleware

import (
	"github.com/j-em/coachpad/pkg/logger"
	"github.com/labstack/echo/v4"
)

func RequestID() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			requestID := logger.GenerateRequestID()

			ctx := logger.ContextWithRequestID(c.Request().Context(), requestID)
			c.SetRequest(c.Request().WithContext(ctx))

			c.Response().Header().Set("X-Request-ID", requestID)

			return next(c)
		}
	}
}
