package middleware

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	"golang.org/x/crypto/bcrypt"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/pkg/logger"
)

// APIKeyConfig holds the configuration for the API key middleware
type APIKeyConfig struct {
	Queries *db.Queries
	Logger  *logger.Logger
}

// RateLimiter implements a simple in-memory rate limiter with automatic cleanup
type RateLimiter struct {
	mu              sync.RWMutex
	limits          map[string]*rateLimitEntry
	cleanupInterval time.Duration
	ticker          *time.Ticker
	stopChan        chan struct{}
	isRunning       bool
	metrics         RateLimiterMetrics
}

// RateLimiterMetrics tracks rate limiter performance
type RateLimiterMetrics struct {
	ActiveEntries    int
	CleanupRuns      int64
	EntriesCleanedUp int64
	LastCleanupAt    time.Time
	CleanupDuration  time.Duration
}

type rateLimitEntry struct {
	hourlyCount int
	minuteCount int
	hourlyReset time.Time
	minuteReset time.Time
}

var globalRateLimiter = newRateLimiter()

// newRateLimiter creates a new RateLimiter with default configuration
func newRateLimiter() *RateLimiter {
	// Default cleanup interval of 1 hour
	cleanupInterval := 1 * time.Hour

	// Override with environment variable if set
	if intervalStr := os.Getenv("RATE_LIMITER_CLEANUP_INTERVAL"); intervalStr != "" {
		if interval, err := time.ParseDuration(intervalStr); err == nil {
			cleanupInterval = interval
		} else {
			log.Printf("Warning: Invalid RATE_LIMITER_CLEANUP_INTERVAL value '%s', using default of %v", intervalStr, cleanupInterval)
		}
	}

	return &RateLimiter{
		limits:          make(map[string]*rateLimitEntry),
		cleanupInterval: cleanupInterval,
		stopChan:        make(chan struct{}),
		metrics:         RateLimiterMetrics{},
	}
}

// APIKeyAuth returns a middleware that validates API keys
// and sets the user ID in the context if valid
func APIKeyAuth(config APIKeyConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Extract API key from headers
			var apiKey string

			// Try Authorization header first
			auth := c.Request().Header.Get("Authorization")
			if auth != "" {
				parts := strings.SplitN(auth, " ", 2)
				if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
					apiKey = parts[1]
				}
			}

			// Try X-API-Key header as fallback
			if apiKey == "" {
				apiKey = c.Request().Header.Get("X-API-Key")
			}

			if apiKey == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]interface{}{
					"error": map[string]interface{}{
						"code":    "MISSING_API_KEY",
						"message": "API key required. Provide via Authorization: Bearer <key> or X-API-Key header",
					},
				})
			}

			// Validate API key format
			if !isValidAPIKeyFormat(apiKey) {
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]interface{}{
					"error": map[string]interface{}{
						"code":    "INVALID_API_KEY_FORMAT",
						"message": "Invalid API key format",
					},
				})
			}

			// We need to find the API key by comparing against all stored hashes
			// since bcrypt hashes are unique each time, we can't lookup by hash directly
			dbAPIKey, err := findAPIKeyByComparison(c.Request().Context(), config.Queries, apiKey)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]interface{}{
					"error": map[string]interface{}{
						"code":    "INVALID_API_KEY",
						"message": "Invalid or revoked API key",
					},
				})
			}

			// Check if API key is expired
			if dbAPIKey.ExpiresAt.Valid && dbAPIKey.ExpiresAt.Time.Before(time.Now()) {
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]interface{}{
					"error": map[string]interface{}{
						"code":    "EXPIRED_API_KEY",
						"message": "API key has expired",
					},
				})
			}

			// Apply rate limiting
			if !globalRateLimiter.Allow(fmt.Sprintf("api_key_%d", dbAPIKey.ID)) {
				return echo.NewHTTPError(http.StatusTooManyRequests, map[string]interface{}{
					"error": map[string]interface{}{
						"code":    "RATE_LIMIT_EXCEEDED",
						"message": "Rate limit exceeded. Max 1000 requests per hour, 100 per minute",
					},
				})
			}

			// Update last used timestamp (async to not slow down the request)
			go func() {
				ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()

				err := config.Queries.UpdateAPIKeyLastUsed(ctx, dbAPIKey.ID)
				if err != nil {
					config.Logger.LogError(ctx, "Failed to update API key last used timestamp", err,
						"api_key_id", dbAPIKey.ID,
						"user_id", dbAPIKey.UserID,
						"operation", "api_key_last_used_update")
				}
			}()

			// Set the user ID in the context (same as JWT middleware)
			c.Set(UserIDKey, dbAPIKey.UserID)

			// Continue with the next handler
			return next(c)
		}
	}
}

// isValidAPIKeyFormat validates the API key format
func isValidAPIKeyFormat(key string) bool {
	// Expected format: cpb_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx or cpb_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
	if len(key) != 44 {
		return false
	}

	if !strings.HasPrefix(key, "cpb_live_") && !strings.HasPrefix(key, "cpb_test_") {
		return false
	}

	return true
}

// findAPIKeyByComparison finds an API key by comparing the provided key against stored hashes
func findAPIKeyByComparison(ctx context.Context, queries *db.Queries, providedKey string) (db.ApiKey, error) {
	// Get all active API keys
	// TODO: Optimize this by adding a prefix-based query for better performance
	allKeys, err := queries.GetAllActiveAPIKeys(ctx)
	if err != nil {
		return db.ApiKey{}, err
	}

	// Compare the provided key against each stored hash
	for _, key := range allKeys {
		if strings.HasPrefix(providedKey, key.Prefix) {
			err := bcrypt.CompareHashAndPassword([]byte(key.KeyHash), []byte(providedKey))
			if err == nil {
				return key, nil
			}
		}
	}

	return db.ApiKey{}, fmt.Errorf("API key not found")
}

// HashAPIKey creates a bcrypt hash of an API key for storage
func HashAPIKey(key string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(key), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// Allow checks if a request is allowed based on rate limits
func (rl *RateLimiter) Allow(key string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	entry, exists := rl.limits[key]

	if !exists {
		rl.limits[key] = &rateLimitEntry{
			hourlyCount: 1,
			minuteCount: 1,
			hourlyReset: now.Add(time.Hour),
			minuteReset: now.Add(time.Minute),
		}
		return true
	}

	// Reset counters if time has passed
	if now.After(entry.hourlyReset) {
		entry.hourlyCount = 0
		entry.hourlyReset = now.Add(time.Hour)
	}

	if now.After(entry.minuteReset) {
		entry.minuteCount = 0
		entry.minuteReset = now.Add(time.Minute)
	}

	// Check limits
	if entry.hourlyCount >= 1000 || entry.minuteCount >= 100 {
		return false
	}

	// Increment counters
	entry.hourlyCount++
	entry.minuteCount++

	return true
}

// Start begins the rate limiter cleanup routine
func (rl *RateLimiter) Start(ctx context.Context) error {
	rl.mu.Lock()
	if rl.isRunning {
		rl.mu.Unlock()
		return fmt.Errorf("rate limiter cleanup already running")
	}
	rl.isRunning = true
	rl.mu.Unlock()

	log.Printf("Starting rate limiter cleanup with %v interval", rl.cleanupInterval)

	rl.ticker = time.NewTicker(rl.cleanupInterval)
	rl.stopChan = make(chan struct{})

	go func() {
		defer rl.ticker.Stop()

		for {
			select {
			case <-rl.ticker.C:
				rl.cleanupExpiredEntries()
			case <-rl.stopChan:
				log.Println("Rate limiter cleanup stopped")
				return
			case <-ctx.Done():
				log.Println("Rate limiter cleanup context cancelled")
				return
			}
		}
	}()

	return nil
}

// Stop stops the rate limiter cleanup routine
func (rl *RateLimiter) Stop() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if !rl.isRunning {
		return
	}

	close(rl.stopChan)
	rl.isRunning = false
}

// IsRunning returns whether the cleanup routine is currently running
func (rl *RateLimiter) IsRunning() bool {
	rl.mu.RLock()
	defer rl.mu.RUnlock()
	return rl.isRunning
}

// GetMetrics returns current rate limiter metrics
func (rl *RateLimiter) GetMetrics() RateLimiterMetrics {
	rl.mu.RLock()
	defer rl.mu.RUnlock()
	// Update active entries count
	rl.metrics.ActiveEntries = len(rl.limits)
	return rl.metrics
}

// cleanupExpiredEntries removes old rate limit entries (private method called by background routine)
func (rl *RateLimiter) cleanupExpiredEntries() {
	startTime := time.Now()

	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	initialCount := len(rl.limits)
	cleanedCount := 0

	// More aggressive cleanup: remove entries that are 1 hour past their expiry
	cleanupThreshold := 1 * time.Hour

	for key, entry := range rl.limits {
		// Remove if both counters have expired and it's been at least cleanupThreshold since expiry
		if now.After(entry.hourlyReset.Add(cleanupThreshold)) && now.After(entry.minuteReset.Add(cleanupThreshold)) {
			delete(rl.limits, key)
			cleanedCount++
		}
	}

	// Update metrics
	rl.metrics.CleanupRuns++
	rl.metrics.EntriesCleanedUp += int64(cleanedCount)
	rl.metrics.LastCleanupAt = now
	rl.metrics.CleanupDuration = time.Since(startTime)

	if cleanedCount > 0 {
		log.Printf("Rate limiter cleanup: removed %d expired entries (had %d, now %d) in %v",
			cleanedCount, initialCount, len(rl.limits), rl.metrics.CleanupDuration)
	}
}

// CleanupExpiredEntries removes old rate limit entries (public method for manual cleanup)
func (rl *RateLimiter) CleanupExpiredEntries() {
	rl.cleanupExpiredEntries()
}

// StartRateLimiterCleanup starts the global rate limiter cleanup routine
func StartRateLimiterCleanup(ctx context.Context) error {
	return globalRateLimiter.Start(ctx)
}

// StopRateLimiterCleanup stops the global rate limiter cleanup routine
func StopRateLimiterCleanup() {
	globalRateLimiter.Stop()
}

// GetRateLimiterMetrics returns metrics for the global rate limiter
func GetRateLimiterMetrics() RateLimiterMetrics {
	return globalRateLimiter.GetMetrics()
}
