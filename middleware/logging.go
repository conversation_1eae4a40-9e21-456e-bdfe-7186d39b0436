package middleware

import (
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/j-em/coachpad/pkg/logger"
	"github.com/labstack/echo/v4"
)

// RequestMetrics contains comprehensive metrics for HTTP requests
type RequestMetrics struct {
	Duration     time.Duration
	Status       int
	Method       string
	Path         string
	UserID       int32
	UserAgent    string
	IP           string
	ResponseSize int64
	RequestSize  int64
	ErrorType    string // "business", "validation", "system"
}

// ToLogAttrs converts RequestMetrics to log attributes
func (m *RequestMetrics) ToLogAttrs() []any {
	attrs := []any{
		"duration_ms", m.Duration.Milliseconds(),
		"status", m.Status,
		"method", m.Method,
		"path", m.Path,
		"user_id", m.UserID,
		"ip", m.IP,
		"response_size", m.ResponseSize,
		"request_size", m.RequestSize,
	}

	// Only include UserAgent if it's not empty (verbose mode)
	if m.UserAgent != "" {
		attrs = append(attrs, "user_agent", m.UserAgent)
	}

	// Only include ErrorType if it's not empty
	if m.ErrorType != "" {
		attrs = append(attrs, "error_type", m.ErrorType)
	}

	return attrs
}

func shouldLogRequest(path string) bool {
	// Skip static assets
	staticPaths := []string{"/assets/", "/images/", "/favicon.ico"}
	for _, staticPath := range staticPaths {
		if strings.HasPrefix(path, staticPath) {
			return false
		}
	}
	return true
}

func extractUserID(c echo.Context) int32 {
	// Try to get user ID from JWT context (set by auth middleware)
	if userID := c.Get(UserIDKey); userID != nil {
		if uid, ok := userID.(int32); ok {
			return uid
		}
	}
	return 0 // 0 indicates anonymous user
}

// classifyError determines the type of error based on the error and HTTP status
func classifyError(err error, status int) string {
	if err == nil {
		return ""
	}

	// Classification based on HTTP status codes
	switch {
	case status >= 400 && status < 500:
		// 4xx errors are typically client/validation errors
		switch status {
		case 400, 422: // Bad Request, Unprocessable Entity
			return "validation"
		case 401, 403: // Unauthorized, Forbidden
			return "business"
		case 404: // Not Found
			return "business"
		case 409: // Conflict
			return "business"
		default:
			return "validation"
		}
	case status >= 500:
		// 5xx errors are system errors
		return "system"
	default:
		// For successful status codes but with an error, classify as system error
		return "system"
	}
}

func CreateRequestLoggingMiddleware(appLogger *logger.Logger) echo.MiddlewareFunc {
	verboseLogging := os.Getenv("VERBOSE_REQUEST_LOGS") == "true"

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			path := c.Request().URL.Path
			method := c.Request().Method

			// Skip logging for static assets unless verbose mode
			if !shouldLogRequest(path) && !verboseLogging {
				return next(c)
			}

			start := time.Now()

			// Get request size from Content-Length header
			var requestSize int64
			if contentLength := c.Request().Header.Get("Content-Length"); contentLength != "" {
				if size, err := strconv.ParseInt(contentLength, 10, 64); err == nil {
					requestSize = size
				}
			}

			// Process request
			err := next(c)

			// Build RequestMetrics
			metrics := RequestMetrics{
				Duration:     time.Since(start),
				Status:       c.Response().Status,
				Method:       method,
				Path:         path,
				UserID:       extractUserID(c),
				IP:           c.RealIP(),
				ResponseSize: c.Response().Size,
				RequestSize:  requestSize,
				ErrorType:    classifyError(err, c.Response().Status),
			}

			// Add UserAgent only in verbose mode
			if verboseLogging {
				metrics.UserAgent = c.Request().Header.Get("User-Agent")
			}

			// Get request context for logging
			ctx := c.Request().Context()
			requestID := logger.RequestIDFromContext(ctx)

			// Build log attributes from metrics
			logAttrs := metrics.ToLogAttrs()

			// Add request ID if available
			if requestID != "" {
				logAttrs = append(logAttrs, "request_id", requestID)
			}

			appLogger.LogInfo(ctx, "HTTP Request", logAttrs...)

			return err
		}
	}
}
