package handlers

import (
	"net/http"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/middleware"
	templatesapphelp "github.com/j-em/coachpad/templates/app/help"
	"github.com/labstack/echo/v4"
)

type HelpHandler struct{}

func (h *HelpHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/help", h.<PERSON>)
	r.GET("/help/search", h.SearchHelp)
	r.GET("/help/faq", h.GetFAQ)
	r.GET("/help/troubleshooting", h.GetTroubleshooting)
	r.GET("/help/:section", h.GetHelpSection)
	r.GET("/help/:section/:topic", h.<PERSON>elp<PERSON>opic)
}

// GetHelpCenter renders the main help center page
func (h *HelpHandler) GetHelpCenter(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	lang := cookies.GetLanguageFromCookie(c)

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesapphelp.HelpCenterContent(lang, templatesapphelp.HelpCenterContentProps{
			Lang:   lang,
			UserID: userID,
		})
		return node.Render(c.Response().Writer)
	}

	node := templatesapphelp.HelpCenterPage(templatesapphelp.HelpCenterPageProps{
		Lang:       lang,
		ActiveLink: "/app/help",
		UserID:     userID,
	})
	return node.Render(c.Response().Writer)
}

// SearchHelp handles help search requests
func (h *HelpHandler) SearchHelp(c echo.Context) error {
	query := c.QueryParam("q")
	lang := cookies.GetLanguageFromCookie(c)

	node := templatesapphelp.HelpSearchResults(lang, templatesapphelp.HelpSearchResultsProps{
		Query: query,
		Lang:  lang,
	})
	return node.Render(c.Response().Writer)
}

// GetHelpSection renders a specific help section
func (h *HelpHandler) GetHelpSection(c echo.Context) error {
	section := c.Param("section")
	lang := cookies.GetLanguageFromCookie(c)

	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesapphelp.HelpSectionContent(lang, templatesapphelp.HelpSectionContentProps{
			Section: section,
			Lang:    lang,
			UserID:  userID,
		})
		return node.Render(c.Response().Writer)
	}

	node := templatesapphelp.HelpSectionPage(templatesapphelp.HelpSectionPageProps{
		Section:    section,
		Lang:       lang,
		ActiveLink: "/app/help",
		UserID:     userID,
	})
	return node.Render(c.Response().Writer)
}

// GetHelpTopic renders a specific help topic
func (h *HelpHandler) GetHelpTopic(c echo.Context) error {
	section := c.Param("section")
	topic := c.Param("topic")
	lang := cookies.GetLanguageFromCookie(c)

	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesapphelp.HelpTopicContent(lang, templatesapphelp.HelpTopicContentProps{
			Section: section,
			Topic:   topic,
			Lang:    lang,
			UserID:  userID,
		})
		return node.Render(c.Response().Writer)
	}

	node := templatesapphelp.HelpTopicPage(templatesapphelp.HelpTopicPageProps{
		Section:    section,
		Topic:      topic,
		Lang:       lang,
		ActiveLink: "/app/help",
		UserID:     userID,
	})
	return node.Render(c.Response().Writer)
}

// GetFAQ renders the FAQ page
func (h *HelpHandler) GetFAQ(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	lang := cookies.GetLanguageFromCookie(c)

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesapphelp.FAQContent(lang, templatesapphelp.FAQContentProps{
			Lang:   lang,
			UserID: userID,
		})
		return node.Render(c.Response().Writer)
	}

	node := templatesapphelp.FAQPage(templatesapphelp.FAQPageProps{
		Lang:       lang,
		ActiveLink: "/app/help",
		UserID:     userID,
	})
	return node.Render(c.Response().Writer)
}

// GetTroubleshooting renders the troubleshooting page
func (h *HelpHandler) GetTroubleshooting(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	lang := cookies.GetLanguageFromCookie(c)

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesapphelp.TroubleshootingContent(lang, templatesapphelp.TroubleshootingContentProps{
			Lang:   lang,
			UserID: userID,
		})
		return node.Render(c.Response().Writer)
	}

	node := templatesapphelp.TroubleshootingPage(templatesapphelp.TroubleshootingPageProps{
		Lang:       lang,
		ActiveLink: "/app/help",
		UserID:     userID,
	})
	return node.Render(c.Response().Writer)
}
