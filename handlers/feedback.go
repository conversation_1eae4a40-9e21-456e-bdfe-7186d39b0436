package handlers

import (
	"net/http"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/middleware"
	templatesappfeedback "github.com/j-em/coachpad/templates/app/feedback"
	"github.com/j-em/coachpad/templates/ui/toast"
	"github.com/labstack/echo/v4"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

type FeedbackHandler struct {
	Queries *db.Queries
}

// GetFeedbackModal serves the feedback modal
func (h *FeedbackHandler) GetFeedbackModal(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)

	writer := c.Response().Writer
	return templatesappfeedback.FeedbackModal(lang).Render(writer)
}

// PostFeedback handles feedback submission
func (h *FeedbackHandler) PostFeedback(c echo.Context) error {
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Get form values
	name := c.FormValue("name")
	description := c.FormValue("description")
	feedbackType := c.FormValue("feedback_type")

	// Get language for error messages
	lang := cookies.GetLanguageFromCookie(c)

	// Validate required fields
	if name == "" || description == "" || feedbackType == "" {
		writer := c.Response().Writer
		errorDiv := html.Div(
			html.Class("text-red-500 text-sm p-4"),
			gomponents.Text("Please fill in all required fields"),
		)
		return errorDiv.Render(writer)
	}

	// Validate feedback type
	if feedbackType != "bug" && feedbackType != "feature_request" && feedbackType != "other" {
		writer := c.Response().Writer
		errorDiv := html.Div(
			html.Class("text-red-500 text-sm p-4"),
			gomponents.Text("Invalid feedback type"),
		)
		return errorDiv.Render(writer)
	}

	// Create feedback in database
	ctx := c.Request().Context()
	_, err := h.Queries.CreateFeedback(ctx, db.CreateFeedbackParams{
		UserID:       userID,
		Name:         name,
		Description:  description,
		FeedbackType: feedbackType,
	})

	if err != nil {
		// Load locales for error message
		locales := i18n.MustLoadTemplateLocales("./templates/app/feedback/feedbackModal.locales.json", lang)
		errorMessage := "There was an error submitting your feedback. Please try again."
		if locales["feedback_error"] != "" {
			errorMessage = locales["feedback_error"]
		}

		writer := c.Response().Writer
		errorDiv := html.Div(
			html.Class("text-red-500 text-sm p-4"),
			gomponents.Text(errorMessage),
		)
		return errorDiv.Render(writer)
	}

	// Success response - return success toast

	// Load locales for the success message
	locales := i18n.MustLoadTemplateLocales("./templates/app/feedback/feedbackModal.locales.json", lang)
	successMessage := "Thank you for your feedback!"
	if locales["feedback_success"] != "" {
		successMessage = locales["feedback_success"]
	}

	return toast.Toast(toast.ToastConfig{
		ID:        "feedback-success",
		Message:   successMessage,
		Style:     "success",
		AutoClose: true,
		Lang:      lang,
	}).Render(c.Response().Writer)
}

// RegisterRoutes registers the feedback handler routes
func (h *FeedbackHandler) RegisterRoutes(e *echo.Group) {
	e.GET("/feedback", h.GetFeedbackModal)
	e.POST("/feedback", h.PostFeedback)
}
