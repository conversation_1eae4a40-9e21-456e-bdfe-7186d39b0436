package handlers

import (
	"context"
	"fmt"
	"math"
	"math/big"
	"net/http"
	"strconv"
	"time"

	stripego "github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/client"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/matchmaker"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/notifications"
	templatesappdev "github.com/j-em/coachpad/templates/app/dev"
	"github.com/j-em/coachpad/templates/storybook"
	"github.com/j-em/coachpad/utils/datamapper"
	"github.com/j-em/coachpad/utils/responseutils"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/passwords"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/users"
)

type DevelopmentHandler struct {
	Queries             *db.Queries
	StytchClient        *stytchapi.API
	StripeClient        *client.API
	NotificationService *notifications.Service
}

type CreatePlayerForm struct {
	Name                      string `json:"name" validate:"required"`
	Email                     string `json:"email" validate:"required,email"`
	PreferredMatchGroup       int32  `json:"preferredMatchGroup" validate:"required,min=1"`
	EmailNotificationsEnabled bool   `json:"emailNotificationsEnabled"`
	UserID                    int32  `json:"userId"`
}

type DeletePlayerForm struct {
	ID int `param:"id" validate:"required,min=1"`
}

type DeleteUserForm struct {
	ID int `param:"id" validate:"required,min=1"`
}

type CreateTeamForm struct {
	Name        string `json:"name" validate:"required"`
	Description string `json:"description"`
	UserID      int32  `json:"userId"`
}

type DeleteTeamForm struct {
	ID int `param:"id" validate:"required,min=1"`
}

type CreateNotificationForm struct {
	UserID  int32  `json:"userId" validate:"required,min=1"`
	Type    string `json:"type" validate:"required"`
	Title   string `json:"title" validate:"required"`
	Message string `json:"message" validate:"required"`
}

type DeleteNotificationForm struct {
	ID int `param:"id" validate:"required,min=1"`
}

type CreateSeasonForm struct {
	UserID         int     `json:"userId" validate:"required,min=1"`
	Name           string  `json:"name" validate:"required"`
	StartDate      string  `json:"startDate" validate:"required"`
	SeasonType     string  `json:"seasontype" validate:"required,oneof=pool bowling other"`
	Frequency      string  `json:"frequency" validate:"required,oneof=weekly biweekly monthly quarterly yearly"`
	PlayerIds      []int32 `json:"playerIds"`
	AmountOfTables int     `json:"amountOfTables"`
}

type CreateSpendingForm struct {
	UserID      int32    `form:"userId" json:"userId" validate:"required,min=1"`
	Amount      string   `form:"amount" json:"amount" validate:"required"`
	Description string   `form:"description" json:"description" validate:"required"`
	Date        string   `form:"date" json:"date" validate:"required"`
	Category    string   `form:"category" json:"category" validate:"required"`
	TeamIds     []int32  `form:"teamIds" json:"teamIds"`
	PlayerIds   []int32  `form:"playerIds" json:"playerIds"`
	SeasonIds   []int32  `form:"seasonIds" json:"seasonIds"`
	MatchIds    []int32  `form:"matchIds" json:"matchIds"`
	FileUrls    []string `form:"fileUrls" json:"fileUrls"`
}

type DeleteSpendingForm struct {
	ID int `param:"id" validate:"required,min=1"`
}

// Helper function to create pgtype.Numeric from float64
func createNumericDev(f float64) pgtype.Numeric {
	// Round to 2 decimal places first to avoid precision issues
	rounded := math.Round(f*100) / 100

	// Convert to cents and store as big.Int
	cents := int64(math.Round(rounded * 100))
	intVal := big.NewInt(cents)

	return pgtype.Numeric{
		Int:   intVal,
		Exp:   -2, // Two decimal places
		Valid: true,
	}
}

func (h *DevelopmentHandler) debugEndpoint(c echo.Context) error {
	return c.String(http.StatusOK, "Debug endpoint active")
}

// createTestUser creates a test user for e2e testing
func (h *DevelopmentHandler) createTestUser(c echo.Context) error {
	// Accept test user credentials from body
	type TestUserRequest struct {
		Email    string `json:"email"`
		Password string `json:"password"`
		Name     string `json:"name"`
	}

	var request TestUserRequest
	if err := c.Bind(&request); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid request parameters: " + err.Error(),
		})
	}

	// Get the Stytch client from the handler
	if h.StytchClient == nil {
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Stytch client not available",
		})
	}

	// First, try to create the user in Stytch
	createParams := &passwords.CreateParams{
		Email:    request.Email,
		Password: request.Password,
	}

	res, err := h.StytchClient.Passwords.Create(c.Request().Context(), createParams)
	if err != nil {
		// Check if the error is because the user already exists
		// If so, we can proceed with authentication instead
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to create test user in Stytch: " + err.Error(),
		})
	}

	// Create a Stripe customer for the test user
	stripeClient := h.StripeClient
	stripeCustomerParams := &stripego.CustomerParams{
		Email: stripego.String(request.Email),
		Name:  stripego.String(request.Name),
	}
	stripeCustomer, err := stripeClient.Customers.New(stripeCustomerParams)
	if err != nil {
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to create Stripe customer: " + err.Error(),
		})
	}
	stripeID := stripeCustomer.ID

	// Generate a random birthday
	birthday := time.Now().AddDate(-20, 0, 0) // 20 years ago

	// Create user in our database
	userDb, err := h.Queries.CreateUser(c.Request().Context(), db.CreateUserParams{
		StytchID:   res.UserID,
		StripeID:   stripeID,
		Name:       request.Name,
		Email:      request.Email,
		Country:    "US",
		Lang:       "en",
		IsVerified: true, // Set to true for test users
		Birthday: pgtype.Date{
			Time:  birthday,
			Valid: true,
		},
	})

	if err != nil {
		// Log the error but continue - the user might already exist in our database
		fmt.Printf("Error creating test user in database: %v\n", err)
	} else {
		// Update user on Stytch to have our userId in trusted metadata
		updateParams := &users.UpdateParams{
			UserID: res.UserID,
			TrustedMetadata: map[string]any{
				"userId": userDb.ID,
			},
		}

		_, err = h.StytchClient.Users.Update(c.Request().Context(), updateParams)
		if err != nil {
			fmt.Printf("Error updating Stytch user metadata: %v\n", err)
		}
	}

	// Return success with the test user credentials
	return responseutils.SendJSON(c, http.StatusOK, map[string]string{
		"status":   "success",
		"message":  "Test user created successfully",
		"email":    request.Email,
		"password": request.Password,
	})
}

// testSignin handles test user authentication similar to the real signin flow
// but specifically for automated testing purposes
func (h *DevelopmentHandler) testSignin(c echo.Context) error {
	// Create a login form structure similar to the one in SigninHandler
	type TestLoginForm struct {
		Email    string `json:"email" validate:"required,email"`
		Password string `json:"password" validate:"required"`
	}

	var form TestLoginForm

	// Bind the form values from JSON
	if err := c.Bind(&form); err != nil {
		fmt.Println("Error binding test login form values:", err)
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid form data: " + err.Error(),
		})
	}

	// Validate the form (simplified validation for testing)
	if form.Email == "" || form.Password == "" {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Email and password are required",
		})
	}

	// Authenticate with Stytch
	authParams := &passwords.AuthenticateParams{
		Email:                  form.Email,
		Password:               form.Password,
		SessionDurationMinutes: 527040, // Match the duration in the real signin
	}

	res, err := h.StytchClient.Passwords.Authenticate(c.Request().Context(), authParams)
	if err != nil {
		fmt.Println("Test Stytch authentication error:", err)
		return responseutils.SendJSON(c, http.StatusUnauthorized, map[string]string{
			"status":  "error",
			"message": "Authentication failed: " + err.Error(),
		})
	}

	token := res.SessionJWT

	// Create and set the JWT token as a cookie using the cookies package
	sessionCookie := cookies.CreateSessionCookie(token)
	// Override SameSite to Lax for test/dev endpoints if needed
	sessionCookie.SameSite = http.SameSiteLaxMode
	c.SetCookie(sessionCookie)

	// Get userId from trusted metadata and return it in the response
	var userId int
	if res.User.TrustedMetadata != nil {
		if uid, ok := res.User.TrustedMetadata["userId"].(float64); ok {
			userId = int(uid)
		}
	}

	return responseutils.SendJSON(c, http.StatusOK, map[string]any{
		"status":  "success",
		"message": "Test authentication successful",
		"token":   token,
		"userId":  userId,
	})
}

func (h *DevelopmentHandler) createPlayer(c echo.Context) error {
	var form CreatePlayerForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	player := db.CreatePlayerParams{
		UserID:                    form.UserID,
		Name:                      form.Name,
		Email:                     pgtype.Text{String: form.Email, Valid: true},
		PreferredMatchGroup:       form.PreferredMatchGroup,
		EmailNotificationsEnabled: form.EmailNotificationsEnabled,
	}
	createdPlayer, err := h.Queries.CreatePlayer(context.Background(), player)
	if err != nil {
		fmt.Println("Error creating player:", err)
		return c.String(http.StatusInternalServerError, "Failed to create player")
	}

	return responseutils.SendJSON(c, http.StatusOK, createdPlayer)
}

func (h *DevelopmentHandler) createTeam(c echo.Context) error {
	var form CreateTeamForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	team := db.CreateTeamParams{
		UserID:      form.UserID,
		Name:        form.Name,
		Description: pgtype.Text{String: form.Description, Valid: form.Description != ""},
	}
	createdTeam, err := h.Queries.CreateTeam(context.Background(), team)
	if err != nil {
		fmt.Println("Error creating team:", err)
		return c.String(http.StatusInternalServerError, "Failed to create team")
	}

	return responseutils.SendJSON(c, http.StatusOK, createdTeam)
}

func (h *DevelopmentHandler) deletePlayer(c echo.Context) error {
	var form DeletePlayerForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	if err := h.Queries.DeletePlayer(context.Background(), int32(form.ID)); err != nil {
		fmt.Println("Error deleting player:", err)
		return c.String(http.StatusInternalServerError, "Failed to delete player")
	}

	return c.String(http.StatusOK, "Player deleted")
}

func (h *DevelopmentHandler) deleteTeam(c echo.Context) error {
	var form DeleteTeamForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	if err := h.Queries.DeleteTeam(context.Background(), int32(form.ID)); err != nil {
		fmt.Println("Error deleting team:", err)
		return c.String(http.StatusInternalServerError, "Failed to delete team")
	}

	return c.String(http.StatusOK, "Team deleted")
}

func (h *DevelopmentHandler) createSpending(c echo.Context) error {
	var form CreateSpendingForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Parse the amount
	amount, err := strconv.ParseFloat(form.Amount, 64)
	if err != nil {
		return c.String(http.StatusBadRequest, "Invalid amount format")
	}

	// Parse the date
	date, err := time.Parse("2006-01-02", form.Date)
	if err != nil {
		return c.String(http.StatusBadRequest, "Invalid date format")
	}

	// Create spending record
	spending, err := h.Queries.CreateSpending(context.Background(), db.CreateSpendingParams{
		UserID:      form.UserID,
		Amount:      createNumericDev(amount),
		Description: form.Description,
		Date:        pgtype.Date{Time: date, Valid: true},
		Category:    form.Category,
		FileUrls:    form.FileUrls,
	})
	if err != nil {
		fmt.Println("Error creating spending:", err)
		return c.String(http.StatusInternalServerError, "Failed to create spending")
	}

	// Create associations
	for _, teamID := range form.TeamIds {
		err := h.Queries.CreateSpendingTeamAssociation(context.Background(), db.CreateSpendingTeamAssociationParams{
			SpendingID: spending.ID,
			TeamID:     teamID,
		})
		if err != nil {
			fmt.Printf("Error creating team association: %v\n", err)
		}
	}

	for _, playerID := range form.PlayerIds {
		err := h.Queries.CreateSpendingPlayerAssociation(context.Background(), db.CreateSpendingPlayerAssociationParams{
			SpendingID: spending.ID,
			PlayerID:   playerID,
		})
		if err != nil {
			fmt.Printf("Error creating player association: %v\n", err)
		}
	}

	for _, seasonID := range form.SeasonIds {
		err := h.Queries.CreateSpendingSeasonAssociation(context.Background(), db.CreateSpendingSeasonAssociationParams{
			SpendingID: spending.ID,
			SeasonID:   seasonID,
		})
		if err != nil {
			fmt.Printf("Error creating season association: %v\n", err)
		}
	}

	for _, matchID := range form.MatchIds {
		err := h.Queries.CreateSpendingMatchAssociation(context.Background(), db.CreateSpendingMatchAssociationParams{
			SpendingID: spending.ID,
			MatchID:    matchID,
		})
		if err != nil {
			fmt.Printf("Error creating match association: %v\n", err)
		}
	}

	return responseutils.SendJSON(c, http.StatusOK, map[string]any{
		"id":          spending.ID,
		"amount":      form.Amount,
		"description": spending.Description,
		"date":        spending.Date.Time.Format("2006-01-02"),
		"category":    spending.Category,
	})
}

func (h *DevelopmentHandler) deleteSpending(c echo.Context) error {
	var form DeleteSpendingForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Delete spending record (sets is_active = false)
	if err := h.Queries.DeleteSpending(context.Background(), db.DeleteSpendingParams{
		ID:     int32(form.ID),
		UserID: 0, // For dev endpoints, we don't validate user ownership
	}); err != nil {
		fmt.Println("Error deleting spending:", err)
		return c.String(http.StatusInternalServerError, "Failed to delete spending")
	}

	return c.String(http.StatusOK, "Spending deleted")
}

func (h *DevelopmentHandler) getCurrentUser(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return responseutils.SendJSON(c, http.StatusUnauthorized, map[string]string{
			"status":  "error",
			"message": "User not authenticated",
		})
	}

	return responseutils.SendJSON(c, http.StatusOK, map[string]any{
		"id": userID,
	})
}

func (h *DevelopmentHandler) deleteUser(c echo.Context) error {
	var form DeleteUserForm
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Get user data from database to get the Stytch ID and Stripe ID
	user, err := h.Queries.GetUserByID(context.Background(), int32(form.ID))
	if err != nil {
		fmt.Println("Error fetching user:", err)
		return c.String(http.StatusInternalServerError, "Failed to fetch user data")
	}

	// Delete user on Stripe if a Stripe ID exists
	// This removes the customer from Stripe, which:
	// - Cancels any active subscriptions
	// - Removes payment methods associated with the customer
	// - Prevents future charges to this customer
	// - Maintains billing history for compliance (Stripe soft-deletes)
	// if user.StripeID != "" {
	// 	stripeClient := h.StripeClient
	// 	_, err = stripeClient.Customers.Del(user.StripeID, nil)
	// 	if err != nil {
	// 		fmt.Println("Error deleting user from Stripe:", err)
	// 		// Continue with local deletion even if Stripe deletion fails
	// 		// This ensures test cleanup proceeds even if Stripe is unavailable
	// 	}
	// }
	if err := c.Bind(&form); err != nil {
		return c.String(http.StatusBadRequest, "Invalid input")
	}
	if err := c.Validate(&form); err != nil {
		return c.String(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Delete user on Stytch if a Stytch ID exists
	if user.StytchID != "" && h.StytchClient != nil {
		deleteParams := &users.DeleteParams{
			UserID: user.StytchID,
		}
		_, err := h.StytchClient.Users.Delete(c.Request().Context(), deleteParams)
		if err != nil {
			fmt.Println("Error deleting user from Stytch:", err)
			// Continue with local deletion even if Stytch deletion fails
		}
	}

	// Use the SQL cascade delete to delete the user and all related data
	err = h.Queries.CascadeDeleteUser(context.Background(), user.ID)
	if err != nil {
		fmt.Println("Error cascade deleting user:", err)
		return c.String(http.StatusInternalServerError, "Failed to delete user and related data")
	}

	return responseutils.SendJSON(c, http.StatusOK, map[string]string{
		"status":  "success",
		"message": "User and all associated data deleted successfully",
	})
}

func (h *DevelopmentHandler) createSeason(c echo.Context) error {
	var form CreateSeasonForm
	if err := c.Bind(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid input: " + err.Error(),
		})
	}
	if err := c.Validate(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Validation failed: " + err.Error(),
		})
	}

	// Parse the start date
	startDate, err := time.Parse("2006-01-02", form.StartDate)
	if err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid date format: " + err.Error(),
		})
	}

	// Create the season in the database
	season, err := h.Queries.CreateSeason(c.Request().Context(), db.CreateSeasonParams{
		UserID: pgtype.Int4{
			Int32: int32(form.UserID),
			Valid: true,
		},
		Name: form.Name,
		StartDate: pgtype.Date{
			Time:  startDate,
			Valid: true,
		},
		SeasonType: form.SeasonType,
		Frequency:  form.Frequency,
	})

	if err != nil {
		fmt.Println("Error creating season:", err)
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to create season: " + err.Error(),
		})
	}

	// Generate matches if player IDs were provided
	if len(form.PlayerIds) >= 2 && form.AmountOfTables > 0 {
		// Import matchmaker package
		config := matchmaker.RoundRobinConfig{
			PlayerIDs:      form.PlayerIds,
			StartDate:      startDate,
			Frequency:      form.Frequency,
			AmountOfTables: int32(form.AmountOfTables),
		}

		matches := matchmaker.GenerateRoundRobin(config)

		// Create matches in the database
		for _, match := range matches {
			createMatchParams := db.CreateMatchParams{
				SeasonID: pgtype.Int4{
					Int32: season.ID,
					Valid: true,
				},
				PlayerId1: pgtype.Int4{
					Int32: match.Player1ID,
					Valid: true,
				},
				PlayerId2: pgtype.Int4{
					Int32: match.Player2ID,
					Valid: true,
				},
				PlayerId1Points: pgtype.Int4{Int32: 0, Valid: true},
				PlayerId2Points: pgtype.Int4{Int32: 0, Valid: true},
				MatchDate: pgtype.Date{
					Time:  match.MatchDate,
					Valid: true,
				},
				MatchGroup: match.Group,
			}

			_, err := h.Queries.CreateMatch(c.Request().Context(), createMatchParams)
			if err != nil {
				// Log the error but continue creating other matches
				fmt.Printf("Failed to create match: %v\n", err)
			}
		}
	}

	return responseutils.SendJSON(c, http.StatusOK, season)
}

func (h *DevelopmentHandler) createCustomMatchColumn(c echo.Context) error {
	var form struct {
		UserID       int32  `json:"userId" validate:"required,min=1"`
		Name         string `json:"name" validate:"required"`
		FieldType    string `json:"fieldType" validate:"required,oneof=text number date boolean"`
		Description  string `json:"description"`
		IsRequired   bool   `json:"isRequired"`
		DisplayOrder int32  `json:"displayOrder"`
	}
	if err := c.Bind(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid input: " + err.Error(),
		})
	}
	if err := c.Validate(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Validation failed: " + err.Error(),
		})
	}

	// Create the custom match column in the database
	column, err := h.Queries.CreateMatchCustomColumn(c.Request().Context(), db.CreateMatchCustomColumnParams{
		UserID:       form.UserID,
		Name:         form.Name,
		FieldType:    form.FieldType,
		Description:  pgtype.Text{String: form.Description, Valid: true},
		IsRequired:   pgtype.Bool{Bool: form.IsRequired, Valid: true},
		IsActive:     pgtype.Bool{Bool: true, Valid: true},
		DisplayOrder: form.DisplayOrder,
	})
	if err != nil {
		fmt.Println("Error creating custom match column:", err)
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to create custom match column: " + err.Error(),
		})
	}

	return responseutils.SendJSON(c, http.StatusOK, column)
}

func (h *DevelopmentHandler) deleteSeason(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid season ID",
		})
	}

	// First, delete all matches associated with this season
	// This ensures referential integrity before deleting the season
	seasonID := pgtype.Int4{
		Int32: int32(id),
		Valid: true,
	}

	// Getting associated matches first
	matches, err := h.Queries.GetMatchesBySeasonId(c.Request().Context(), seasonID)
	if err != nil {
		fmt.Printf("Error fetching matches for season %d: %v\n", id, err)
		// Continue with deletion even if fetching matches fails
	} else {
		// Delete each match
		for _, match := range matches {
			err := h.Queries.DeleteMatch(c.Request().Context(), match.ID)
			if err != nil {
				fmt.Printf("Error deleting match %d: %v\n", match.ID, err)
				// Continue deleting other matches
			}
		}
	}

	if err != nil {
		return responseutils.SendJSON(c, http.StatusNotFound, map[string]string{
			"status":  "error",
			"message": "Season not found",
		})
	}

	// Now delete the season
	err = h.Queries.DeleteSeason(c.Request().Context(), int32(id))

	if err != nil {
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to delete season: " + err.Error(),
		})
	}

	return responseutils.SendJSON(c, http.StatusOK, map[string]string{
		"status":  "success",
		"message": "Season deleted successfully",
	})
}

func (h *DevelopmentHandler) getSeasonMatches(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid season ID",
		})
	}

	// Get all matches for the season
	seasonID := pgtype.Int4{
		Int32: int32(id),
		Valid: true,
	}

	matches, err := h.Queries.GetMatchesBySeasonId(c.Request().Context(), seasonID)
	if err != nil {
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to fetch matches: " + err.Error(),
		})
	}

	return responseutils.SendJSON(c, http.StatusOK, matches)
}

func (h *DevelopmentHandler) getAppStorybook(c echo.Context) error {
	// render the template
	node := storybook.StorybookPage()
	return node.Render(c.Response().Writer)
}

func (h *DevelopmentHandler) getDevelopmentPage(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	// Get user data
	user, err := h.Queries.GetUserByID(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Error fetching user data")
	}

	// Get seasons for sidebar
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(context.Background(), userID)
	var seasons []db.Season
	if err != nil {
		fmt.Printf("Error fetching seasons: %v\n", err)
		seasons = nil
	} else {
		// Use datamapper for clean conversion - no manual loop needed!
		seasons = datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)
	}

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesappdev.DevPageContent(lang, templatesappdev.DevPageContentProps{
			Lang: lang,
		})
		return node.Render(c.Response().Writer)
	}

	node := templatesappdev.DevPage(templatesappdev.DevPageProps{
		Lang:          lang,
		UserID:        userID,
		ActiveLink:    "/app/dev",
		Seasons:       seasons,
		IsSidebarOpen: user.IsSidebarOpen,
	})
	return node.Render(c.Response().Writer)
}

func (h *DevelopmentHandler) createNotification(c echo.Context) error {
	var form CreateNotificationForm
	if err := c.Bind(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid input: " + err.Error(),
		})
	}
	if err := c.Validate(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Validation failed: " + err.Error(),
		})
	}

	// For testing purposes, create notification directly in database bypassing the service
	// This ensures the notification is always created regardless of user preferences
	params := db.CreateNotificationParams{
		UserID:  form.UserID,
		Type:    form.Type,
		Title:   form.Title,
		Message: form.Message,
	}

	notification, err := h.Queries.CreateNotification(c.Request().Context(), params)
	if err != nil {
		fmt.Println("Error creating notification:", err)
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to create notification: " + err.Error(),
		})
	}

	return responseutils.SendJSON(c, http.StatusOK, notification)
}

func (h *DevelopmentHandler) deleteNotification(c echo.Context) error {
	var form DeleteNotificationForm
	if err := c.Bind(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Invalid input: " + err.Error(),
		})
	}
	if err := c.Validate(&form); err != nil {
		return responseutils.SendJSON(c, http.StatusBadRequest, map[string]string{
			"status":  "error",
			"message": "Validation failed: " + err.Error(),
		})
	}

	// Delete notification from database
	err := h.Queries.DeleteNotification(c.Request().Context(), int32(form.ID))
	if err != nil {
		fmt.Println("Error deleting notification:", err)
		return responseutils.SendJSON(c, http.StatusInternalServerError, map[string]string{
			"status":  "error",
			"message": "Failed to delete notification: " + err.Error(),
		})
	}

	return responseutils.SendJSON(c, http.StatusOK, map[string]string{
		"status":  "success",
		"message": "Notification deleted successfully",
	})
}

func (h *DevelopmentHandler) RegisterRoutes(e *echo.Echo) {
	// Development-only routes
	e.GET("/dev/debug", h.debugEndpoint)
	e.POST("/dev/create-test-user", h.createTestUser)
	e.POST("/dev/test-signin", h.testSignin)
	e.POST("/dev/players", h.createPlayer)
	e.DELETE("/dev/players/:id", h.deletePlayer)
	e.POST("/dev/teams", h.createTeam)
	e.DELETE("/dev/teams/:id", h.deleteTeam)
	e.GET("/dev/current-user", h.getCurrentUser)
	e.DELETE("/dev/users/:id", h.deleteUser)
	e.POST("/dev/seasons", h.createSeason)
	e.DELETE("/dev/seasons/:id", h.deleteSeason)
	e.GET("/dev/seasons/:id/matches", h.getSeasonMatches)
	e.POST("/dev/notifications", h.createNotification)
	e.DELETE("/dev/notifications/:id", h.deleteNotification)
	e.POST("/dev/spending", h.createSpending)
	e.DELETE("/dev/spending/:id", h.deleteSpending)

	e.GET("/dev/storybook", h.getAppStorybook)
	e.POST("/dev/custom-match-columns", h.createCustomMatchColumn)
}

func (h *DevelopmentHandler) RegisterAppRoutes(appGroup *echo.Group) {
	// App development routes (require auth)
	appGroup.GET("/dev", h.getDevelopmentPage)
}
