package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/pkg/limits"

	templatesappplayers "github.com/j-em/coachpad/templates/app/players"
	templatesappseasons "github.com/j-em/coachpad/templates/app/seasons"
	"github.com/j-em/coachpad/templates/ui/toast"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/datamapper"
	"github.com/j-em/coachpad/utils/pagination"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

// getPlayerAddedSuccessMessage returns the localized success message for player addition
func getPlayerAddedSuccessMessage(lang string) string {
	locales := i18n.MustLoadTemplateLocales("./templates/app/players/playersNewForm.locales.json", lang)
	if locales["playerAddedSuccessfully"] == "" {
		// Fallback messages
		if lang == "fr" {
			return "Joueur ajouté avec succès!"
		}
		return "Player added successfully!"
	}
	return locales["playerAddedSuccessfully"]
}

type PlayersHandler struct {
	Queries          *db.Queries
	LimitsMiddleware *middleware.LimitsMiddleware
	LimitsService    *limits.Service
}

// =====================
// JSON Data API Methods

// playerResponse represents the JSON structure for player data.
type playerResponse struct {
	ID                        int32  `json:"id"`
	Name                      string `json:"name"`
	Email                     string `json:"email"`
	Phone                     string `json:"phone"`
	TeamID                    *int32 `json:"teamId"`
	PreferredMatchGroup       int32  `json:"preferredMatchGroup"`
	IsActive                  bool   `json:"isActive"`
	EmailNotificationsEnabled bool   `json:"emailNotificationsEnabled"`
}

// CreatePlayerRequest represents the expected JSON body for creating a player.
type CreatePlayerRequest struct {
	Name                      string `json:"name"`
	Email                     string `json:"email"`
	Phone                     string `json:"phone"`
	TeamID                    *int32 `json:"teamId"`
	PreferredMatchGroup       int32  `json:"preferredMatchGroup"`
	EmailNotificationsEnabled bool   `json:"emailNotificationsEnabled"`
}

// RegisterAPIRoutes registers JSON API routes under /api.
func (h *PlayersHandler) RegisterAPIRoutes(r *echo.Group) {
	r.GET("/players", h.GetPlayersJSON)
	r.POST("/players", h.CreatePlayerJSON)
}

// GetPlayersJSON returns all players for the authenticated user as JSON.
func (h *PlayersHandler) GetPlayersJSON(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)
	players, err := h.Queries.GetPlayers(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "failed to fetch players"})
	}
	resp := make([]playerResponse, 0, len(players))
	for _, p := range players {
		var teamID *int32
		if p.TeamID.Valid {
			teamID = &p.TeamID.Int32
		}
		resp = append(resp, playerResponse{
			ID:                        p.ID,
			Name:                      p.Name,
			Email:                     p.Email.String,
			Phone:                     p.Phone.String,
			TeamID:                    teamID,
			PreferredMatchGroup:       p.PreferredMatchGroup,
			IsActive:                  p.IsActive,
			EmailNotificationsEnabled: p.EmailNotificationsEnabled,
		})
	}
	return c.JSON(http.StatusOK, resp)
}

// CreatePlayerJSON creates a new player from JSON and returns it.
func (h *PlayersHandler) CreatePlayerJSON(c echo.Context) error {
	var req CreatePlayerRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "invalid request body"})
	}
	userID := c.Get(middleware.UserIDKey).(int32)
	var teamID pgtype.Int4
	if req.TeamID != nil {
		teamID = pgtype.Int4{Int32: *req.TeamID, Valid: true}
	}

	player, err := h.Queries.CreatePlayer(c.Request().Context(), db.CreatePlayerParams{
		Name:                      req.Name,
		Email:                     pgtype.Text{String: req.Email, Valid: req.Email != ""},
		Phone:                     pgtype.Text{String: req.Phone, Valid: req.Phone != ""},
		PreferredMatchGroup:       req.PreferredMatchGroup,
		EmailNotificationsEnabled: req.EmailNotificationsEnabled,
		UserID:                    userID,
		TeamID:                    teamID,
	})
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "failed to create player"})
	}

	var responseTeamID *int32
	if player.TeamID.Valid {
		responseTeamID = &player.TeamID.Int32
	}

	return c.JSON(http.StatusCreated, playerResponse{
		ID:                        player.ID,
		Name:                      player.Name,
		Email:                     player.Email.String,
		Phone:                     player.Phone.String,
		TeamID:                    responseTeamID,
		PreferredMatchGroup:       player.PreferredMatchGroup,
		IsActive:                  player.IsActive,
		EmailNotificationsEnabled: player.EmailNotificationsEnabled,
	})
}

// UpdatePlayerInline handles inline player updates from the HTMX table
func (h *PlayersHandler) UpdatePlayerInline(c echo.Context) error {
	// Get user ID from context (set by StytchJWT middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse player ID from URL
	var playerID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &playerID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid player ID")
	}

	// Parse form data
	name := c.FormValue("name")
	email := c.FormValue("email")
	phone := c.FormValue("phone")
	preferredMatchGroupStr := c.FormValue("preferredMatchGroup")
	teamIDStr := c.FormValue("teamId")
	isActiveStr := c.FormValue("isActive")
	emailNotificationsEnabledStr := c.FormValue("emailNotificationsEnabled")

	// Convert string values to appropriate types
	var preferredMatchGroup int32 = 1
	if preferredMatchGroupStr != "" {
		if pmg, err := strconv.Atoi(preferredMatchGroupStr); err == nil {
			preferredMatchGroup = int32(pmg)
		}
	}

	var teamID pgtype.Int4
	if teamIDStr != "" {
		if tid, err := strconv.Atoi(teamIDStr); err == nil {
			teamID = pgtype.Int4{Int32: int32(tid), Valid: true}
		}
	}

	isActive := isActiveStr == "true" || isActiveStr == "on"
	emailNotificationsEnabled := emailNotificationsEnabledStr == "true" || emailNotificationsEnabledStr == "on"

	// Update the player
	updatedPlayer, err := h.Queries.UpdatePlayer(c.Request().Context(), db.UpdatePlayerParams{
		Name:                      name,
		Email:                     pgtype.Text{String: email, Valid: email != ""},
		Phone:                     pgtype.Text{String: phone, Valid: phone != ""},
		PreferredMatchGroup:       preferredMatchGroup,
		EmailNotificationsEnabled: emailNotificationsEnabled,
		IsActive:                  isActive,
		TeamID:                    teamID,
		PictureUrl:                pgtype.Text{}, // Keep existing value
		ID:                        playerID,
		UserID:                    userID,
	})
	if err != nil {
		// Log the error for debugging
		log.Printf("UpdatePlayerInline ERROR: playerID=%d userID=%d error=%v", playerID, userID, err)
		return c.String(http.StatusInternalServerError, "Failed to update player")
	}

	// Log successful update for debugging
	log.Printf("UpdatePlayerInline SUCCESS: playerID=%d name=%s", updatedPlayer.ID, updatedPlayer.Name)

	// For HTMX requests, return empty response with OK status
	return c.NoContent(http.StatusOK)
}

func (h *PlayersHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/players", h.GetPlayers)
	r.GET("/players/new", h.GetNewPlayerForm)

	// Apply limits middleware to player creation
	if h.LimitsMiddleware != nil {
		r.POST("/players", h.PostNewPlayer, h.LimitsMiddleware.CheckResourceLimit(limits.ResourcePlayers))
	} else {
		r.POST("/players", h.PostNewPlayer)
	}

	r.GET("/players/available", h.GetAvailablePlayers)     // New endpoint
	r.GET("/players/select-modal", h.GetPlayerSelectModal) // New modal endpoint
	r.GET("/players/export/csv", h.ExportPlayersCSV)       // CSV export endpoint
	r.GET("/players/:id/edit", h.GetEditPlayerForm)
	r.PUT("/players/:id", h.PutEditPlayer)
	r.PUT("/players/:id/inline", h.UpdatePlayerInline)
}

// PutEditPlayer handles updating an existing player via PUT
func (h *PlayersHandler) PutEditPlayer(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)

	// Parse player ID from URL
	playerIDParam := c.Param("id")
	var playerID int32
	_, err := fmt.Sscanf(playerIDParam, "%d", &playerID)
	if err != nil {
		return templatesappplayers.PlayersEditError("Invalid player ID", lang).Render(c.Response().Writer)
	}

	// Email preferences struct for form binding
	type EmailPrefsForm struct {
		Match24h        CheckboxBool `form:"emailPrefs.match24h"`
		Match2h         CheckboxBool `form:"emailPrefs.match2h"`
		ScheduleUpdates CheckboxBool `form:"emailPrefs.scheduleUpdates"`
		Results         CheckboxBool `form:"emailPrefs.results"`
	}

	// Bind form data
	type EditPlayerForm struct {
		Name                      string       `form:"name" validate:"required"`
		Email                     string       `form:"email" validate:"omitempty,email"`
		Phone                     string       `form:"phone" validate:"omitempty"`
		TeamID                    *int32       `form:"teamId" validate:"omitempty,number"`
		PreferredMatchGroup       int32        `form:"preferredMatchGroup" validate:"omitempty,number"`
		EmailNotificationsEnabled CheckboxBool `form:"emailNotificationsEnabled"`
		EmailPrefs                EmailPrefsForm
	}
	var form EditPlayerForm
	if err := c.Bind(&form); err != nil {
		return templatesappplayers.PlayersEditError("Invalid form data", lang).Render(c.Response().Writer)
	}
	if err := c.Validate(form); err != nil {
		return templatesappplayers.PlayersEditError("Validation error: "+err.Error(), lang).Render(c.Response().Writer)
	}

	// Update the player in the database
	// Get user ID for the update
	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert email preferences to JSON
	emailPrefs := templatesappplayers.EmailReminderPreferences{
		Match24h:        bool(form.EmailPrefs.Match24h),
		Match2h:         bool(form.EmailPrefs.Match2h),
		ScheduleUpdates: bool(form.EmailPrefs.ScheduleUpdates),
		Results:         bool(form.EmailPrefs.Results),
	}
	emailPrefsJSON, err := json.Marshal(emailPrefs)
	if err != nil {
		return templatesappplayers.PlayersEditError("Failed to process email preferences", lang).Render(c.Response().Writer)
	}

	var teamID pgtype.Int4
	if form.TeamID != nil {
		teamID = pgtype.Int4{Int32: *form.TeamID, Valid: true}
	}

	err = h.Queries.UpdatePlayerWithEmailPreferences(c.Request().Context(), db.UpdatePlayerWithEmailPreferencesParams{
		ID:                        playerID,
		UserID:                    userID,
		Name:                      form.Name,
		Email:                     pgtype.Text{String: form.Email, Valid: form.Email != ""},
		Phone:                     pgtype.Text{String: form.Phone, Valid: form.Phone != ""},
		PreferredMatchGroup:       form.PreferredMatchGroup,
		EmailNotificationsEnabled: bool(form.EmailNotificationsEnabled),
		EmailReminderPreferences:  emailPrefsJSON,
		IsActive:                  true,
		TeamID:                    teamID,
		PictureUrl:                pgtype.Text{}, // Keep existing value
	})
	if err != nil {
		return templatesappplayers.PlayersEditError("Failed to update player", lang).Render(c.Response().Writer)
	}

	// On success, return a response with a htmx event to indicate players were updated
	utils.SetTypedTrigger(c, utils.HXEventPlayersUpdated, utils.PlayersUpdatedEvent{})
	return templatesappplayers.PlayersEditSuccess(lang).Render(c.Response().Writer)
}

// GetEditPlayerForm serves the edit player modal form for a given player ID
func (h *PlayersHandler) GetEditPlayerForm(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)
	lang := cookies.GetLanguageFromCookie(c)

	// Parse player ID from URL
	playerIDParam := c.Param("id")
	var playerID int32
	_, err := fmt.Sscanf(playerIDParam, "%d", &playerID)
	if err != nil {
		return c.String(http.StatusBadRequest, "Invalid player ID")
	}

	// Fetch player from DB
	player, err := h.Queries.GetPlayer(context.Background(), db.GetPlayerParams{
		ID:     playerID,
		UserID: userID,
	})
	if err != nil {
		return c.String(http.StatusNotFound, "Player not found")
	}

	// Parse email reminder preferences
	emailPrefs := templatesappplayers.EmailReminderPreferences{
		Match24h:        true, // Default values
		Match2h:         true,
		ScheduleUpdates: true,
		Results:         false,
	}

	if len(player.EmailReminderPreferences) > 0 {
		if err := json.Unmarshal(player.EmailReminderPreferences, &emailPrefs); err != nil {
			// Use defaults if parsing fails
		}
	}

	// Get team ID for the player
	var teamID *int32
	if player.TeamID.Valid {
		teamID = &player.TeamID.Int32
	}

	node := templatesappplayers.PlayersEditForm(
		lang,
		player.ID,
		player.Name,
		player.Email.String,
		player.Phone.String,
		teamID,
		player.EmailNotificationsEnabled,
		emailPrefs,
	)

	return node.Render(c.Response().Writer)
}

func (h *PlayersHandler) GetNewPlayerForm(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	// Get current player usage
	var playersUsage *limits.LimitCheckResult
	if h.LimitsService != nil {
		usage, err := h.LimitsService.CheckLimit(c.Request().Context(), userID, limits.ResourcePlayers)
		if err != nil {
			// Log error but continue - just don't show usage indicator
			playersUsage = nil
		} else {
			playersUsage = usage
		}
	}

	node := templatesappplayers.PlayersNewForm(templatesappplayers.PlayersNewFormConfig{
		Lang:         lang,
		PlayersUsage: playersUsage,
	})
	return node.Render(c.Response().Writer)
}

type PostNewPlayerForm struct {
	Name                      string       `form:"name" validate:"required"`
	Email                     string       `form:"email" validate:"omitempty,email"`
	Phone                     string       `form:"phone" validate:"omitempty"`
	TeamID                    *int32       `form:"teamId" validate:"omitempty,number"`
	PreferredMatchGroup       int32        `form:"preferredMatchGroup" validate:"omitempty,number"`
	EmailNotificationsEnabled CheckboxBool `form:"emailNotificationsEnabled"`
	KeepAddingPlayers         CheckboxBool `form:"keepAddingPlayers"`
}

func (h *PlayersHandler) PostNewPlayer(c echo.Context) error {
	// Create a new player
	var form PostNewPlayerForm
	if err := c.Bind(&form); err != nil {
		fmt.Println(err)
		return c.String(http.StatusBadRequest, "Invalid form data")
	}

	// Validate the form
	if err := c.Validate(form); err != nil {
		fmt.Println(err)
		return c.String(http.StatusBadRequest, "Validation error: "+err.Error())
	}

	// Create the player in the database
	var teamID pgtype.Int4
	if form.TeamID != nil {
		teamID = pgtype.Int4{Int32: *form.TeamID, Valid: true}
	}

	_, err := h.Queries.CreatePlayer(context.Background(), db.CreatePlayerParams{
		Name:                      form.Name,
		Email:                     pgtype.Text{String: form.Email, Valid: form.Email != ""},
		Phone:                     pgtype.Text{String: form.Phone, Valid: form.Phone != ""},
		PreferredMatchGroup:       int32(form.PreferredMatchGroup),
		EmailNotificationsEnabled: bool(form.EmailNotificationsEnabled),
		UserID:                    c.Get(middleware.UserIDKey).(int32),
		TeamID:                    teamID,
	})
	if err != nil {
		fmt.Printf("PostNewPlayer database error: %v\n", err)
		return c.String(http.StatusInternalServerError, "Failed to create player")
	}

	// Set HX-Trigger header to notify client that players were updated
	triggers := []utils.TypedEventTrigger{
		{EventName: utils.HXEventPlayersUpdated, Data: utils.PlayersUpdatedEvent{}},
	}

	// Only close modal if "Keep adding players" is not checked
	if !bool(form.KeepAddingPlayers) {
		triggers = append(triggers, utils.TypedEventTrigger{
			EventName: utils.HXEventCloseAllModals,
			Data:      utils.CloseAllModalsEvent{},
		})
	}

	utils.SetMultipleTypedTriggers(c, triggers)

	// Return success toast when keeping modal open
	if bool(form.KeepAddingPlayers) {
		// Get the language from the request (could be from header or query param)
		lang := c.QueryParam("lang")
		if lang == "" {
			lang = "en" // default to English
		}

		// Use the toast package to return a success toast
		toast := toast.Toast(toast.ToastConfig{
			ID:         "player-added-toast",
			Message:    getPlayerAddedSuccessMessage(lang),
			Style:      "success",
			DataTestID: "player-added-toast",
			AutoClose:  true,
			Lang:       lang,
		})

		writer := c.Response().Writer
		return toast.Render(writer)
	}

	// Return success response (empty for now, matches teams pattern)
	return c.NoContent(http.StatusOK)
}

func (h *PlayersHandler) GetPlayers(c echo.Context) error {
	// Get user ID from context (set by StytchJWT middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse query parameters for pagination, sorting, and filtering
	params := parsePlayersTableParams(c)
	isPrint := c.QueryParam("print") == "true"

	// Query players for this user
	allPlayers, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch players")
	}

	// Get column visibility preferences (gracefully handle missing table)
	columnVisibilities, err := h.Queries.GetPlayerColumnVisibility(context.Background(), userID)
	if err != nil {
		// If the table doesn't exist (e.g., in test environment), continue with empty preferences
		// This allows the system to work with default column visibility settings
		log.Printf("Warning: Failed to fetch column visibility preferences (table may not exist): %v", err)
		columnVisibilities = []db.GetPlayerColumnVisibilityRow{}
	}

	// Build column visibility map with defaults
	columnVisibilityMap := map[string]bool{
		"Name":                true,
		"Email":               true,
		"Preferred Group":     true,
		"Active":              true,
		"Email Notifications": true,
		"Picture":             false,
		"Created":             false,
		"Updated":             false,
	}

	// Apply user preferences
	for _, cv := range columnVisibilities {
		columnVisibilityMap[cv.ColumnName] = cv.IsVisible
	}

	// Debug logging
	log.Printf("DEBUG: Column visibility map: %+v", columnVisibilityMap)

	// Apply filtering, sorting, and pagination
	filteredPlayers := filterPlayers(allPlayers, params.Search)
	sortedPlayers := sortPlayers(filteredPlayers, params.Sort, params.Direction)
	totalItems := len(sortedPlayers)
	isSearchActive := params.Search != ""

	// Get the language from the cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Check if this is a print request
	if isPrint {
		// For print, use all filtered/sorted players (no pagination)
		return templatesappplayers.PrintPlayers(templatesappplayers.PrintPlayersProps{
			Players: sortedPlayers,
			Lang:    lang,
		}).Render(c.Response().Writer)
	}

	// For non-print requests, apply pagination
	totalPages := pagination.CalculateTotalPages(totalItems, params.ItemsPerPage)
	paginatedPlayers := paginatePlayers(sortedPlayers, params.Page, params.ItemsPerPage)

	// Check if this is a boosted request
	if c.Request().Header.Get("HX-Boosted") == "true" {
		// Return just the page content for boost navigation
		content := templatesappplayers.PlayersDashboardPageContent(templatesappplayers.PlayersDashboardProps{
			Players:          paginatedPlayers,
			AllPlayers:       allPlayers,
			Lang:             lang,
			Params:           &params,
			TotalItems:       totalItems,
			TotalPages:       totalPages,
			IsSearchActive:   isSearchActive,
			ColumnVisibility: columnVisibilityMap,
		})
		return content.Render(c.Response().Writer)
	}

	// Check if this is an htmx request (partial render for table updates)
	if c.Request().Header.Get("HX-Request") == "true" {
		// Only render the players table container for table updates
		tableProps := templatesappplayers.PlayersTableProps{
			Players:          paginatedPlayers,
			AllPlayers:       allPlayers,
			Lang:             lang,
			Params:           params,
			TotalItems:       totalItems,
			TotalPages:       totalPages,
			IsSearchActive:   isSearchActive,
			ColumnVisibility: columnVisibilityMap,
		}
		node := templatesappplayers.PlayersTable(tableProps)
		return node.Render(c.Response().Writer)
	}

	// Get the user's active seasons
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch seasons")
	}

	// Use datamapper for clean conversion - no manual loop needed!
	seasons := datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)

	user, err := h.Queries.GetUserByID(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch user")
	}

	node := templatesappplayers.PlayersDashboardPage(templatesappplayers.PlayersDashboardPageProps{
		IsSidebarOpen:    user.IsSidebarOpen,
		ActiveLink:       "/app/players",
		Players:          paginatedPlayers,
		AllPlayers:       allPlayers,
		Lang:             lang,
		Seasons:          seasons,
		TotalItems:       totalItems,
		TotalPages:       totalPages,
		Params:           params,
		IsSearchActive:   isSearchActive,
		ColumnVisibility: columnVisibilityMap,
	})
	return node.Render(c.Response().Writer)
}

// GetAvailablePlayers returns a list of active players in a format suitable for selection in forms
func (h *PlayersHandler) GetAvailablePlayers(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Query players for this user
	players, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch players")
	}

	lang := cookies.GetLanguageFromCookie(c)

	node := templatesappplayers.PlayersSelectionList(players, lang)
	return node.Render(c.Response().Writer)

}

// GetPlayerSelectModal returns a modal with player selection checkboxes
func (h *PlayersHandler) GetPlayerSelectModal(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Query players for this user
	players, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch players")
	}

	lang := cookies.GetLanguageFromCookie(c)
	node := templatesappseasons.PlayerSelectModal(players, lang)
	return node.Render(c.Response().Writer)
}

// parsePlayersTableParams parses URL query parameters for table functionality
func parsePlayersTableParams(c echo.Context) templatesappplayers.PlayersTableParams {
	return templatesappplayers.PlayersTableParams{
		SortablePaginationParams: pagination.ParseSortablePagination(c, "name"),
	}
}

// filterPlayers filters players based on search term
func filterPlayers(players []db.Player, search string) []db.Player {
	if search == "" {
		return players
	}

	searchLower := strings.ToLower(search)
	var filtered []db.Player

	for _, player := range players {
		if strings.Contains(strings.ToLower(player.Name), searchLower) ||
			strings.Contains(strings.ToLower(player.Email.String), searchLower) ||
			strings.Contains(strconv.Itoa(int(player.PreferredMatchGroup)), searchLower) {
			filtered = append(filtered, player)
		}
	}

	return filtered
}

// sortPlayers sorts players based on field and direction
func sortPlayers(players []db.Player, sortField, direction string) []db.Player {
	if len(players) == 0 {
		return players
	}

	sorted := make([]db.Player, len(players))
	copy(sorted, players)

	sort.Slice(sorted, func(i, j int) bool {
		var aVal, bVal string
		switch sortField {
		case "name":
			aVal, bVal = sorted[i].Name, sorted[j].Name
		case "email":
			aVal, bVal = sorted[i].Email.String, sorted[j].Email.String
		case "preferredMatchGroup":
			aVal, bVal = strconv.Itoa(int(sorted[i].PreferredMatchGroup)), strconv.Itoa(int(sorted[j].PreferredMatchGroup))
		case "isActive":
			aVal, bVal = strconv.FormatBool(sorted[i].IsActive), strconv.FormatBool(sorted[j].IsActive)
		case "emailNotificationsEnabled":
			aVal, bVal = strconv.FormatBool(sorted[i].EmailNotificationsEnabled), strconv.FormatBool(sorted[j].EmailNotificationsEnabled)
		default:
			aVal, bVal = sorted[i].Name, sorted[j].Name
		}

		if direction == "desc" {
			return strings.Compare(aVal, bVal) > 0
		}
		return strings.Compare(aVal, bVal) < 0
	})

	return sorted
}

// paginatePlayers applies pagination to players slice
func paginatePlayers(players []db.Player, page, itemsPerPage int) []db.Player {
	if len(players) == 0 {
		return players
	}

	startIdx := (page - 1) * itemsPerPage
	endIdx := startIdx + itemsPerPage

	if startIdx >= len(players) {
		return []db.Player{}
	}

	if endIdx > len(players) {
		endIdx = len(players)
	}

	return players[startIdx:endIdx]
}

// ExportPlayersCSV exports all players for the current user as CSV
func (h *PlayersHandler) ExportPlayersCSV(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Get all players for this user
	players, err := h.Queries.GetPlayers(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to fetch players"})
	}

	// Set up CSV response
	config := utils.NewCSVExportConfig("players.csv")
	writer := utils.SetupCSVResponse(c, config)
	defer writer.Flush()

	// Write CSV header
	header := []string{
		"ID",
		"Name",
		"Email",
		"Preferred Match Group",
		"Active",
		"Email Notifications Enabled",
		"Picture URL",
		"Created At",
		"Updated At",
	}

	if err := writer.Write(header); err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to write CSV header"})
	}

	// Write player data
	for _, player := range players {
		record := []string{
			fmt.Sprintf("%d", player.ID),
			player.Name,
			utils.FormatCSVText(player.Email),
			fmt.Sprintf("%d", player.PreferredMatchGroup),
			utils.FormatCSVBool(player.IsActive),
			utils.FormatCSVBool(player.EmailNotificationsEnabled),
			utils.FormatCSVText(player.PictureUrl),
			utils.FormatCSVTimestamp(player.CreatedAt),
			utils.FormatCSVTimestamp(player.UpdatedAt),
		}

		if err := writer.Write(record); err != nil {
			return c.JSON(500, map[string]string{"error": "Failed to write CSV data"})
		}
	}

	return nil
}
