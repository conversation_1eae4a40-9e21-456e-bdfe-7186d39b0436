package handlers

import (
	"context"
	"fmt"
	"io"
	"math"
	"math/big"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/notifications"
	templatesspending "github.com/j-em/coachpad/templates/app/spending"
	"github.com/j-em/coachpad/templates/ui/inlinedatepicker"
	"github.com/j-em/coachpad/templates/ui/inlineedit"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/datamapper"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

// Helper function to create pgtype.Numeric from float64
func createNumeric(f float64) pgtype.Numeric {
	// Round to 2 decimal places first to avoid precision issues
	rounded := math.Round(f*100) / 100

	// Convert to cents and store as big.Int
	cents := int64(math.Round(rounded * 100))
	intVal := big.NewInt(cents)

	return pgtype.Numeric{
		Int:   intVal,
		Exp:   -2, // Two decimal places
		Valid: true,
	}
}

type SpendingHandler struct {
	Queries             *db.Queries
	NotificationService *notifications.Service
}

func (h *SpendingHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/spending", h.GetSpending)
	r.GET("/spending/search", h.SearchSpending)
	r.GET("/spending/table", h.GetSpendingTable)
	r.GET("/spending/total", h.GetSpendingTotal)
	r.GET("/spending/new", h.GetNewSpendingForm)
	r.POST("/spending", h.PostNewSpending)
	r.GET("/spending/export/csv", h.ExportSpendingCSV) // CSV export endpoint
	r.PUT("/spending/:id/amount", h.UpdateSpendingAmount)
	r.PUT("/spending/:id/description", h.UpdateSpendingDescription)
	r.PUT("/spending/:id/date", h.UpdateSpendingDate)
	r.PUT("/spending/:id/category", h.UpdateSpendingCategory)
	r.DELETE("/spending/:id", h.DeleteSpending)
}

func (h *SpendingHandler) GetSpending(c echo.Context) error {
	// Get user ID from context
	userID := c.Get(middleware.UserIDKey).(int32)
	lang := cookies.GetLanguageFromCookie(c)

	// Get all spending records for the user
	spendingRecords, err := h.Queries.GetUserSpending(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch spending records")
	}

	// Check if this is a boosted request
	if c.Request().Header.Get("HX-Boosted") == "true" {
		content := templatesspending.SpendingPageContent(templatesspending.SpendingPageContentProps{
			Spending: spendingRecords,
			Lang:     lang,
		})
		return content.Render(c.Response().Writer)
	}

	// Get user info for sidebar
	user, err := h.Queries.GetUserByID(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch user")
	}

	// Get unread notification count
	unreadCount, err := h.NotificationService.GetUnreadCount(context.Background(), userID)
	if err != nil {
		unreadCount = 0 // Fallback to 0 if error
	}

	// Get seasons for navigation
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch seasons")
	}

	// Use datamapper for clean conversion - no manual loop needed!
	seasons := datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)

	node := templatesspending.SpendingPage(templatesspending.SpendingPageProps{
		Spending:      spendingRecords,
		Lang:          lang,
		Seasons:       seasons,
		IsSidebarOpen: user.IsSidebarOpen,
		UserID:        userID,
		UnreadCount:   unreadCount,
	})
	return node.Render(c.Response().Writer)
}

func (h *SpendingHandler) GetSpendingTable(c echo.Context) error {
	// Get user ID from context
	userID := c.Get(middleware.UserIDKey).(int32)
	lang := cookies.GetLanguageFromCookie(c)

	// Get all spending records for the user
	spendingRecords, err := h.Queries.GetUserSpending(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch spending records")
	}

	// Load locales for the table
	locales := i18n.MustLoadTemplateLocales("./templates/app/spending/spending.locales.json", lang)

	// Return only the spending table content
	table := templatesspending.SpendingTable(spendingRecords, locales, lang)
	return table.Render(c.Response().Writer)
}

func (h *SpendingHandler) GetSpendingTotal(c echo.Context) error {
	// Get user ID from context
	userID := c.Get(middleware.UserIDKey).(int32)
	lang := cookies.GetLanguageFromCookie(c)

	// Get all spending records for the user
	spendingRecords, err := h.Queries.GetUserSpending(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch spending records")
	}

	// Load locales for the total
	locales := i18n.MustLoadTemplateLocales("./templates/app/spending/spending.locales.json", lang)

	// Return only the spending total content
	total := templatesspending.SpendingTotal(spendingRecords, locales)
	return total.Render(c.Response().Writer)
}

func (h *SpendingHandler) GetNewSpendingForm(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)
	lang := cookies.GetLanguageFromCookie(c)

	// Get all available entities for associations
	teams, err := h.Queries.GetTeams(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch teams")
	}

	players, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch players")
	}

	seasons, err := h.Queries.GetSeasons(context.Background(), pgtype.Int4{Int32: userID, Valid: true})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch seasons")
	}

	// For matches, we need to get matches across all user's seasons
	var matches []db.Match
	for _, season := range seasons {
		seasonMatches, err := h.Queries.GetMatchesBySeasonId(context.Background(), pgtype.Int4{Int32: season.ID, Valid: true})
		if err != nil {
			continue // Skip this season if we can't get matches
		}

		// Convert to db.Match type
		for _, m := range seasonMatches {
			matches = append(matches, db.Match{
				ID:              m.ID,
				SeasonID:        m.SeasonID,
				PlayerId1:       m.PlayerId1,
				PlayerId1Points: m.PlayerId1Points,
				PlayerId2:       m.PlayerId2,
				PlayerId2Points: m.PlayerId2Points,
				MatchDate:       m.MatchDate,
				WinnerID:        m.WinnerID,
				CreatedAt:       m.CreatedAt,
				UpdatedAt:       m.UpdatedAt,
				IsActive:        m.IsActive,
				MatchGroup:      m.MatchGroup,
			})
		}
	}

	// Always return modal - this endpoint is only used for modal-triggered forms
	modal := templatesspending.NewSpendingModal(teams, players, seasons, matches, lang)
	return modal.Render(c.Response().Writer)
}

type PostNewSpendingForm struct {
	Amount      string   `form:"amount" validate:"required"`
	Description string   `form:"description" validate:"required"`
	Date        string   `form:"date" validate:"required"`
	Category    string   `form:"category" validate:"required"`
	PlayerIDs   []string `form:"playerIds"`
	MatchIDs    []string `form:"matchIds"`
	SeasonIDs   []string `form:"seasonIds"`
	TeamIDs     []string `form:"teamIds"`
}

func (h *SpendingHandler) PostNewSpending(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	var form PostNewSpendingForm
	if err := c.Bind(&form); err != nil {
		fmt.Printf("Form binding error: %v\n", err)
		return c.String(http.StatusBadRequest, "Invalid form data")
	}

	// Debug: print form data
	fmt.Printf("Form data: %+v\n", form)
	fmt.Printf("TeamIDs: %v (len=%d)\n", form.TeamIDs, len(form.TeamIDs))
	fmt.Printf("PlayerIDs: %v (len=%d)\n", form.PlayerIDs, len(form.PlayerIDs))

	// Validate the form
	if err := c.Validate(form); err != nil {
		fmt.Printf("Validation error: %v\n", err)
		lang := cookies.GetLanguageFromCookie(c)
		c.Response().WriteHeader(http.StatusBadRequest)
		return templatesspending.NewSpendingErrorToast("Validation error: "+err.Error(), lang).Render(c.Response().Writer)
	}

	// Check that at least one association is provided
	if len(form.PlayerIDs) == 0 && len(form.MatchIDs) == 0 && len(form.SeasonIDs) == 0 && len(form.TeamIDs) == 0 {
		fmt.Printf("No associations provided\n")
		lang := cookies.GetLanguageFromCookie(c)
		c.Response().WriteHeader(http.StatusBadRequest)
		return templatesspending.NewSpendingErrorToast("At least one entity association is required", lang).Render(c.Response().Writer)
	}

	// Parse and validate amount
	amount, err := strconv.ParseFloat(form.Amount, 64)
	if err != nil || amount <= 0 || amount > 1000000 {
		lang := cookies.GetLanguageFromCookie(c)
		c.Response().WriteHeader(http.StatusBadRequest)
		return templatesspending.NewSpendingErrorToast("Invalid amount", lang).Render(c.Response().Writer)
	}

	// Parse date
	date, err := time.Parse("2006-01-02", form.Date)
	if err != nil {
		lang := cookies.GetLanguageFromCookie(c)
		c.Response().WriteHeader(http.StatusBadRequest)
		return templatesspending.NewSpendingErrorToast("Invalid date format", lang).Render(c.Response().Writer)
	}

	// Create the spending record
	fmt.Printf("Creating spending record with amount: %f, description: %s, category: %s\n", amount, form.Description, form.Category)
	spending, err := h.Queries.CreateSpending(context.Background(), db.CreateSpendingParams{
		UserID:      userID,
		Amount:      createNumeric(amount),
		Description: form.Description,
		Date:        pgtype.Date{Time: date, Valid: true},
		Category:    form.Category,
		FileUrls:    []string{}, // TODO: Implement file upload
	})
	if err != nil {
		fmt.Printf("Failed to create spending record: %v\n", err)
		return c.String(http.StatusInternalServerError, "Failed to create spending record")
	}
	fmt.Printf("Created spending record with ID: %d\n", spending.ID)

	// Create associations
	var associationErrors []string

	for _, playerIDStr := range form.PlayerIDs {
		if playerIDStr == "" {
			continue
		}
		playerID, err := strconv.Atoi(playerIDStr)
		if err != nil {
			continue
		}
		if err := h.Queries.CreateSpendingPlayerAssociation(context.Background(), db.CreateSpendingPlayerAssociationParams{
			SpendingID: spending.ID,
			PlayerID:   int32(playerID),
		}); err != nil {
			associationErrors = append(associationErrors, fmt.Sprintf("Failed to associate player %d", playerID))
		}
	}

	for _, matchIDStr := range form.MatchIDs {
		if matchIDStr == "" {
			continue
		}
		matchID, err := strconv.Atoi(matchIDStr)
		if err != nil {
			continue
		}
		if err := h.Queries.CreateSpendingMatchAssociation(context.Background(), db.CreateSpendingMatchAssociationParams{
			SpendingID: spending.ID,
			MatchID:    int32(matchID),
		}); err != nil {
			associationErrors = append(associationErrors, fmt.Sprintf("Failed to associate match %d", matchID))
		}
	}

	for _, seasonIDStr := range form.SeasonIDs {
		if seasonIDStr == "" {
			continue
		}
		seasonID, err := strconv.Atoi(seasonIDStr)
		if err != nil {
			continue
		}
		if err := h.Queries.CreateSpendingSeasonAssociation(context.Background(), db.CreateSpendingSeasonAssociationParams{
			SpendingID: spending.ID,
			SeasonID:   int32(seasonID),
		}); err != nil {
			associationErrors = append(associationErrors, fmt.Sprintf("Failed to associate season %d", seasonID))
		}
	}

	for _, teamIDStr := range form.TeamIDs {
		if teamIDStr == "" {
			continue
		}
		teamID, err := strconv.Atoi(teamIDStr)
		if err != nil {
			continue
		}
		if err := h.Queries.CreateSpendingTeamAssociation(context.Background(), db.CreateSpendingTeamAssociationParams{
			SpendingID: spending.ID,
			TeamID:     int32(teamID),
		}); err != nil {
			associationErrors = append(associationErrors, fmt.Sprintf("Failed to associate team %d", teamID))
		}
	}

	lang := cookies.GetLanguageFromCookie(c)

	// Dispatch HTMX event for table refresh
	err = utils.SetTypedTrigger(c, utils.HXEventUpdateSpendings, utils.UpdateSpendingsEvent{})
	if err != nil {
		fmt.Printf("Failed to set HTMX trigger: %v\n", err)
	}

	// Return appropriate toast component - this endpoint targets #toast-body-container from modal
	if len(associationErrors) > 0 {
		toastMessage := "Spending created successfully, but some associations failed: " + strings.Join(associationErrors, ", ")
		return templatesspending.NewSpendingErrorToast(toastMessage, lang).Render(c.Response().Writer)
	} else {
		return templatesspending.NewSpendingSuccessToast("Spending created successfully", lang).Render(c.Response().Writer)
	}
}

func (h *SpendingHandler) UpdateSpendingAmount(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse spending ID from URL
	var spendingID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &spendingID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid spending ID")
	}

	// Parse new amount from contenteditable content
	amountStr := strings.TrimSpace(c.FormValue(""))
	if amountStr == "" {
		// Get from request body for contenteditable
		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return c.String(http.StatusBadRequest, "Failed to read request body")
		}
		amountStr = strings.TrimSpace(string(body))
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount <= 0 || amount > 1000000 {
		return c.String(http.StatusBadRequest, "Invalid amount")
	}

	// Round the amount to 2 decimal places for consistent display
	roundedAmount := math.Round(amount*100) / 100

	// Update the spending record
	err = h.Queries.UpdateSpendingAmount(context.Background(), db.UpdateSpendingAmountParams{
		ID:     spendingID,
		Amount: createNumeric(amount),
		UserID: userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to update spending amount")
	}

	// Dispatch HTMX event for total refresh
	err = utils.SetTypedTrigger(c, utils.HXEventUpdateSpendings, utils.UpdateSpendingsEvent{})
	if err != nil {
		fmt.Printf("Failed to set HTMX trigger: %v\n", err)
	}

	// Return updated inline edit component for HTMX with rounded amount
	return inlineedit.InlineEdit(inlineedit.InlineEditProps{
		ID:          fmt.Sprintf("amount-edit-%d", spendingID),
		Value:       fmt.Sprintf("%.2f", roundedAmount),
		EndpointID:  fmt.Sprintf("%d", spendingID),
		EndpointURL: fmt.Sprintf("/app/spending/%d/amount", spendingID),
		DataTestID:  fmt.Sprintf("amount-edit-%d", spendingID),
		Type:        "number",
	}).Render(c.Response().Writer)
}

func (h *SpendingHandler) UpdateSpendingDescription(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse spending ID from URL
	var spendingID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &spendingID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid spending ID")
	}

	// Parse new description from contenteditable content
	description := strings.TrimSpace(c.FormValue(""))
	if description == "" {
		// Get from request body for contenteditable
		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return c.String(http.StatusBadRequest, "Failed to read request body")
		}
		description = strings.TrimSpace(string(body))
	}

	if description == "" {
		return c.String(http.StatusBadRequest, "Description cannot be empty")
	}

	// Update the spending record
	err := h.Queries.UpdateSpendingDescription(context.Background(), db.UpdateSpendingDescriptionParams{
		ID:          spendingID,
		Description: description,
		UserID:      userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to update spending description")
	}

	// Return updated inline edit component for HTMX
	return inlineedit.InlineEdit(inlineedit.InlineEditProps{
		ID:          fmt.Sprintf("description-edit-%d", spendingID),
		Value:       description,
		EndpointID:  fmt.Sprintf("%d", spendingID),
		EndpointURL: fmt.Sprintf("/app/spending/%d/description", spendingID),
		DataTestID:  fmt.Sprintf("description-edit-%d", spendingID),
		Type:        "text",
	}).Render(c.Response().Writer)
}

func (h *SpendingHandler) UpdateSpendingDate(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse spending ID from URL
	var spendingID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &spendingID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid spending ID")
	}

	// Parse new date from contenteditable content
	dateStr := strings.TrimSpace(c.FormValue(""))
	if dateStr == "" {
		// Get from request body for contenteditable
		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return c.String(http.StatusBadRequest, "Failed to read request body")
		}
		dateStr = strings.TrimSpace(string(body))
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return c.String(http.StatusBadRequest, "Invalid date format")
	}

	// Update the spending record
	err = h.Queries.UpdateSpendingDate(context.Background(), db.UpdateSpendingDateParams{
		ID:     spendingID,
		Date:   pgtype.Date{Time: date, Valid: true},
		UserID: userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to update spending date")
	}

	// Get language for localization
	lang := cookies.GetLanguageFromCookie(c)

	// Return updated inline date picker component for HTMX
	return inlinedatepicker.InlineDatePicker(inlinedatepicker.InlineDatePickerProps{
		ID:          fmt.Sprintf("%d", spendingID),
		Value:       date.Format("2006-01-02"),
		EndpointURL: fmt.Sprintf("/app/spending/%d/date", spendingID),
		DataTestID:  fmt.Sprintf("date-edit-%d", spendingID),
		Lang:        lang,
	}).Render(c.Response().Writer)
}

func (h *SpendingHandler) UpdateSpendingCategory(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse spending ID from URL
	var spendingID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &spendingID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid spending ID")
	}

	// Parse new category
	category := c.FormValue("category")
	if category == "" {
		return c.String(http.StatusBadRequest, "Category cannot be empty")
	}

	// Update the spending record
	err := h.Queries.UpdateSpendingCategory(context.Background(), db.UpdateSpendingCategoryParams{
		ID:       spendingID,
		Category: category,
		UserID:   userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to update spending category")
	}

	return c.NoContent(http.StatusOK)
}

func (h *SpendingHandler) DeleteSpending(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse spending ID from URL
	var spendingID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &spendingID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid spending ID")
	}

	// Delete the spending record (soft delete)
	err := h.Queries.DeleteSpending(context.Background(), db.DeleteSpendingParams{
		ID:     spendingID,
		UserID: userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to delete spending record")
	}

	// Dispatch HTMX event for total refresh
	err = utils.SetTypedTrigger(c, utils.HXEventUpdateSpendings, utils.UpdateSpendingsEvent{})
	if err != nil {
		fmt.Printf("Failed to set HTMX trigger: %v\n", err)
	}

	// Return empty content for HTMX swap to remove the row
	return c.String(http.StatusOK, "")
}

func (h *SpendingHandler) SearchSpending(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)
	lang := cookies.GetLanguageFromCookie(c)

	// Get search and filter parameters
	searchTerm := c.QueryParam("search")
	category := c.QueryParam("category")
	startDate := c.QueryParam("startDate")
	endDate := c.QueryParam("endDate")

	// Debug logging
	fmt.Printf("SearchSpending called: search='%s' category='%s' startDate='%s' endDate='%s' userID=%d\n",
		searchTerm, category, startDate, endDate, userID)

	var spendingRecords []db.Spending
	var err error

	// Use the comprehensive search and filter query
	params := db.SearchAndFilterUserSpendingParams{
		UserID:  userID,
		Column2: searchTerm,
		Column3: category,
	}

	// Parse date parameters
	if startDate != "" {
		if parsedDate, parseErr := time.Parse("2006-01-02", startDate); parseErr == nil {
			params.Column4 = pgtype.Date{Time: parsedDate, Valid: true}
		}
	}

	if endDate != "" {
		if parsedDate, parseErr := time.Parse("2006-01-02", endDate); parseErr == nil {
			params.Column5 = pgtype.Date{Time: parsedDate, Valid: true}
		}
	}

	spendingRecords, err = h.Queries.SearchAndFilterUserSpending(context.Background(), params)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to search spending records")
	}

	// Debug logging
	fmt.Printf("SearchSpending results: found %d records\n", len(spendingRecords))
	for i, record := range spendingRecords {
		fmt.Printf("  Record %d: ID=%d, Description='%s', Category='%s'\n",
			i+1, record.ID, record.Description, record.Category)
	}

	// Return only the table content for HTMX
	locales := i18n.MustLoadTemplateLocales("./templates/app/spending/spending.locales.json", lang)
	content := templatesspending.SpendingTable(spendingRecords, locales, lang)
	return content.Render(c.Response().Writer)
}

// ExportSpendingCSV exports all spending records for the current user as CSV
func (h *SpendingHandler) ExportSpendingCSV(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Get all spending records for this user
	spendingRecords, err := h.Queries.GetUserSpending(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to fetch spending records"})
	}

	// Set up CSV response
	config := utils.NewCSVExportConfig("spending.csv")
	writer := utils.SetupCSVResponse(c, config)
	defer writer.Flush()

	// Write CSV header
	header := []string{
		"ID",
		"Date",
		"Amount",
		"Description",
		"Category",
		"Created At",
		"Updated At",
	}

	if err := writer.Write(header); err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to write CSV header"})
	}

	// Write spending data
	for _, spending := range spendingRecords {
		record := []string{
			fmt.Sprintf("%d", spending.ID),
			utils.FormatCSVDate(spending.Date),
			utils.FormatCSVNumeric(spending.Amount),
			spending.Description,
			spending.Category,
			utils.FormatCSVTimestamp(spending.CreatedAt),
			utils.FormatCSVTimestamp(spending.UpdatedAt),
		}

		if err := writer.Write(record); err != nil {
			return c.JSON(500, map[string]string{"error": "Failed to write CSV data"})
		}
	}

	return nil
}
