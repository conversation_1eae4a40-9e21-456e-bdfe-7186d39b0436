package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
)

// APIBaseHandler provides common functionality for all API endpoints
type APIBaseHandler struct {
	Queries *db.Queries
}

// APIResponse represents the standard API response format
type APIResponse struct {
	Data  interface{} `json:"data,omitempty"`
	Meta  *APIMeta    `json:"meta,omitempty"`
	Error *APIError   `json:"error,omitempty"`
}

// APIMeta provides metadata for paginated responses
type APIMeta struct {
	Total int `json:"total"`
	Page  int `json:"page"`
	Limit int `json:"limit"`
	Pages int `json:"pages"`
}

// APIError represents error responses
type APIError struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// APISuccessResponse creates a successful API response
func APISuccessResponse(data interface{}) *APIResponse {
	return &APIResponse{
		Data: data,
	}
}

// APIPaginatedResponse creates a paginated API response
func APIPaginatedResponse(data interface{}, meta *APIMeta) *APIResponse {
	return &APIResponse{
		Data: data,
		Meta: meta,
	}
}

// APIErrorResponse creates an error API response
func APIErrorResponse(code, message string, details interface{}) *APIResponse {
	return &APIResponse{
		Error: &APIError{
			Code:    code,
			Message: message,
			Details: details,
		},
	}
}

// GetProfile returns the current user's profile information
func (h *APIBaseHandler) GetProfile(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return c.JSON(http.StatusUnauthorized, APIErrorResponse(
			"UNAUTHORIZED",
			"User not authenticated",
			nil,
		))
	}

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, APIErrorResponse(
			"INTERNAL_ERROR",
			"Failed to retrieve user profile",
			nil,
		))
	}

	// Transform user data for API response (remove sensitive fields)
	profile := map[string]interface{}{
		"id":                user.ID,
		"name":              user.Name,
		"email":             user.Email,
		"country":           user.Country,
		"lang":              user.Lang,
		"subscription_tier": user.SubscriptionTier,
		"is_verified":       user.IsVerified,
		"created_at":        user.CreatedAt.Time,
	}

	if user.Phone.Valid {
		profile["phone"] = user.Phone.String
	}
	if user.Birthday.Valid {
		profile["birthday"] = user.Birthday.Time.Format("2006-01-02")
	}
	if user.PictureUrl.Valid {
		profile["picture_url"] = user.PictureUrl.String
	}

	return c.JSON(http.StatusOK, APISuccessResponse(profile))
}

// HealthCheck provides an API health check endpoint
func (h *APIBaseHandler) HealthCheck(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]interface{}{
		"status":  "healthy",
		"version": "1.0.0",
		"message": "CoachPad API is operational",
	})
}

// RegisterRoutes registers the base API routes
func (h *APIBaseHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/profile", h.GetProfile)
	r.GET("/health", h.HealthCheck)
}
