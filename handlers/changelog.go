package handlers

import (
	"github.com/j-em/coachpad/cookies"
	templatesappchangelog "github.com/j-em/coachpad/templates/app/changelog"
	"github.com/labstack/echo/v4"
)

type ChangelogHandler struct{}

func (h *ChangelogHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/changelog", h.GetChangelogModal)
}

func (h *ChangelogHandler) GetChangelogModal(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)

	node := templatesappchangelog.ChangelogContent(lang)
	return node.Render(c.Response().Writer)
}
