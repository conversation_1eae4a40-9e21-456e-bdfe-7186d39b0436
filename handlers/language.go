package handlers

import (
	"net/http"

	"github.com/j-em/coachpad/cookies"
	"github.com/labstack/echo/v4"
)

// LanguageForm represents the language selection form
type LanguageForm struct {
	Lang string `form:"lang" validate:"required"`
}

// LanguageHandler handles language preference endpoints
type LanguageHandler struct{}

func (h *LanguageHandler) RegisterRoutes(e *echo.Echo) {
	e.PUT("/lang", h.UpdateLanguage)
}

// UpdateLanguage handles PUT /lang request and updates the language cookie
func (h *LanguageHandler) UpdateLanguage(c echo.Context) error {
	// Create a new form instance
	var form LanguageForm

	// Bind the form values
	if err := c.Bind(&form); err != nil {
		return c.NoContent(http.StatusBadRequest)
	}

	// Validate the language value
	if form.Lang == "" {
		return c.NoContent(http.StatusBadRequest)
	}

	// Use the cookies package to set the language cookie
	cookies.SetLanguageCookie(c, form.Lang)

	// Return no content for success
	return c.NoContent(http.StatusNoContent)
}
