package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/sse"
	"github.com/j-em/coachpad/stripe"
	templatesappsettings "github.com/j-em/coachpad/templates/app/settings"
	"github.com/labstack/echo/v4"
	stripego "github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/client"
	"github.com/stripe/stripe-go/v82/webhook"
)

type StripeHandler struct {
	Queries      *db.Queries
	StripeClient *client.API
	SSEManager   *sse.Manager
}

func (h *StripeHandler) RegisterRoutes(r *echo.Group) {
	r.POST("/api/subscribe", h.CreateSubscription)
	r.POST("/api/update-payment-method", h.CreateSetupIntent)
	r.POST("/api/sync-stripe-user", h.SyncStripeUser)
	r.POST("/api/cancel-subscription", h.CancelSubscription)
}

func (h *StripeHandler) RegisterWebhook(e *echo.Echo) {
	e.POST("/stripe-webhook", h.StripeWebhook)
}

// CreateSubscription creates a Stripe subscription for the user and returns the client secret
func (h *StripeHandler) CreateSubscription(c echo.Context) error {
	userID := c.Get("userID").(int32) // Assumes middleware sets userID
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		fmt.Print(err)
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "User not found"})
	}
	stripeClient := h.StripeClient
	params := &stripego.SubscriptionParams{
		Customer:        stripego.String(user.StripeID),
		Items:           []*stripego.SubscriptionItemsParams{{Price: stripego.String(stripe.GetProPlanPriceID())}},
		PaymentBehavior: stripego.String("default_incomplete"),
		Expand:          []*string{stripego.String("latest_invoice"), stripego.String("latest_invoice.confirmation_secret")},
	}
	subscription, err := stripeClient.Subscriptions.New(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}
	clientSecret := ""
	// Debug logging to file
	debugMsg := fmt.Sprintf("=== SUBSCRIPTION CREATED ===\nSubscription ID: %s\nSubscription Status: %s\nLatest Invoice: %v\n",
		subscription.ID, subscription.Status, subscription.LatestInvoice != nil)

	// Write debug to file
	os.WriteFile("/var/home/<USER>/coachpad/tmp/subscription-debug.log", []byte(debugMsg), 0644)

	fmt.Printf("=== SUBSCRIPTION CREATED ===\n")
	fmt.Printf("Subscription ID: %s\n", subscription.ID)
	fmt.Printf("Subscription Status: %s\n", subscription.Status)
	fmt.Printf("Latest Invoice: %v\n", subscription.LatestInvoice != nil)

	if subscription.LatestInvoice != nil {
		fmt.Printf("Invoice ID: %s\n", subscription.LatestInvoice.ID)
		fmt.Printf("Invoice Status: %s\n", subscription.LatestInvoice.Status)
		fmt.Printf("Confirmation Secret: %v\n", subscription.LatestInvoice.ConfirmationSecret != nil)

		if subscription.LatestInvoice.ConfirmationSecret != nil {
			clientSecret = subscription.LatestInvoice.ConfirmationSecret.ClientSecret
			fmt.Printf("Client Secret: %s...\n", clientSecret[:20])
		}
	}

	fmt.Printf("Returning client secret: %v\n", clientSecret != "")
	fmt.Printf("=== RESPONSE: clientSecret='%s', subscriptionId='%s' ===\n", clientSecret, subscription.ID)
	return c.JSON(http.StatusOK, map[string]string{"clientSecret": clientSecret, "subscriptionId": subscription.ID})
}

// CancelSubscription cancels the user's active Stripe subscription with immediate cancellation and proration
func (h *StripeHandler) CancelSubscription(c echo.Context) error {
	userID := c.Get("userID").(int32)
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "User not found"})
	}

	if user.StripeID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "User has no Stripe customer ID"})
	}

	stripeClient := h.StripeClient

	// Find active subscription for the customer
	subs := stripeClient.Subscriptions.List(&stripego.SubscriptionListParams{
		Customer: stripego.String(user.StripeID),
		Status:   stripego.String("active"),
	})

	cancelledCount := 0
	for subs.Next() {
		s := subs.Subscription()
		// Cancel immediately with proration - Stripe will handle refunds automatically
		cancelParams := &stripego.SubscriptionCancelParams{
			Prorate: stripego.Bool(true), // Enable proration for mid-cycle cancellations
		}

		_, err := stripeClient.Subscriptions.Cancel(s.ID, cancelParams)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to cancel subscription: " + err.Error()})
		}
		cancelledCount++
	}

	if cancelledCount == 0 {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "No active subscription found"})
	}

	// Don't update DB here - let the webhook handle it for consistency
	// Stripe webhook will fire shortly and update the user to free tier
	return c.JSON(http.StatusOK, map[string]string{
		"status":  "cancellation_requested",
		"message": "Subscription cancellation initiated. You will be downgraded shortly and receive a prorated refund.",
	})
}

// CreateSetupIntent creates a Stripe SetupIntent for updating payment method
func (h *StripeHandler) CreateSetupIntent(c echo.Context) error {
	userID := c.Get("userID").(int32)
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "User not found"})
	}
	stripeClient := h.StripeClient
	params := &stripego.SetupIntentParams{
		Customer: stripego.String(user.StripeID),
	}
	si, err := stripeClient.SetupIntents.New(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}
	return c.JSON(http.StatusOK, map[string]string{"clientSecret": si.ClientSecret})
}

// SyncStripeUser fetches latest Stripe customer/subscription and updates local DB
func (h *StripeHandler) SyncStripeUser(c echo.Context) error {
	userID := c.Get("userID").(int32)
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "User not found"})
	}

	// Check if user has a Stripe ID
	if user.StripeID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "User has no Stripe customer ID"})
	}

	stripeClient := h.StripeClient
	// Fetch customer from Stripe
	customer, err := stripeClient.Customers.Get(user.StripeID, nil)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Stripe customer fetch failed: " + err.Error()})
	}

	// Verify customer exists
	if customer == nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Stripe customer not found"})
	}

	// Fetch active subscription
	subs := stripeClient.Subscriptions.List(&stripego.SubscriptionListParams{Customer: stripego.String(user.StripeID), Status: stripego.String("active")})
	subTier := "free"
	fmt.Printf("=== SYNC DEBUG: Checking subscriptions for customer %s ===\n", user.StripeID)
	for subs.Next() {
		s := subs.Subscription()
		fmt.Printf("Found subscription: ID=%s, Status=%s\n", s.ID, s.Status)
		if s.Status == "active" || s.Status == "trialing" {
			subTier = "pro"
			break
		}
	}
	fmt.Printf("Final subscription tier: %s\n", subTier)
	// Update user subscription tier in DB
	err = h.Queries.UpdateUserSubscriptionTier(c.Request().Context(), db.UpdateUserSubscriptionTierParams{
		ID:               userID,
		SubscriptionTier: subTier,
	})
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to update user subscription tier: " + err.Error()})
	}

	// Send SSE event to update the UI in real-time
	h.sendSubscriptionUpdateSSE(userID, subTier, user.Lang)

	return c.JSON(http.StatusOK, map[string]string{"status": "synced", "tier": subTier})
	// End of SyncStripeUser
}

// StripeWebhook handles Stripe webhook events
func (h *StripeHandler) StripeWebhook(c echo.Context) error {
	// Enhanced logging to file
	logFile := "/var/home/<USER>/coachpad/tmp/webhook-debug.log"
	logMsg := fmt.Sprintf("\n=== WEBHOOK RECEIVED ===\nTimestamp: %s\nURL: %s\nMethod: %s\nContent-Type: %s\nContent-Length: %s\n",
		time.Now().Format(time.RFC3339), c.Request().URL.String(), c.Request().Method,
		c.Request().Header.Get("Content-Type"), c.Request().Header.Get("Content-Length"))
	os.WriteFile(logFile, []byte(logMsg), 0644)

	// Console logging
	fmt.Printf("\n=== WEBHOOK RECEIVED ===\n")
	fmt.Printf("Timestamp: %s\n", time.Now().Format(time.RFC3339))
	fmt.Printf("URL: %s\n", c.Request().URL.String())
	fmt.Printf("Method: %s\n", c.Request().Method)
	fmt.Printf("Content-Type: %s\n", c.Request().Header.Get("Content-Type"))
	fmt.Printf("User-Agent: %s\n", c.Request().Header.Get("User-Agent"))

	// Read the request body
	body, err := io.ReadAll(c.Request().Body)
	if err != nil {
		errMsg := fmt.Sprintf("Error reading request body: %v\n", err)
		fmt.Print(errMsg)
		appendToFile(logFile, errMsg)
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Failed to read request body"})
	}

	bodyLogMsg := fmt.Sprintf("Body length: %d bytes\nBody preview: %s\n", len(body), string(body[:min(len(body), 200)]))
	fmt.Print(bodyLogMsg)
	appendToFile(logFile, bodyLogMsg)

	sigHeader := c.Request().Header.Get("Stripe-Signature")
	webhookSecret := stripe.GetWebhookSecret()

	sigLogMsg := fmt.Sprintf("Stripe-Signature: %s\nWebhook secret set: %v\n",
		sigHeader, webhookSecret != "")
	fmt.Print(sigLogMsg)
	appendToFile(logFile, sigLogMsg)

	event, err := verifyStripeWebhook(body, sigHeader, webhookSecret)
	if err != nil {
		errMsg := fmt.Sprintf("Webhook verification failed: %v\n", err)
		fmt.Print(errMsg)
		appendToFile(logFile, errMsg)
		// Webhook verification failed
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Webhook signature verification failed: " + err.Error()})
	}
	// Processing webhook event

	eventLogMsg := fmt.Sprintf("Webhook verified successfully!\nEvent ID: %s\nEvent Type: %s\n", event.ID, event.Type)
	fmt.Print(eventLogMsg)
	appendToFile(logFile, eventLogMsg)
	switch event.Type {
	case "payment_method.attached":
		caseLogMsg := fmt.Sprintf("Processing payment_method.attached event\n")
		fmt.Print(caseLogMsg)
		appendToFile(logFile, caseLogMsg)
		// Update user's payment method in DB (optional: detach old methods)
		var pm stripego.PaymentMethod
		if err := json.Unmarshal(event.Data.Raw, &pm); err == nil {
			_, err := h.Queries.GetUserByStripeID(c.Request().Context(), pm.Customer.ID)
			if err == nil {
				// Payment method attached - could store info if needed
			}
		}
	case "payment_intent.succeeded":
		caseLogMsg := fmt.Sprintf("Processing payment_intent.succeeded event\n")
		fmt.Print(caseLogMsg)
		appendToFile(logFile, caseLogMsg)
		// Upgrade user to Pro in DB when payment intent succeeds
		var pi stripego.PaymentIntent
		if err := json.Unmarshal(event.Data.Raw, &pi); err == nil {
			if pi.Customer != nil {
				customerLogMsg := fmt.Sprintf("Payment intent has customer: %s\n", pi.Customer.ID)
				fmt.Print(customerLogMsg)
				appendToFile(logFile, customerLogMsg)

				user, err := h.Queries.GetUserByStripeID(c.Request().Context(), pi.Customer.ID)
				if err != nil {
					errMsg := fmt.Sprintf("Error finding user by Stripe ID %s: %v\n", pi.Customer.ID, err)
					fmt.Print(errMsg)
					appendToFile(logFile, errMsg)
				} else {
					userLogMsg := fmt.Sprintf("Found user: ID=%d, Lang=%s\n", user.ID, user.Lang)
					fmt.Print(userLogMsg)
					appendToFile(logFile, userLogMsg)

					err = h.Queries.UpdateUserSubscriptionTier(c.Request().Context(), db.UpdateUserSubscriptionTierParams{
						ID:               user.ID,
						SubscriptionTier: "pro",
					})
					if err != nil {
						errMsg := fmt.Sprintf("Error updating subscription tier: %v\n", err)
						fmt.Print(errMsg)
						appendToFile(logFile, errMsg)
					} else {
						successMsg := fmt.Sprintf("Successfully updated user %d to pro tier\n", user.ID)
						fmt.Print(successMsg)
						appendToFile(logFile, successMsg)

						// Send SSE event to update the UI in real-time
						sseMsg := fmt.Sprintf("Sending SSE update to user %d\n", user.ID)
						fmt.Print(sseMsg)
						appendToFile(logFile, sseMsg)
						h.sendSubscriptionUpdateSSE(user.ID, "pro", user.Lang)
					}
				}
			} else {
				noCustomerMsg := fmt.Sprintf("Payment intent has no customer\n")
				fmt.Print(noCustomerMsg)
				appendToFile(logFile, noCustomerMsg)
			}
		} else {
			unmarshalErrMsg := fmt.Sprintf("Error unmarshaling payment intent: %v\n", err)
			fmt.Print(unmarshalErrMsg)
			appendToFile(logFile, unmarshalErrMsg)
		}
	case "customer.subscription.deleted":
		caseLogMsg := fmt.Sprintf("Processing customer.subscription.deleted event\n")
		fmt.Print(caseLogMsg)
		appendToFile(logFile, caseLogMsg)
		// Downgrade user to Free in DB
		var sub stripego.Subscription
		if err := json.Unmarshal(event.Data.Raw, &sub); err == nil {
			subscriptionLogMsg := fmt.Sprintf("Subscription cancelled: ID=%s, Customer=%s, CancelAt=%v\n",
				sub.ID, sub.Customer.ID, sub.CanceledAt)
			fmt.Print(subscriptionLogMsg)
			appendToFile(logFile, subscriptionLogMsg)

			user, err := h.Queries.GetUserByStripeID(c.Request().Context(), sub.Customer.ID)
			if err == nil {
				userLogMsg := fmt.Sprintf("Found user for cancelled subscription: ID=%d, Lang=%s\n", user.ID, user.Lang)
				fmt.Print(userLogMsg)
				appendToFile(logFile, userLogMsg)

				err = h.Queries.UpdateUserSubscriptionTier(c.Request().Context(), db.UpdateUserSubscriptionTierParams{
					ID:               user.ID,
					SubscriptionTier: "free",
				})
				if err == nil {
					successMsg := fmt.Sprintf("Successfully downgraded user %d to free tier\n", user.ID)
					fmt.Print(successMsg)
					appendToFile(logFile, successMsg)

					// Send SSE event to update the UI in real-time
					h.sendSubscriptionUpdateSSE(user.ID, "free", user.Lang)
				} else {
					errMsg := fmt.Sprintf("Error updating subscription tier for user %d: %v\n", user.ID, err)
					fmt.Print(errMsg)
					appendToFile(logFile, errMsg)
				}
			} else {
				errMsg := fmt.Sprintf("Error finding user by Stripe ID %s: %v\n", sub.Customer.ID, err)
				fmt.Print(errMsg)
				appendToFile(logFile, errMsg)
			}
		} else {
			unmarshalErrMsg := fmt.Sprintf("Error unmarshaling subscription: %v\n", err)
			fmt.Print(unmarshalErrMsg)
			appendToFile(logFile, unmarshalErrMsg)
		}
	case "invoice.payment_succeeded":
		caseLogMsg := fmt.Sprintf("Processing invoice.payment_succeeded event\n")
		fmt.Print(caseLogMsg)
		appendToFile(logFile, caseLogMsg)
		// Handle successful recurring payments - ensures pro status is maintained
		var inv stripego.Invoice
		if err := json.Unmarshal(event.Data.Raw, &inv); err == nil {
			if inv.Customer != nil {
				user, err := h.Queries.GetUserByStripeID(c.Request().Context(), inv.Customer.ID)
				if err == nil {
					// Ensure user is still on pro tier for successful payments
					err = h.Queries.UpdateUserSubscriptionTier(c.Request().Context(), db.UpdateUserSubscriptionTierParams{
						ID:               user.ID,
						SubscriptionTier: "pro",
					})
					if err == nil {
						h.sendSubscriptionUpdateSSE(user.ID, "pro", user.Lang)
					}
				}
			}
		}
	default:
		defaultMsg := fmt.Sprintf("Unhandled event type: %s\n", event.Type)
		fmt.Print(defaultMsg)
		appendToFile(logFile, defaultMsg)
	}

	return c.JSON(http.StatusOK, map[string]string{"status": "webhook processed"})
}

// sendSubscriptionUpdateSSE sends an SSE event to update the subscription section in real-time
func (h *StripeHandler) sendSubscriptionUpdateSSE(userID int32, subscriptionTier, lang string) {
	logFile := "/var/home/<USER>/coachpad/tmp/webhook-debug.log"

	if h.SSEManager == nil {
		errMsg := fmt.Sprintf("SSEManager is nil, cannot send subscription update event\n")
		fmt.Print(errMsg)
		appendToFile(logFile, errMsg)
		return
	}

	sseStartMsg := fmt.Sprintf("SSE Manager available, generating HTML for user %d tier %s lang %s\n", userID, subscriptionTier, lang)
	fmt.Print(sseStartMsg)
	appendToFile(logFile, sseStartMsg)

	// Generate the inner HTML content for innerHTML swap
	subscriptionHTML := templatesappsettings.SubscriptionSectionInner(templatesappsettings.SubscriptionSectionProps{
		SubscriptionTier: subscriptionTier,
		Lang:             lang,
	})

	// Convert to HTML string
	var htmlBuilder strings.Builder
	if err := subscriptionHTML.Render(&htmlBuilder); err != nil {
		errMsg := fmt.Sprintf("Error rendering subscription section HTML: %v\n", err)
		fmt.Print(errMsg)
		appendToFile(logFile, errMsg)
		return
	}

	// Clean up the HTML content for SSE transmission
	rawHTML := htmlBuilder.String()
	htmlContent := strings.ReplaceAll(rawHTML, "\n", "")
	htmlContent = strings.ReplaceAll(htmlContent, "\t", "")
	// Normalize multiple spaces to single space
	htmlContent = regexp.MustCompile(`\s+`).ReplaceAllString(htmlContent, " ")
	htmlContent = strings.TrimSpace(htmlContent)

	htmlLengthMsg := fmt.Sprintf("Generated HTML content length: %d characters (cleaned from %d)\n", len(htmlContent), len(rawHTML))
	fmt.Print(htmlLengthMsg)
	appendToFile(logFile, htmlLengthMsg)

	// Create SSE event
	event := sse.Event{
		Event: "SubscriptionUpdated",
		Data:  htmlContent,
		ID:    fmt.Sprintf("sub_%d_%d", userID, time.Now().UnixNano()),
	}

	// Check connected clients count
	clientCount := h.SSEManager.GetUserClientCount(userID)
	clientCountMsg := fmt.Sprintf("User %d has %d connected SSE clients\n", userID, clientCount)
	fmt.Print(clientCountMsg)
	appendToFile(logFile, clientCountMsg)

	// Send to user
	h.SSEManager.SendToUser(userID, event)

	sentMsg := fmt.Sprintf("SSE event sent to user %d: event=%s, id=%s\n", userID, event.Event, event.ID)
	fmt.Print(sentMsg)
	appendToFile(logFile, sentMsg)
}

// appendToFile appends text to a file
func appendToFile(filename, text string) {
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return
	}
	defer file.Close()
	file.WriteString(text)
}

// verifyStripeWebhook verifies the Stripe webhook signature and parses the event
func verifyStripeWebhook(body []byte, sigHeader, webhookSecret string) (stripego.Event, error) {
	var event stripego.Event
	if webhookSecret == "" {
		return event, fmt.Errorf("webhook secret not set")
	}
	event, err := webhook.ConstructEvent(body, sigHeader, webhookSecret)
	return event, err
}
