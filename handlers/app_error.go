package handlers

import (
	"net/http"
	"strconv"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/templates/components/errortmpl"
	"github.com/labstack/echo/v4"
	"maragu.dev/gomponents"
)

type AppErrorHandler struct{}

// HandleAppError handles the /app-error route with error code as query parameter
func (h *AppErrorHandler) HandleAppError(c echo.Context) error {
	// Get error code from query parameter
	codeStr := c.QueryParam("code")
	if codeStr == "" {
		codeStr = "500" // Default to 500 if no code provided
	}

	code, err := strconv.Atoi(codeStr)
	if err != nil {
		code = 500 // Default to 500 if invalid code
	}

	// Get optional error message from query parameter
	message := c.QueryParam("message")
	if message == "" {
		message = http.StatusText(code)
	}

	// Get language from cookies
	lang := cookies.GetLanguageFromCookie(c)

	// Generate appropriate error page
	var html gomponents.Node
	if code == http.StatusUnauthorized {
		// Use branded 401 page
		html = errortmpl.Unauthorized401Page(lang, message)
	} else {
		// Use generic error page for other errors
		html = errortmpl.ErrorPage(lang, code, message)
	}

	// Set the status code and render the page
	writer := c.Response().Writer
	c.Response().Status = code
	html.Render(writer)
	return nil
}

// RegisterRoutes registers the app error routes
func (h *AppErrorHandler) RegisterRoutes(e *echo.Echo) {
	e.GET("/app-error", h.HandleAppError)
}
