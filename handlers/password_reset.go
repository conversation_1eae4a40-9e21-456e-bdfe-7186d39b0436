package handlers

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/app/signin"
	"github.com/j-em/coachpad/templates/components/errortmpl"
	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/passwords/email"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
)

type PasswordResetHandler struct {
	StytchClient *stytchapi.API
	Queries      *db.Queries
}

type PasswordResetInitForm struct {
	Email string `form:"email" validate:"required,email"`
}

type PasswordResetCompleteForm struct {
	Token           string `form:"token" validate:"required"`
	NewPassword     string `form:"new_password" validate:"required,min=8"`
	ConfirmPassword string `form:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// Register the GET /reset-password route for the reset password page
func (h *PasswordResetHandler) RegisterRoutes(e *echo.Echo) {
	e.POST("/auth/password-reset/initiate", h.Initiate)
	e.POST("/auth/password-reset/complete", h.Complete)
	e.GET("/auth/reset-password", h.GetResetPasswordPage)
}

// GetResetPasswordPage handles GET /reset-password and renders the reset password page
func (h *PasswordResetHandler) GetResetPasswordPage(c echo.Context) error {
	token := c.QueryParam("token")
	lang := cookies.GetLanguageFromCookie(c)
	writer := c.Response().Writer

	// Validate that token exists
	if token == "" {
		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, "Password reset token is missing. Please request a new password reset link.")
		return html.Render(writer)
	}

	// Optional: Validate token format (Stytch tokens have a specific format)
	if len(token) < 10 {
		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, "Invalid password reset token. Please request a new password reset link.")
		return html.Render(writer)
	}

	html := signin.ResetPasswordPage(signin.ResetPasswordPageConfig{
		Token: token,
	})
	return html.Render(writer)
}

// POST /auth/password-reset/initiate
func (h *PasswordResetHandler) Initiate(c echo.Context) error {
	var form PasswordResetInitForm
	lang := cookies.GetLanguageFromCookie(c)
	writer := c.Response().Writer

	if err := c.Bind(&form); err != nil {
		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, "Invalid form data")
		return html.Render(writer)
	}
	if err := c.Validate(form); err != nil {
		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, err.Error())
		return html.Render(writer)
	}
	params := &email.ResetStartParams{
		Email:                    form.Email,
		LoginRedirectURL:         "https://your-app-url/signin",         // TODO: set correct URL
		ResetPasswordRedirectURL: "https://your-app-url/reset-password", // TODO: set correct URL
	}
	_, err := h.StytchClient.Passwords.Email.ResetStart(c.Request().Context(), params)
	if err != nil {
		fmt.Println("Stytch reset start error:", err)
		html := errortmpl.ErrorPage(lang, http.StatusInternalServerError, "Failed to send reset email. Please try again.")
		return html.Render(writer)
	}
	// Render a success message (HTMX can swap this in the dialog)
	html := signin.ForgotPasswordSuccess(lang)
	return html.Render(writer)
}

// POST /auth/password-reset/complete
func (h *PasswordResetHandler) Complete(c echo.Context) error {
	var form PasswordResetCompleteForm
	lang := cookies.GetLanguageFromCookie(c)
	writer := c.Response().Writer

	if err := c.Bind(&form); err != nil {
		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, "Invalid form data")
		return html.Render(writer)
	}
	if err := c.Validate(&form); err != nil {
		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, err.Error())
		return html.Render(writer)
	}
	params := email.ResetParams{
		Token:    form.Token,
		Password: form.NewPassword,
	}
	_, err := h.StytchClient.Passwords.Email.Reset(c.Request().Context(), &params)
	if err != nil {
		fmt.Println("Stytch reset error:", err)

		// Provide more specific error messages based on the error type
		errorMessage := "Failed to reset password. "
		if err.Error() != "" {
			// Check for common Stytch error patterns
			errStr := err.Error()
			if containsAny(errStr, []string{"token", "invalid", "expired", "not found"}) {
				errorMessage += "The reset link is invalid or has expired. Please request a new password reset link."
			} else if containsAny(errStr, []string{"password", "weak", "common"}) {
				errorMessage += "The password does not meet security requirements. Please choose a stronger password."
			} else {
				errorMessage += "Please try again or request a new reset link."
			}
		} else {
			errorMessage += "The link may be invalid or expired. Please request a new password reset link."
		}

		html := errortmpl.ErrorPage(lang, http.StatusBadRequest, errorMessage)
		return html.Render(writer)
	}
	return c.String(http.StatusOK, "Password reset successful. You may now sign in.")
}

// containsAny checks if a string contains any of the provided substrings (case-insensitive)
func containsAny(s string, substrings []string) bool {
	s = strings.ToLower(s)
	for _, substring := range substrings {
		if strings.Contains(s, strings.ToLower(substring)) {
			return true
		}
	}
	return false
}
