package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
	"github.com/labstack/echo/v4"
)

type PermissionsHandler struct {
	BaseHandler
	Queries              *db.Queries
	PermissionMiddleware *middleware.PermissionMiddleware
}

// Request/Response types
type GrantPermissionRequest struct {
	Email           string `json:"email" form:"email" validate:"required,email"`
	PermissionLevel string `json:"permission_level" form:"permission_level" validate:"required,oneof=admin manager viewer"`
}

type UpdatePermissionRequest struct {
	PermissionLevel string `json:"permission_level" form:"permission_level" validate:"required,oneof=admin manager viewer"`
}

type PermissionResponse struct {
	UserID          int32  `json:"user_id"`
	UserName        string `json:"user_name"`
	UserEmail       string `json:"user_email"`
	PermissionLevel string `json:"permission_level"`
	GrantedAt       string `json:"granted_at"`
	GrantedBy       string `json:"granted_by"`
}

func NewPermissionsHandler(queries *db.Queries, permissionMiddleware *middleware.PermissionMiddleware) *PermissionsHandler {
	return &PermissionsHandler{
		Queries:              queries,
		PermissionMiddleware: permissionMiddleware,
	}
}

func (h *PermissionsHandler) RegisterRoutes(group *echo.Group) {
	// Owner-only routes for managing permissions
	ownerGroup := group.Group("/seasons/:id/permissions")
	ownerGroup.Use(h.PermissionMiddleware.RequireSeasonPermission(middleware.PermissionOwner))

	ownerGroup.GET("", h.GetSeasonPermissions)
	ownerGroup.POST("", h.GrantPermission)
	ownerGroup.PUT("/:userId", h.UpdatePermission)
	ownerGroup.DELETE("/:userId", h.RevokePermission)
}

// GetSeasonPermissions returns all permissions for a season
func (h *PermissionsHandler) GetSeasonPermissions(c echo.Context) error {
	seasonID := middleware.GetSeasonID(c)

	permissions, err := h.Queries.GetSeasonPermissions(c.Request().Context(), seasonID)
	if err != nil {
		h.LogError(c, "Failed to get season permissions", err, "seasonID", seasonID)
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get permissions")
	}

	// Convert to response format
	var response []PermissionResponse
	for _, perm := range permissions {
		response = append(response, PermissionResponse{
			UserID:          perm.UserID,
			UserName:        perm.UserName,
			UserEmail:       perm.UserEmail,
			PermissionLevel: string(perm.PermissionLevel),
			GrantedAt:       perm.GrantedAt.Time.Format("2006-01-02 15:04:05"),
			GrantedBy:       perm.GrantedByName,
		})
	}

	return c.JSON(http.StatusOK, response)
}

// GrantPermission grants permission to a user for a season
func (h *PermissionsHandler) GrantPermission(c echo.Context) error {
	seasonID := middleware.GetSeasonID(c)
	granterID := middleware.GetUserID(c)

	var req GrantPermissionRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Find user by email
	user, err := h.Queries.GetUserByEmailActive(c.Request().Context(), req.Email)
	if err != nil {
		h.LogWarn(c, "User not found for permission grant", "email", req.Email, "seasonID", seasonID)
		return echo.NewHTTPError(http.StatusNotFound, "User not found")
	}

	// Check if user is trying to grant permission to themselves
	if user.ID == granterID {
		return echo.NewHTTPError(http.StatusBadRequest, "Cannot grant permission to yourself")
	}

	// Grant permission
	permission, err := h.Queries.GrantSeasonPermission(c.Request().Context(), db.GrantSeasonPermissionParams{
		SeasonID:        seasonID,
		UserID:          user.ID,
		PermissionLevel: db.PermissionLevelEnum(req.PermissionLevel),
		GrantedBy:       granterID,
	})

	if err != nil {
		h.LogError(c, "Failed to grant season permission", err,
			"seasonID", seasonID, "userID", user.ID, "permissionLevel", req.PermissionLevel)
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to grant permission")
	}

	// Log the change
	err = h.Queries.LogPermissionChange(c.Request().Context(), db.LogPermissionChangeParams{
		SeasonID:        seasonID,
		UserID:          user.ID,
		PermissionLevel: db.NullPermissionLevelEnum{PermissionLevelEnum: db.PermissionLevelEnum(req.PermissionLevel), Valid: true},
		Action:          db.PermissionActionEnumGranted,
		PerformedBy:     granterID,
	})

	if err != nil {
		h.LogError(c, "Failed to log permission change", err, "seasonID", seasonID, "userID", user.ID)
	}

	h.LogInfo(c, "Permission granted",
		"seasonID", seasonID, "userID", user.ID, "permissionLevel", req.PermissionLevel, "granterID", granterID)

	return c.JSON(http.StatusCreated, PermissionResponse{
		UserID:          user.ID,
		UserName:        user.Name,
		UserEmail:       user.Email,
		PermissionLevel: req.PermissionLevel,
		GrantedAt:       permission.GrantedAt.Time.Format("2006-01-02 15:04:05"),
		GrantedBy:       fmt.Sprintf("User %d", granterID), // You might want to fetch the granter's name
	})
}

// UpdatePermission updates a user's permission level for a season
func (h *PermissionsHandler) UpdatePermission(c echo.Context) error {
	seasonID := middleware.GetSeasonID(c)
	granterID := middleware.GetUserID(c)

	userIDStr := c.Param("userId")
	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid user ID")
	}

	var req UpdatePermissionRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Check if user is trying to update their own permission
	if int32(userID) == granterID {
		return echo.NewHTTPError(http.StatusBadRequest, "Cannot update your own permission")
	}

	// Update permission
	permission, err := h.Queries.UpdateSeasonPermission(c.Request().Context(), db.UpdateSeasonPermissionParams{
		SeasonID:        seasonID,
		UserID:          int32(userID),
		PermissionLevel: db.PermissionLevelEnum(req.PermissionLevel),
		GrantedBy:       granterID,
	})

	if err != nil {
		h.LogError(c, "Failed to update season permission", err,
			"seasonID", seasonID, "userID", userID, "permissionLevel", req.PermissionLevel)
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update permission")
	}

	// Log the change
	err = h.Queries.LogPermissionChange(c.Request().Context(), db.LogPermissionChangeParams{
		SeasonID:        seasonID,
		UserID:          int32(userID),
		PermissionLevel: db.NullPermissionLevelEnum{PermissionLevelEnum: db.PermissionLevelEnum(req.PermissionLevel), Valid: true},
		Action:          db.PermissionActionEnumModified,
		PerformedBy:     granterID,
	})

	if err != nil {
		h.LogError(c, "Failed to log permission change", err, "seasonID", seasonID, "userID", userID)
	}

	h.LogInfo(c, "Permission updated",
		"seasonID", seasonID, "userID", userID, "permissionLevel", req.PermissionLevel, "granterID", granterID)

	return c.JSON(http.StatusOK, PermissionResponse{
		UserID:          int32(userID),
		PermissionLevel: req.PermissionLevel,
		GrantedAt:       permission.GrantedAt.Time.Format("2006-01-02 15:04:05"),
		GrantedBy:       fmt.Sprintf("User %d", granterID),
	})
}

// RevokePermission revokes a user's permission for a season
func (h *PermissionsHandler) RevokePermission(c echo.Context) error {
	seasonID := middleware.GetSeasonID(c)
	revokerID := middleware.GetUserID(c)

	userIDStr := c.Param("userId")
	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid user ID")
	}

	// Check if user is trying to revoke their own permission
	if int32(userID) == revokerID {
		return echo.NewHTTPError(http.StatusBadRequest, "Cannot revoke your own permission")
	}

	// Revoke permission
	err = h.Queries.RevokeSeasonPermission(c.Request().Context(), db.RevokeSeasonPermissionParams{
		SeasonID: seasonID,
		UserID:   int32(userID),
	})

	if err != nil {
		h.LogError(c, "Failed to revoke season permission", err, "seasonID", seasonID, "userID", userID)
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to revoke permission")
	}

	// Log the change
	err = h.Queries.LogPermissionChange(c.Request().Context(), db.LogPermissionChangeParams{
		SeasonID:    seasonID,
		UserID:      int32(userID),
		Action:      db.PermissionActionEnumRevoked,
		PerformedBy: revokerID,
	})

	if err != nil {
		h.LogError(c, "Failed to log permission change", err, "seasonID", seasonID, "userID", userID)
	}

	h.LogInfo(c, "Permission revoked", "seasonID", seasonID, "userID", userID, "revokerID", revokerID)

	return c.NoContent(http.StatusNoContent)
}
