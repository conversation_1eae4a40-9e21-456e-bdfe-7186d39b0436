package handlers

import (
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/notifications"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/sse"
	"github.com/j-em/coachpad/stripe"
	templatesnav "github.com/j-em/coachpad/templates/app/nav"
	templatesappsettings "github.com/j-em/coachpad/templates/app/settings"
	templatesappapiacccess "github.com/j-em/coachpad/templates/app/settings/apiAccess"
	templatesappsubscription "github.com/j-em/coachpad/templates/app/settings/subscription"
	templatesappuser "github.com/j-em/coachpad/templates/app/settings/user"
	templatesappverification "github.com/j-em/coachpad/templates/app/verification"
	"github.com/j-em/coachpad/templates/components/errortmpl"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/datamapper"
	"github.com/j-em/coachpad/utils/hx"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
	stripego "github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/client"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/users"
)

type SettingsHandler struct {
	Queries                  *db.Queries
	StripeClient             *client.API
	StytchClient             *stytchapi.API
	EmailVerificationHandler *EmailVerificationHandler
	SSEManager               *sse.Manager
	NotificationService      *notifications.Service
	LimitsService            *limits.Service
}

// GetUpgradeForm serves the Stripe payment form for upgrading to Pro
func (h *SettingsHandler) GetUpgradeForm(c echo.Context) error {
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}
	tmpCtx := c.Request().Context()
	user, err := h.Queries.GetUserByID(tmpCtx, userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "User not found")
	}
	stripeClient := h.StripeClient
	params := &stripego.SubscriptionParams{
		Customer:        stripego.String(user.StripeID),
		Items:           []*stripego.SubscriptionItemsParams{{Price: stripego.String(stripe.GetProPlanPriceID())}},
		PaymentBehavior: stripego.String("default_incomplete"),
		Expand:          []*string{stripego.String("latest_invoice"), stripego.String("latest_invoice.confirmation_secret")},
	}
	subscription, err := stripeClient.Subscriptions.New(params)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Stripe error: "+err.Error())
	}
	clientSecret := ""
	if subscription.LatestInvoice != nil && subscription.LatestInvoice.ConfirmationSecret != nil {
		clientSecret = subscription.LatestInvoice.ConfirmationSecret.ClientSecret
	}
	publishableKey := stripe.GetPublishableKey()
	form := templatesappsettings.SubscriptionPaymentForm(templatesappsettings.SubscriptionPaymentFormProps{
		ClientSecret:   clientSecret,
		PublishableKey: publishableKey,
	})
	return form.Render(c.Response().Writer)
}

func (h *SettingsHandler) RegisterRoutes(r *echo.Group) {
	// Custom player columns dashboard modal
	r.GET("/settings/custom-player-columns/dashboard", h.GetCustomPlayerColumnsDashboardModal)

	// Player column config dialog
	r.GET("/settings/player-column-config", h.GetPlayerColumnConfigModal)
	r.POST("/settings/player-column-config", h.SavePlayerColumnConfig)

	// Custom match columns dashboard modal
	r.GET("/settings/custom-match-columns/dashboard", h.GetCustomMatchColumnsDashboardModal)

	// API documentation modal
	r.GET("/settings/api-access/documentation", h.GetAPIDocumentationModal)

	r.PATCH("/settings/user", h.PatchUserSettings)
	r.GET("/settings/user", h.GetUserSettings)
	r.GET("/settings/app", h.GetAppSettings)
	r.GET("/settings/subscription", h.GetSubscriptionSettings)
	r.GET("/settings/api-access", h.GetAPIAccessSettings)
	r.GET("/settings/subscription-content", h.GetSubscriptionContent)
	r.PUT("/settings/isSidebarOpen", h.UpdateIsSideBarOpen)
	r.GET("/settings/upgrade", h.GetUpgradeForm)
	r.POST("/settings/downgrade", h.Downgrade)
	r.GET("/settings/subscription-section", h.GetSubscriptionSection)
	r.GET("/settings/sse", h.HandleSSE)
	r.GET("/nav", h.GetNavbar)
	r.GET("/nav/seasons", h.GetNavSeasonsList)
	r.GET("/send-verification-email", h.SendVerificationEmail)
	r.DELETE("/user/delete", h.DeleteUser)

	// Custom player columns routes
	r.GET("/settings/custom-player-columns/new", h.GetNewCustomPlayerColumnForm)
	r.GET("/settings/custom-player-columns/cancel", h.CancelCustomPlayerColumnForm)
	r.POST("/settings/custom-player-columns", h.CreateCustomPlayerColumn)
	r.PATCH("/settings/custom-player-columns/update", h.UpdateCustomPlayerColumn)
	r.DELETE("/settings/custom-player-columns/delete", h.DeleteCustomPlayerColumn)

	// Custom match columns routes
	r.GET("/settings/custom-match-columns/new", h.GetNewMatchCustomColumnForm)
	r.GET("/settings/custom-match-columns/cancel", h.CancelMatchCustomColumnForm)
	r.POST("/settings/custom-match-columns", h.CreateMatchCustomColumn)
	r.PATCH("/settings/custom-match-columns/update", h.UpdateMatchCustomColumn)
	r.DELETE("/settings/custom-match-columns/delete", h.DeleteMatchCustomColumn)
}

// GetCustomPlayerColumnsDashboardModal serves the custom player columns dashboard modal
func (h *SettingsHandler) GetCustomPlayerColumnsDashboardModal(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)
	userID := middleware.GetUserID(c)
	playerCustomColumns, err := h.Queries.GetPlayerCustomColumns(c.Request().Context(), userID)
	if err != nil {
		return c.String(500, "Could not load custom player columns.")
	}
	modal := templatesappsettings.CustomPlayerColumnsModal(playerCustomColumns, lang)
	return modal.Render(c.Response().Writer)
}

// GetPlayerColumnConfigModal serves the player column configuration modal
func (h *SettingsHandler) GetPlayerColumnConfigModal(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)
	userID := middleware.GetUserID(c)

	// Get all available custom columns
	playerCustomColumns, err := h.Queries.GetPlayerCustomColumns(c.Request().Context(), userID)
	if err != nil {
		return c.String(500, "Could not load custom player columns.")
	}

	// Get existing column visibility preferences
	columnVisibilities, err := h.Queries.GetPlayerColumnVisibility(c.Request().Context(), userID)
	if err != nil {
		return c.String(500, "Could not load column visibility preferences.")
	}

	modal := templatesappsettings.PlayerColumnConfigModal(playerCustomColumns, columnVisibilities, lang)
	return modal.Render(c.Response().Writer)
}

// SavePlayerColumnConfig handles saving the player column configuration
func (h *SettingsHandler) SavePlayerColumnConfig(c echo.Context) error {
	userID := middleware.GetUserID(c)
	ctx := c.Request().Context()

	// Parse form data explicitly
	err := c.Request().ParseForm()
	if err != nil {
		return c.String(500, "Could not parse form data.")
	}

	// Get form data
	standardColumns := c.Request().Form["standardColumns"]
	customColumns := c.Request().Form["customColumns"]

	// Define all possible standard columns
	allStandardColumns := []string{
		"Name", "Email", "Preferred Group", "Active", "Email Notifications", "Picture", "Created", "Updated",
	}

	// Convert to visibility map
	visibilityMap := make(map[string]bool)

	// Set all standard columns to false first
	for _, column := range allStandardColumns {
		visibilityMap[column] = false
	}

	// Set checked columns to true
	for _, column := range standardColumns {
		visibilityMap[column] = true
	}

	// Add custom columns to the visibility map
	for _, column := range customColumns {
		visibilityMap[column] = true
	}

	// Save to database
	for columnName, isVisible := range visibilityMap {
		err := h.Queries.UpsertPlayerColumnVisibility(ctx, db.UpsertPlayerColumnVisibilityParams{
			UserID:     userID,
			ColumnName: columnName,
			IsVisible:  isVisible,
		})
		if err != nil {
			return c.String(500, "Could not save column visibility preferences.")
		}
	}

	// Close modal by returning empty content
	return c.String(200, "")
}

// GetCustomMatchColumnsDashboardModal serves the custom match columns dashboard modal
func (h *SettingsHandler) GetCustomMatchColumnsDashboardModal(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)
	userID := middleware.GetUserID(c)
	matchCustomColumns, err := h.Queries.GetMatchCustomColumns(c.Request().Context(), userID)
	if err != nil {
		return c.String(500, "Could not load custom match columns.")
	}
	modal := templatesappsettings.CustomMatchColumnsModal(matchCustomColumns, lang)
	return modal.Render(c.Response().Writer)
}

// GetAPIDocumentationModal serves the API documentation modal
func (h *SettingsHandler) GetAPIDocumentationModal(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)
	modal := templatesappapiacccess.APIDocumentationModal(lang)
	return modal.Render(c.Response().Writer)
}

// Downgrade handles the downgrade from pro to free subscription
func (h *SettingsHandler) Downgrade(c echo.Context) error {
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.String(http.StatusBadRequest, "User not found")
	}

	stripeClient := h.StripeClient

	// Find active subscription for the customer
	subs := stripeClient.Subscriptions.List(&stripego.SubscriptionListParams{
		Customer: stripego.String(user.StripeID),
		Status:   stripego.String("active"),
	})

	for subs.Next() {
		s := subs.Subscription()
		_, err := stripeClient.Subscriptions.Cancel(s.ID, nil)
		if err != nil {
			return c.String(http.StatusInternalServerError, "Failed to cancel subscription: "+err.Error())
		}
	}

	// Update local DB to free tier
	err = h.Queries.UpdateUserSubscriptionTier(c.Request().Context(), db.UpdateUserSubscriptionTierParams{
		ID:               userID,
		SubscriptionTier: "free",
	})

	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to update subscription tier: "+err.Error())
	}

	// Return updated subscription content for htmx to swap
	lang := cookies.GetLanguageFromCookie(c)
	return templatesappsubscription.SubscriptionCardsContent(templatesappsubscription.SubscriptionCardsContentConfig{
		Lang:             lang,
		SubscriptionTier: "free",
	}).Render(c.Response().Writer)
}

func (h *SettingsHandler) GetUserSettings(c echo.Context) error {
	userID := middleware.GetUserID(c)
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		// Inline renderErrorPage logic
		c.Response().WriteHeader(http.StatusInternalServerError)
		return errortmpl.ErrorPage("en", 500, "Could not load user data.").Render(c.Response().Writer)
	}

	// Convert pgtype.Text and pgtype.Date to string for config
	phone := ""
	if user.Phone.Valid {
		phone = user.Phone.String
	}
	birthday := ""
	if user.Birthday.Valid {
		birthday = user.Birthday.Time.Format("2006-01-02")
	}

	lang := cookies.GetLanguageFromCookie(c)

	// Check for verification status from URL parameters
	verificationSent := c.QueryParam("verification_sent") == "true"
	verificationError := c.QueryParam("error") != ""
	emailVerified := c.QueryParam("success") == "email_verified"

	// --- Stripe client secret and publishable key ---
	stripeClientSecret := ""
	stripePublishableKey := ""
	if user.StripeID != "" {
		stripeClient := h.StripeClient
		params := &stripego.SubscriptionParams{
			Customer:        stripego.String(user.StripeID),
			Items:           []*stripego.SubscriptionItemsParams{{Price: stripego.String(stripe.GetProPlanPriceID())}},
			PaymentBehavior: stripego.String("default_incomplete"),
			Expand:          []*string{stripego.String("latest_invoice")},
		}
		subscription, err := stripeClient.Subscriptions.New(params)
		if err == nil && subscription.LatestInvoice != nil && subscription.LatestInvoice.ConfirmationSecret != nil {
			stripeClientSecret = subscription.LatestInvoice.ConfirmationSecret.ClientSecret
		}
		// Get publishable key from environment
		stripePublishableKey = os.Getenv("STRIPE_PUBLISHABLE_KEY")
	}
	// -----------------------------------------------

	pageConfig := templatesappuser.UserSettingsPageConfig{
		Lang:                 lang,
		Name:                 user.Name,
		Email:                user.Email,
		Country:              user.Country,
		Phone:                phone,
		Birthday:             birthday,
		IsSidebarOpen:        user.IsSidebarOpen,
		ActiveLink:           "/app/settings/user",
		SubscriptionTier:     user.SubscriptionTier,
		StripeClientSecret:   stripeClientSecret,
		StripePublishableKey: stripePublishableKey,
	}

	// If htmx boosted request, render only the page content
	if c.Request().Header.Get("HX-Boosted") == "true" {
		content := templatesappuser.UserSettingsPageContent(templatesappuser.UserSettingsPageContentConfig{
			Lang:                 lang,
			Name:                 user.Name,
			Email:                user.Email,
			Country:              user.Country,
			Phone:                phone,
			Birthday:             birthday,
			UserIsVerified:       user.IsVerified || emailVerified,
			VerificationSent:     verificationSent,
			VerificationError:    verificationError,
			SubscriptionTier:     user.SubscriptionTier,
			StripeClientSecret:   stripeClientSecret,
			StripePublishableKey: stripePublishableKey,
		})
		return content.Render(c.Response().Writer)
	}

	page := templatesappuser.UserSettingsPage(pageConfig)
	return page.Render(c.Response().Writer)
}

func (h *SettingsHandler) GetAppSettings(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		c.Response().WriteHeader(http.StatusInternalServerError)
		return errortmpl.ErrorPage(lang, 500, "Could not load user data.").Render(c.Response().Writer)
	}

	// Get all custom player columns
	playerCustomColumns, err := h.Queries.GetPlayerCustomColumns(c.Request().Context(), userID)
	if err != nil {
		c.Response().WriteHeader(http.StatusInternalServerError)
		return errortmpl.ErrorPage(lang, 500, "Could not load custom player columns.").Render(c.Response().Writer)
	}

	// Get all custom match columns for the user
	matchCustomColumns, err := h.Queries.GetMatchCustomColumns(c.Request().Context(), userID)
	if err != nil {
		c.Response().WriteHeader(http.StatusInternalServerError)
		return errortmpl.ErrorPage(lang, 500, "Could not load custom match columns.").Render(c.Response().Writer)
	}

	// Get notification preferences for the user
	notificationPreferences, err := h.NotificationService.GetUserPreferences(c.Request().Context(), userID)
	if err != nil {
		// If no preferences found, use nil (defaults will be used in the template)
		notificationPreferences = nil
	}

	// Get current usage for all resource types
	usageLimits := make(map[limits.ResourceType]*limits.LimitCheckResult)
	resourceTypes := []limits.ResourceType{
		limits.ResourcePlayers,
		limits.ResourceSeasons,
		limits.ResourceMatches,
		limits.ResourceTeams,
	}

	for _, resourceType := range resourceTypes {
		result, err := h.LimitsService.CheckLimit(c.Request().Context(), userID, resourceType)
		if err != nil {
			// Log error but don't fail the page - just skip this resource type
			continue
		}
		usageLimits[resourceType] = result
	}

	pageConfig := templatesappsettings.AppSettingsPageConfig{
		Lang:                    lang,
		IsSidebarOpen:           user.IsSidebarOpen,
		ActiveLink:              "/app/settings/app",
		PlayerCustomColumns:     playerCustomColumns,
		MatchCustomColumns:      matchCustomColumns,
		NotificationPreferences: notificationPreferences,
		UsageLimits:             usageLimits,
	}

	// If htmx boosted request, render only the page content
	if c.Request().Header.Get("HX-Boosted") == "true" {
		content := templatesappsettings.AppSettingsPageContent(templatesappsettings.AppSettingsPageContentConfig{
			Lang:                    lang,
			PlayerCustomColumns:     playerCustomColumns,
			MatchCustomColumns:      matchCustomColumns,
			NotificationPreferences: notificationPreferences,
			UsageLimits:             usageLimits,
		})
		return content.Render(c.Response().Writer)
	}

	// Render the full page
	return templatesappsettings.AppSettingsPage(pageConfig).Render(c.Response().Writer)
}

func (h *SettingsHandler) GetSubscriptionSettings(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		c.Response().WriteHeader(http.StatusInternalServerError)
		return errortmpl.ErrorPage(lang, 500, "Could not load user data.").Render(c.Response().Writer)
	}

	pageConfig := templatesappsubscription.SubscriptionPageConfig{
		Lang:             lang,
		ActiveLink:       "/app/settings/subscription",
		IsSidebarOpen:    user.IsSidebarOpen,
		SubscriptionTier: user.SubscriptionTier,
	}

	// If htmx boosted request, render only the page content
	if c.Request().Header.Get("HX-Boosted") == "true" {
		content := templatesappsubscription.SubscriptionPageContent(templatesappsubscription.SubscriptionPageContentConfig{
			Lang:             lang,
			SubscriptionTier: user.SubscriptionTier,
		})
		return content.Render(c.Response().Writer)
	}

	// Render the full page
	return templatesappsubscription.SubscriptionPage(pageConfig).Render(c.Response().Writer)
}

func (h *SettingsHandler) GetSubscriptionContent(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Could not load user data.")
	}

	return templatesappsubscription.SubscriptionCardsContent(templatesappsubscription.SubscriptionCardsContentConfig{
		Lang:             lang,
		SubscriptionTier: user.SubscriptionTier,
	}).Render(c.Response().Writer)
}

type UpdateIsSideBarOpenForm struct {
	IsSideBarOpen bool `form:"isSideBarOpen"`
}

func (h *SettingsHandler) UpdateIsSideBarOpen(c echo.Context) error {
	// Parse request
	var form UpdateIsSideBarOpenForm
	if err := c.Bind(&form); err != nil {
		fmt.Println("Bind error:", err)
		return c.String(http.StatusBadRequest, "Invalid form data")
	}
	// Validate the form
	if err := c.Validate(form); err != nil {
		fmt.Println("Validation error:", err)
		return c.String(http.StatusBadRequest, "Validation error: "+err.Error())
	}
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		fmt.Println("Invalid user ID in context")
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}
	// Update the user settings in the database
	err := h.Queries.UpdateUserSideBarOpen(c.Request().Context(), db.UpdateUserSideBarOpenParams{
		ID:            userID,
		IsSidebarOpen: form.IsSideBarOpen,
	})
	if err != nil {
		fmt.Println("Database update error:", err)
		return c.String(http.StatusInternalServerError, "Failed to update user settings")
	}

	// Return success response
	return c.String(http.StatusOK, "User settings updated successfully")

}

// GetNavbar returns the navigation markup for HTMX refresh
func (h *SettingsHandler) GetNavbar(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	activeLink := c.Request().Header.Get("HX-Current-URL")
	if activeLink == "" {
		// Fallback to request path if not set (e.g., direct load or non-HTMX)
		activeLink = c.Request().URL.Path
	}

	dataTestID := "sidebar-nav" // Default fallback

	// Get user's seasons
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(c.Request().Context(), userID)
	var seasons []db.Season
	if err != nil {
		// If we can't load seasons, just return an empty slice
		seasons = []db.Season{}
	} else {
		// Use datamapper for clean conversion - no manual loop needed!
		seasons = datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)
	}

	// Check if we're in development environment
	isDevelopment := os.Getenv("APP_ENV") == "development"

	// Get unread notification count
	unreadCount, err := h.NotificationService.GetUnreadCount(c.Request().Context(), userID)
	if err != nil {
		unreadCount = 0 // Fallback to 0 if error
	}

	nav := templatesnav.Nav(templatesnav.NavProps{
		ActiveLink:    activeLink,
		Seasons:       seasons,
		DataTestID:    dataTestID,
		Lang:          lang,
		IsDevelopment: isDevelopment,
		UserID:        userID,
		UnreadCount:   unreadCount,
	})
	return nav.Render(c.Response().Writer)
}

// GetNavSeasonsList returns just the seasons list partial for HTMX refresh
func (h *SettingsHandler) GetNavSeasonsList(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	// Get user's seasons
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(c.Request().Context(), userID)
	var seasons []db.Season
	if err != nil {
		// If we can't load seasons, just return an empty slice
		seasons = []db.Season{}
	} else {
		// Use datamapper for clean conversion - no manual loop needed!
		seasons = datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)
	}

	seasonsList := templatesnav.NavSeasonsList(lang, seasons)
	return seasonsList.Render(c.Response().Writer)
}

type PatchUserSettingsForm struct {
	Name     *string         `form:"name"`
	Email    *string         `form:"email" validate:"email"`
	Phone    *string         `form:"phone"`
	Country  *string         `form:"country"`
	Birthday *utils.DateOnly `form:"birthday"`
	Lang     *string         `form:"lang"`
}

func (h *SettingsHandler) PatchUserSettings(c echo.Context) error {
	var form PatchUserSettingsForm
	// Parse request
	if err := c.Bind(&form); err != nil {
		fmt.Println("Bind error:", err)
		return c.String(http.StatusBadRequest, "Invalid form data")
	}

	// Validate the form
	if err := c.Validate(form); err != nil {
		fmt.Println("Validation error:", err)
		return c.String(http.StatusBadRequest, "Validation error: "+err.Error())
	}

	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		fmt.Println("Invalid user ID in context")
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	lang := cookies.GetLanguageFromCookie(c)

	updateUserParams := db.UpdateUserParams{
		ID: userID,
	}

	if form.Name != nil {
		updateUserParams.Name = pgtype.Text{String: *form.Name, Valid: true}
	}
	if form.Email != nil {
		updateUserParams.Email = pgtype.Text{String: *form.Email, Valid: true}
	}
	if form.Country != nil {
		updateUserParams.Country = pgtype.Text{String: *form.Country, Valid: true}
	}
	if form.Lang != nil {
		updateUserParams.Lang = pgtype.Text{String: *form.Lang, Valid: true}
		// Also update the language cookie to match the new language
		cookies.SetLanguageCookie(c, *form.Lang)
	}
	if form.Phone != nil {
		updateUserParams.Phone = pgtype.Text{String: *form.Phone, Valid: true}
	}
	if form.Birthday != nil {
		if !form.Birthday.Time.IsZero() {
			updateUserParams.Birthday = pgtype.Date{Time: form.Birthday.Time, Valid: true}
		}
	}

	// log updated user params
	fmt.Printf("Updating user %d with params: %+v\n", userID, updateUserParams)

	err := h.Queries.UpdateUser(c.Request().Context(), updateUserParams)
	if err != nil {
		// log error
		fmt.Println("Database update error:", err)
		html := templatesappsettings.UserSettingsPatchError(err.Error(), lang)
		return html.Render(c.Response().Writer)
	}

	// Set HTMX trigger to notify client of settings update
	hx.SetTrigger(c, hx.EventSettingsUpdated, nil)
	// Return success response
	html := templatesappsettings.UserSettingsPatchSuccess(lang)
	return html.Render(c.Response().Writer)
}

// GetNewCustomPlayerColumnForm serves the form for creating a new custom player column
func (h *SettingsHandler) GetNewCustomPlayerColumnForm(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)
	form := templatesappsettings.NewCustomPlayerColumnForm(lang)
	return form.Render(c.Response().Writer)
}

// CancelCustomColumnForm returns an empty div for htmx to replace the form with
func (h *SettingsHandler) CancelCustomPlayerColumnForm(c echo.Context) error {
	return c.HTML(http.StatusOK, `<div id="newCustomColumnFormContainer"></div>`)
}

// GetNewMatchCustomColumnForm serves the form for creating a new custom match column
func (h *SettingsHandler) GetNewMatchCustomColumnForm(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)
	form := templatesappsettings.NewMatchCustomColumnForm(lang)
	return form.Render(c.Response().Writer)
}

// CancelMatchCustomColumnForm returns an empty div for htmx to replace the form with
func (h *SettingsHandler) CancelMatchCustomColumnForm(c echo.Context) error {
	return c.HTML(http.StatusOK, `<div id="newCustomMatchColumnFormContainer"></div>`)
}

// CreateCustomColumnRequest represents the request body for creating a custom column
type CreateCustomColumnRequest struct {
	Name        string       `json:"name" form:"name"`
	FieldType   string       `json:"fieldType" form:"fieldType"`
	Description string       `json:"description" form:"description"`
	IsRequired  CheckboxBool `json:"isRequired" form:"isRequired"`
	IsActive    CheckboxBool `json:"isActive" form:"isActive"`
}

// CreateCustomColumn handles the creation of a new custom player column
func (h *SettingsHandler) CreateCustomPlayerColumn(c echo.Context) error {
	var req CreateCustomColumnRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	// Get user ID from context (set by auth middleware)
	userID := middleware.GetUserID(c)

	// Create the custom player column
	_, err := h.Queries.CreatePlayerCustomColumn(c.Request().Context(), db.CreatePlayerCustomColumnParams{
		UserID:       userID,
		Name:         req.Name,
		FieldType:    req.FieldType,
		Description:  pgtype.Text{String: req.Description, Valid: req.Description != ""},
		IsRequired:   pgtype.Bool{Bool: true, Valid: true},
		DisplayOrder: pgtype.Int4{Int32: 0, Valid: true},
	})

	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to create custom player column"})
	}

	// Return an empty div to replace the form
	return c.HTML(http.StatusOK, `<div id="newCustomColumnFormContainer"></div>`)
}

func (h *SettingsHandler) CreateMatchCustomColumn(c echo.Context) error {
	var req CreateCustomColumnRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Create the custom match column
	_, err := h.Queries.CreateMatchCustomColumn(c.Request().Context(), db.CreateMatchCustomColumnParams{
		UserID:       userID,
		Name:         req.Name,
		FieldType:    req.FieldType,
		Description:  pgtype.Text{String: req.Description, Valid: req.Description != ""},
		IsRequired:   pgtype.Bool{Bool: bool(req.IsRequired), Valid: true},
		IsActive:     pgtype.Bool{Bool: bool(req.IsActive), Valid: true},
		DisplayOrder: 1, // TODO: Calculate proper display order
	})

	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to create custom match column"})
	}

	// Fetch updated list of custom match columns for the user
	updatedColumns, err := h.Queries.GetMatchCustomColumns(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch updated custom match columns"})
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Render the updated table
	updatedTable := templatesappsettings.CustomMatchColumnsTable(updatedColumns, lang)

	return updatedTable.Render(c.Response())
}

// UpdateCustomColumnRequest represents the request body for updating a custom column
type UpdateCustomColumnRequest struct {
	ID          int32         `json:"id"`
	Name        string        `json:"name,omitempty"`
	FieldType   string        `json:"fieldType,omitempty"`
	Description string        `json:"description,omitempty"`
	IsRequired  *bool         `json:"isRequired,omitempty"`
	IsActive    *CheckboxBool `json:"isActive,omitempty"`
}

// UpdateCustomColumn handles updating an existing custom player column
func (h *SettingsHandler) UpdateCustomPlayerColumn(c echo.Context) error {
	var req UpdateCustomColumnRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	// Get user ID from context (set by auth middleware)
	userID := middleware.GetUserID(c)

	// Update the custom player column
	updateParams := db.UpdatePlayerCustomColumnParams{
		ID:     req.ID,
		UserID: userID,
	}
	if req.Name != "" {
		updateParams.Name = req.Name
	}
	if req.FieldType != "" {
		updateParams.FieldType = req.FieldType
	}
	if req.Description != "" {
		updateParams.Description = pgtype.Text{String: req.Description, Valid: true}
	}
	if req.IsRequired != nil {
		updateParams.IsRequired = pgtype.Bool{Bool: *req.IsRequired, Valid: true}
	}
	if req.IsActive != nil {
		updateParams.IsActive = pgtype.Bool{Bool: bool(*req.IsActive), Valid: true}
	}

	updatedPlayer, err := h.Queries.UpdatePlayerCustomColumn(c.Request().Context(), updateParams)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to update custom player column"})
	}

	return c.JSON(http.StatusOK, updatedPlayer)
}

// UpdateMatchCustomColumn handles updating an existing custom match column
func (h *SettingsHandler) UpdateMatchCustomColumn(c echo.Context) error {
	var req UpdateCustomColumnRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	// Update the custom match column
	updateParams := db.UpdateMatchCustomColumnParams{
		ID: req.ID,
	}
	if req.Name != "" {
		updateParams.Name = req.Name
	}
	if req.FieldType != "" {
		updateParams.FieldType = req.FieldType
	}
	if req.Description != "" {
		updateParams.Description = pgtype.Text{String: req.Description, Valid: true}
	}
	if req.IsRequired != nil {
		updateParams.IsRequired = pgtype.Bool{Bool: *req.IsRequired, Valid: true}
	}
	if req.IsActive != nil {
		updateParams.IsActive = pgtype.Bool{Bool: bool(*req.IsActive), Valid: true}
	}

	updatedMatch, err := h.Queries.UpdateMatchCustomColumn(c.Request().Context(), updateParams)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to update custom match column"})
	}

	return c.JSON(http.StatusOK, updatedMatch)
}

// DeleteCustomColumnRequest represents the request body for deleting a custom column
type DeleteCustomColumnRequest struct {
	ID int32 `json:"id"`
}

// DeleteCustomColumn handles deleting a custom player column
func (h *SettingsHandler) DeleteCustomPlayerColumn(c echo.Context) error {
	var req DeleteCustomColumnRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	// Get user ID from context (set by auth middleware)
	userID := middleware.GetUserID(c)

	// Delete the custom player column
	err := h.Queries.DeletePlayerCustomColumn(c.Request().Context(), db.DeletePlayerCustomColumnParams{
		ID:     req.ID,
		UserID: userID,
	})
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to delete custom player column"})
	}

	return c.JSON(http.StatusOK, map[string]string{"success": "Custom player column deleted"})
}

// DeleteMatchCustomColumn handles deleting a custom match column
func (h *SettingsHandler) DeleteMatchCustomColumn(c echo.Context) error {
	var req DeleteCustomColumnRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	// Delete the custom match column
	err := h.Queries.DeleteMatchCustomColumn(c.Request().Context(), req.ID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to delete custom match column"})
	}

	return c.JSON(http.StatusOK, map[string]string{"success": "Custom match column deleted"})
}

// SendVerificationEmail handles sending verification emails from the settings page
func (h *SettingsHandler) SendVerificationEmail(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)
	writer := c.Response().Writer

	// Get user details from database
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		fmt.Printf("Error fetching user: %v\n", err)
		html := templatesappverification.VerificationError(lang, "Error retrieving user data. Please try again.")
		return html.Render(writer)
	}

	if user.IsVerified {
		// User's email is already verified
		html := templatesappverification.AlreadyVerified(lang)
		return html.Render(writer)
	}

	// Delegate to the email verification handler
	err = h.EmailVerificationHandler.sendVerificationEmailInternal(c, user)
	if err != nil {
		fmt.Printf("Error sending verification email: %v\n", err)
		html := templatesappverification.VerificationError(lang, "Failed to send verification email. Please try again.")
		return html.Render(writer)
	}

	// Return success response
	html := templatesappverification.VerificationEmailSent(lang, user.Email)
	return html.Render(writer)
}

// DeleteUser handles the complete deletion of a user account
func (h *SettingsHandler) DeleteUser(c echo.Context) error {
	fmt.Printf("DeleteUser handler called - method: %s, path: %s\n", c.Request().Method, c.Request().URL.Path)

	userID := middleware.GetUserID(c)

	// Get user details for cleanup
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		fmt.Printf("Error fetching user for deletion: %v\n", err)
		return c.String(http.StatusInternalServerError, "Error retrieving user data")
	}

	fmt.Printf("Starting deletion process for user ID: %d, email: %s\n", userID, user.Email)

	// Delete from database (cascade delete all related data)
	err = h.Queries.CascadeDeleteUser(c.Request().Context(), userID)
	if err != nil {
		fmt.Printf("Error deleting user from database: %v\n", err)
		// Continue with other deletions even if DB fails
	} else {
		fmt.Printf("Successfully deleted user from database\n")
	}

	// Delete from Stytch (user always has StytchID)
	_, err = h.StytchClient.Users.Delete(c.Request().Context(), &users.DeleteParams{
		UserID: user.StytchID,
	})
	if err != nil {
		fmt.Printf("Error deleting user from Stytch: %v\n", err)
		// Continue with other deletions even if Stytch fails
	} else {
		fmt.Printf("Successfully deleted user from Stytch\n")
	}

	// Delete from Stripe (user always has StripeID)
	// First, cancel any active subscriptions
	subs := h.StripeClient.Subscriptions.List(&stripego.SubscriptionListParams{
		Customer: stripego.String(user.StripeID),
		Status:   stripego.String("active"),
	})
	for subs.Next() {
		s := subs.Subscription()
		_, err := h.StripeClient.Subscriptions.Cancel(s.ID, nil)
		if err != nil {
			fmt.Printf("Error canceling Stripe subscription %s: %v\n", s.ID, err)
		} else {
			fmt.Printf("Successfully canceled Stripe subscription %s\n", s.ID)
		}
	}

	// Then delete the customer
	_, err = h.StripeClient.Customers.Del(user.StripeID, nil)
	if err != nil {
		fmt.Printf("Error deleting Stripe customer: %v\n", err)
		// Continue even if Stripe fails
	} else {
		fmt.Printf("Successfully deleted Stripe customer\n")
	}

	// Clear session cookie
	cookie := &http.Cookie{
		Name:   "session_token",
		Value:  "",
		Path:   "/",
		MaxAge: -1,
	}
	c.SetCookie(cookie)

	fmt.Printf("Sending HX-Redirect to /?deleted=true\n")
	// Redirect to homepage with success message
	c.Response().Header().Set("HX-Redirect", "/?deleted=true")
	return c.NoContent(200)
}

// HandleSSE handles Server-Sent Events connections for real-time subscription updates
func (h *SettingsHandler) HandleSSE(c echo.Context) error {
	fmt.Printf("Settings HandleSSE called!\n")
	userID := middleware.GetUserID(c)
	clientID := fmt.Sprintf("user_%d_%d", userID, time.Now().UnixNano())

	// Set the stream parameter that the SSE library expects
	streamName := fmt.Sprintf("user_%d", userID)
	req := c.Request()
	q := req.URL.Query()
	q.Set("stream", streamName)
	req.URL.RawQuery = q.Encode()

	return h.SSEManager.HandleConnection(c, clientID, userID)
}

// GetSubscriptionSection returns the latest subscription section content for SSE updates
func (h *SettingsHandler) GetSubscriptionSection(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Could not load user data")
	}

	// Return just the subscription section content (without SSE attributes to avoid recursion)
	subscriptionSection := templatesappsettings.SubscriptionSectionContent(templatesappsettings.SubscriptionSectionProps{
		SubscriptionTier: user.SubscriptionTier,
		Lang:             lang,
	})

	return subscriptionSection.Render(c.Response().Writer)
}

func (h *SettingsHandler) GetAPIAccessSettings(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		c.Response().WriteHeader(http.StatusInternalServerError)
		return errortmpl.ErrorPage(lang, 500, "Could not load user data.").Render(c.Response().Writer)
	}

	// Get API keys for the user
	apiKeys, err := h.Queries.GetAPIKeysByUserID(c.Request().Context(), userID)
	if err != nil {
		// If no API keys found, just use empty slice
		apiKeys = []db.GetAPIKeysByUserIDRow{}
	}

	pageConfig := templatesappapiacccess.APIAccessPageConfig{
		Lang:          lang,
		IsSidebarOpen: user.IsSidebarOpen,
		ActiveLink:    "/app/settings/api-access",
		APIKeys:       apiKeys,
	}

	// If htmx boosted request, render only the page content
	if c.Request().Header.Get("HX-Boosted") == "true" {
		content := templatesappapiacccess.APIAccessPageContent(templatesappapiacccess.APIAccessPageContentConfig{
			Lang:    lang,
			APIKeys: apiKeys,
		})
		return content.Render(c.Response().Writer)
	}

	// Render the full page
	return templatesappapiacccess.APIAccessPage(pageConfig).Render(c.Response().Writer)
}
