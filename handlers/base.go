package handlers

import (
	"github.com/j-em/coachpad/pkg/logger"
	"github.com/labstack/echo/v4"
)

type BaseHandler struct {
	Logger *logger.Logger
}

func (h *BaseHandler) SetLogger(l *logger.Logger) {
	h.Logger = l
}

type HandlerWithLogger interface {
	SetLogger(*logger.Logger)
	RegisterRoutes(*echo.Group)
}

func (h *BaseHandler) LogError(c echo.Context, msg string, err error, attrs ...any) {
	h.Logger.LogError(c.Request().Context(), msg, err, attrs...)
}

func (h *BaseHandler) LogInfo(c echo.Context, msg string, attrs ...any) {
	h.Logger.LogInfo(c.Request().Context(), msg, attrs...)
}

func (h *BaseHandler) LogWarn(c echo.Context, msg string, attrs ...any) {
	h.Logger.LogWarn(c.<PERSON>quest().Context(), msg, attrs...)
}

func (h *BaseHandler) LogDebug(c echo.Context, msg string, attrs ...any) {
	h.Logger.LogDebug(c.Request().Context(), msg, attrs...)
}
