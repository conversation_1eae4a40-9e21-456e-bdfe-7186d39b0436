package handlers

import (
	"fmt"
	"net/http"

	urlutil "github.com/j-em/coachpad/utils/url"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/magiclinks"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/magiclinks/email"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
)

type EmailVerificationHandler struct {
	StytchClient *stytchapi.API
	Queries      *db.Queries
}

type SendVerificationEmailForm struct {
	Email string `form:"email" validate:"required,email"`
}

func (h *EmailVerificationHandler) RegisterRoutes(e *echo.Echo, protectedGroup *echo.Group) {

	// Protected routes (require authentication)
	protectedGroup.POST("/verification/send", h.SendVerificationEmail)

	// Public routes (for handling magic link clicks)
	e.GET("/auth/verify-email", h.VerifyEmailToken)
}

// SendVerificationEmail handles sending verification emails to authenticated users
func (h *EmailVerificationHandler) SendVerificationEmail(c echo.Context) error {
	userID := middleware.GetUserID(c)

	// Get user details from database
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		fmt.Printf("Error fetching user: %v\n", err)
		return c.String(http.StatusInternalServerError, "Error retrieving user data. Please try again.")
	}

	if user.IsVerified {
		return c.String(http.StatusOK, "Your email is already verified.")
	}

	err = h.sendVerificationEmailInternal(c, user)
	if err != nil {
		fmt.Printf("Error sending verification email: %v\n", err)
		return c.String(http.StatusInternalServerError, "Failed to send verification email. Please try again.")
	}

	return c.String(http.StatusOK, fmt.Sprintf("Verification email sent to %s. Please check your inbox.", user.Email))
}

// sendVerificationEmailInternal handles the actual email sending logic
func (h *EmailVerificationHandler) sendVerificationEmailInternal(c echo.Context, user db.User) error {
	// Send magic link using Stytch
	redirectURL := fmt.Sprintf("%s/auth/verify-email", urlutil.GetBaseURL(c))
	params := &email.SendParams{
		Email:              user.Email,
		LoginMagicLinkURL:  redirectURL,
		SignupMagicLinkURL: redirectURL,
	}

	_, err := h.StytchClient.MagicLinks.Email.Send(c.Request().Context(), params)
	return err
}

// VerifyEmailToken handles the magic link verification from email clicks
func (h *EmailVerificationHandler) VerifyEmailToken(c echo.Context) error {
	token := c.QueryParam("token")
	if token == "" {
		// Handle missing token, perhaps redirect to an error page or login
		return c.Redirect(http.StatusFound, "/app/settings?error=invalid_token")
	}

	authParams := &magiclinks.AuthenticateParams{
		Token: token,
	}

	resp, err := h.StytchClient.MagicLinks.Authenticate(c.Request().Context(), authParams)
	if err != nil {
		// Handle Stytch API error
		fmt.Printf("Error authenticating magic link: %v", err)
		// It's good practice to check the type of error and respond accordingly
		// For now, redirecting to settings with a generic error
		return c.Redirect(http.StatusFound, "/app/settings?error=verification_failed")
	}

	// Update user's is_verified status in your database using UpdateUserVerificationStatus
	user, err := h.Queries.GetUserByStytchID(c.Request().Context(), resp.UserID)
	if err != nil {
		fmt.Printf("Error fetching user by Stytch ID: %v", err)
		return c.Redirect(http.StatusFound, "/app/settings?error=user_not_found")
	}

	err = h.Queries.UpdateUserVerificationStatus(c.Request().Context(), db.UpdateUserVerificationStatusParams{
		IsVerified: true,
		ID:         user.ID,
	})
	if err != nil {
		fmt.Printf("Error updating user email verified status: %v", err)
		// Handle database update error
		return c.Redirect(http.StatusFound, "/app/settings?error=db_update_failed")
	}

	// Redirect to a success page or the settings page with a success message
	return c.Redirect(http.StatusFound, "/app/settings?success=email_verified")
}
