package handlers

import (
	"context"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/datamapper"

	templatesappteams "github.com/j-em/coachpad/templates/app/teams"
	"github.com/j-em/coachpad/utils/pagination"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

type TeamsHandler struct {
	Queries          *db.Queries
	LimitsMiddleware *middleware.LimitsMiddleware
	LimitsService    *limits.Service
}

func (h *TeamsHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/teams", h.GetTeams)
	r.GET("/teams-table", h.GetTeamsTable)
	r.GET("/teams/new", h.GetNewTeamForm)

	// Apply limits middleware to team creation
	if h.LimitsMiddleware != nil {
		r.POST("/teams", h.PostNewTeam, h.LimitsMiddleware.CheckResourceLimit(limits.ResourceTeams))
	} else {
		r.POST("/teams", h.PostNewTeam)
	}

	r.GET("/teams/select-modal", h.GetTeamSelectModal)
	r.GET("/teams/export/csv", h.ExportTeamsCSV) // CSV export endpoint
	r.GET("/teams/:id/edit", h.GetEditTeamForm)
	r.PUT("/teams/:id", h.PutEditTeam)
	r.PUT("/teams/:id/inline", h.UpdateTeamInline)
	r.DELETE("/teams/:id", h.DeleteTeam)
}

func (h *TeamsHandler) GetTeams(c echo.Context) error {
	// Get user ID from context (set by StytchJWT middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse query parameters for pagination, sorting, and filtering
	params := parseTeamsTableParams(c)
	isPrint := c.QueryParam("print") == "true"

	// Query teams for this user
	allTeams, err := h.Queries.GetTeams(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch teams")
	}

	// Apply filtering, sorting, and pagination
	filteredTeams := filterTeams(allTeams, params.Search)
	sortedTeams := sortTeams(filteredTeams, params.Sort, params.Direction)
	totalItems := len(sortedTeams)
	isSearchActive := params.Search != ""

	// Get the language from the cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Check if this is a print request
	if isPrint {
		// For print, use all filtered/sorted teams (no pagination)
		return templatesappteams.PrintTeams(templatesappteams.PrintTeamsProps{
			Teams: sortedTeams,
			Lang:  lang,
		}).Render(c.Response().Writer)
	}

	// For non-print requests, apply pagination
	totalPages := (totalItems + params.ItemsPerPage - 1) / params.ItemsPerPage
	if totalItems == 0 {
		totalPages = 1
	}
	paginatedTeams := paginateTeams(sortedTeams, params.Page, params.ItemsPerPage)

	// Teams are already db.Team type
	dbTeams := paginatedTeams
	dbAllTeams := allTeams

	// Check if this is a boosted request
	if c.Request().Header.Get("HX-Boosted") == "true" {
		// Return just the page content for boost navigation
		content := templatesappteams.TeamsDashboardPageContent(dbTeams, dbAllTeams, lang, &params, totalItems, totalPages, isSearchActive)
		return content.Render(c.Response().Writer)
	}

	// Get the user's active seasons
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch seasons")
	}

	// Use datamapper for clean conversion - no manual loop needed!
	seasons := datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)

	user, err := h.Queries.GetUserByID(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch user")
	}

	node := templatesappteams.TeamsDashboardPage(templatesappteams.TeamsDashboardPageProps{
		IsSidebarOpen:  user.IsSidebarOpen,
		ActiveLink:     "/app/teams",
		Teams:          dbTeams,
		AllTeams:       dbAllTeams,
		Lang:           lang,
		Seasons:        seasons,
		TotalItems:     totalItems,
		TotalPages:     totalPages,
		Params:         params,
		IsSearchActive: isSearchActive,
	})
	return node.Render(c.Response().Writer)
}

func (h *TeamsHandler) GetTeamsTable(c echo.Context) error {
	// Get user ID from context (set by StytchJWT middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse query parameters for pagination, sorting, and filtering
	params := parseTeamsTableParams(c)

	// Query teams for this user
	allTeams, err := h.Queries.GetTeams(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch teams")
	}

	// Apply filtering, sorting, and pagination
	filteredTeams := filterTeams(allTeams, params.Search)
	sortedTeams := sortTeams(filteredTeams, params.Sort, params.Direction)
	totalItems := len(sortedTeams)
	isSearchActive := params.Search != ""

	// Get the language from the cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Apply pagination
	totalPages := (totalItems + params.ItemsPerPage - 1) / params.ItemsPerPage
	if totalItems == 0 {
		totalPages = 1
	}
	paginatedTeams := paginateTeams(sortedTeams, params.Page, params.ItemsPerPage)

	// Render table content only
	tableProps := templatesappteams.TeamsTableProps{
		Teams:          paginatedTeams,
		AllTeams:       allTeams,
		Lang:           lang,
		Params:         params,
		TotalItems:     totalItems,
		TotalPages:     totalPages,
		IsSearchActive: isSearchActive,
	}

	content := templatesappteams.TeamsTableContent(tableProps)
	return content.Render(c.Response().Writer)
}

func (h *TeamsHandler) GetNewTeamForm(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	// Get current teams usage
	var teamsUsage *limits.LimitCheckResult
	if h.LimitsService != nil {
		usage, err := h.LimitsService.CheckLimit(c.Request().Context(), userID, limits.ResourceTeams)
		if err != nil {
			// Log error but continue - just don't show usage indicator
			teamsUsage = nil
		} else {
			teamsUsage = usage
		}
	}

	node := templatesappteams.TeamsNewForm(templatesappteams.TeamsNewFormConfig{
		Lang:       lang,
		TeamsUsage: teamsUsage,
	})
	return node.Render(c.Response().Writer)
}

type PostNewTeamForm struct {
	Name        string `form:"name" validate:"required"`
	Description string `form:"description"`
}

func (h *TeamsHandler) PostNewTeam(c echo.Context) error {
	// Create a new team
	var form PostNewTeamForm
	if err := c.Bind(&form); err != nil {
		fmt.Println(err)
		return c.String(http.StatusBadRequest, "Invalid form data")
	}

	// Validate the form
	if err := c.Validate(form); err != nil {
		fmt.Println(err)
		return c.String(http.StatusBadRequest, "Validation error: "+err.Error())
	}

	// Create the team in the database
	_, err := h.Queries.CreateTeam(context.Background(), db.CreateTeamParams{
		Name:        form.Name,
		Description: pgtype.Text{String: form.Description, Valid: form.Description != ""},
		UserID:      c.Get(middleware.UserIDKey).(int32),
	})
	if err != nil {
		fmt.Println(err)
		return c.String(http.StatusInternalServerError, "Failed to create team")
	}

	// Set HX-Trigger header to notify client that teams were updated and close modals
	utils.SetMultipleTypedTriggers(c, []utils.TypedEventTrigger{
		{EventName: utils.HXEventTeamsUpdated, Data: utils.TeamsUpdatedEvent{}},
		{EventName: utils.HXEventCloseAllModals, Data: utils.CloseAllModalsEvent{}},
	})

	// Return success response (empty for now, but could render a success message)
	return c.NoContent(http.StatusOK)
}

func (h *TeamsHandler) GetEditTeamForm(c echo.Context) error {
	return c.String(http.StatusOK, "Edit team form not implemented yet")
}

func (h *TeamsHandler) PutEditTeam(c echo.Context) error {
	return c.String(http.StatusOK, "Put edit team not implemented yet")
}

// GetTeamSelectModal returns a modal with team selection radio buttons
func (h *TeamsHandler) GetTeamSelectModal(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Query teams for this user
	teams, err := h.Queries.GetTeams(context.Background(), userID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to fetch teams")
	}

	lang := cookies.GetLanguageFromCookie(c)
	node := templatesappteams.TeamSelectModal(teams, lang)
	return node.Render(c.Response().Writer)
}

func (h *TeamsHandler) UpdateTeamInline(c echo.Context) error {
	// Get user ID from context (set by StytchJWT middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse team ID from URL
	var teamID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &teamID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid team ID")
	}

	// Get the current team first to preserve existing values
	currentTeam, err := h.Queries.GetTeam(c.Request().Context(), db.GetTeamParams{
		ID:     teamID,
		UserID: userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to get current team")
	}

	// Parse form data - only update fields that are provided
	name := c.FormValue("name")
	description := c.FormValue("description")
	isActiveStr := c.FormValue("isActive")

	// Use current values as defaults, update only if new values are provided
	if name == "" {
		name = currentTeam.Name
	}
	if description == "" {
		description = currentTeam.Description.String
	}

	isActive := currentTeam.IsActive
	if isActiveStr != "" {
		isActive = isActiveStr == "true" || isActiveStr == "on"
	}

	// Update the team
	_, err = h.Queries.UpdateTeam(c.Request().Context(), db.UpdateTeamParams{
		Name:        name,
		Description: pgtype.Text{String: description, Valid: description != ""},
		PictureUrl:  pgtype.Text{String: "", Valid: false}, // Empty picture URL for now
		IsActive:    isActive,
		ID:          teamID,
		UserID:      userID,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to update team")
	}

	// For HTMX requests, return empty response with OK status (no event trigger for inline updates)
	return c.NoContent(http.StatusOK)
}

func (h *TeamsHandler) DeleteTeam(c echo.Context) error {
	// Get user ID from context (set by StytchJWT middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Parse team ID from URL
	var teamID int32
	if _, err := fmt.Sscanf(c.Param("id"), "%d", &teamID); err != nil {
		return c.String(http.StatusBadRequest, "Invalid team ID")
	}

	// Verify the team belongs to the user before deleting
	team, err := h.Queries.GetTeam(c.Request().Context(), db.GetTeamParams{
		ID:     teamID,
		UserID: userID,
	})
	if err != nil {
		return c.String(http.StatusNotFound, "Team not found")
	}

	// Delete the team
	err = h.Queries.DeleteTeam(c.Request().Context(), team.ID)
	if err != nil {
		return c.String(http.StatusInternalServerError, "Failed to delete team")
	}

	// For HTMX requests, return empty response to remove the table row
	return c.NoContent(http.StatusOK)
}

// parseTeamsTableParams parses URL query parameters for table functionality
func parseTeamsTableParams(c echo.Context) templatesappteams.TeamsTableParams {
	return templatesappteams.TeamsTableParams{
		SortablePaginationParams: pagination.ParseSortablePagination(c, "name"),
	}
}

// filterTeams filters teams based on search term
func filterTeams(teams []db.Team, search string) []db.Team {
	if search == "" {
		return teams
	}

	searchLower := strings.ToLower(search)
	var filtered []db.Team

	for _, team := range teams {
		if strings.Contains(strings.ToLower(team.Name), searchLower) ||
			strings.Contains(strings.ToLower(team.Description.String), searchLower) {
			filtered = append(filtered, team)
		}
	}

	return filtered
}

// sortTeams sorts teams based on field and direction
func sortTeams(teams []db.Team, sortField, direction string) []db.Team {
	if len(teams) == 0 {
		return teams
	}

	sorted := make([]db.Team, len(teams))
	copy(sorted, teams)

	sort.Slice(sorted, func(i, j int) bool {
		var aVal, bVal string
		switch sortField {
		case "name":
			aVal, bVal = sorted[i].Name, sorted[j].Name
		case "description":
			aVal, bVal = sorted[i].Description.String, sorted[j].Description.String
		case "isActive":
			aVal, bVal = strconv.FormatBool(sorted[i].IsActive), strconv.FormatBool(sorted[j].IsActive)
		default:
			aVal, bVal = sorted[i].Name, sorted[j].Name
		}

		if direction == "desc" {
			return strings.Compare(aVal, bVal) > 0
		}
		return strings.Compare(aVal, bVal) < 0
	})

	return sorted
}

// paginateTeams applies pagination to teams slice
func paginateTeams(teams []db.Team, page, itemsPerPage int) []db.Team {
	if len(teams) == 0 {
		return teams
	}

	startIdx := (page - 1) * itemsPerPage
	endIdx := startIdx + itemsPerPage

	if startIdx >= len(teams) {
		return []db.Team{}
	}

	if endIdx > len(teams) {
		endIdx = len(teams)
	}

	return teams[startIdx:endIdx]
}

// ExportTeamsCSV exports all teams for the current user as CSV
func (h *TeamsHandler) ExportTeamsCSV(c echo.Context) error {
	userID := c.Get(middleware.UserIDKey).(int32)

	// Get all teams for this user
	teams, err := h.Queries.GetTeams(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to fetch teams"})
	}

	// Set up CSV response
	config := utils.NewCSVExportConfig("teams.csv")
	writer := utils.SetupCSVResponse(c, config)
	defer writer.Flush()

	// Write CSV header
	header := []string{
		"ID",
		"Name",
		"Description",
		"Active",
		"Created At",
		"Updated At",
	}

	if err := writer.Write(header); err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to write CSV header"})
	}

	// Write team data
	for _, team := range teams {
		record := []string{
			fmt.Sprintf("%d", team.ID),
			team.Name,
			utils.FormatCSVText(team.Description),
			utils.FormatCSVBool(team.IsActive),
			utils.FormatCSVTimestamp(team.CreatedAt),
			utils.FormatCSVTimestamp(team.UpdatedAt),
		}

		if err := writer.Write(record); err != nil {
			return c.JSON(500, map[string]string{"error": "Failed to write CSV data"})
		}
	}

	return nil
}
