package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
	templatesappsettings "github.com/j-em/coachpad/templates/app/settings"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/apikey"
)

type APIKeyHandler struct {
	Queries *db.Queries
}

// CreateAPIKeyRequest represents the request to create a new API key
type CreateAPIKeyRequest struct {
	Name      string `form:"name" validate:"required,min=1,max=255"`
	ExpiresAt string `form:"expires_at"` // Optional, ISO date format
}

// UpdateAPIKeyRequest represents the request to update an API key
type UpdateAPIKeyRequest struct {
	Name string `form:"name" validate:"required,min=1,max=255"`
}

// GetAPIKeysTable returns the API keys table for HTMX requests
func (h *APIKeyHandler) GetAPIKeysTable(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	apiKeys, err := h.Queries.GetAPIKeysByUserID(c.Request().Context(), userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve API keys")
	}

	table := templatesappsettings.APIKeysTable(templatesappsettings.APIKeysSectionConfig{
		Lang:    "en", // You can get this from cookies if needed
		APIKeys: apiKeys,
	})

	return table.Render(c.Response().Writer)
}

// GetNewAPIKeyForm returns the form for creating a new API key
func (h *APIKeyHandler) GetNewAPIKeyForm(c echo.Context) error {
	form := templatesappsettings.NewAPIKeyForm(templatesappsettings.APIKeyFormConfig{
		Lang: "en",
	})
	return form.Render(c.Response().Writer)
}

// GetEditAPIKeyForm returns the form for editing an API key
func (h *APIKeyHandler) GetEditAPIKeyForm(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	keyIDStr := c.Param("id")
	keyID, err := strconv.Atoi(keyIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid API key ID")
	}

	// Get the API key to populate the form
	apiKey, err := h.Queries.GetAPIKeyByID(c.Request().Context(), db.GetAPIKeyByIDParams{
		ID:     int32(keyID),
		UserID: userID,
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "API key not found")
	}

	form := templatesappsettings.EditAPIKeyForm(templatesappsettings.APIKeyFormConfig{
		Lang:  "en",
		KeyID: apiKey.ID,
		Name:  apiKey.Name,
	})
	return form.Render(c.Response().Writer)
}

// CancelAPIKeyForm clears the form container
func (h *APIKeyHandler) CancelAPIKeyForm(c echo.Context) error {
	return c.HTML(http.StatusOK, "")
}

// CreateAPIKey creates a new API key for the authenticated user
func (h *APIKeyHandler) CreateAPIKey(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	// Parse and validate request
	var req CreateAPIKeyRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request data")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Generate new API key
	apiKey, err := apikey.GenerateAPIKey()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate API key")
	}

	// Hash the API key for storage
	hashedKey, err := middleware.HashAPIKey(apiKey)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to hash API key")
	}

	// Extract prefix for efficient lookups
	prefix := apikey.ExtractPrefix(apiKey)

	// Parse expiration date if provided
	var expiresAt pgtype.Timestamp
	if req.ExpiresAt != "" {
		parsedTime, err := time.Parse("2006-01-02", req.ExpiresAt)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid expiration date format. Use YYYY-MM-DD")
		}
		expiresAt = pgtype.Timestamp{Time: parsedTime, Valid: true}
	}

	// Save to database
	_, err = h.Queries.CreateAPIKey(c.Request().Context(), db.CreateAPIKeyParams{
		UserID:    userID,
		KeyHash:   hashedKey,
		Name:      req.Name,
		Prefix:    prefix,
		ExpiresAt: expiresAt,
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create API key")
	}

	// Return the success template with the full API key
	success := templatesappsettings.APIKeyCreatedSuccess(templatesappsettings.APIKeyFormConfig{
		Lang:   "en",
		APIKey: apiKey,
	})

	// Also refresh the table
	utils.SetTypedTrigger(c, utils.HXEventRefreshApiKeysTable, utils.RefreshApiKeysTableEvent{})

	return success.Render(c.Response().Writer)
}

// GetAPIKeys returns all API keys for the authenticated user
func (h *APIKeyHandler) GetAPIKeys(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	apiKeys, err := h.Queries.GetAPIKeysByUserID(c.Request().Context(), userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve API keys")
	}

	// Transform for response (never include the hash or full key)
	response := make([]map[string]interface{}, len(apiKeys))
	for i, key := range apiKeys {
		response[i] = map[string]interface{}{
			"id":           key.ID,
			"name":         key.Name,
			"prefix":       key.Prefix + "...",
			"last_used_at": getTimestampValue(key.LastUsedAt),
			"expires_at":   getTimestampValue(key.ExpiresAt),
			"created_at":   key.CreatedAt.Time,
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data": response,
	})
}

// UpdateAPIKey updates an API key's name
func (h *APIKeyHandler) UpdateAPIKey(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	// Get API key ID from URL
	keyIDStr := c.Param("id")
	keyID, err := strconv.Atoi(keyIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid API key ID")
	}

	// Parse request
	var req UpdateAPIKeyRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request data")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Validation failed: "+err.Error())
	}

	// Verify ownership and update
	err = h.Queries.UpdateAPIKeyName(c.Request().Context(), db.UpdateAPIKeyNameParams{
		ID:     int32(keyID),
		UserID: userID,
		Name:   req.Name,
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "API key not found or access denied")
	}

	// Clear the form and refresh the table
	utils.SetTypedTrigger(c, utils.HXEventRefreshApiKeysTable, utils.RefreshApiKeysTableEvent{})
	return c.HTML(http.StatusOK, "")
}

// DeleteAPIKey deletes an API key
func (h *APIKeyHandler) DeleteAPIKey(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	// Get API key ID from URL
	keyIDStr := c.Param("id")
	keyID, err := strconv.Atoi(keyIDStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid API key ID")
	}

	// Verify ownership and delete (soft delete by setting is_active = false)
	err = h.Queries.DeleteAPIKey(c.Request().Context(), db.DeleteAPIKeyParams{
		ID:     int32(keyID),
		UserID: userID,
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "API key not found or access denied")
	}

	// Return the updated table
	return h.GetAPIKeysTable(c)
}

// RegisterRoutes registers all API key management routes
func (h *APIKeyHandler) RegisterRoutes(r *echo.Group) {
	// API key management routes (web UI)
	r.GET("/settings/api-keys", h.GetAPIKeysTable)
	r.GET("/settings/api-keys/new", h.GetNewAPIKeyForm)
	r.GET("/settings/api-keys/:id/edit", h.GetEditAPIKeyForm)
	r.GET("/settings/api-keys/cancel", h.CancelAPIKeyForm)
	r.POST("/settings/api-keys", h.CreateAPIKey)
	r.PUT("/settings/api-keys/:id", h.UpdateAPIKey)
	r.DELETE("/settings/api-keys/:id", h.DeleteAPIKey)
}

// Helper function to safely extract timestamp values
func getTimestampValue(ts pgtype.Timestamp) interface{} {
	if ts.Valid {
		return ts.Time
	}
	return nil
}
