package handlers

import (
	"context"
	"fmt"
	"log"
	"time"

	stripego "github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/client"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/mailgun"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/templates/app/signin"
	"github.com/j-em/coachpad/templates/app/signup"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/passwords"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/stytchapi"
	"github.com/stytchauth/stytch-go/v16/stytch/consumer/users"
)

// Split AuthForm into two separate structs: SignupForm and SigninForm
type SignupForm struct {
	Name            string `form:"name" validate:"required"`
	Email           string `form:"email" validate:"required,email"`
	Phone           string `form:"phone"`
	Country         string `form:"country" validate:"required"`
	Birthday        string `form:"birthday"`
	Lang            string `form:"lang" validate:"required"`
	Password        string `form:"password" validate:"required,min=8"`
	ConfirmPassword string `form:"confirm-password" validate:"required,eqfield=Password"`
}

type SigninForm struct {
	Email    string `form:"email" validate:"required,email"`
	Password string `form:"password" validate:"required"`
}

type AuthHandler struct {
	StytchClient  *stytchapi.API
	Queries       *db.Queries
	MailgunClient *mailgun.MailgunClient
	StripeClient  *client.API
}

func (h *AuthHandler) RegisterRoutes(e *echo.Echo) {
	e.POST("/signin", h.PostSignin)
	e.GET("/signin", h.GetSignin)
	e.GET("/signup", h.GetSignup)
	e.POST("/signup", h.PostSignup)
	e.POST("/check-email", h.CheckEmailAvailability)
}

func (h *AuthHandler) PostSignin(c echo.Context) error {
	var form SigninForm
	writer := c.Response().Writer
	lang := cookies.GetLanguageFromCookie(c)

	if err := c.Bind(&form); err != nil {
		fmt.Println("Error binding form values:", err)
		html := signin.PostSignInError("Invalid form data", lang)
		if c.Request().Header.Get("HX-Request") == "true" {
			c.Response().Header().Set("Content-Type", "text/html")
			return html.Render(writer)
		}
		return html.Render(writer)
	}
	if err := c.Validate(form); err != nil {
		fmt.Println("Validation error:", err)
		html := signin.PostSignInError(err.Error(), lang)
		if c.Request().Header.Get("HX-Request") == "true" {
			c.Response().Header().Set("Content-Type", "text/html")
			return html.Render(writer)
		}
		return html.Render(writer)
	}
	authParams := &passwords.AuthenticateParams{
		Email:                  form.Email,
		Password:               form.Password,
		SessionDurationMinutes: 527040,
	}
	res, err := h.StytchClient.Passwords.Authenticate(c.Request().Context(), authParams)
	if err != nil {
		fmt.Println("Stytch authentication error:", err)
		html := signin.PostSignInError("Invalid credentials. Please try again.", lang)
		if c.Request().Header.Get("HX-Request") == "true" {
			c.Response().Header().Set("Content-Type", "text/html")
			return html.Render(writer)
		}
		return html.Render(writer)
	}
	userDb, err := h.Queries.GetUserByEmail(c.Request().Context(), form.Email)
	if err != nil {
		fmt.Println("Database lookup error:", err)
		html := signin.PostSignInError("Error retrieving user data. Please try again.", lang)
		if c.Request().Header.Get("HX-Request") == "true" {
			c.Response().Header().Set("Content-Type", "text/html")
			return html.Render(writer)
		}
		return html.Render(writer)
	}
	updateParams := &users.UpdateParams{
		UserID: res.User.UserID,
		TrustedMetadata: map[string]any{
			"userId": userDb.ID,
		},
	}
	_, err = h.StytchClient.Users.Update(c.Request().Context(), updateParams)
	if err != nil {
		fmt.Println("Error updating user metadata:", err)
		html := signin.PostSignInError("Error setting up your session. Please try again.", lang)
		if c.Request().Header.Get("HX-Request") == "true" {
			c.Response().Header().Set("Content-Type", "text/html")
			return html.Render(writer)
		}
		return html.Render(writer)
	}
	cookie := cookies.CreateSessionCookie(res.SessionJWT)
	c.SetCookie(cookie)

	// Use HX-Redirect for HTMX to perform a full page redirect on success
	c.Response().Header().Set("HX-Redirect", "/app/home")
	return c.NoContent(200)
}

func (h *AuthHandler) GetSignin(c echo.Context) error {
	writer := c.Response().Writer
	lang := cookies.GetLanguageFromCookie(c)

	// Check if user is already authenticated
	userID, err := middleware.GetOptionalUserID(c, h.StytchClient)
	if err != nil {
		log.Printf("Error checking authentication on signin page: %v", err)
		userID = 0 // Reset to 0 on error
	}

	var user *db.User
	if userID != 0 {
		userData, err := h.Queries.GetUserByID(context.Background(), userID)
		if err != nil {
			log.Printf("Error fetching user details: %v", err)
		} else {
			user = &userData
		}
	}

	html := signin.SignIn(lang, user)
	html.Render(writer)
	return nil
}

func (h *AuthHandler) GetSignup(c echo.Context) error {
	writer := c.Response().Writer
	lang := cookies.GetLanguageFromCookie(c)

	// Check if user is already authenticated
	userID, err := middleware.GetOptionalUserID(c, h.StytchClient)
	if err != nil {
		log.Printf("Error checking authentication on signup page: %v", err)
		userID = 0 // Reset to 0 on error
	}

	var user *db.User
	if userID != 0 {
		userData, err := h.Queries.GetUserByID(context.Background(), userID)
		if err != nil {
			log.Printf("Error fetching user details: %v", err)
		} else {
			user = &userData
		}
	}

	html := signup.SignUp(lang, user)
	html.Render(writer)
	return nil
}

func (h *AuthHandler) PostSignup(c echo.Context) error {
	var form SignupForm
	writer := c.Response().Writer
	lang := cookies.GetLanguageFromCookie(c)

	if err := c.Bind(&form); err != nil {
		html := signup.SignupError(lang, "Invalid form data")
		return html.Render(writer)
	}

	if err := c.Validate(form); err != nil {
		html := signup.SignupError(lang, err.Error())
		return html.Render(writer)
	}
	createParams := &passwords.CreateParams{
		Email:    form.Email,
		Password: form.Password,
	}
	res, err := h.StytchClient.Passwords.Create(c.Request().Context(), createParams)
	if err != nil {
		log.Printf("Error while creating account: %v", err)
		html := signup.SignupError(lang, "Error while creating account. Please try again.")
		return html.Render(writer)
	}
	var phoneParam pgtype.Text
	if form.Phone != "" {
		phoneParam.String = form.Phone
		phoneParam.Valid = true
	}
	// Create Stripe customer
	stripeClient := h.StripeClient
	stripeCustomerParams := &stripego.CustomerParams{
		Email: stripego.String(form.Email),
		Name:  stripego.String(form.Name),
	}
	stripeCustomer, err := stripeClient.Customers.New(stripeCustomerParams)
	if err != nil {
		log.Printf("Error creating Stripe customer: %v", err)
		html := signup.SignupError(lang, "Error creating Stripe customer. Please try again.")
		return html.Render(writer)
	}
	stripeID := stripeCustomer.ID
	var birthdayParam pgtype.Date
	if form.Birthday != "" {
		parsedDate, err := time.Parse("2006-01-02", form.Birthday)
		if err == nil {
			birthdayParam.Time = parsedDate
			birthdayParam.Valid = true
		} else {
			log.Printf("Error parsing birthday: %v", err)
		}
	}
	userDb, err := h.Queries.CreateUser(c.Request().Context(), db.CreateUserParams{
		StytchID:   res.UserID,
		StripeID:   stripeID,
		Name:       form.Name,
		Email:      form.Email,
		Phone:      phoneParam,
		Country:    form.Country,
		Birthday:   birthdayParam,
		Lang:       form.Lang,
		IsVerified: false,
	})
	if err != nil {
		log.Printf("Database user creation error: %v", err)
		html := signup.SignupError(lang, "An error occurred saving your information. Please try again.")
		return html.Render(writer)
	}
	updateParams := &users.UpdateParams{
		UserID: res.UserID,
		TrustedMetadata: map[string]any{
			"userId": userDb.ID,
		},
	}
	_, err = h.StytchClient.Users.Update(c.Request().Context(), updateParams)
	if err != nil {
		log.Printf("Error updating user metadata: %v", err)
		html := signup.SignupError(lang, "An error occurred saving your information. Please try again.")
		return html.Render(writer)
	}
	// Send confirmation email if enabled
	if h.MailgunClient.IsEmailConfirmationEnabled() {
		emailParams := mailgun.EmailParams{
			Recipient: form.Email,
			Subject:   "Confirm Your Account",
			Variables: map[string]string{
				"name": form.Name,
			},
		}
		err := h.MailgunClient.SendEmail(c.Request().Context(), emailParams)
		if err != nil {
			log.Printf("Failed to send confirmation email to %s: %v", form.Email, err)
		} else {
			log.Printf("Confirmation email sent to %s", form.Email)
		}
	}
	cookie := cookies.CreateSessionCookie(res.SessionJWT)
	c.SetCookie(cookie)
	html := signup.SignupSuccess(lang)
	return html.Render(writer)
}

func (h *AuthHandler) CheckEmailAvailability(c echo.Context) error {
	writer := c.Response().Writer
	lang := cookies.GetLanguageFromCookie(c)
	email := c.FormValue("email")

	if email == "" {
		html := signup.EmailValidationRequired(lang)
		return html.Render(writer)
	}

	// First validate email format
	if err := c.Validate(struct {
		Email string `validate:"email"`
	}{Email: email}); err != nil {
		html := signup.EmailValidationInvalid(lang)
		return html.Render(writer)
	}

	// Check if email already exists in database
	_, err := h.Queries.GetUserByEmail(c.Request().Context(), email)
	if err != nil {
		// Email not found, so it's available
		html := signup.EmailValidationAvailable(lang)
		return html.Render(writer)
	}

	// Email found, so it's not available
	html := signup.EmailValidationTaken(lang)
	return html.Render(writer)
}
