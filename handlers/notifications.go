package handlers

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/notifications"
	templatesappnotifications "github.com/j-em/coachpad/templates/app/notifications"
	"github.com/j-em/coachpad/templates/components/notificationbell"
	"github.com/j-em/coachpad/templates/ui/toast"
	"github.com/labstack/echo/v4"
)

type NotificationsHandler struct {
	Queries             *db.Queries
	NotificationService *notifications.Service
}

type NotificationPreferencesForm struct {
	InAppEnabled    bool `form:"in_app_enabled"`
	MatchUpdates    bool `form:"match_updates"`
	ScheduleChanges bool `form:"schedule_changes"`
	Results         bool `form:"results"`
	Announcements   bool `form:"announcements"`
}

// NewNotificationsHandler creates a new notifications handler
func NewNotificationsHandler(queries *db.Queries, notificationService *notifications.Service) *NotificationsHandler {
	return &NotificationsHandler{
		Queries:             queries,
		NotificationService: notificationService,
	}
}

// RegisterRoutes registers notification routes
func (h *NotificationsHandler) RegisterRoutes(g *echo.Group) {
	g.GET("/notifications", h.NotificationsList)
	g.GET("/notifications/bell-html", h.GetNotificationBellHTML)
	g.POST("/notifications/:id/read", h.MarkAsRead)
	g.POST("/notifications/read-all", h.MarkAllAsRead)
	g.GET("/notifications/count", h.GetUnreadCount)
	g.GET("/notifications/preferences", h.GetPreferences)
	g.PUT("/notifications/preferences", h.UpdatePreferences)
}

// NotificationsList returns the notifications modal content (HTMX only)
func (h *NotificationsHandler) NotificationsList(c echo.Context) error {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		fmt.Printf("NotificationsList: No userID found in middleware\n")
		return echo.NewHTTPError(401, "Unauthorized")
	}

	lang := cookies.GetLanguageFromCookie(c)
	fmt.Printf("NotificationsList called for userID: %d\n", userID)

	// Parse pagination parameters
	page := 1
	if pageStr := c.QueryParam("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	limit := int32(20) // 20 notifications per page
	offset := int32((page - 1) * int(limit))

	// Get notifications
	filters := notifications.NotificationFilters{
		UserID: userID,
		Limit:  limit,
		Offset: offset,
	}

	notificationList, err := h.NotificationService.GetUserNotifications(c.Request().Context(), filters)
	if err != nil {
		return echo.NewHTTPError(500, "Failed to load notifications")
	}

	// Get unread count
	unreadCount, err := h.NotificationService.GetUnreadCount(c.Request().Context(), userID)
	if err != nil {
		unreadCount = 0
	}

	// Calculate total pages (this is simplified - in production you'd want a more efficient count query)
	totalPages := (len(notificationList) + int(limit) - 1) / int(limit)
	if len(notificationList) == int(limit) {
		totalPages = page + 1 // There might be more pages
	}

	// Return modal content
	modal := templatesappnotifications.NotificationsModal(notificationList, lang, page, totalPages, unreadCount)
	writer := c.Response().Writer
	return modal.Render(writer)
}

// GetNotificationBellHTML returns the notification bell HTML with current data
func (h *NotificationsHandler) GetNotificationBellHTML(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	// Get unread count
	unreadCount, err := h.NotificationService.GetUnreadCount(c.Request().Context(), userID)
	if err != nil {
		unreadCount = 0
	}

	// Get target from HTMX header
	hxTarget := c.Request().Header.Get("HX-Target")
	id := hxTarget[1:] // Extract ID from target selector (remove the # prefix)

	props := notificationbell.NotificationBellProps{
		Lang:        lang,
		UserID:      userID,
		UnreadCount: unreadCount,
		ID:          id,
		HxTarget:    hxTarget,
	}

	writer := c.Response().Writer
	return notificationbell.NotificationBell(props).Render(writer)
}

// MarkAsRead marks a notification as read
func (h *NotificationsHandler) MarkAsRead(c echo.Context) error {
	userID := middleware.GetUserID(c)
	notificationIDStr := c.Param("id")

	notificationID, err := strconv.ParseInt(notificationIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(400, "Invalid notification ID")
	}

	err = h.NotificationService.MarkAsRead(c.Request().Context(), int32(notificationID), userID)
	if err != nil {
		return echo.NewHTTPError(500, "Failed to mark notification as read")
	}

	// Return updated modal content
	return h.NotificationsList(c)
}

// MarkAllAsRead marks all notifications as read
func (h *NotificationsHandler) MarkAllAsRead(c echo.Context) error {
	userID := middleware.GetUserID(c)

	err := h.NotificationService.MarkAllAsRead(c.Request().Context(), userID)
	if err != nil {
		return echo.NewHTTPError(500, "Failed to mark all notifications as read")
	}

	// Return updated modal content
	return h.NotificationsList(c)
}

// GetUnreadCount returns the unread notification count
func (h *NotificationsHandler) GetUnreadCount(c echo.Context) error {
	userID := middleware.GetUserID(c)

	count, err := h.NotificationService.GetUnreadCount(c.Request().Context(), userID)
	if err != nil {
		return echo.NewHTTPError(500, "Failed to get unread count")
	}

	return c.JSON(200, map[string]interface{}{
		"unread_count": count,
	})
}

// GetPreferences returns the user's notification preferences
func (h *NotificationsHandler) GetPreferences(c echo.Context) error {
	userID := middleware.GetUserID(c)

	prefs, err := h.NotificationService.GetUserPreferences(c.Request().Context(), userID)
	if err != nil {
		// Return default preferences if none found
		return c.JSON(200, map[string]interface{}{
			"in_app_enabled":   true,
			"match_updates":    true,
			"schedule_changes": true,
			"results":          true,
			"announcements":    true,
		})
	}

	return c.JSON(200, map[string]interface{}{
		"in_app_enabled":   prefs.InAppEnabled,
		"match_updates":    prefs.MatchUpdates,
		"schedule_changes": prefs.ScheduleChanges,
		"results":          prefs.Results,
		"announcements":    prefs.Announcements,
	})
}

// UpdatePreferences updates the user's notification preferences
func (h *NotificationsHandler) UpdatePreferences(c echo.Context) error {
	userID := middleware.GetUserID(c)
	lang := cookies.GetLanguageFromCookie(c)

	var form NotificationPreferencesForm
	if err := c.Bind(&form); err != nil {
		return echo.NewHTTPError(400, "Invalid form data")
	}

	prefs := notifications.NotificationPreferences{
		InAppEnabled:    form.InAppEnabled,
		MatchUpdates:    form.MatchUpdates,
		ScheduleChanges: form.ScheduleChanges,
		Results:         form.Results,
		Announcements:   form.Announcements,
	}

	_, err := h.NotificationService.UpdateUserPreferences(c.Request().Context(), userID, prefs)
	if err != nil {
		// Return error toast
		writer := c.Response().Writer
		return toast.Toast(toast.ToastConfig{
			ID:         "notification-preferences-error",
			Message:    "Failed to update notification preferences",
			Style:      "error",
			DataTestID: "notification-preferences-error",
			AutoClose:  true,
			Lang:       lang,
		}).Render(writer)
	}

	// Return success toast
	locales := i18n.MustLoadTemplateLocales("./templates/app/settings/usersettingsPatchSuccess.locales.json", lang)
	message := "Preferences updated successfully"
	if locales["preferences_updated_successfully"] != "" {
		message = locales["successMessage"]
	}

	writer := c.Response().Writer
	return toast.Toast(toast.ToastConfig{
		ID:         "notification-preferences-success",
		Message:    message,
		Style:      "success",
		DataTestID: "notification-preferences-success",
		AutoClose:  true,
		Lang:       lang,
	}).Render(writer)
}
