package cookies

import (
	"net/http"
	"os"
	"time"

	"github.com/labstack/echo/v4"
)

// CookieOptions contains configuration for cookie creation
type CookieOptions struct {
	Name     string
	Value    string
	Expires  time.Duration
	Path     string
	HttpOnly bool
	Secure   bool
	SameSite http.SameSite
}

// DefaultCookieOptions returns the default cookie options
func DefaultCookieOptions() CookieOptions {
	// Default to Secure, but disable Secure for local development/testing (http)
	secure := true
	// Check environment variable APP_ENV or E2E_TEST, or if running on localhost
	if os.Getenv("APP_ENV") == "development" || os.Getenv("E2E_TEST") == "1" {
		secure = false
	}

	return CookieOptions{
		Expires:  24 * time.Hour,
		Path:     "/",
		HttpOnly: true,
		Secure:   secure,
		SameSite: http.SameSiteStrictMode,
	}
}

// SetLanguageCookie sets the language preference cookie on the response
func SetLanguageCookie(c echo.Context, lang string) {
	options := DefaultCookieOptions()
	options.Name = "lang"
	options.Value = lang
	// Not HttpOnly so <PERSON><PERSON> can read it if needed
	options.HttpOnly = false
	cookie := CreateCookie(options)
	c.SetCookie(cookie)
}

// CreateSessionCookie creates a cookie with the given JWT token
func CreateSessionCookie(token string) *http.Cookie {
	options := DefaultCookieOptions()
	options.Name = "token"
	options.Value = token

	return CreateCookie(options)
}

// CreateCookie creates and returns an http.Cookie with the specified options
func CreateCookie(options CookieOptions) *http.Cookie {
	cookie := new(http.Cookie)
	cookie.Name = options.Name
	cookie.Value = options.Value
	cookie.Expires = time.Now().Add(options.Expires)
	cookie.Path = options.Path
	cookie.HttpOnly = options.HttpOnly
	cookie.Secure = options.Secure
	cookie.SameSite = options.SameSite
	return cookie
}

// GetLanguageFromCookie retrieves the language preference from a cookie
func GetLanguageFromCookie(c echo.Context) string {
	cookie, err := c.Cookie("lang")
	if err != nil {
		return "en" // Default to English if no cookie is found
	}
	return cookie.Value
}
