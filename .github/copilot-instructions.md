## Coachpad Copilot Instructions

### How to Run Tests
- To run all Playwright E2E and utility tests, run:
	- `npm test` (see `package.json`)

### Project Structure (2025)

- **Root**: Shell scripts for DB management (`backup-db.sh`, `bootstrap-db-dev.sh`, etc.), SQL files (`schema.sql`, `query.sql`), config files (`go.mod`, `package.json`, `vite.config.mjs`, etc.), and documentation (`README.md`, `DEPLOYMENT.md`, etc.).
- **`bin/`**: (if present) Compiled binaries or helper scripts.
- **`cookies/`**: Go code for cookie/session management.
- **`db/`**: Go database access layer (models, queries, generated code).
- **`deployment/`**: Deployment scripts and configs (systemd, nginx, backup/restore scripts).
- **`docs/`**: Project documentation and implementation plans.
- **`emailreminders/`**: Go code for scheduled email reminders.
- **`frontend/`**: JavaScript and CSS for client-side interactivity. Includes reusable UI JS components in `components/`.
- **`handlers/`**: Go HTTP handlers for all app features (auth, matches, players, seasons, etc.).
- **`htmlrenderer/`**: Go code for rendering HTML templates.
- **`i18n/`**: Internationalization support (Go code and JSON locale files).
- **`llm/`**: Language model/validation logic and modal patterns.
- **`mailgun/`**: Go code for Mailgun email integration.
- **`matchmaker/`**: Matchmaking logic (e.g., round robin scheduling).
- **`middleware/`**: HTTP middleware (auth, logging, permissions, etc.).
- **`migrations/`**: SQL migration files.
- **`notifications/`**: Go code for notification services.
- **`pkg/`**: Go packages (e.g., logger).
- **`playwright-report/`**: Playwright E2E test reports.
- **`public/`**: Static assets (images, icons, etc.).
- **`sse/`**: Go code for server-sent events.
- **`stripe/`**: Go code for Stripe integration.
- **`templates/`**: Go HTML templates, organized by feature (landing, app, nav, players, seasons, settings, signin, signup, components, layouts, storybook, ui, etc.) and locale JSON files.
- **`test-results/`**: Playwright and other test result outputs.
- **`tests/`**: End-to-end and utility test files (Playwright, fixtures, utils, etc.).
- **`utils/`**: Utility Go code (date handling, JSON, htmx, type conversion, URL helpers, etc.).
- **`tmp/`**: Temporary files (e.g., logs).

#### Key Technologies
- **Backend**: Go (Echo, pgx, sqlc, etc.)
- **Frontend**: JS/CSS (Vite, Tailwind, custom components)
- **Templates**: Go HTML templates (gomponents)
- **Testing**: Playwright (E2E, utility, and fixtures)
- **Database**: PostgreSQL

#### Icons
When needing icons in Go templates, use: https://pkg.go.dev/github.com/maragudk/gomponents-heroicons/v2@v2.1.0/solid

#### Notes
- Most business logic is in Go, with templates and handlers organized by feature.
- Playwright is used for E2E testing, with tests and fixtures in `tests/e2e/`.
- Static assets are in `public/`.
- For DB schema and queries, see `schema.sql` and `query.sql`.

## Development Patterns and Best Practices 

### Architectural Overview
- **Server-Side Rendering (SSR)**: HTML is generated server-side using gomponents (type-safe Go HTML components). HTMX handles partial page updates; Alpine.js adds client-side reactivity as needed.
- **Type-Safe Database Layer**: SQL queries are written in `query.sql` and SQLC generates type-safe Go code. Models are in `/db/`.
- **Handler-Based Request Processing**: Each feature has a handler struct in `/handlers/` with a `Queries *db.Queries` field. Handlers register routes via `RegisterRoutes(*echo.Group)` and delegate UI rendering to templates in `/templates/`.
- **Component-Based UI**: UI components are Go functions returning `gomponents.Node`. Localization files (`.locales.json`) are co-located with templates.
- **Authentication**: JWT tokens from Stytch stored in cookies; auth middleware validates tokens; user context passed via Echo context.
- **Page Builder Pattern**: Use `@templates/pagebuilder/pagebuilder.go` for building consistent top-level views. Chain configuration methods (e.g., `WithTitle()`, `WithTarget()`). Supports `RenderContent()` for HTMX swaps and `RenderFullPage()` for initial loads. Handles locale loading and HTMX attributes automatically.

### UI and Template Patterns
- **Toast Pattern**: For forms/actions needing feedback, target `#toast-body-container` with `hx-target` and `hx-swap`. Backend returns toast components (see `toast.Toast`). Errors persist, success auto-closes.
- **Inline Edit Pattern**: Use `@templates/ui/inlineedit/inlineedit.go` for inline editing, not manual contenteditable spans.
- **Date Input Pattern**: Use `@templates/ui/inlinedatepicker/inlinedatepicker.go` or `@templates/ui/datepicker/datepicker.go` for date fields. Never use contenteditable for dates.
- **Handler HTML Generation**: Never create HTML directly in handlers. Always use template components and call their `Render()` method. Handlers contain business logic only.
- **Submit Button Pattern**: Use simple button components (e.g., `button.PrimaryButton`) with `ButtonType: "submit"`. Avoid custom onclick/double-click prevention.

### Testing Patterns
- **E2E Tests**: Use Playwright with fixture-based test data and isolated user contexts. Always use `data-testid` attributes for DOM element selection.
- **Fixture with Auth Pattern**: When using fixtures, always use the page context from the auth fixture for interactions (e.g., `isolatedUser.page`).
- **Test Data Pattern**: Use fixture data for predictable tests, not dynamic selection.
- **E2E Context Reuse**: Reuse page context from auth fixture to have cookies dialog pre-accepted and hidden.

### Localization and Internationalization
- Never define default localization values as fallbacks. If a locale file fails to load, log the error and use empty locales.
- Add translations to `.locales.json` files for each template.

### Server-Triggered Events Pattern
- Decouple data changes from UI updates: server sends minimal response with a custom event header (e.g., `newItemAdded`). Components listen for these events and refresh themselves.

### SSE Pattern
- Always use htmx-sse for server-sent events. Never implement custom client code for SSE.

### Alpine.js and HTMX
- Register Alpine.js components with `Alpine.data()` in frontend JS.
- Use `hx-*` attributes for server communication.
- For dropdown components using Alpine.js bindings (like `XHxGet`, `XOnClick`, `XHref`), always pass string values wrapped in quotes. For example: `XHxGet: "'/app/teams/new'"` instead of `XHxGet: "/app/teams/new"`. This ensures Alpine.js can properly evaluate the binding with `x-bind:hx-get`.

### Go and SQLC Patterns
- After editing `query.sql`, run `sqlc generate` to regenerate Go code.
- Format Go code with `go fmt ./...` and organize imports with `goimports -w .`.
- Use `go mod tidy` to clean up dependencies.
- Echo structs use `form:"field"`, `query:"param"`, or `json:"key"` tags for binding, and `validate:"rules"` for validation.
- Always get the response writer in handlers with `writer := c.Response().Writer` and pass to template `Render(writer)`. Never use buffers or `c.HTML()` for gomponents templates.
- Never put request-level data at the package level in var declarations.

### Miscellaneous Patterns and Tips
- Use Podman for PostgreSQL dev DB. Connect with `podman exec -t postgres psql -c -U postgres -d postgres`.
- Log inspection: live logs at `tmp/app.log`. Grep for keywords instead of sampling lines.
- Stripe webhook logs: `tmp/stripe_webhook.log`.
- To check if a request is boosted, look for `HX-Boosted` in the request header.
- Use short commit messages.
- Static assets are built with Vite and manifest for production.
- For DB management, use provided shell scripts in the root.

For further details and rationale, see `CLAUDE.md`.

---

To run a specific test file, use `npm run test -- <path-to-test-file>`.
NEVER run any tests with --debug flag.
Always use our design system modal when implementing a modal #file:templates/ui/modal/modal.go
When implementing a server rendered modal that gets inserted into #modal-body-container, make sure that we always use the innerHTML swap strategy to avoid messing up our markup.