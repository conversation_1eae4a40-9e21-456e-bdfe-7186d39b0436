package limits

import (
	"context"
	"fmt"

	"github.com/j-em/coachpad/db"
	"github.com/jackc/pgx/v5/pgtype"
)

// Service provides limit checking functionality
type Service struct {
	queries *db.Queries
}

// NewService creates a new limits service
func NewService(queries *db.Queries) *Service {
	return &Service{
		queries: queries,
	}
}

// CheckLimit verifies if a user can create a new resource of the given type
func (s *Service) CheckLimit(ctx context.Context, userID int32, resourceType ResourceType) (*LimitCheckResult, error) {
	// Get user's subscription tier
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	tier := SubscriptionTier(user.SubscriptionTier)
	limits := GetLimitsForTier(tier)

	// Get current count for the resource type
	currentCount, err := s.getCurrentCount(ctx, userID, resourceType)
	if err != nil {
		return nil, fmt.Errorf("failed to get current count: %w", err)
	}

	limit := limits.GetLimit(resourceType)
	isUnlimited := limits.IsUnlimited(resourceType)

	result := &LimitCheckResult{
		CurrentCount: currentCount,
		Limit:        limit,
		ResourceType: resourceType,
		IsUnlimited:  isUnlimited,
	}

	if isUnlimited {
		result.Allowed = true
	} else {
		result.Allowed = currentCount < limit
		if !result.Allowed {
			result.ErrorMessage = fmt.Sprintf("You have reached the limit of %d %s for your subscription tier", limit, resourceType)
		}
	}

	return result, nil
}

// CheckBulkLimit verifies if a user can create multiple resources of the given type
func (s *Service) CheckBulkLimit(ctx context.Context, userID int32, resourceType ResourceType, quantity int) (*LimitCheckResult, error) {
	// Get current limit check
	result, err := s.CheckLimit(ctx, userID, resourceType)
	if err != nil {
		return nil, err
	}

	if result.IsUnlimited {
		result.Allowed = true
		return result, nil
	}

	// Check if current count + quantity exceeds limit
	newTotal := result.CurrentCount + quantity
	result.Allowed = newTotal <= result.Limit

	if !result.Allowed {
		result.ErrorMessage = fmt.Sprintf("Creating %d %s would exceed your limit of %d for your subscription tier", quantity, resourceType, result.Limit)
	}

	return result, nil
}

// GetCurrentUsage returns current usage for all resource types for a user
func (s *Service) GetCurrentUsage(ctx context.Context, userID int32) (map[ResourceType]*LimitCheckResult, error) {
	usage := make(map[ResourceType]*LimitCheckResult)

	resourceTypes := []ResourceType{ResourcePlayers, ResourceSeasons, ResourceMatches, ResourceTeams}

	for _, resourceType := range resourceTypes {
		result, err := s.CheckLimit(ctx, userID, resourceType)
		if err != nil {
			return nil, fmt.Errorf("failed to check limit for %s: %w", resourceType, err)
		}
		usage[resourceType] = result
	}

	return usage, nil
}

// getCurrentCount returns the current count of resources for a user
func (s *Service) getCurrentCount(ctx context.Context, userID int32, resourceType ResourceType) (int, error) {
	switch resourceType {
	case ResourcePlayers:
		count, err := s.queries.CountUserPlayers(ctx, userID)
		return int(count), err
	case ResourceSeasons:
		userIDPg := pgtype.Int4{Int32: userID, Valid: true}
		count, err := s.queries.CountUserSeasons(ctx, userIDPg)
		return int(count), err
	case ResourceMatches:
		userIDPg := pgtype.Int4{Int32: userID, Valid: true}
		count, err := s.queries.CountUserMatches(ctx, userIDPg)
		return int(count), err
	case ResourceTeams:
		count, err := s.queries.CountUserTeams(ctx, userID)
		return int(count), err
	default:
		return 0, fmt.Errorf("unknown resource type: %s", resourceType)
	}
}

// CanUpgrade checks if user can upgrade a specific resource
func (s *Service) CanUpgrade(ctx context.Context, userID int32, resourceType ResourceType) (bool, error) {
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user: %w", err)
	}

	// If user is already pro, they can upgrade
	if SubscriptionTier(user.SubscriptionTier) == TierPro {
		return true, nil
	}

	// Check if they're at or near their limit
	result, err := s.CheckLimit(ctx, userID, resourceType)
	if err != nil {
		return false, err
	}

	// Consider upgrade if they're at 80% or more of their limit
	if result.IsUnlimited {
		return false, nil // Already unlimited
	}

	threshold := float64(result.Limit) * 0.8
	return float64(result.CurrentCount) >= threshold, nil
}
