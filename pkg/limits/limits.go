package limits

import (
	"fmt"
	"os"
	"strconv"
)

// SubscriptionTier represents a user's subscription level
type SubscriptionTier string

const (
	TierFree SubscriptionTier = "free"
	TierPro  SubscriptionTier = "pro"
)

// ResourceType represents the type of resource being limited
type ResourceType string

const (
	ResourcePlayers ResourceType = "players"
	ResourceSeasons ResourceType = "seasons"
	ResourceMatches ResourceType = "matches"
	ResourceTeams   ResourceType = "teams"
)

// ResourceLimits defines the limits for each resource type per subscription tier
type ResourceLimits struct {
	Players int
	Seasons int
	Matches int
	Teams   int
}

// Global limits instance
var Limits struct {
	Free ResourceLimits
	Pro  ResourceLimits
}

// init initializes the subscription limits from environment variables
func init() {
	var err error

	// Free tier limits
	Limits.Free.Players, err = getRequiredEnvInt("COACHPAD_FREE_USER_MAX_PLAYERS")
	if err != nil {
		panic(fmt.Sprintf("Failed to load free tier player limit: %v", err))
	}

	Limits.Free.Teams, err = getRequiredEnvInt("COACHPAD_FREE_USER_MAX_TEAMS")
	if err != nil {
		panic(fmt.Sprintf("Failed to load free tier team limit: %v", err))
	}

	Limits.Free.Seasons, err = getRequiredEnvInt("COACHPAD_FREE_USER_MAX_SEASONS")
	if err != nil {
		panic(fmt.Sprintf("Failed to load free tier season limit: %v", err))
	}

	Limits.Free.Matches, err = getRequiredEnvInt("COACHPAD_FREE_USER_MAX_MATCHES")
	if err != nil {
		panic(fmt.Sprintf("Failed to load free tier match limit: %v", err))
	}

	// Pro tier limits
	Limits.Pro.Players, err = getRequiredEnvInt("COACHPAD_PRO_USER_MAX_PLAYERS")
	if err != nil {
		panic(fmt.Sprintf("Failed to load pro tier player limit: %v", err))
	}

	Limits.Pro.Teams, err = getRequiredEnvInt("COACHPAD_PRO_USER_MAX_TEAMS")
	if err != nil {
		panic(fmt.Sprintf("Failed to load pro tier team limit: %v", err))
	}

	Limits.Pro.Seasons, err = getRequiredEnvInt("COACHPAD_PRO_USER_MAX_SEASONS")
	if err != nil {
		panic(fmt.Sprintf("Failed to load pro tier season limit: %v", err))
	}

	Limits.Pro.Matches, err = getRequiredEnvInt("COACHPAD_PRO_USER_MAX_MATCHES")
	if err != nil {
		panic(fmt.Sprintf("Failed to load pro tier match limit: %v", err))
	}
}

// getRequiredEnvInt gets a required environment variable and converts it to int
func getRequiredEnvInt(key string) (int, error) {
	value := os.Getenv(key)
	if value == "" {
		return 0, fmt.Errorf("environment variable %s is required but not set", key)
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0, fmt.Errorf("environment variable %s must be a valid integer, got: %s", key, value)
	}

	return intValue, nil
}

// GetLimitsForTier returns the resource limits for a given subscription tier
func GetLimitsForTier(tier SubscriptionTier) ResourceLimits {
	switch tier {
	case TierFree:
		return Limits.Free
	case TierPro:
		return Limits.Pro
	default:
		// Default to free tier limits
		return Limits.Free
	}
}

// IsUnlimited checks if a limit is unlimited (-1)
func (rl ResourceLimits) IsUnlimited(resourceType ResourceType) bool {
	switch resourceType {
	case ResourcePlayers:
		return rl.Players == -1
	case ResourceSeasons:
		return rl.Seasons == -1
	case ResourceMatches:
		return rl.Matches == -1
	case ResourceTeams:
		return rl.Teams == -1
	default:
		return false
	}
}

// GetLimit returns the limit for a specific resource type
func (rl ResourceLimits) GetLimit(resourceType ResourceType) int {
	switch resourceType {
	case ResourcePlayers:
		return rl.Players
	case ResourceSeasons:
		return rl.Seasons
	case ResourceMatches:
		return rl.Matches
	case ResourceTeams:
		return rl.Teams
	default:
		return 0
	}
}

// LimitCheckResult represents the result of a limit check
type LimitCheckResult struct {
	Allowed      bool
	CurrentCount int
	Limit        int
	ResourceType ResourceType
	IsUnlimited  bool
	ErrorMessage string
}

// IsLimitExceeded checks if the current count exceeds the limit
func (lcr LimitCheckResult) IsLimitExceeded() bool {
	return !lcr.Allowed
}

// RemainingCapacity calculates how many more resources can be created
func (lcr LimitCheckResult) RemainingCapacity() int {
	if lcr.IsUnlimited {
		return -1 // Unlimited
	}
	remaining := lcr.Limit - lcr.CurrentCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

// Legacy functions for backward compatibility
func GetPlayerLimit(subscriptionType string) int {
	switch subscriptionType {
	case "pro":
		return Limits.Pro.Players
	case "free":
		return Limits.Free.Players
	default:
		return Limits.Free.Players
	}
}

func GetTeamLimit(subscriptionType string) int {
	switch subscriptionType {
	case "pro":
		return Limits.Pro.Teams
	case "free":
		return Limits.Free.Teams
	default:
		return Limits.Free.Teams
	}
}

func GetSeasonLimit(subscriptionType string) int {
	switch subscriptionType {
	case "pro":
		return Limits.Pro.Seasons
	case "free":
		return Limits.Free.Seasons
	default:
		return Limits.Free.Seasons
	}
}

func GetMatchLimit(subscriptionType string) int {
	switch subscriptionType {
	case "pro":
		return Limits.Pro.Matches
	case "free":
		return Limits.Free.Matches
	default:
		return Limits.Free.Matches
	}
}
