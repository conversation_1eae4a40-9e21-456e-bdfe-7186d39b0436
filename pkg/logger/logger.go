package logger

import (
	"context"
	"log/slog"
	"os"
)

type Logger struct {
	*slog.Logger
}

type Config struct {
	Level     slog.Level
	Format    Format
	Output    Output
	AddSource bool
	FilePath  string
}

type Format int

const (
	FormatJSON Format = iota
	FormatText
)

type Output int

const (
	OutputConsole Output = iota
	OutputFile
	OutputBoth
)

func New(config Config) *Logger {
	var handler slog.Handler

	opts := &slog.HandlerOptions{
		Level:     config.Level,
		AddSource: config.AddSource,
	}

	switch config.Output {
	case OutputConsole:
		handler = createHandler(config.Format, os.Stdout, opts)
	case OutputFile:
		file, err := os.OpenFile(config.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			panic(err)
		}
		handler = createHandler(config.Format, file, opts)
	case OutputBoth:
		consoleHandler := createHandler(config.Format, os.Stdout, opts)
		fileWriter, err := os.OpenFile(config.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			panic(err)
		}
		fileHandler := createHandler(config.Format, fileWriter, opts)
		handler = &multiHandler{handlers: []slog.Handler{consoleHandler, fileHandler}}
	default:
		handler = createHandler(FormatText, os.Stdout, opts)
	}

	return &Logger{
		Logger: slog.New(handler),
	}
}

func createHandler(format Format, writer *os.File, opts *slog.HandlerOptions) slog.Handler {
	switch format {
	case FormatJSON:
		return slog.NewJSONHandler(writer, opts)
	case FormatText:
		return slog.NewTextHandler(writer, opts)
	default:
		return slog.NewTextHandler(writer, opts)
	}
}

func (l *Logger) WithContext(ctx context.Context) *Logger {
	if reqID := RequestIDFromContext(ctx); reqID != "" {
		return &Logger{l.Logger.With("request_id", reqID)}
	}
	return l
}

func (l *Logger) WithUser(userID string) *Logger {
	if userID != "" {
		return &Logger{l.Logger.With("user_id", userID)}
	}
	return l
}

func (l *Logger) WithOperation(operation string) *Logger {
	return &Logger{l.Logger.With("operation", operation)}
}

func (l *Logger) LogError(ctx context.Context, msg string, err error, attrs ...any) {
	args := []any{"error", err.Error()}
	args = append(args, attrs...)
	l.WithContext(ctx).Error(msg, args...)
}

func (l *Logger) LogInfo(ctx context.Context, msg string, attrs ...any) {
	l.WithContext(ctx).Info(msg, attrs...)
}

func (l *Logger) LogWarn(ctx context.Context, msg string, attrs ...any) {
	l.WithContext(ctx).Warn(msg, attrs...)
}

func (l *Logger) LogDebug(ctx context.Context, msg string, attrs ...any) {
	l.WithContext(ctx).Debug(msg, attrs...)
}
