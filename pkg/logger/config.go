package logger

import (
	"log/slog"
	"os"
	"strings"
)

func DefaultConfig() Config {
	return Config{
		Level:     slog.LevelInfo,
		Format:    FormatText,
		Output:    OutputBoth,
		AddSource: true,
		FilePath:  "tmp/app.log",
	}
}

func ConfigFromEnv() Config {
	config := DefaultConfig()

	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Level = parseLogLevel(level)
	}

	if format := os.Getenv("LOG_FORMAT"); format != "" {
		config.Format = parseLogFormat(format)
	}

	if output := os.Getenv("LOG_OUTPUT"); output != "" {
		config.Output = parseLogOutput(output)
	}

	if path := os.Getenv("LOG_FILE_PATH"); path != "" {
		config.FilePath = path
	}

	if source := os.Getenv("LOG_ADD_SOURCE"); source == "false" {
		config.AddSource = false
	}

	if isDevelopment() {
		config.Level = slog.LevelDebug
		config.Format = FormatText
	}

	return config
}

func parseLogLevel(level string) slog.Level {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return slog.LevelDebug
	case "INFO":
		return slog.LevelInfo
	case "WARN", "WARNING":
		return slog.LevelWarn
	case "ERROR":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

func parseLogFormat(format string) Format {
	switch strings.ToLower(format) {
	case "json":
		return FormatJSON
	case "text":
		return FormatText
	default:
		return FormatText
	}
}

func parseLogOutput(output string) Output {
	switch strings.ToLower(output) {
	case "console":
		return OutputConsole
	case "file":
		return OutputFile
	case "both":
		return OutputBoth
	default:
		return OutputBoth
	}
}

func isDevelopment() bool {
	env := os.Getenv("ENVIRONMENT")
	return env == "" || env == "development" || env == "dev"
}
