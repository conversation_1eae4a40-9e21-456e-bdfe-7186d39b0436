// Alpine.js x-data logic for Stripe payment form.
// Usage: Attach this as x-data to your payment form root element.
// Call with: stripePayment(clientSecret, publishableKey)
import { loadStripe } from '@stripe/stripe-js';

export default (clientSecret, publishableKey) => ({
  stripe: null,
  elements: null,
  card: null,
  clientSecret,
  publishableKey,
  loading: false,
  error: '',
  success: false,
  async init() {
    this.stripe = await loadStripe(this.publishableKey);
    this.elements = this.stripe.elements();
    this.card = this.elements.create('card', {
      hidePostalCode: false
    });
    this.card.mount('#card-element');
  },
  async submitPayment() {
    this.loading = true;
    this.error = '';
    const {error, paymentIntent} = await this.stripe.confirmCardPayment(this.clientSecret, {
      payment_method: {card: this.card}
    });
    if (error) {
      this.error = error.message;
      this.loading = false;
    } else if (paymentIntent && paymentIntent.status === 'succeeded') {
      this.success = true;
      this.loading = false;
      // Close all modals after showing success message briefly
      setTimeout(() => {
        Alpine.store('appStore').closeAllModals();
      }, 2000); // Wait 2 seconds to show success message
    }
  }
});
