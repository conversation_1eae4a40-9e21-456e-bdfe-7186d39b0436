# main.js Purpose and Structure

This file is the main JavaScript entry point for the frontend of the application. It is responsible for initializing and configuring client-side libraries, registering custom components, and setting up global state and directives for the UI.

## Key Responsibilities

- **Imports and Polyfills**: Loads required libraries such as Alpine.js, htmx, and a Vite polyfill for module preloading.
- **HTMX Configuration**: Sets up custom response handling for htmx, allowing for advanced client-server interactions.
- **Component Registration**: Imports and registers reusable UI components (e.g., playersTable, matchesTable, datePicker, selectComponent, dropdown) for use with Alpine.js.
- **Alpine.js Initialization**:
  - Registers Alpine.js data stores and directives for components and form validation.
  - Adds a global Alpine store (`appStore`) to manage app-wide state, such as active navigation links and global UI actions (e.g., closing all dropdowns or modals).
  - Loads the Alpine.js focus plugin for improved accessibility and keyboard navigation.
- **Global Exposure**: Exposes Alpine and htmx on the `window` object for debugging and integration with other scripts.
- **CSS Import**: Loads the main CSS file for frontend styling.

## Summary

`main.js` acts as the bootstrapper for the frontend, ensuring all interactive components, global state, and event handling are set up before the application runs. It provides a foundation for a modular, maintainable, and interactive user interface using Alpine.js and htmx.
