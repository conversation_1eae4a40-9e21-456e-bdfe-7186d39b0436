export default (initialColumns) => ({
    columns: initialColumns || [],
    searchTerm: '',
    currentPage: 1,
    itemsPerPage: localStorage.getItem('customColumnsTablePageSize') || '10',
    sortField: 'displayOrder',
    sortDirection: 'asc',
    error: null,
    selectedColumns: [],
    
    init() {
        // Watch for changes to itemsPerPage and save to localStorage
        this.$watch('itemsPerPage', (value) => {
            localStorage.setItem('customColumnsTablePageSize', value);
            this.currentPage = 1; // Reset to first page when changing items per page
        });
    },
    
    get filteredColumns() {
        return this.columns.filter(column => {
            const term = this.searchTerm.toLowerCase();
            return (
                column.name.toLowerCase().includes(term) ||
                column.fieldType.toLowerCase().includes(term) ||
                (column.description && column.description.toLowerCase().includes(term))
            );
        });
    },
    
    get sortedColumns() {
        return [...this.filteredColumns].sort((a, b) => {
            const fieldA = a[this.sortField];
            const fieldB = b[this.sortField];
            
            if (typeof fieldA === 'string' && typeof fieldB === 'string') {
                return this.sortDirection === 'asc' 
                    ? fieldA.localeCompare(fieldB) 
                    : fieldB.localeCompare(fieldA);
            }
            
            return this.sortDirection === 'asc' 
                ? (fieldA - fieldB) 
                : (fieldB - fieldA);
        });
    },
    
    get paginatedColumns() {
        const startIndex = (this.currentPage - 1) * Number(this.itemsPerPage);
        const endIndex = startIndex + Number(this.itemsPerPage);
        return this.sortedColumns.slice(startIndex, endIndex);
    },
    
    get totalPages() {
        return Math.max(1, Math.ceil(this.filteredColumns.length / Number(this.itemsPerPage)));
    },
    
    get isAllSelected() {
        return this.paginatedColumns.length > 0 && this.paginatedColumns.every(col => this.selectedColumns.includes(col.id));
    },
    
    get hasSelectedColumns() {
        return this.selectedColumns.length > 0;
    },
    
    sortBy(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
    },
    
    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
        }
    },
    
    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
        }
    },
    
    async updateColumn(column, field) {
        column.saving = true;
        this.error = null;
        
        // Create payload with only the changed field
        const payload = {
            id: column.id,
            [field]: column[field]
        };
        
        try {
            const response = await fetch('/app/settings/custom-columns/update', {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error('Failed to update custom column');
            }

            const data = await response.json();
            // Update the column with returned data
            Object.assign(column, data);
        } catch (err) {
            this.error = document.getElementById('save-error-message')?.textContent || err.message;
        } finally {
            column.saving = false;
        }
    },
    
    async deleteColumn(column) {
        if (!confirm('Are you sure you want to delete this custom field?')) {
            return;
        }
        
        column.saving = true;
        this.error = null;
        
        try {
            const response = await fetch('/app/settings/custom-columns/delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: column.id })
            });

            if (!response.ok) {
                throw new Error('Failed to delete custom column');
            }
            
            // Remove the column from the array
            this.columns = this.columns.filter(c => c.id !== column.id);
            
            // Adjust current page if needed
            if (this.paginatedColumns.length === 0 && this.currentPage > 1) {
                this.currentPage--;
            }
        } catch (err) {
            this.error = document.getElementById('save-error-message')?.textContent || err.message;
        } finally {
            if (this.columns.includes(column)) {
                column.saving = false;
            }
        }
    },
    
    toggleColumnSelection(columnId) {
        if (this.selectedColumns.includes(columnId)) {
            this.selectedColumns = this.selectedColumns.filter(id => id !== columnId);
        } else {
            this.selectedColumns.push(columnId);
        }
    },
    
    toggleAllSelection() {
        if (this.isAllSelected) {
            this.selectedColumns = this.selectedColumns.filter(id => !this.paginatedColumns.map(col => col.id).includes(id));
        } else {
            const pageColumnIds = this.paginatedColumns.map(col => col.id);
            pageColumnIds.forEach(id => {
                if (!this.selectedColumns.includes(id)) {
                    this.selectedColumns.push(id);
                }
            });
        }
    },
    
    clearSelection() {
        this.selectedColumns = [];
    },
    
    async deleteSelectedColumns() {
        if (!confirm(`Are you sure you want to delete ${this.selectedColumns.length} selected columns?`)) {
            return;
        }
        
        this.error = null;
        
        try {
            const deletePromises = this.selectedColumns.map(async (columnId) => {
                const response = await fetch('/app/settings/custom-match-columns/delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: columnId })
                });

                if (!response.ok) {
                    throw new Error(`Failed to delete column ${columnId}`);
                }
                
                return columnId;
            });
            
            const deletedIds = await Promise.all(deletePromises);
            
            // Remove deleted columns from the array
            this.columns = this.columns.filter(c => !deletedIds.includes(c.id));
            
            // Clear selection
            this.clearSelection();
            
            // Adjust current page if needed
            if (this.paginatedColumns.length === 0 && this.currentPage > 1) {
                this.currentPage--;
            }
        } catch (err) {
            this.error = document.getElementById('save-error-message')?.textContent || err.message;
        }
    }
})
