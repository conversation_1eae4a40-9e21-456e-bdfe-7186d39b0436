// Alpine.js x-data logic for a dropdown component.
// Usage: Attach this as x-data to your dropdown root element.
// Provides isOpen state, dynamic menu positioning, and a close method.

export default (position = 'bottom') => ({
  // Tracks whether the dropdown menu is open.
  isOpen: false,
  // Inline style string for positioning the dropdown menu.
  menuStyles: '',
  // Preferred position for the dropdown menu
  position: position,
  // Track if position has been calculated for current open state
  positionCalculated: false,
  // Calculates and sets the dropdown menu's absolute position
  // based on the button's position in the viewport.
  updateMenuPosition(retryCount = 0) {
    const btn = this.$refs.button;
    if (!btn) return;
    
    // For top position, we need the menu to be in DOM to measure height
    if (this.position === 'top' && !this.$refs.menu) {
      // Handle teleportation timing differences between browsers (Chrome vs Firefox)
      if (retryCount < 5) { // Allow up to 5 retries
        setTimeout(() => {
          this.updateMenuPosition(retryCount + 1);
        }, 10); // Small delay to allow teleportation to complete
        return;
      }
      // If still not available after retries, continue with fallback
    }
    
    const rect = btn.getBoundingClientRect();
    const spacing = 4; // Gap between button and menu
    let top, left;
    
    switch (this.position) {
      case 'top':
        const menu = this.$refs.menu;
        // Make menu temporarily visible but off-screen to measure
        if (menu) {
          const originalDisplay = menu.style.display;
          const originalVisibility = menu.style.visibility;
          menu.style.display = 'block';
          menu.style.visibility = 'hidden';
          const menuHeight = menu.getBoundingClientRect().height;
          menu.style.display = originalDisplay;
          menu.style.visibility = originalVisibility;
          
          // Anchor bottom of menu to top of trigger
          top = rect.top + window.scrollY - menuHeight - spacing;
        } else {
          // Fallback if menu not available - use more conservative positioning
          // Ensure the dropdown is visible by positioning it below the button if near top of page
          const approximateMenuHeight = 200;
          const potentialTop = rect.top + window.scrollY - approximateMenuHeight - spacing;
          
          if (potentialTop < 0) {
            // If dropdown would be above viewport, position it below the button instead
            top = rect.bottom + window.scrollY + spacing;
          } else {
            top = potentialTop;
          }
        }
        left = rect.left + window.scrollX;
        break;
        
      case 'bottom':
        // Anchor top of menu to bottom of trigger
        top = rect.bottom + window.scrollY + spacing;
        left = rect.left + window.scrollX;
        break;
        
      case 'left':
        const menuWidth = 224; // w-56 = 14rem = 224px
        top = rect.top + window.scrollY;
        left = rect.left + window.scrollX - menuWidth - spacing;
        break;
        
      case 'right':
        top = rect.top + window.scrollY;
        left = rect.right + window.scrollX + spacing;
        break;
    }
    
    this.menuStyles =
      'position: absolute; top: ' +
      top +
      'px; left: ' +
      left +
      'px; min-width: ' +
      rect.width +
      'px;';
  },
  // Closes the dropdown menu.
  closeAllDropdowns() {
    this.isOpen = false;
    this.positionCalculated = false;
  },
  // Initialize component and add window resize listener
  init() {
    this.resizeHandler = () => {
      if (this.isOpen) {
        this.closeAllDropdowns();
      }
    };
    window.addEventListener('resize', this.resizeHandler);
  },
  // Clean up event listener when component is destroyed
  destroy() {
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }
  }
});
