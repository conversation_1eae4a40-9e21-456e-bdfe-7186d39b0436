/**
 * Parses a date string in YYYY-MM-dd format.
 * 
 * @param {string} dateString - The date string to parse in YYYY-MM-dd format
 * @returns {object|null} An object with year, month (0-11), and day properties, or null if invalid
 */
export function parseDateString(dateString) {
    if (!dateString || !dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return null;
    }
    
    const parts = dateString.split('-');
    return {
        year: parseInt(parts[0], 10),
        month: parseInt(parts[1], 10) - 1, // Convert month from 1-based to 0-based
        day: parseInt(parts[2], 10)
    };
}

/**
 * Copies text to the clipboard using the modern Clipboard API with fallback
 * 
 * @param {string} text - The text to copy to clipboard
 * @returns {Promise<boolean>} - Promise that resolves to true if successful, false otherwise
 */
export async function copyToClipboard(text) {
    try {
        // Try modern Clipboard API first
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            return successful;
        }
    } catch (err) {
        console.error('Failed to copy text to clipboard:', err);
        return false;
    }
}

