@import "tailwindcss";
@source "../templates/**/*.go";

@theme {
  --color-surface: #ffffff;
  --color-surface-dark: #06091c;
}

html,
body {
  font-family: system-ui, sans-serif;
}

[x-cloak] {
  display: none !important;
}


/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }

  html,
  body {
    background-color: var(--color-surface-dark);
    /* Dark background */
    color: #ffffff;
    /* White text */
  }

}

/* HTMX loading indicators */
.loading-pulse.htmx-request {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-fade.htmx-request {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.loading-spinner.htmx-request {
  opacity: 0.8;
}

/* HTMX indicator default state (hidden) */
.htmx-indicator {
  opacity: 0;
}

/* Multi-step signup animations */
.step-transition {
  transition: all 0.3s ease-in-out;
}

.step-indicator {
  transition: all 0.2s ease-in-out;
}

.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Show indicator when request is active */
.htmx-indicator.htmx-request {
  opacity: 1;
  visibility: visible;
}

.htmx-indicator {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease
}

/* Button loading states */
.coachpad-base-button.htmx-request {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
}

.coachpad-base-button.htmx-request::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.coachpad-button-link.htmx-request {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
}

.coachpad-button-link.htmx-request::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.coachpad-icon-button.htmx-request {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
}

.coachpad-icon-button.htmx-request>* {
  opacity: 0.5;
}

.coachpad-icon-button.htmx-request::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1;
}

.coachpad-icon-button-link.htmx-request {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
}

.coachpad-icon-button-link.htmx-request>* {
  opacity: 0.5;
}

.coachpad-icon-button-link.htmx-request::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}