/**
 * Datepicker Alpine.js directive factory.
 *
 * @param {string} [initialValue] - The initial date value for the datepicker. Should be an ISO 8601 date string (e.g., '2025-05-01').
 *   This value will be parsed using JavaScript's Date.parse().
 *   If not provided or invalid, the current date will be used.
 * @returns {object} Alpine.js x-data object for the datepicker component.
 */
// Define a directive for the datepicker component's x-data
import { parseDateString } from './utils.js';

export default (initialValue = '') => ({
    datePickerValue: initialValue,
    datePickerFormat: 'YYYY-MM-dd',
    datePickerMonth: '',
    datePickerYear: '',
    datePickerDay: '',
    datePickerDaysInMonth: [],
    datePickerBlankDaysInMonth: [],
    datePickerMonthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    datePickerDays: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    datePickerShowYearGrid: false,
    datePickerYearGrid: [],
    datePickerDayClicked(day) {
        let selectedDate = new Date(this.datePickerYear, this.datePickerMonth, day);
        this.datePickerDay = day;
        this.datePickerValue = this.datePickerFormatDate(selectedDate);
        this.datePickerIsSelectedDate(day);
        this.datePickerOpen = false;
        this.datePickerShowYearGrid = false;
    },
    datePickerPreviousMonth() {
        if (this.datePickerMonth == 0) {
            this.datePickerYear--;
            this.datePickerMonth = 12;
        }
        this.datePickerMonth--;
        this.datePickerCalculateDays();
    },
    datePickerNextMonth() {
        if (this.datePickerMonth == 11) {
            this.datePickerMonth = 0;
            this.datePickerYear++;
        } else {
            this.datePickerMonth++;
        }
        this.datePickerCalculateDays();
    },
    datePickerIsSelectedDate(day) {
        const d = new Date(this.datePickerYear, this.datePickerMonth, day);
        return this.datePickerValue === this.datePickerFormatDate(d);
    },
    datePickerIsToday(day) {
        const today = new Date();
        const d = new Date(this.datePickerYear, this.datePickerMonth, day);
        return today.toDateString() === d.toDateString();
    },

    datePickerToggleYearGrid() {
        this.datePickerShowYearGrid = !this.datePickerShowYearGrid;
        if (this.datePickerShowYearGrid) {
            this.datePickerGenerateYearGrid();
        }
    },
    datePickerGenerateYearGrid(centerYear) {
        // Show 20 years in a grid, centered on centerYear (or current year if not provided)
        const gridSize = 20;
        const currentYear = typeof centerYear === 'number' ? centerYear : this.datePickerYear;
        const startYear = currentYear - Math.floor(gridSize / 2);
        this.datePickerYearGrid = Array.from({length: gridSize}, (_, i) => startYear + i);
    },
    datePickerPreviousYearGrid() {
        // Move grid 20 years back
        const gridSize = 20;
        const newCenter = this.datePickerYearGrid[0] - Math.floor(gridSize / 2);
        this.datePickerGenerateYearGrid(newCenter);
    },
    datePickerNextYearGrid() {
        // Move grid 20 years forward
        const gridSize = 20;
        const newCenter = this.datePickerYearGrid[this.datePickerYearGrid.length - 1] + Math.ceil(gridSize / 2);
        this.datePickerGenerateYearGrid(newCenter);
    },
    datePickerSelectYear(year) {
        this.datePickerYear = year;
        this.datePickerShowYearGrid = false;
        this.datePickerCalculateDays();
    },
    datePickerCalculateDays() {
        let daysInMonth = new Date(this.datePickerYear, this.datePickerMonth + 1, 0).getDate();
        let dayOfWeek = new Date(this.datePickerYear, this.datePickerMonth).getDay();
        let blankdaysArray = [];
        for (var i = 1; i <= dayOfWeek; i++) {
            blankdaysArray.push(i);
        }
        let daysArray = [];
        for (var i = 1; i <= daysInMonth; i++) {
            daysArray.push(i);
        }
        this.datePickerBlankDaysInMonth = blankdaysArray;
        this.datePickerDaysInMonth = daysArray;
    },
    datePickerFormatDate(date) {
        let formattedDay = this.datePickerDays[date.getDay()];
        let formattedDate = ('0' + date.getDate()).slice(-2);
        let formattedMonth = this.datePickerMonthNames[date.getMonth()];
        let formattedMonthShortName = this.datePickerMonthNames[date.getMonth()].substring(0, 3);
        let formattedMonthInNumber = ('0' + (parseInt(date.getMonth()) + 1)).slice(-2);
        let formattedYear = date.getFullYear();

        if (this.datePickerFormat === 'YYYY-MM-dd') {
            return `${formattedYear}-${formattedMonthInNumber}-${formattedDate}`;
        }

        return `${formattedMonth} ${formattedDate}, ${formattedYear}`;
    },
    // Year navigation
    datePickerPreviousYear() {
        this.datePickerYear--;
        this.datePickerCalculateDays();
    },
    datePickerNextYear() {
        this.datePickerYear++;
        this.datePickerCalculateDays();
    },
    datePickerOpen: false,
    datePickerResetToDefault() {
        const parsedDate = parseDateString(this.datePickerValue);
        
        if (parsedDate) {
            this.datePickerYear = parsedDate.year;
            this.datePickerMonth = parsedDate.month;
            this.datePickerDay = parsedDate.day;
        } else {
            // Fallback to current date if value is missing or doesn't match expected format
            const currentDate = new Date();
            this.datePickerMonth = currentDate.getMonth();
            this.datePickerYear = currentDate.getFullYear();
            this.datePickerDay = currentDate.getDate();
        }
        this.datePickerShowYearGrid = false;
        this.datePickerCalculateDays();
    },
    init() {
        this.datePickerResetToDefault();
        this.datePickerValue = this.datePickerFormatDate(new Date(this.datePickerYear, this.datePickerMonth, (this.datePickerDay || 1)));
        console.log(this.datePickerDay)
        console.log(this.datePickerValue)
        if (typeof this.$watch === 'function') {
            this.$watch('datePickerOpen', (value) => {
                if (value === false) {
                    this.datePickerResetToDefault();
                }
            });
        }
    },
});