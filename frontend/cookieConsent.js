// Cookie Consent Management
export default function cookieConsent() {
  return {
    cookieConsent_modalOpen: false,
    init() {
      console.log("Cookie Consent component initialized");
      // Check if consent has been given
      const consent = localStorage.getItem('cookieConsent');
      if (consent === null) {
        // Show modal if no consent is recorded
        this.cookieConsent_modalOpen = true;
      }
    }
  };
}