/**
 * Teleport Datepicker Alpine.js component for use in scrollable containers
 * Extends the base datepicker with teleport positioning and scroll handling
 */
import datePickerComponent from './datepicker.js';

export default (initialValue = '', uniqueId = '') => ({
    // Inherit all base datepicker functionality
    ...datePickerComponent(initialValue),
    
    // Teleport-specific properties
    teleportId: uniqueId,
    scrollContainer: null,
    
    // Override the open state to handle positioning
    datePickerOpen: false,
    
    /**
     * Opens the date picker and positions it correctly relative to the input
     */
    openDatePicker() {
        this.datePickerOpen = true;
        
        // Wait for DOM update before positioning
        this.$nextTick(() => {
            this.updatePopupPosition();
            this.attachScrollListeners();
        });
    },
    
    /**
     * Closes the date picker and cleans up event listeners
     */
    closeDatePicker() {
        this.datePickerOpen = false;
        this.detachScrollListeners();
    },
    
    /**
     * Updates the popup position based on the input field location
     */
    updatePopupPosition() {
        const input = this.$refs.datePickerInput;
        const popup = document.querySelector(`#datepicker-portal [data-teleport-id="${this.teleportId}"]`);
        
        if (!input || !popup) return;
        
        const inputRect = input.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        // Calculate preferred position (below input)
        let top = inputRect.bottom + window.scrollY + 8;
        let left = inputRect.left + window.scrollX;
        
        // Get popup dimensions (temporarily show it to measure)
        popup.style.visibility = 'hidden';
        popup.style.display = 'block';
        const popupRect = popup.getBoundingClientRect();
        
        // Adjust horizontal position if popup would go off-screen
        if (left + popupRect.width > viewportWidth) {
            left = viewportWidth - popupRect.width - 16; // 16px margin from edge
        }
        if (left < 16) {
            left = 16; // 16px margin from left edge
        }
        
        // Adjust vertical position if popup would go off-screen
        if (inputRect.bottom + popupRect.height + 8 > viewportHeight) {
            // Show above input instead
            top = inputRect.top + window.scrollY - popupRect.height - 8;
        }
        
        // Apply position
        popup.style.top = top + 'px';
        popup.style.left = left + 'px';
        popup.style.visibility = 'visible';
        popup.style.display = 'block';
    },
    
    /**
     * Attaches scroll event listeners to update position when container scrolls
     */
    attachScrollListeners() {
        // Find the scrollable table container 
        this.scrollContainer = this.$el.closest('.overflow-auto, .overflow-y-auto, .overflow-x-auto');
        
        if (this.scrollContainer) {
            this.scrollContainer.addEventListener('scroll', this.handleScroll);
        }
        
        // Also listen to window scroll and resize
        window.addEventListener('scroll', this.handleScroll);
        window.addEventListener('resize', this.handleResize);
    },
    
    /**
     * Removes scroll event listeners
     */
    detachScrollListeners() {
        if (this.scrollContainer) {
            this.scrollContainer.removeEventListener('scroll', this.handleScroll);
        }
        
        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
    },
    
    /**
     * Handles scroll events - throttled for performance
     */
    handleScroll: null, // Will be set in init()
    
    /**
     * Handles resize events - debounced for performance  
     */
    handleResize: null, // Will be set in init()
    
    /**
     * Checks if the input is still visible and closes picker if not
     */
    checkVisibility() {
        const input = this.$refs.datePickerInput;
        if (!input) return;
        
        const inputRect = input.getBoundingClientRect();
        const isVisible = inputRect.top >= 0 && 
                         inputRect.left >= 0 && 
                         inputRect.bottom <= window.innerHeight && 
                         inputRect.right <= window.innerWidth;
        
        if (!isVisible && this.datePickerOpen) {
            this.closeDatePicker();
        }
    },
    
    /**
     * Throttle utility function
     */
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        return function (...args) {
            const currentTime = Date.now();
            
            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    },
    
    /**
     * Debounce utility function
     */
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },
    
    /**
     * Override the datePickerDayClicked to handle teleport-specific logic
     */
    datePickerDayClicked(day) {
        // Call parent implementation
        let selectedDate = new Date(this.datePickerYear, this.datePickerMonth, day);
        this.datePickerDay = day;
        this.datePickerValue = this.datePickerFormatDate(selectedDate);
        this.datePickerIsSelectedDate(day);
        this.datePickerShowYearGrid = false;
        
        // Close with cleanup
        this.closeDatePicker();
        
        // Trigger change event for HTMX
        if (this.onDateChange) {
            this.onDateChange();
        }
    },
    
    /**
     * Initialize teleport-specific functionality
     */
    init() {
        // Call parent init
        this.datePickerResetToDefault();
        this.datePickerValue = this.datePickerFormatDate(new Date(this.datePickerYear, this.datePickerMonth, (this.datePickerDay || 1)));
        
        // Set up throttled/debounced handlers
        this.handleScroll = this.throttle(() => {
            if (this.datePickerOpen) {
                this.updatePopupPosition();
                this.checkVisibility();
            }
        }, 16); // ~60fps
        
        this.handleResize = this.debounce(() => {
            if (this.datePickerOpen) {
                this.updatePopupPosition();
            }
        }, 250);
        
        // Watch for open state changes
        if (typeof this.$watch === 'function') {
            this.$watch('datePickerOpen', (value) => {
                if (value === false) {
                    this.datePickerResetToDefault();
                    this.detachScrollListeners();
                } else {
                    this.updatePopupPosition();
                    this.attachScrollListeners();
                }
            });
        }
        
        // Cleanup on component destroy
        this.$el.addEventListener('destroy', () => {
            this.detachScrollListeners();
        });
    }
});