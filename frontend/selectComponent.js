// Define a directive for the select component's x-data
export default (selectableItems = [], defaultValue = "") => ({
  selectOpen: false,
  selectedItem: defaultValue,
  selectableItems: selectableItems,
  selectableItemActive: null,
  selectKeydownValue: "",
  selectKeydownTimeout: 1000,
  selectKeydownClearTimeout: null,
  selectDropdownPosition: "bottom",
  init() {
    // Generate a unique select identifier and apply default selection
    this.selectId = this.$id("select");
    // If defaultValue is a string value, map it to the corresponding item object
    if (typeof this.selectedItem === "string" && this.selectedItem) {
      const defaultItem = this.selectableItemsMap[this.selectedItem];
      if (defaultItem) {
        this.selectedItem = defaultItem;
      }
    }

    // Watch for external changes to the hidden input (for XModel binding)
    this.$watch('$refs.hiddenInput.value', (value) => {
      if (value && value !== this.selectedItemValue) {
        const item = this.selectableItemsMap[value];
        if (item) {
          this.selectedItem = item;
        }
      }
    });

    // Add resize handler to close dropdown on window resize
    this.resizeHandler = () => {
      if (this.selectOpen) {
        this.selectOpen = false;
      }
    };
    window.addEventListener("resize", this.resizeHandler);
  },

  get selectableItemsMap() {
    return this.selectableItems.reduce((map, item) => {
      map[item.value] = item;
      return map;
    }, {});
  },

  get selectedItemValue() {
    return this.selectedItem ? this.selectedItem.value : "";
  },

  get selectedItemTitle() {
    return this.selectedItem ? this.selectedItem.title : "";
  },

  selectItem(item) {
    this.selectedItem = item;
    this.selectOpen = false;

    // Update the hidden input value first
    this.$refs.hiddenInput.value = item.value;

    // Dispatch standard events that the validation system can handle
    const inputEvent = new Event("input", { bubbles: true });
    this.$refs.hiddenInput.dispatchEvent(inputEvent);
    
    const changeEvent = new Event("change", { bubbles: true });
    this.$refs.hiddenInput.dispatchEvent(changeEvent);
  },

  selectPositionUpdate() {
    const selectDropdownBottomPos =
      this.$refs.selectButton.getBoundingClientRect().top +
      this.$refs.selectButton.offsetHeight +
      parseInt(
        window.getComputedStyle(this.$refs.selectableItemsList).maxHeight,
      );
    this.selectDropdownPosition =
      window.innerHeight < selectDropdownBottomPos ? "top" : "bottom";
  },

  // Cleanup method
  destroy() {
    if (this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }
  },
  selectableItemIsActive(item) {
    return (
      this.selectableItemActive &&
      this.selectableItemActive.value === item.value
    );
  },
  selectableItemActiveNext() {
    let index = this.selectableItems.indexOf(this.selectableItemActive);
    if (index < this.selectableItems.length - 1) {
      this.selectableItemActive = this.selectableItems[index + 1];
      this.selectScrollToActiveItem();
    }
  },
  selectableItemActivePrevious() {
    let index = this.selectableItems.indexOf(this.selectableItemActive);
    if (index > 0) {
      this.selectableItemActive = this.selectableItems[index - 1];
      this.selectScrollToActiveItem();
    }
  },
  selectScrollToActiveItem() {
    if (this.selectableItemActive) {
      const activeElement = document.getElementById(
        `${this.selectableItemActive.value}-${this.selectId}`,
      );
      const newScrollPos =
        activeElement.offsetTop +
        activeElement.offsetHeight -
        this.$refs.selectableItemsList.offsetHeight;
      this.$refs.selectableItemsList.scrollTop =
        newScrollPos > 0 ? newScrollPos : 0;
    }
  },
  selectKeydown(event) {
    if (event.keyCode >= 65 && event.keyCode <= 90) {
      this.selectKeydownValue += event.key;
      const selectedItemBestMatch = this.selectItemsFindBestMatch();
      if (selectedItemBestMatch) {
        if (this.selectOpen) {
          this.selectableItemActive = selectedItemBestMatch;
          this.selectScrollToActiveItem();
        } else {
          this.selectedItem = this.selectableItemActive = selectedItemBestMatch;
        }
      }

      if (this.selectKeydownValue !== "") {
        clearTimeout(this.selectKeydownClearTimeout);
        this.selectKeydownClearTimeout = setTimeout(() => {
          this.selectKeydownValue = "";
        }, this.selectKeydownTimeout);
      }
    }
  },
  selectItemsFindBestMatch() {
    const typedValue = this.selectKeydownValue.toLowerCase();
    let bestMatch = null;
    let bestMatchIndex = -1;
    for (let i = 0; i < this.selectableItems.length; i++) {
      const title = this.selectableItems[i].title.toLowerCase();
      const index = title.indexOf(typedValue);
      if (
        index > -1 &&
        (bestMatchIndex === -1 || index < bestMatchIndex) &&
        !this.selectableItems[i].disabled
      ) {
        bestMatch = this.selectableItems[i];
        bestMatchIndex = index;
      }
    }
    return bestMatch;
  },
});
