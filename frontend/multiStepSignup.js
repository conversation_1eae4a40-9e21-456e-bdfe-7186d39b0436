import multiStepForm from "./multiStepForm.js";

// Preconfigured multiStepSignup Alpine.js directive for legacy compatibility
export default function multiStepSignup() {
  // Get the default language from the page's language setting
  const defaultLang = document.documentElement.lang || 'en';
  
  // Get localized text from data attributes on the form
  const form = document.querySelector('form[action="/signup"]');
  const getLocale = (key) => {
    const value = form?.dataset[key];
    if (!value) {
      throw new Error(`Missing localization data attribute: data-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`);
    }
    return value;
  };
  
  return multiStepForm({
    steps: [
      { title: getLocale('stepTitle1'), fields: ['name', 'email', 'lang'] },
      { title: getLocale('stepTitle2'), fields: ['country', 'phone', 'birthday'] },
      { title: getLocale('stepTitle3'), fields: ['password', 'confirmPassword'] },
      { title: getLocale('stepTitle4'), fields: [] },
    ],
    fields: [
      'name',
      'email',
      'lang',
      'country',
      'phone',
      'birthday',
      'password',
      'confirmPassword',
      'isEmailAvailable',
      'emailValidationState',
    ],
    initialData: {
      'lang': defaultLang,
    },
  fieldMapping: { confirmPassword: 'confirm-password',
           password: 'password' },
    validate: [
      // Step 1 validation
      (formData) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return (
          formData.name.trim() !== '' &&
          formData.email.trim() !== '' &&
          emailRegex.test(formData.email) &&
          formData.emailValidationState === 'available'
        );
      },
      // Step 2 validation
      (formData) => {
        const countryValue = typeof formData.country === 'string' 
          ? formData.country 
          : (formData.country && formData.country.value) || '';
        // Only validate country for now, phone and birthday are optional in basic validation
        return countryValue.trim() !== '';
      },
      // Step 3 validation
      (formData) =>
        formData.password.length >= 8 &&
        formData['confirm-password'] === formData.password,
      // Step 4 (review)
      () => true,
    ],
    focusSelectors: [
      '[name="name"]',
      '[name="country"]',
      '[name="password"]',
      null,
    ],
    locales: {
      step_of: getLocale('stepOf'),
    },
    submitOnEnter: true,
    pollingInterval: 200,
    enablePolling: true,
    hooks: {
      afterFieldUpdate: (field, value, component) => {
        if (field === 'email') {
          const validationDiv = document.getElementById('email-validation');
          if (validationDiv) {
            const span = validationDiv.querySelector('span');
            if (span && span.classList.contains('text-green-600')) {
              component.formData.emailValidationState = 'available';
            } else {
              component.formData.emailValidationState = 'unavailable';
            }
          }
        }
      }
    },
  });
}
