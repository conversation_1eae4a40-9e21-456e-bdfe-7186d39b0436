/**
 * Documentation for x-validate-form Directive
 * =========================================
 *
 * Overview
 * --------
 * The `x-validate-form` directive is a custom Alpine.js directive designed to provide robust form input validation using the browser's Constraint Validation API. It enables real-time validation of form fields as users type or change input values, displaying appropriate error messages for various validation states such as required fields, minimum/maximum length, pattern mismatches, and type mismatches (e.g., invalid email). Additionally, it supports validation of related fields, such as ensuring two fields match (e.g., password and confirmation).
 *
 * Usage
 * -----
 * To use the `x-validate-form` directive, apply it to a `<form>` element in your HTML. The directive will automatically detect and validate all `<input>` elements within the form that have a `data-field-name` attribute. This attribute is required to uniquely identify each field for tracking and validation purposes.
 *
 * Supported Validation Attributes
 * -------------------------------
 * The directive leverages standard HTML5 validation attributes along with custom data attributes for enhanced control.
 * Standard HTML5 attributes (like `required`, `minlength`, `pattern`, `type="email"`, `min`, `max`) are validated
 * using the browser's Constraint Validation API on the input element itself.
 *
 * Custom `data-validate-*` attributes are provided for more control, especially with custom components
 * where the validated value comes from Alpine's scope (`scope.fields[fieldName]`) rather than the input's direct value.
 * These custom attributes are checked before falling back to standard HTML5 validation on the input element.
 *
 * - `id`: A unique identifier for the field (required for validation tracking).
 *
 * ### Standard HTML5 Attributes (validated on the input element):
 * - `required`: Marks the field as required. Uses `data-required-message` for custom error.
 * - `minlength`: Specifies the minimum number of characters.
 * - `maxlength`: Specifies the maximum number of characters.
 * - `pattern`: Defines a regex pattern. Uses `data-pattern-message` for custom error.
 * - `type="email"`: Validates email format.
 * - `min`, `max`: Sets min/max for numeric/date inputs.
 *
 * ### Custom `data-validate-*` Attributes (validated on `scope.fields[fieldName]`):
 * - `data-validate-required="true"`: Checks if the field's value in Alpine scope is non-empty.
 *   - Message: `data-required-message` (e.g., "This selection is required.")
 * - `data-validate-minlength="<number>"`: Checks min length of the field's value in Alpine scope.
 *   - Message: `data-minlength-message` (e.g., "Must have at least 5 characters.")
 * - `data-validate-maxlength="<number>"`: Checks max length of the field's value in Alpine scope.
 *   - Message: `data-maxlength-message` (e.g., "Cannot exceed 20 characters.")
 * - `data-validate-pattern="<regex>"`: Matches the field's value in Alpine scope against a regex.
 *   - Message: `data-pattern-message` (e.g., "Invalid format provided.")
 * - `data-validate-minvalue="<number>"`: For numeric values in Alpine scope, checks minimum value.
 *   - Message: `data-minvalue-message` (e.g., "Value must be 10 or more.")
 * - `data-validate-maxvalue="<number>"`: For numeric values in Alpine scope, checks maximum value.
 *   - Message: `data-maxvalue-message` (e.g., "Value must be 100 or less.")
 *
 * ### Related Field Validation:
 * - `data-validate-with="<otherFieldId>"`: Specifies another field's `id` to validate against.
 * - `data-validate-type="match"`: Defines the type of related validation (default is 'match').
 * - `data-related-error-message`: Custom error message for related field validation failure.
 *
 * ### Advanced Custom Validation:
 * - `data-custom-validator="<functionName>"`: Name of a JS function (in Alpine scope or global) for complex validation.
 *   Receives `(value, inputElement, alpineScope)`. Returns error string or `true`/`null`.
 * - `data-custom-error-message`: Generic message used by `data-custom-validator` if it returns `false`.
 *
 * Group Validation
 * ----------------
 * The directive also supports validation of groups of inputs, such as checkboxes, using attributes
 * that can be placed on a parent element (like a fieldset, div, or ul) within the form:
 *
 * - `data-group-name`: Identifies inputs that belong to the same logical group
 * - `data-group-min`: Minimum number of items that must be selected within the group
 * - `data-group-max`: Maximum number of items that can be selected within the group
 * - `data-group-even`: When set to "true", requires an even number of selections
 * - `data-group-odd`: When set to "true", requires an odd number of selections
 * - `data-group-error-message`: Custom error message for group validation failures
 *
 * Example: Validating a checklist of players requiring even number selection with rules defined on the container
 * ```html
 * <form x-validate-form x-data="{ formValidation: { fields: {}, errors: {}, errorMessages: {}, groupErrors: {} } }">
 *   <div>
 *     <h3>Select Players</h3>
 *     <ul data-group-name="players" data-group-even="true">
 *       <li>
 *         <input type="checkbox" id="player1" name="playerIds[]" value="1">
 *         <label for="player1">Player 1</label>
 *       </li>
 *       <li>
 *         <input type="checkbox" id="player2" name="playerIds[]" value="2">
 *         <label for="player2">Player 2</label>
 *       </li>
 *       <!-- more players... -->
 *     </ul>
 *     <div id="players-error" x-show="formValidation.groupErrors.players" 
 *          x-text="formValidation.groupErrors.players" class="text-red-600"></div>
 *   </div>
 *   <button type="submit">Create Teams</button>
 * </form>
 * ```
 *
 * Integration with Alpine.js
 * --------------------------
 * Ensure that the `x-validate-form` directive is registered with Alpine.js before use. This can be done by importing this module in your main JavaScript file and registering it as follows:
 *
 * ```javascript
 * import Alpine from 'alpinejs';
 * import validateForm from './validation.js';
 *
 * Alpine.directive('validate-form', validateForm);
 *
 * window.Alpine = Alpine;
 * Alpine.start();
 * ```
 *
 * Examples
 * --------
 * Below are several examples demonstrating how to use the `x-validate-form` directive in different scenarios. These examples assume Tailwind CSS for styling, but the validation functionality is independent of the CSS framework.
 *
 * Example 1: Basic Form with Required Fields
 * ------------------------------------------
 * This example shows a simple form with required fields for name and email.
 *
 * ```html
 * <form x-validate-form class="space-y-4">
 *   <div>
 *     <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
 *     <input id="name" type="text" required
 *            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
 *     <p x-show="formValidation.errors.name" x-text="formValidation.errorMessages.name" class="mt-1 text-sm text-red-600"></p>
 *   </div>
 *   <div>
 *     <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
 *     <input id="email" type="email" required
 *            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
 *     <p x-show="formValidation.errors.email" x-text="formValidation.errorMessages.email" class="mt-1 text-sm text-red-600"></p>
 *   </div>
 *   <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Submit</button>
 * </form>
 * ```
 * In this example, both fields are marked as `required`, and the email input must match the email format. Error messages are displayed below each input field when validation fails, using Alpine.js to reactively show/hide the error text based on the validation state.
 *
 * Example 2: Form with Length and Pattern Validation
 * -------------------------------------------------
 * This example includes validation for a username with specific length constraints and a phone number with a pattern.
 *
 * ```html
 * <form x-validate-form class="space-y-4">
 *   <div>
 *     <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
 *     <input id="username" type="text" required minlength="3" maxlength="20"
 *            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
 *     <p x-show="formValidation.errors.username" x-text="formValidation.errorMessages.username" class="mt-1 text-sm text-red-600"></p>
 *   </div>
 *   <div>
 *     <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
 *     <input id="phone" type="tel" pattern="[0-9]{10}" data-pattern-message="Please enter a valid 10-digit phone number."
 *            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
 *     <p x-show="formValidation.errors.phone" x-text="formValidation.errorMessages.phone" class="mt-1 text-sm text-red-600"></p>
 *   </div>
 *   <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Submit</button>
 * </form>
 * ```
 * Here, the username must be between 3 and 20 characters, and the phone number must match a 10-digit pattern. Error messages are displayed below the fields using Alpine.js bindings to react to validation errors dynamically.
 *
 * Example 3: Form with Related Field Validation (Password Confirmation)
 * ---------------------------------------------------------------------
 * This example demonstrates validating that two fields match, such as a password and its confirmation.
 *
 * ```html
 * <form x-validate-form class="space-y-4">
 *   <div>
 *     <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
 *     <input id="password" type="password" required minlength="8"
 *            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
 *     <p x-show="formValidation.errors.password" x-text="formValidation.errorMessages.password" class="mt-1 text-sm text-red-600"></p>
 *   </div>
 *   <div>
 *     <label for="confirmPassword" class="block text-sm font-medium text-gray-700">Confirm Password</label>
 *     <input id="confirmPassword" type="password" required data-validate-with="password" data-related-error-message="Passwords do not match."
 *            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
 *     <p x-show="formValidation.errors.confirmPassword" x-text="formValidation.errorMessages.confirmPassword" class="mt-1 text-sm text-red-600"></p>
 *   </div>
 *   <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Submit</button>
 * </form>
 * ```
 * In this form, the `confirmPassword` field is linked to the `password` field via `data-validate-with="password"`. If the values do not match, an error message "Passwords do not match." will be displayed below the confirmation field using Alpine.js to reactively show the error.
 *
 * Notes
 * -----
 * - The directive initializes validation on page load but does not report errors immediately to avoid overwhelming users with error messages before they interact with the form. However, error states are tracked and can be displayed in the UI using Alpine.js bindings.
 * - Real-time validation occurs as users type (via `input` event) and on focus loss (via `change` event), providing immediate feedback.
 * - Ensure that each input within the form has a unique `id` value to prevent validation conflicts.
 * - The directive exposes validation states and error messages via the `formValidation` object in the Alpine.js scope, allowing reactive UI updates for error display.
 * - The directive automatically cleans up event listeners when the form element is removed from the DOM, preventing memory leaks.
 *
 * By using the `x-validate-form` directive, you can create user-friendly forms with robust validation, enhancing the overall user experience in your application.
 */

// Form input validation directive for Alpine.js using Constraint Validation API
export default function FormValidation(
  el,
  { value, modifiers, expression },
  { Alpine, effect, cleanup, evaluate },
) {
  // Evaluate the scope to access fields, errors, and errorMessages from x-data
  const scope = Alpine.$data(el);
  
  // Initialize the form by collecting all fields
  // Remove validation on initialization to avoid setting error states prematurely
  const init = () => {
    // Initialize groupErrors object if not already present
    if (!scope.groupErrors) {
      scope.groupErrors = {};
    }

    const inputs = el.querySelectorAll("input[id], textarea[id]");
    inputs.forEach((input) => {
      const fieldName = input.getAttribute("id");
      if (fieldName) {
        scope.fields[fieldName] = input.type === "checkbox" ? input.checked : (input.value || "");
        scope.errors[fieldName] = false;
        scope.errorMessages[fieldName] = "";

        // Bind input event for real-time validation feedback
        const inputHandler = (e) => {
          if (input.type === "checkbox") {
            scope.fields[fieldName] = e.target.checked;
          } else {
            scope.fields[fieldName] = e.target.value || e.detail.value;
          }
          validateField(fieldName, input);
          
          // Find any parent element with a group name for this checkbox
          if (input.type === "checkbox") {
            let parentElement = input.parentElement;
            while (parentElement && parentElement !== el) {
              const groupName = parentElement.getAttribute("data-group-name");
              if (groupName) {
                validateGroup(groupName, parentElement);
                break;
              }
              parentElement = parentElement.parentElement;
            }
          }
        };
        input.addEventListener("input", inputHandler);

        // Bind change event for additional validation on focus loss
        const changeHandler = (e) => {
          if (input.type === "checkbox") {
            scope.fields[fieldName] = e.target.checked;
          } else {
            scope.fields[fieldName] = e.target.value;
          }
          validateField(fieldName, input);
          
          // Find any parent element with a group name for this checkbox
          if (input.type === "checkbox") {
            let parentElement = input.parentElement;
            while (parentElement && parentElement !== el) {
              const groupName = parentElement.getAttribute("data-group-name");
              if (groupName) {
                validateGroup(groupName, parentElement);
                break;
              }
              parentElement = parentElement.parentElement;
            }
          }
        };
        input.addEventListener("change", changeHandler);

        // Add blur event specifically for related field validation
        const blurHandler = (e) => {
          validateRelatedFields(fieldName, input);
          
          // Find any parent element with a group name for this checkbox
          if (input.type === "checkbox") {
            let parentElement = input.parentElement;
            while (parentElement && parentElement !== el) {
              const groupName = parentElement.getAttribute("data-group-name");
              if (groupName) {
                validateGroup(groupName, parentElement);
                break;
              }
              parentElement = parentElement.parentElement;
            }
          }
        };
        input.addEventListener("blur", blurHandler);

        // Store handlers for cleanup
        input._validateFormHandlers = { 
          inputHandler, 
          changeHandler, 
          blurHandler 
        };
      }
    });
    
    // Initialize validation for input groups
    initializeGroups();
  };
  
  // Find all unique input groups and set up initial validation
  const initializeGroups = () => {
    // Look for group containers within the form element
    const groupContainers = el.querySelectorAll("[data-group-name]");
    
    // Initialize group error states and validate each group
    groupContainers.forEach(container => {
      const groupName = container.getAttribute("data-group-name");
      scope.groupErrors[groupName] = "";
      validateGroup(groupName, container);
    });
  };
  
  // Validate input groups (like checkboxes)
  const validateGroup = (groupName, groupContainer) => {
    // Clear existing group error
    scope.groupErrors[groupName] = "";
    
    // Extract validation rules from the container
    const validationRules = {
      minSelections: parseInt(groupContainer.getAttribute("data-group-min"), 10),
      maxSelections: parseInt(groupContainer.getAttribute("data-group-max"), 10),
      evenRequired: groupContainer.getAttribute("data-group-even") === "true",
      oddRequired: groupContainer.getAttribute("data-group-odd") === "true",
      errorMessage: groupContainer.getAttribute("data-group-error-message")
    };
    
    // Find all checkbox inputs within the container
    const groupInputs = Array.from(groupContainer.querySelectorAll('input[type="checkbox"]'));
    
    if (!groupInputs.length) return;
    
    // Count selected inputs
    const selectedCount = groupInputs.filter(input => input.checked).length;
    
    // Check min selections
    if (!isNaN(validationRules.minSelections) && selectedCount < validationRules.minSelections) {
      const message = validationRules.errorMessage || 
                     `Please select at least ${validationRules.minSelections} option${validationRules.minSelections !== 1 ? 's' : ''}.`;
      scope.groupErrors[groupName] = message;
      return;
    }
    
    // Check max selections
    if (!isNaN(validationRules.maxSelections) && selectedCount > validationRules.maxSelections) {
      const message = validationRules.errorMessage || 
                     `Please select no more than ${validationRules.maxSelections} option${validationRules.maxSelections !== 1 ? 's' : ''}.`;
      scope.groupErrors[groupName] = message;
      return;
    }
    
    // Check if even number required
    if (validationRules.evenRequired && selectedCount % 2 !== 0) {
      const message = validationRules.errorMessage || 
                     "Please select an even number of options.";
      scope.groupErrors[groupName] = message;
      return;
    }
    
    // Check if odd number required
    if (validationRules.oddRequired && selectedCount % 2 === 0 && selectedCount > 0) {
      const message = validationRules.errorMessage || 
                     "Please select an odd number of options.";
      scope.groupErrors[groupName] = message;
      return;
    }
  };

  const validateField = (fieldName, input, report = true) => {
    if (input.setCustomValidity) input.setCustomValidity("");
    scope.errors[fieldName] = false;
    scope.errorMessages[fieldName] = "";

    const fieldValue = scope.fields[fieldName]; // Value from Alpine scope

    // --- Priority-based validation with early return ---
    // Each validation check returns immediately upon finding the first error
    
    // Priority 1: Required field validation (highest priority)
    if (input.getAttribute("data-validate-required") === "true") {
        if (fieldValue === null || fieldValue === undefined || String(fieldValue).trim() === "") {
            const errorMessage = input.getAttribute("data-required-message") || "This field is required.";
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 required check
    if (typeof input.checkValidity === 'function' && input.hasAttribute('required')) {
        if (!input.checkValidity() && input.validity.valueMissing) {
            const errorMessage = input.getAttribute("data-required-message") || "This field is required.";
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }

    // Priority 2: Format/pattern validation
    if (input.hasAttribute("data-validate-pattern")) {
        const patternString = input.getAttribute("data-validate-pattern");
        try {
            const pattern = new RegExp(patternString);
            if (!pattern.test(String(fieldValue || ""))) {
                const errorMessage = input.getAttribute("data-pattern-message") || "Invalid format.";
                scope.errors[fieldName] = true;
                scope.errorMessages[fieldName] = errorMessage;
                if (input.setCustomValidity) input.setCustomValidity(errorMessage);
                return; // Early return - stop at first error
            }
        } catch (e) {
            console.error(`Invalid regex for data-validate-pattern on field '${fieldName}': ${patternString}`, e);
            const errorMessage = "Invalid validation pattern configured.";
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 pattern check
    if (typeof input.checkValidity === 'function' && input.hasAttribute('pattern')) {
        if (!input.checkValidity() && input.validity.patternMismatch) {
            const errorMessage = input.getAttribute("data-pattern-message") || "Invalid format.";
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 type mismatch (e.g., invalid email)
    if (typeof input.checkValidity === 'function') {
        if (!input.checkValidity() && input.validity.typeMismatch) {
            let errorMessage = "Please enter a valid value for the type.";
            if (input.type === "email") errorMessage = "Please enter a valid email address.";
            else if (input.type === "number") errorMessage = "Please enter a valid number.";
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }

    // Priority 3: Length validation (min/max)
    if (input.hasAttribute("data-validate-minlength")) {
        const minLength = parseInt(input.getAttribute("data-validate-minlength"), 10);
        if (!isNaN(minLength) && String(fieldValue || "").length < minLength) {
            const errorMessage = input.getAttribute("data-minlength-message") || `Must be at least ${minLength} characters.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 min length check
    if (typeof input.checkValidity === 'function' && input.hasAttribute('minlength')) {
        if (!input.checkValidity() && input.validity.tooShort) {
            const errorMessage = input.getAttribute("data-minlength-message") || `Must be at least ${input.minLength} characters.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }

    if (input.hasAttribute("data-validate-maxlength")) {
        const maxLength = parseInt(input.getAttribute("data-validate-maxlength"), 10);
        if (!isNaN(maxLength) && String(fieldValue || "").length > maxLength) {
            const errorMessage = input.getAttribute("data-maxlength-message") || `Must not exceed ${maxLength} characters.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 max length check
    if (typeof input.checkValidity === 'function' && input.hasAttribute('maxlength')) {
        if (!input.checkValidity() && input.validity.tooLong) {
            const errorMessage = input.getAttribute("data-maxlength-message") || `Must not exceed ${input.maxLength} characters.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }

    // Priority 4: Range validation (for numbers)
    if (input.hasAttribute("data-validate-minvalue")) {
        const minValue = parseFloat(input.getAttribute("data-validate-minvalue"));
        const numValue = parseFloat(fieldValue);
        if (!isNaN(minValue) && (isNaN(numValue) || numValue < minValue)) {
            const errorMessage = input.getAttribute("data-minvalue-message") || `Value must be at least ${minValue}.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 min value check
    if (typeof input.checkValidity === 'function' && input.hasAttribute('min')) {
        if (!input.checkValidity() && input.validity.rangeUnderflow) {
            const errorMessage = input.getAttribute("data-minvalue-message") || `Must be at least ${input.min}.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }

    if (input.hasAttribute("data-validate-maxvalue")) {
        const maxValue = parseFloat(input.getAttribute("data-validate-maxvalue"));
        const numValue = parseFloat(fieldValue);
        if (!isNaN(maxValue) && (isNaN(numValue) || numValue > maxValue)) {
            const errorMessage = input.getAttribute("data-maxvalue-message") || `Value must not exceed ${maxValue}.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Standard HTML5 max value check
    if (typeof input.checkValidity === 'function' && input.hasAttribute('max')) {
        if (!input.checkValidity() && input.validity.rangeOverflow) {
            const errorMessage = input.getAttribute("data-maxvalue-message") || `Must not exceed ${input.max}.`;
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }
    
    // Priority 5: Custom validators (lowest priority)
    if (input.hasAttribute("data-custom-validator")) {
        const customValidatorName = input.getAttribute("data-custom-validator");
        let validatorFn;
        if (scope && typeof scope[customValidatorName] === 'function') {
            validatorFn = scope[customValidatorName];
        } else if (typeof window[customValidatorName] === 'function') {
            validatorFn = window[customValidatorName];
        }

        if (validatorFn) {
            try {
                const result = validatorFn(fieldValue, input, scope);
                if (typeof result === 'string' && result.length > 0) {
                    scope.errors[fieldName] = true;
                    scope.errorMessages[fieldName] = result;
                    if (input.setCustomValidity) input.setCustomValidity(result);
                    return; // Early return - stop at first error
                } else if (result === false) { // Explicit false means failure
                    const errorMessage = input.getAttribute("data-custom-error-message") || "Invalid input (custom validation).";
                    scope.errors[fieldName] = true;
                    scope.errorMessages[fieldName] = errorMessage;
                    if (input.setCustomValidity) input.setCustomValidity(errorMessage);
                    return; // Early return - stop at first error
                }
            } catch (e) {
                console.error(`Error executing custom validator '${customValidatorName}' for field '${fieldName}':`, e);
                const errorMessage = "Custom validation function encountered an error.";
                scope.errors[fieldName] = true;
                scope.errorMessages[fieldName] = errorMessage;
                if (input.setCustomValidity) input.setCustomValidity(errorMessage);
                return; // Early return - stop at first error
            }
        } else {
            console.warn(`Custom validator function '${customValidatorName}' not found for field '${fieldName}'.`);
        }
    }

    // Check for any remaining HTML5 validation errors
    if (typeof input.checkValidity === 'function') {
        if (!input.checkValidity()) {
            const errorMessage = input.validationMessage || "Invalid input.";
            scope.errors[fieldName] = true;
            scope.errorMessages[fieldName] = errorMessage;
            if (input.setCustomValidity) input.setCustomValidity(errorMessage);
            return; // Early return - stop at first error
        }
    }

    // All validation checks passed
    scope.errors[fieldName] = false;
    scope.errorMessages[fieldName] = "";
    if (input.setCustomValidity) input.setCustomValidity("");
  };

  const validateRelatedFields = (fieldName, input) => {
    // Check if this field has a validation dependency on another field
    const validateWith =
      input.getAttribute("data-validate-with") || input.dataset.validateWith;
    const validateType = input.getAttribute("data-validate-type") || "match";
    const relatedErrorMessage =
      input.getAttribute("data-related-error-message") ||
      input.dataset.relatedErrorMessage ||
      "Fields do not match.";

    if (validateWith && scope.fields[validateWith] !== undefined) {
      const relatedFieldName = fieldName;
      
      // Cross-field validation (Priority 6) - only if no higher priority error exists
      if (!scope.errors[relatedFieldName] &&
        validateType === "match" &&
        scope.fields[validateWith] !== scope.fields[relatedFieldName] &&
        scope.fields[relatedFieldName]
      ) {
        scope.errors[relatedFieldName] = true;
        scope.errorMessages[relatedFieldName] = relatedErrorMessage;
        input.setCustomValidity(relatedErrorMessage);
      } else if (scope.fields[validateWith] === scope.fields[relatedFieldName]) {
        // Clear custom validity if valid and no other errors
        if (!scope.errors[relatedFieldName]) {
          input.setCustomValidity("");
        }
        validateField(relatedFieldName, input, true);
      }
    }

    // Also validate any fields that depend on this field
    const dependentInputs = el.querySelectorAll(
      `input[data-validate-with="${fieldName}"], input[data-validate-with-id="${fieldName}"]`,
    );
    dependentInputs.forEach((dependentInput) => {
      const dependentFieldName = dependentInput.getAttribute("id");
      if (dependentFieldName && scope.fields[dependentFieldName] !== undefined) {
        const dependentValidateType =
          dependentInput.getAttribute("data-validate-type") ||
          dependentInput.dataset.validateType ||
          "match";
        const dependentErrorMessage =
          dependentInput.getAttribute("data-related-error-message") ||
          dependentInput.dataset.relatedErrorMessage ||
          "Fields do not match.";

        // Cross-field validation (Priority 6) - only if no higher priority error exists
        if (!scope.errors[dependentFieldName] &&
          dependentValidateType === "match" &&
          scope.fields[fieldName] !== scope.fields[dependentFieldName] &&
          scope.fields[dependentFieldName]
        ) {
          scope.errors[dependentFieldName] = true;
          scope.errorMessages[dependentFieldName] = dependentErrorMessage;
          dependentInput.setCustomValidity(dependentErrorMessage);
        } else {
          // Re-validate the dependent field to clear error if now matching
          validateField(dependentFieldName, dependentInput, true);
        }
      }
    });
  };

  // Initialize the form validation
  init();

  // Enhance htmx:beforeRequest to validate groups and fields
  const beforeRequestHandler = (e) => {
    // Check if the event's target is the form element
    if (e.target !== el) {
      return; // Return early if the request is not from the form element
    }

    // Validate all groups before the AJAX request
    const groupContainers = el.querySelectorAll("[data-group-name]");
    let isValid = true;

    groupContainers.forEach(container => {
      const groupName = container.getAttribute("data-group-name");
      validateGroup(groupName, container);
      if (scope.groupErrors[groupName]) {
        isValid = false;
      }
    });

    // Validate all individual inputs before the AJAX request
    const inputs = el.querySelectorAll("input[id], textarea[id]");
    inputs.forEach(input => {
      const fieldName = input.getAttribute("id");
      validateField(fieldName, input, false); // Validate without reporting
      if (scope.errors[fieldName]) {
        isValid = false;
      }
    });

    // If any validation failed, prevent the AJAX request
    if (!isValid) {
      console.error("AJAX request prevented due to validation errors.");
      e.preventDefault();
    }
  };

  el.addEventListener("htmx:beforeRequest", beforeRequestHandler);

  // Cleanup event listeners and scope when the directive is removed
  cleanup(() => {
    const inputs = el.querySelectorAll("input[id], textarea[id]");
    inputs.forEach((input) => {
      if (input._validateFormHandlers) {
        input.removeEventListener(
          "input",
          input._validateFormHandlers.inputHandler,
        );
        input.removeEventListener(
          "change",
          input._validateFormHandlers.changeHandler,
        );
        input.removeEventListener(
          "blur",
          input._validateFormHandlers.blurHandler,
        );
        delete input._validateFormHandlers;
      }
    });
    
    // Remove htmx:beforeRequest handler
    el.removeEventListener("htmx:beforeRequest", beforeRequestHandler);
  });
}
