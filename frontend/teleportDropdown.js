/**
 * Teleport Dropdown Alpine.js component for use in scrollable containers
 * Extends the base dropdown with teleport positioning and scroll handling
 */
import dropdownComponent from './dropdown.js';

export default (position = 'bottom', uniqueId = '') => ({
    // Inherit all base dropdown functionality
    ...dropdownComponent(position),

    // Teleport-specific properties
    teleportId: uniqueId,
    scrollContainer: null,

    // Override the open state to handle positioning
    isOpen: false,

    /**
     * Opens the dropdown and positions it correctly relative to the button
     */
    openDropdown() {
        this.isOpen = true;

        // Wait for DOM update before positioning
        this.$nextTick(() => {
            this.updateMenuPosition();
            this.attachScrollListeners();
        });
    },

    /**
     * Closes the dropdown and cleans up event listeners
     */
    closeDropdown() {
        this.isOpen = false;
        this.detachScrollListeners();
    },

    /**
     * Updates the dropdown position based on the button location
     */
    updateMenuPosition() {
        const button = this.$refs.button;
        const menu = document.querySelector(`#dropdown-portal [data-teleport-id="${this.teleportId}"]`);

        if (!button || !menu) return;

        const buttonRect = button.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // Calculate preferred position based on position prop
        let top, left;
        const spacing = 8; // 8px gap like datepicker

        // Get menu dimensions (temporarily show it to measure)
        menu.style.visibility = 'hidden';
        menu.style.display = 'block';
        const menuRect = menu.getBoundingClientRect();

        // Calculate initial position based on preferred direction
        switch (this.position) {
            case 'top':
                top = buttonRect.top + window.scrollY - menuRect.height - spacing;
                left = buttonRect.left + window.scrollX;
                break;
            case 'bottom':
                top = buttonRect.bottom + window.scrollY + spacing;
                left = buttonRect.left + window.scrollX;
                break;
            case 'left':
                top = buttonRect.top + window.scrollY;
                left = buttonRect.left + window.scrollX - menuRect.width - spacing;
                break;
            case 'right':
                top = buttonRect.top + window.scrollY;
                left = buttonRect.right + window.scrollX + spacing;
                break;
            default: // bottom
                top = buttonRect.bottom + window.scrollY + spacing;
                left = buttonRect.left + window.scrollX;
        }

        // Adjust horizontal position if menu would go off-screen
        if (left + menuRect.width > viewportWidth) {
            left = viewportWidth - menuRect.width - 16; // 16px margin from edge
        }
        if (left < 16) {
            left = 16; // 16px margin from left edge
        }

        // Adjust vertical position if menu would go off-screen
        if (this.position === 'bottom' && buttonRect.bottom + menuRect.height + spacing > viewportHeight) {
            // Show above button instead
            top = buttonRect.top + window.scrollY - menuRect.height - spacing;
        } else if (this.position === 'top' && top < window.scrollY + 16) {
            // Show below button instead
            top = buttonRect.bottom + window.scrollY + spacing;
        }

        // Apply position
        this.menuStyles = `position: absolute; top: ${top}px; left: ${left}px; min-width: ${buttonRect.width}px;`;

        menu.style.visibility = 'visible';
        menu.style.display = 'block';
    },

    /**
     * Attaches scroll event listeners to update position when container scrolls
     */
    attachScrollListeners() {
        // Find the scrollable container
        this.scrollContainer = this.$el.closest('.overflow-auto, .overflow-y-auto, .overflow-x-auto');

        if (this.scrollContainer) {
            this.scrollContainer.addEventListener('scroll', this.handleScroll);
        }

        // Also listen to window scroll and resize
        window.addEventListener('scroll', this.handleScroll);
        window.addEventListener('resize', this.handleResize);
    },

    /**
     * Removes scroll event listeners
     */
    detachScrollListeners() {
        if (this.scrollContainer) {
            this.scrollContainer.removeEventListener('scroll', this.handleScroll);
        }

        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
    },

    /**
     * Handles scroll events - throttled for performance
     */
    handleScroll: null, // Will be set in init()

    /**
     * Handles resize events - debounced for performance
     */
    handleResize: null, // Will be set in init()

    /**
     * Checks if the button is still visible and closes dropdown if not
     */
    checkVisibility() {
        const button = this.$refs.button;
        if (!button) return;

        const buttonRect = button.getBoundingClientRect();
        const isVisible = buttonRect.top >= 0 &&
                         buttonRect.left >= 0 &&
                         buttonRect.bottom <= window.innerHeight &&
                         buttonRect.right <= window.innerWidth;

        if (!isVisible && this.isOpen) {
            this.closeDropdown();
        }
    },

    /**
     * Throttle utility function
     */
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        return function (...args) {
            const currentTime = Date.now();

            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    },

    /**
     * Debounce utility function
     */
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },

    /**
     * Override the closeAllDropdowns to handle teleport-specific logic
     */
    closeAllDropdowns() {
        this.closeDropdown();
        this.positionCalculated = false;
    },

    /**
     * Initialize teleport-specific functionality
     */
    init() {
        // Call parent init if it exists
        if (typeof this.resizeHandler === 'undefined') {
            this.resizeHandler = () => {
                if (this.isOpen) {
                    this.closeAllDropdowns();
                }
            };
        }

        // Set up throttled/debounced handlers
        this.handleScroll = this.throttle(() => {
            if (this.isOpen) {
                this.updateMenuPosition();
                this.checkVisibility();
            }
        }, 16); // ~60fps

        this.handleResize = this.debounce(() => {
            if (this.isOpen) {
                this.updateMenuPosition();
            } else {
                this.closeDropdown();
            }
        }, 250);

        // Watch for open state changes
        if (typeof this.$watch === 'function') {
            this.$watch('isOpen', (value) => {
                if (value === false) {
                    this.detachScrollListeners();
                } else {
                    this.updateMenuPosition();
                    this.attachScrollListeners();
                }
            });
        }

        // Cleanup on component destroy
        this.$el.addEventListener('destroy', () => {
            this.detachScrollListeners();
        });
    }
});
