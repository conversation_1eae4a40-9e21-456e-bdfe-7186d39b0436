/**
 * Alpine.js directive for mobile menu toggle functionality
 * Usage: x-data="mobileMenuToggle" on the container element
 * Usage: @click="toggle" on the toggle button
 */
export default function mobileMenuToggle() {
    return {
        isOpen: false,
        toggle() {
            this.isOpen = !this.isOpen;
            const mobileMenu = this.$el.querySelector('#mobile-menu') || document.getElementById('mobile-menu');
            if (mobileMenu) {
                if (this.isOpen) {
                    mobileMenu.classList.remove('hidden');
                } else {
                    mobileMenu.classList.add('hidden');
                }
            }
        }
    };
}