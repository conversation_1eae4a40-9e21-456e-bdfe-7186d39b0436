#!/bin/bash

# Create tmp directory if it doesn't exist
if ! mkdir -p tmp; then
    echo "ERROR: Failed to create tmp directory"
    exit 1
fi

# Check if stripe CLI is available
if ! command -v stripe &> /dev/null; then
    echo "ERROR: stripe CLI not found. Please install it first:"
    echo "  https://stripe.com/docs/stripe-cli"
    exit 1
fi

# Check if we can reach localhost:8000
if ! curl -s --connect-timeout 5 http://localhost:8000 >/dev/null 2>&1; then
    echo "WARNING: Cannot connect to localhost:8000"
    echo "Make sure your application is running before starting the webhook listener"
fi

echo "Starting Stripe webhook listener..."
echo "Forwarding to: localhost:8000/stripe-webhook"
echo "Log file: tmp/stripe_webhook.log"

# Start Stripe webhook listener with latest API version
if ! stripe listen --forward-to localhost:8000/stripe-webhook --latest 2>&1 | tee tmp/stripe_webhook.log; then
    echo "ERROR: Stripe webhook listener failed"
    echo "This could indicate:"
    echo "  1. Stripe CLI not properly authenticated"
    echo "  2. Network connectivity issues"
    echo "  3. Invalid webhook endpoint"
    exit 1
fi
