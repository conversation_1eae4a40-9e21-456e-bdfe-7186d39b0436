# Multi-Environment Deployment Guide

Your deployment script now supports both **production** and **staging** environments running in parallel.

## Environment Setup

### Production Environment
- **Path**: `/opt/coachpad/`
- **Service**: `coachpad.service`
- **Default Port**: 8080 (configure in your app's .env)

### Staging Environment  
- **Path**: `/opt/coachpad-staging/`
- **Service**: `coachpad-staging.service`
- **Recommended Port**: 8081 (configure in staging .env)

## Deployment Commands

### Deploy to Production (default)
```bash
./deploy_prod.sh coachpad-v1.2.3.tar.gz --host myserver.com
# or explicitly
./deploy_prod.sh coachpad-v1.2.3.tar.gz --host myserver.com --environment production
```

### Deploy to Staging
```bash
./deploy_prod.sh coachpad-v1.2.4.tar.gz --host myserver.com --environment staging
```

## Server Setup Required

### 1. Create staging directories
```bash
sudo mkdir -p /opt/coachpad-staging/{tmp,logs}
sudo chown -R coachpad:coachpad /opt/coachpad-staging
```

### 2. Create staging environment file
```bash
sudo cp /opt/coachpad/.env /opt/coachpad-staging/.env
# Edit staging .env to use different port (8081) and potentially different DB
sudo nano /opt/coachpad-staging/.env
```

### 3. Nginx configuration
Add to your nginx config:
```nginx
# Production
server {
    server_name coachpad.com www.coachpad.com;
    location / {
        proxy_pass http://localhost:8080;
        # ... your existing proxy settings
    }
}

# Staging
server {
    server_name staging.coachpad.com;
    location / {
        proxy_pass http://localhost:8081;
        # ... your existing proxy settings
    }
}
```

## Service Management

### Check both services
```bash
sudo systemctl status coachpad
sudo systemctl status coachpad-staging
```

### Start/stop specific environments
```bash
sudo systemctl start coachpad-staging
sudo systemctl stop coachpad-staging
```

### View logs
```bash
sudo journalctl -u coachpad -f
sudo journalctl -u coachpad-staging -f
```

## Typical Workflow

1. **Deploy to staging** and test:
   ```bash
   ./deploy_prod.sh new-version.tar.gz --host myserver.com --environment staging
   ```

2. **Test staging** at `https://staging.coachpad.com`

3. **Deploy to production** when satisfied:
   ```bash
   ./deploy_prod.sh new-version.tar.gz --host myserver.com --environment production
   ```

## Database Considerations

- **Shared Database**: Both environments can use the same database (simpler)
- **Separate Databases**: Create `coachpad_staging` database for complete isolation

Configure the appropriate database connection in each environment's `.env` file.
