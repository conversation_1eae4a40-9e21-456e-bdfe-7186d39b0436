#!/bin/bash

# CoachPad A/B Production Deployment Script
# Supports staging, blue/green production deployments, and traffic switching
#
# SECURITY IMPROVEMENTS:
# - Input validation for all user-provided parameters
# - Sanitization of variables used in shell interpolation
# - Strict parameter format checking to prevent command injection
# - Safe handling of filenames and hostnames

set -e  # Exit on any error

# Configuration
REMOTE_USER="${DEPLOY_USER:-root}"
REMOTE_HOST="${DEPLOY_HOST}"

# Default values
ENVIRONMENT=""
SWITCH_TO=""
TARBALL=""

# Input validation functions
validate_hostname() {
    local hostname="$1"
    
    # Check for empty hostname
    if [[ -z "$hostname" ]]; then
        echo "Error: Hostname cannot be empty"
        exit 1
    fi
    
    # Check length (RFC 1035: max 253 characters for FQDN)
    if [[ ${#hostname} -gt 253 ]]; then
        echo "Error: Hostname too long (max 253 characters): $hostname"
        exit 1
    fi
    
    # Check for valid IPv4 address
    if [[ "$hostname" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        # Validate each octet is 0-255
        IFS='.' read -ra octets <<< "$hostname"
        for octet in "${octets[@]}"; do
            # Remove leading zeros for comparison (but 0 itself is valid)
            if [[ "$octet" =~ ^0[0-9]+ ]] && [[ "$octet" != "0" ]]; then
                echo "Error: Invalid IPv4 address (leading zeros): $hostname"
                exit 1
            fi
            if (( octet > 255 )); then
                echo "Error: Invalid IPv4 address (octet > 255): $hostname"
                exit 1
            fi
        done
        return 0
    fi
    
    # Check for valid hostname format (RFC 1123)
    # - Cannot start or end with hyphen or dot
    # - Cannot have consecutive dots
    # - Labels cannot start or end with hyphen
    # - Labels must be 1-63 characters
    # - Only alphanumeric and hyphens allowed
    
    # Check for invalid patterns
    if [[ "$hostname" =~ ^[.-] ]] || [[ "$hostname" =~ [.-]$ ]]; then
        echo "Error: Hostname cannot start or end with dot or hyphen: $hostname"
        exit 1
    fi
    
    if [[ "$hostname" =~ \.\. ]]; then
        echo "Error: Hostname cannot contain consecutive dots: $hostname"
        exit 1
    fi
    
    if [[ "$hostname" =~ -- ]]; then
        echo "Error: Hostname cannot contain consecutive hyphens: $hostname"
        exit 1
    fi
    
    # Check each label (split by dots)
    IFS='.' read -ra labels <<< "$hostname"
    for label in "${labels[@]}"; do
        # Check label length
        if [[ ${#label} -eq 0 ]] || [[ ${#label} -gt 63 ]]; then
            echo "Error: Invalid hostname label length (1-63 chars): '$label' in $hostname"
            exit 1
        fi
        
        # Check label starts/ends with alphanumeric
        if [[ "$label" =~ ^- ]] || [[ "$label" =~ -$ ]]; then
            echo "Error: Hostname label cannot start or end with hyphen: '$label' in $hostname"
            exit 1
        fi
        
        # Check label contains only valid characters
        if [[ ! "$label" =~ ^[a-zA-Z0-9-]+$ ]]; then
            echo "Error: Hostname label contains invalid characters: '$label' in $hostname"
            echo "Only alphanumeric characters and hyphens are allowed"
            exit 1
        fi
    done
}

validate_environment() {
    local env="$1"
    case "$env" in
        staging|production)
            return 0
            ;;
        *)
            echo "Error: Invalid environment: $env"
            echo "Environment must be 'staging' or 'production'"
            exit 1
            ;;
    esac
}

validate_slot() {
    local slot="$1"
    case "$slot" in
        blue|green)
            return 0
            ;;
        *)
            echo "Error: Invalid slot: $slot"
            echo "Slot must be 'blue' or 'green'"
            exit 1
            ;;
    esac
}

validate_tarball_name() {
    local tarball="$1"
    local basename_tarball=$(basename "$tarball")
    # Allow only safe characters in tarball names
    if [[ ! "$basename_tarball" =~ ^[a-zA-Z0-9._-]+\.tar\.gz$ ]]; then
        echo "Error: Invalid tarball name format: $basename_tarball"
        echo "Tarball name must contain only alphanumeric characters, dots, underscores, hyphens and end with .tar.gz"
        exit 1
    fi
}

validate_user() {
    local user="$1"
    # Allow valid Unix usernames
    if [[ ! "$user" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        echo "Error: Invalid username format: $user"
        echo "Username must contain only alphanumeric characters, underscores, and hyphens"
        exit 1
    fi
}

# Sanitization function for shell interpolation
sanitize_for_shell() {
    local input="$1"
    # Remove any characters that could be dangerous in shell context
    printf '%s' "$input" | sed 's/[^a-zA-Z0-9._-]//g'
}

# SSH options for security
SSH_OPTS="-o StrictHostKeyChecking=yes -o UserKnownHostsFile=~/.ssh/known_hosts"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --switch-to)
            SWITCH_TO="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS] [TARBALL]"
            echo ""
            echo "OPTIONS:"
            echo "  --host HOST           Remote host to deploy to"
            echo "  --environment ENV     Environment: staging, production"
            echo "  --switch-to SLOT      Switch traffic to: blue, green"
            echo "  --help                Show this help message"
            echo ""
            echo "EXAMPLES:"
            echo "  # Deploy to staging"
            echo "  $0 app.tar.gz --host server.com --environment staging"
            echo ""
            echo "  # Deploy to production (auto-detects inactive slot)"
            echo "  $0 app.tar.gz --host server.com --environment production"
            echo ""
            echo "  # Switch production traffic"
            echo "  $0 --switch-to green --host server.com"
            echo ""
            echo "Environment Variables:"
            echo "  DEPLOY_HOST - Default remote host"
            echo "  DEPLOY_USER - Remote user (default: root)"
            exit 0
            ;;
        *)
            if [ -z "$TARBALL" ]; then
                TARBALL="$1"
            fi
            shift
            ;;
    esac
done

# Validate arguments
if [ -z "$SWITCH_TO" ] && [ -z "$TARBALL" ]; then
    echo "Error: Either tarball or --switch-to is required"
    echo "Use --help for usage information"
    exit 1
fi

if [ -n "$TARBALL" ] && [ ! -f "$TARBALL" ]; then
    echo "Error: Tarball file not found: $TARBALL"
    exit 1
fi

if [ -z "$REMOTE_HOST" ]; then
    echo "Error: Remote host is required"
    echo "Use --host HOST or set DEPLOY_HOST environment variable"
    exit 1
fi

if [ -n "$TARBALL" ] && [ -z "$ENVIRONMENT" ]; then
    echo "Error: Environment is required when deploying tarball"
    echo "Use --environment staging or --environment production"
    exit 1
fi

# Validate inputs for security
if [ -n "$REMOTE_HOST" ]; then
    validate_hostname "$REMOTE_HOST"
fi

if [ -n "$REMOTE_USER" ]; then
    validate_user "$REMOTE_USER"
fi

if [ -n "$ENVIRONMENT" ]; then
    validate_environment "$ENVIRONMENT"
fi

if [ -n "$SWITCH_TO" ]; then
    validate_slot "$SWITCH_TO"
fi

if [ -n "$TARBALL" ]; then
    validate_tarball_name "$TARBALL"
fi

# Function to switch production traffic
switch_traffic() {
    local slot="$1"

    echo "Switching production traffic to $slot slot..."

    # Validate and sanitize slot
    validate_slot "$slot"
    local safe_slot=$(sanitize_for_shell "$slot")

    # Determine port for the slot
    local port
    if [ "$safe_slot" = "blue" ]; then
        port="8080"
    else
        port="8081"
    fi

    # Check if the target service is running
    echo "Checking if $safe_slot slot service is running..."
    ssh $SSH_OPTS "$REMOTE_USER@$REMOTE_HOST" "
        if ! systemctl is-active --quiet coachpad-${safe_slot}.service; then
            echo 'Error: coachpad-${safe_slot}.service is not running'
            echo 'Deploy to ${safe_slot} slot first before switching traffic'
            exit 1
        fi
    "

    # Update nginx configuration
    echo "Updating nginx configuration to point to $safe_slot slot (port $port)..."
    ssh $SSH_OPTS "$REMOTE_USER@$REMOTE_HOST" "
        SLOT='${safe_slot}'
        PORT='${port}'

        # Update the production nginx config with the new port
        sed \"s/proxy_pass http:\/\/localhost:[0-9]*;/proxy_pass http:\/\/localhost:\${PORT};/\" /etc/nginx/sites-available/coachpad-production > /tmp/coachpad-production.new

        # Update root path to match the slot
        sed -i \"s|root /opt/coachpad-[^/]*|root /opt/coachpad-\${SLOT}|\" /tmp/coachpad-production.new

        # Test the new configuration
        if nginx -t 2>/dev/null; then
            # Move new config to active location
            mv /tmp/coachpad-production.new /etc/nginx/sites-available/coachpad-production

            # Reload nginx (zero-downtime)
            systemctl reload nginx

            echo \"Traffic successfully switched to \${SLOT} slot (port \${PORT})\"
            echo \"Active slot: \${SLOT}\"
        else
            echo 'Error: Nginx configuration test failed'
            echo 'Nginx test output:'
            nginx -t 2>&1
            rm -f /tmp/coachpad-production.new
            exit 1
        fi
    " || exit 1
}

# Function to run database migrations for specific environment
run_migrations() {
    local environment=$1
    local slot_dir=$2

    echo "Running database migrations for $environment environment..."

    # Determine the database name based on environment
    local db_name
    if [ "$environment" = "staging" ]; then
        db_name="coachpad_staging"
    elif [ "$environment" = "production" ]; then
        db_name="coachpad_production"
    else
        echo "Unknown environment: $environment"
        return 1
    fi

    # Run migrations using goose
    cd $slot_dir
    if ./goose postgres "postgresql://coachpad@localhost:5432/$db_name" up; then
        echo "Database migrations completed successfully for $db_name"
        cd - > /dev/null
    else
        echo "Database migrations failed for $db_name"
        cd - > /dev/null
        return 1
    fi
}

# Function to detect inactive production slot
detect_inactive_slot() {
    echo "Detecting inactive production slot..." >&2

    # Check which slot is currently active in nginx
    local active_port
    active_port=$(ssh $SSH_OPTS "$REMOTE_USER@$REMOTE_HOST" "
        if [ -f /etc/nginx/sites-available/coachpad-production ]; then
            grep -o 'proxy_pass http://localhost:[0-9]*' /etc/nginx/sites-available/coachpad-production | grep -o '[0-9]*' || echo '8080'
        else
            echo '8080'
        fi
    ")

    local inactive_slot
    if [ "$active_port" = "8080" ]; then
        inactive_slot="green"
        echo "Active slot: blue (port 8080)" >&2
        echo "Deploying to inactive slot: green (port 8081)" >&2
    else
        inactive_slot="blue"
        echo "Active slot: green (port 8081)" >&2
        echo "Deploying to inactive slot: blue (port 8080)" >&2
    fi

    echo "$inactive_slot"
}

# Function to deploy to staging
deploy_staging() {
    local tarball="$1"
    validate_tarball_name "$tarball"
    local tarball_name=$(basename "$tarball")
    local safe_tarball_name=$(sanitize_for_shell "$tarball_name")

    echo "Deploying $tarball to staging environment..."

    # Transfer tarball to remote server using rsync
    echo "Copying tarball to remote server..."
    rsync -avz --progress "$tarball" "$REMOTE_USER@$REMOTE_HOST:/tmp/$safe_tarball_name"

    # Extract and setup staging deployment
    echo "Setting up staging deployment..."
    ssh $SSH_OPTS "$REMOTE_USER@$REMOTE_HOST" "
        # Create staging directory if it doesn't exist
        if [ ! -d /opt/coachpad-staging ]; then
            mkdir -p /opt/coachpad-staging
            echo 'Created staging directory: /opt/coachpad-staging'
        fi

        # Create coachpad user if it doesn't exist
        echo 'Ensuring coachpad user exists...'
        if ! id coachpad >/dev/null 2>&1; then
            useradd -r -s /bin/false -d /opt/coachpad-staging coachpad
            echo 'Created coachpad user'
        fi

        echo 'Extracting application files...'
        cd /opt/coachpad-staging
        tar -xzf /tmp/${safe_tarball_name}
        rm /tmp/${safe_tarball_name}
        chmod +x coachpad

        # Create tmp and logs directories
        mkdir -p tmp logs
        
        # Copy environment-specific .env file
        if [ -f .env.staging ]; then
            cp .env.staging .env
            echo 'Using staging environment configuration'
        else
            echo 'WARNING: .env.staging not found, using default .env'
        fi

        # NOTE: COACHPAD_BACKEND_PORT=9000 is set by systemd service file
        # No need to modify .env for port configuration

        # Set proper ownership first, then permissions
        chown -R coachpad:coachpad /opt/coachpad-staging/
        
        # Ensure tmp and logs directories have full write permissions
        chmod 777 tmp logs
        
        # Pre-create the log file with proper permissions
        touch tmp/app.log
        chown coachpad:coachpad tmp/app.log
        chmod 666 tmp/app.log
        
        # Debug: Check final permissions
        echo 'Final permissions check:'
        ls -la tmp logs
        ls -la tmp/app.log
        
        echo 'Application files extracted and permissions set'

        # Stop staging service if running
        echo 'Stopping staging service if running...'
        if systemctl is-active --quiet coachpad-staging.service; then
            systemctl stop coachpad-staging.service
            echo 'Staging service stopped'
        else
            echo 'Staging service was not running'
        fi

        # Run database migrations for staging
        echo 'Running database migrations for staging...'
        
        # Load staging environment variables
        if [ -f .env.staging ]; then
            set -o allexport
            source .env.staging
            set +o allexport
            echo 'Loaded staging environment variables'
        else
            echo 'ERROR: .env.staging file not found'
            exit 1
        fi

        # Ensure database exists and user has proper permissions
        echo 'Ensuring database and permissions are set up...'
        sudo -u postgres psql -c \"CREATE DATABASE coachpad_staging;\" 2>/dev/null || true
        sudo -u postgres psql -c \"GRANT ALL PRIVILEGES ON DATABASE coachpad_staging TO coachpad;\" 2>/dev/null || true
        sudo -u postgres psql -d coachpad_staging -c \"GRANT ALL ON SCHEMA public TO coachpad;\" 2>/dev/null || true

        # Change to migrations directory
        cd migrations

        # Run goose migrations (goose will create the version table if it doesn't exist)
        echo 'Running goose migrations...'
        echo \"Using DATABASE_URL: \$DATABASE_URL\"
        if ../goose -dir . postgres \"\$DATABASE_URL\" up; then
            echo 'Database migrations completed successfully for coachpad_staging'
            cd ..
        else
            echo 'Error: Failed to run migrations for coachpad_staging'
            echo 'Checking if database exists and user has permissions...'
            sudo -u postgres psql -l | grep coachpad_staging || echo 'Database does not exist'
            cd ..
            exit 1
        fi

        # Install and restart staging service
        cp /opt/coachpad-staging/deployment/coachpad-staging.service /etc/systemd/system/coachpad-staging.service
        systemctl daemon-reload
        systemctl restart coachpad-staging.service

        # Verify staging service is running
        echo 'Checking if staging service is running...'
        if ! systemctl is-active --quiet coachpad-staging.service; then
            echo 'Error: coachpad-staging.service failed to start'
            systemctl status coachpad-staging.service
            exit 1
        fi
        echo 'Staging service is running successfully'

        # Setup staging nginx configuration with safety checks
        echo 'Updating nginx configuration for staging...'

        # Create temporary nginx config for testing
        cp /opt/coachpad-staging/deployment/nginx-staging.conf /tmp/coachpad-staging.new

        # Test the new configuration before applying
        echo 'Testing nginx configuration...'
        # Copy to sites-available temporarily for testing
        cp /tmp/coachpad-staging.new /etc/nginx/sites-available/coachpad-staging-test
        if nginx -t 2>&1; then
            # Move new config to active location
            mv /tmp/coachpad-staging.new /etc/nginx/sites-available/coachpad-staging

            # Clean up test config
            rm -f /etc/nginx/sites-available/coachpad-staging-test

            # Enable staging site if not already enabled
            if [ ! -L /etc/nginx/sites-enabled/coachpad-staging ]; then
                ln -s /etc/nginx/sites-available/coachpad-staging /etc/nginx/sites-enabled/coachpad-staging
            fi

            # Reload nginx (zero-downtime)
            systemctl reload nginx
            echo 'Staging nginx configuration updated successfully'
        else
            echo 'ERROR: Nginx configuration test failed'
            echo 'Nginx configuration errors:'
            nginx -t 2>&1 | head -20
            # Clean up test config
            rm -f /etc/nginx/sites-available/coachpad-staging-test
            rm -f /tmp/coachpad-staging.new
            echo ''
            echo 'Possible issues:'
            echo '1. SSL certificate files missing or incorrect paths'
            echo '2. Nginx modules not loaded (ssl, http2)'
            echo '3. Configuration syntax errors'
            echo '4. Port conflicts'
            echo ''
            echo 'To debug manually:'
            echo 'ssh $REMOTE_USER@$REMOTE_HOST \"nginx -t\"'
            exit 1
        fi

        echo 'Staging deployment completed successfully!'
        echo 'Staging environment available at https://staging.coachpad.ca'
        echo ''
        echo 'Final status check:'
        systemctl is-active coachpad-staging.service && echo '✓ Staging service is running' || echo '✗ Staging service is not running'
        nginx -t >/dev/null 2>&1 && echo '✓ Nginx configuration is valid' || echo '✗ Nginx configuration has errors'
    "
}

# Function to deploy to production slot
deploy_production() {
    local tarball="$1"
    validate_tarball_name "$tarball"
    local tarball_name=$(basename "$tarball")
    local safe_tarball_name=$(sanitize_for_shell "$tarball_name")

    # Detect inactive slot for deployment
    local slot
    slot=$(detect_inactive_slot)
    validate_slot "$slot"
    local safe_slot=$(sanitize_for_shell "$slot")

    local port
    if [ "$safe_slot" = "blue" ]; then
        port="8080"
    else
        port="8081"
    fi

    echo "Deploying $tarball to production $safe_slot slot (port $port)..."

    # Transfer tarball to remote server using rsync
    echo "Copying tarball to remote server..."
    rsync -avz --progress "$tarball" "$REMOTE_USER@$REMOTE_HOST:/tmp/$safe_tarball_name"

    # Extract and setup production deployment
    echo "Setting up production $safe_slot slot deployment..."
    ssh $SSH_OPTS "$REMOTE_USER@$REMOTE_HOST" "
        # Create slot directory if it doesn't exist
        if [ ! -d /opt/coachpad-${safe_slot} ]; then
            mkdir -p /opt/coachpad-${safe_slot}
        fi

        cd /opt/coachpad-${safe_slot}
        tar -xzf /tmp/${safe_tarball_name}
        rm /tmp/${safe_tarball_name}
        chmod +x coachpad
        # Create tmp and logs directories
        mkdir -p tmp logs
        
        # Copy environment-specific .env file
        if [ -f .env.production ]; then
            cp .env.production .env
            echo 'Using production environment configuration'
        else
            echo 'WARNING: .env.production not found, using default .env'
        fi

        # NOTE: COACHPAD_BACKEND_PORT is set by systemd service files:
        # Blue slot: Environment=COACHPAD_BACKEND_PORT=8080
        # Green slot: Environment=COACHPAD_BACKEND_PORT=8081
        # No need to modify .env for port configuration

        # Create coachpad user if it doesn't exist (matching staging)
        echo 'Ensuring coachpad user exists...'
        if ! id coachpad >/dev/null 2>&1; then
            useradd -r -s /bin/false -d /opt/coachpad-${safe_slot} coachpad
            echo 'Created coachpad user'
        fi

        # Set proper ownership first, then permissions
        chown -R coachpad:coachpad /opt/coachpad-${safe_slot}/
        
        # Ensure tmp and logs directories have full write permissions
        chmod 777 tmp logs
        
        # Pre-create the log file with proper permissions
        touch tmp/app.log
        chown coachpad:coachpad tmp/app.log
        chmod 666 tmp/app.log
        
        # Debug: Check final permissions
        echo 'Final permissions check:'
        ls -la tmp logs
        ls -la tmp/app.log
        
        echo 'Application files extracted and permissions set'

        # Stop slot service if running
        if systemctl is-active --quiet coachpad-$slot.service; then
            systemctl stop coachpad-$slot.service
        fi

        # Run database migrations for production
        echo 'Running database migrations for production...'
        
        # Load production environment variables
        if [ -f .env.production ]; then
            set -o allexport
            source .env.production
            set +o allexport
            echo 'Loaded production environment variables'
        else
            echo 'ERROR: .env.production file not found'
            exit 1
        fi

        # Ensure database exists and user has proper permissions
        echo 'Ensuring database and permissions are set up...'
        sudo -u postgres psql -c \"CREATE DATABASE coachpad_production;\" 2>/dev/null || true
        sudo -u postgres psql -c \"GRANT ALL PRIVILEGES ON DATABASE coachpad_production TO coachpad;\" 2>/dev/null || true
        sudo -u postgres psql -d coachpad_production -c \"GRANT ALL ON SCHEMA public TO coachpad;\" 2>/dev/null || true

        # Change to migrations directory
        cd migrations

        # Run goose migrations (goose will create the version table if it doesn't exist)
        echo 'Running goose migrations...'
        echo \"Using DATABASE_URL: \$DATABASE_URL\"
        if ../goose -dir . postgres \"\$DATABASE_URL\" up; then
            echo 'Database migrations completed successfully for coachpad_production'
            cd ..
        else
            echo 'Error: Failed to run migrations for coachpad_production'
            echo 'Checking if database exists and user has permissions...'
            sudo -u postgres psql -l | grep coachpad_production || echo 'Database does not exist'
            cd ..
            exit 1
        fi

        # Install and restart slot service
        cp /opt/coachpad-$slot/deployment/coachpad-$slot.service /etc/systemd/system/coachpad-$slot.service
        systemctl daemon-reload
        systemctl restart coachpad-$slot.service

        # Setup/update production nginx configuration (always update to latest)
        echo 'Updating nginx configuration for production...'
        
        # Backup existing config if it exists
        if [ -f /etc/nginx/sites-available/coachpad-production ]; then
            cp /etc/nginx/sites-available/coachpad-production /etc/nginx/sites-available/coachpad-production.backup
            echo 'Backed up existing nginx configuration'
        fi
        
        # Copy new configuration from deployment
        cp /opt/coachpad-${safe_slot}/deployment/nginx-production.conf /etc/nginx/sites-available/coachpad-production.new
        
        # Test the new configuration
        echo 'Testing new nginx configuration...'
        # Temporarily move new config for testing
        mv /etc/nginx/sites-available/coachpad-production.new /etc/nginx/sites-available/coachpad-production
        
        if nginx -t 2>&1; then
            # Configuration is valid, ensure site is enabled
            if [ ! -L /etc/nginx/sites-enabled/coachpad-production ]; then
                ln -s /etc/nginx/sites-available/coachpad-production /etc/nginx/sites-enabled/coachpad-production
                echo 'Enabled production site'
            fi

            # Remove old single-deployment config if it exists
            if [ -L /etc/nginx/sites-enabled/coachpad ]; then
                rm /etc/nginx/sites-enabled/coachpad
                echo 'Removed old single-deployment config'
            fi

            # Reload nginx with new config
            systemctl reload nginx
            echo 'Production nginx configuration updated and reloaded successfully'
            
            # Clean up backup since new config works
            rm -f /etc/nginx/sites-available/coachpad-production.backup
        else
            echo 'ERROR: New nginx configuration test failed'
            echo 'Nginx configuration errors:'
            nginx -t 2>&1 | head -20
            
            # Restore backup if it exists
            if [ -f /etc/nginx/sites-available/coachpad-production.backup ]; then
                mv /etc/nginx/sites-available/coachpad-production.backup /etc/nginx/sites-available/coachpad-production
                echo 'Restored previous working nginx configuration'
                
                # Verify restored config works
                if nginx -t >/dev/null 2>&1; then
                    echo 'Previous configuration restored successfully'
                else
                    echo 'CRITICAL: Previous configuration also failed, manual intervention required'
                fi
            else
                echo 'No backup configuration available'
            fi
            
            echo ''
            echo 'To debug manually:'
            echo 'ssh $REMOTE_USER@$REMOTE_HOST \"nginx -t\"'
            exit 1
        fi

        echo 'Production ${safe_slot} slot deployment completed successfully!'
        echo 'Test at: http://'\$(hostname)':${port}'
        echo ''
        echo 'To switch traffic to this slot:'
        echo './deploy_prod.sh --switch-to ${safe_slot} --host '\$(hostname)
    "
}

# Main execution logic
if [ -n "$SWITCH_TO" ]; then
    # Traffic switching mode
    switch_traffic "$SWITCH_TO"
elif [ "$ENVIRONMENT" = "staging" ]; then
    # Staging deployment
    deploy_staging "$TARBALL"
elif [ "$ENVIRONMENT" = "production" ]; then
    # Production A/B deployment
    deploy_production "$TARBALL"
else
    echo "Error: Unknown environment '$ENVIRONMENT'"
    exit 1
fi
