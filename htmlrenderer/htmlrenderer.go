package htmlrenderer

import (
	"errors"
	"html/template"
	"io"
	"log"
	"os"
	"path/filepath"

	"github.com/j-em/coachpad/i18n"
	"github.com/labstack/echo/v4"
)

// Template renderer
type Renderer struct {
	templates    *template.Template
	appLayout    *template.Template
	publicLayout *template.Template
	vite         string
	locales      map[string]map[string]map[string]interface{} // Add the locales field
}

// NewRenderer creates and initializes a new template renderer
func NewRenderer(viteFragment string) (*Renderer, error) {
	// Create base template and register functions first
	htmlLayout := template.New("html-layout").Funcs(template.FuncMap{
		"vite": func() template.HTML {
			return template.HTML(viteFragment)
		},
		"dict": func(values ...interface{}) (map[string]interface{}, error) {
			if len(values)%2 != 0 {
				return nil, errors.New("invalid dict call")
			}
			dict := make(map[string]interface{}, len(values)/2)
			for i := 0; i < len(values); i += 2 {
				key, ok := values[i].(string)
				if !ok {
					return nil, errors.New("dict keys must be strings")
				}
				dict[key] = values[i+1]
			}
			return dict, nil
		},
	})

	// Find all .go.html files under templates/
	var templateFiles []string
	err := filepath.WalkDir("templates", func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if !d.IsDir() && filepath.Ext(path) == ".html" && filepath.Ext(filepath.Base(path[:len(path)-len(filepath.Ext(path))])) == ".go" {
			templateFiles = append(templateFiles, path)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	if len(templateFiles) == 0 {
		return nil, errors.New("no templates found")
	}

	htmlLayout, err = htmlLayout.ParseFiles(templateFiles...)
	if err != nil {
		return nil, err
	}

	// Load the locales
	locales, err := i18n.LoadLocales("templates")
	if err != nil {
		return nil, err
	}

	renderer := &Renderer{
		templates: htmlLayout,
		vite:      viteFragment,
		locales:   locales,
	}
	return renderer, nil
}

func (t *Renderer) Render(w io.Writer, name string, data interface{}, c echo.Context) error {
	// Get the current language from the cookie
	lang := getLanguageFromCookie(c)

	// Add locale data to the template data
	templateData := map[string]interface{}{
		"data":    data,
		"locales": t.locales[lang], // Access the locale data for the specific template and language
	}

	// Execute the top-level layout template
	err := t.templates.ExecuteTemplate(w, name, templateData)
	if err != nil {
		log.Printf("Template execution error for %s: %v", name, err)
	}
	return err
}

func getLanguageFromCookie(c echo.Context) string {
	cookie, err := c.Cookie("lang")
	if err != nil {
		return "en" // Default to English if no cookie is found
	}
	return cookie.Value
}
