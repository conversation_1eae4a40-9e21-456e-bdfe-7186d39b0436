require('dotenv').config();

const stytch = require('stytch');

// Stytch API credentials
const client = new stytch.Client({
  project_id: process.env.STYTCH_PROJECT_ID,
  secret: process.env.STYTCH_SECRET,
});

async function deleteAllUsers() {
  try {
    console.log('Authentication successful. Proceeding to delete users...');

    // Fetch all users using the search_users method
    const usersResponse = await client.users.search({
      limit: 1000, // Adjust the limit as needed
    });
    const users = usersResponse.results;

    for (const user of users) {
      // Delete each user
      await client.users.delete({ user_id: user.user_id });
      console.log(`Deleted user: ${user.user_id}`);
    }

    console.log('All users have been deleted.');
  } catch (error) {
    console.error('Error deleting users:', error.response?.data || error.message);
  }
}

// Run the script
deleteAllUsers();