{"name": "coachpad", "version": "1.0.0", "main": "index.js", "scripts": {"test": "npx playwright test --project=public-chromium --project=public-firefox --project=app-chromium --project=app-firefox --reporter=list", "test:ui": "npx playwright test --ui", "test:debug": "npx playwright test --debug", "test:report": "npx playwright show-report", "test:install-browsers": "npx playwright install", "start": "npx vite build && DATABASE_URL=postgresql://coachpad@localhost:5432/postgres go run main.go", "build:prod": "./build_prod.sh", "deploy:prod": "./deploy_prod.sh", "deploy:quick": "./build_prod.sh && ./deploy_prod.sh coachpad-production-$(date +%Y%m%d)*.tar.gz"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@alpinejs/focus": "^3.14.9", "@stripe/stripe-js": "^7.3.1", "alpinejs": "^3.14.9", "htmx-ext-head-support": "^2.0.4", "htmx-ext-sse": "^2.2.3", "htmx.org": "^2.0.4", "tailwindcss": "^4.1.3", "vite": "^6.2.5"}, "devDependencies": {"@playwright/test": "^1.52.0", "@tailwindcss/vite": "^4.1.3", "@types/node": "^22.15.29", "axios": "^1.8.4", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "prettier": "^3.5.3", "prettier-plugin-go-template": "^0.0.15", "stytch": "^12.17.0", "uuid": "^11.1.0"}}