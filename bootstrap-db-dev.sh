#!/bin/bash

CONTAINER_NAME=postgres
VOLUME_NAME=postgres_data

# Stop and remove the container if it exists
podman rm -f $CONTAINER_NAME 2>/dev/null

# Run the Postgres container
echo "Starting PostgreSQL container..."
if ! podman run --name $CONTAINER_NAME \
  -p 5432:5432 \
  -v $VOLUME_NAME:/var/lib/postgresql/data:Z \
  -e POSTGRES_PASSWORD=postgres \
  -d docker.io/library/postgres:16; then
    echo "ERROR: Failed to start PostgreSQL container"
    echo "This could indicate:"
    echo "  1. Port 5432 is already in use"
    echo "  2. <PERSON><PERSON>/Docker is not running"
    echo "  3. Image pull failed"
    echo "  4. Insufficient permissions"
    exit 1
fi
echo "PostgreSQL container started successfully"

# Wait for PostgreSQL to start
echo "Waiting for PostgreSQL to start..."
until podman exec $CONTAINER_NAME pg_isready; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

echo "PostgreSQL is ready! Setting up databases..."

# Run the multi-environment database setup
if [ -f "./setup-databases.sh" ]; then
    chmod +x ./setup-databases.sh
    ./setup-databases.sh
else
    echo "ERROR: setup-databases.sh not found"
    echo "Please ensure setup-databases.sh exists in the current directory"
    exit 1
fi

# Configure trust authentication for development
echo "Configuring development authentication..."
podman exec $CONTAINER_NAME sed -i '1i host    all             coachpad        all                     trust' /var/lib/postgresql/data/pg_hba.conf
podman exec $CONTAINER_NAME psql -U postgres -c "SELECT pg_reload_conf();" > /dev/null

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "Available databases:"
echo "  Development: postgresql://coachpad@localhost:5432/coachpad_development"
echo "  Staging:     postgresql://coachpad@localhost:5432/coachpad_staging"
echo "  Production:  postgresql://coachpad@localhost:5432/coachpad_production"
echo ""
echo "To connect manually:"
echo "  podman exec -it postgres psql -U coachpad -d coachpad_development"
echo "  podman exec -it postgres psql -U coachpad -d coachpad_staging"
echo "  podman exec -it postgres psql -U coachpad -d coachpad_production"
