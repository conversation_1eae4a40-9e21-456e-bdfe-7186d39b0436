package assets

import (
	"io/fs"

	"github.com/olivere/vite"
)

// GetViteFragment initializes and returns the Vite HTML fragment based on the development mode.
// The distFS parameter should be the embedded or filesystem for the dist directory.
func GetViteFragment(isDev bool, distFS fs.FS) *vite.Fragment {
	viteFragment, err := vite.HTMLFragment(vite.Config{
		FS:        distFS,                  // Use the provided filesystem
		IsDev:     isDev,                   // required: true or false
		ViteURL:   "http://localhost:5173", // optional: defaults to this
		ViteEntry: "frontend/main.js",      // full path to the entry file
	})
	if err != nil {
		// Return a safe default when vite fails to generate HTML fragment
		return &vite.Fragment{
			Tags: "",
		}
	}
	return viteFragment
}
