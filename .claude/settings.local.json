{"permissions": {"allow": ["Bash(grep:*)", "Bash(go fmt:*)", "Bash(go build:*)", "Bash(sqlc:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(go mod:*)", "Bash(npm test:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(npm run test:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(git commit:*)", "Bash(go get:*)", "Bash(go install:*)", "Bash(goose create:*)", "Bash(ls:*)", "Bash(~/go/bin/goose -dir migrations create initial_schema sql)", "<PERSON><PERSON>(goimports:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "WebFetch(domain:playwright.dev)"], "deny": []}}