package stripe

import (
	"os"
	"sync"

	"github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/client"
)

var (
	stripeClient *client.API
	once         sync.Once
)

func GetClient() *client.API {
	once.Do(func() {
		secretKey := os.Getenv("STRIPE_SECRET_KEY")
		if secretKey == "" {
			panic("STRIPE_SECRET_KEY environment variable is required")
		}

		stripe.Key = secretKey
		stripeClient = client.New(secretKey, nil)
	})
	return stripeClient
}

// GetPublishableKey returns the Stripe publishable key from environment
func GetPublishableKey() string {
	return os.Getenv("STRIPE_PUBLISHABLE_KEY")
}

// GetProPlanPriceID returns the Stripe price ID for the Pro plan
func GetProPlanPriceID() string {
	return os.Getenv("STRIPE_PRO_PLAN_PRICE_ID")
}

// GetWebhookSecret returns the Stripe webhook secret for webhook verification
func GetWebhookSecret() string {
	return os.Getenv("STRIPE_WEBHOOK_SECRET")
}
