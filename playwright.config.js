// @ts-check
const { defineConfig, devices } = require("@playwright/test");
const fs = require("fs");
const path = require("path");
// Load .env file if present
const dotenvPath = path.resolve(__dirname, ".env.development");
if (fs.existsSync(dotenvPath)) {
  require("dotenv").config({ path: dotenvPath });
}

/**
 * Get the backend port from environment variable, matching the backend logic
 * CRASH if not set - port must be explicitly configured
 */
function getBackendPort() {
  const port = process.env.COACHPAD_BACKEND_PORT;
  if (!port) {
    throw new Error(
      "COACHPAD_BACKEND_PORT environment variable is not set. Please check your .env.development file.",
    );
  }
  return port;
}

/**
 * Get the base URL for the application
 */
function getBaseURL() {
  return `http://localhost:${getBackendPort()}`;
}

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: "./tests/e2e",
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : 2,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: "html",
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: getBaseURL(),

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "on-first-retry",

    /* Set global timeout for assertions to 15 seconds */
    expect: {
      timeout: 15000,
    },

    /* Set color scheme preference for dark mode logo testing */
    colorScheme: "dark",
  },

  /* Configure projects for major browsers */
  projects: [
    // Public area
    {
      name: "public-chromium",
      testDir: "./tests/e2e/public",
      use: { ...devices["Desktop Chrome"] },
    },
    {
      name: "public-firefox",
      testDir: "./tests/e2e/public",
      use: { ...devices["Desktop Firefox"] },
    },
    {
      name: "public-webkit",
      testDir: "./tests/e2e/public",
      use: { ...devices["Desktop Safari"] },
    },
    {
      name: "public-mobile-chrome",
      testDir: "./tests/e2e/public",
      use: {
        ...devices["Desktop Chrome"],

        viewport: { width: 375, height: 667 }, // iPhone 6/7/8 viewport
      },
    },
    // App area
    {
      name: "app-chromium",
      testDir: "./tests/e2e/app",
      use: {
        ...devices["Desktop Chrome"],
        // No need to specify storageState here as it's handled by the fixture
      },
    },
    {
      name: "app-firefox",
      testDir: "./tests/e2e/app",
      use: {
        ...devices["Desktop Firefox"],
        // No need to specify storageState here as it's handled by the fixture
        launchOptions: {
          slowMo: 100, // Slow down actions by 100ms to help with timing issues
        },
      },
    },
    {
      name: "app-webkit",
      testDir: "./tests/e2e/app",
      use: {
        ...devices["Desktop Safari"],
        // No need to specify storageState here as it's handled by the fixture
      },
    },
    {
      name: "app-mobile-chrome",
      testDir: "./tests/e2e/app",
      use: {
        ...devices["Desktop Chrome"],

        viewport: { width: 375, height: 667 }, // iPhone 6/7/8 viewport
      },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: "npx vite build && go run main.go",
    url: getBaseURL(),
    reuseExistingServer: !process.env.CI,
    timeout: 180 * 1000, // 3 minutes timeout for CI to handle slow startup
    stdout: "pipe",
    stderr: "pipe",
    env: {
      ...process.env, // Inherit all environment variables from the system
      DATABASE_URL:
        process.env.DATABASE_URL ||
        "postgresql://coachpad@localhost:5432/coachpad_development",
      COACHPAD_BACKEND_PORT: getBackendPort(),
      APP_ENV: "development",
    },
  },
});
