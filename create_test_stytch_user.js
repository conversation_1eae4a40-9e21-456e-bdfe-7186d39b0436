require('dotenv').config();

const stytch = require('stytch');

// Stytch API credentials
const client = new stytch.Client({
  project_id: process.env.STYTCH_PROJECT_ID,
  secret: process.env.STYTCH_SECRET,
});

async function createOrReplaceUser(email) {
  try {
    // Search for the user by email
    const usersResponse = await client.users.search({
      query: {
        operator: 'AND',
        operands: [
          {
            filter_name: 'email_address',
            filter_value: email,
          },
        ],
      },
    });

    const users = usersResponse.results;

    if (users.length > 0) {
      // If the user exists, delete them
      const userId = users[0].user_id;
      await client.users.delete({ user_id: userId });
      console.log(`Deleted user with email: ${email}`);
    } else {
      console.log(`No user found with email: ${email}`);
    }

    // Create a new user with the specified email
    const newUserResponse = await client.users.create({
      email: email,
      name: {
        first_name: '<PERSON><PERSON><PERSON>', // Replace with desired first name
        last_name: '<PERSON><PERSON><PERSON>',  // Replace with desired last name
      },
    });

    console.log(`Created new user with email: ${email}`);
    console.log('New user details:', newUserResponse);
  } catch (error) {
    console.error('Error managing user:', error.response?.data || error.message);
  }
}

// Replace '<EMAIL>' with the email you want to manage
const emailToManage = '<EMAIL>';
createOrReplaceUser(emailToManage);