# Coachpad

## Deployment

CoachPad supports two deployment architectures:

### A/B (Blue/Green) Deployment - **Recommended for Production**
Zero-downtime deployments with staging isolation:

```bash
# Deploy to staging for testing
./deploy_prod.sh app.tar.gz --environment staging --host your-server.com

# Deploy to production (auto-detects inactive slot)
./deploy_prod.sh app.tar.gz --environment production --host your-server.com

# Switch traffic instantly (zero downtime)
./deploy_prod.sh --switch-to green --host your-server.com
```

See [`docs/AB_DEPLOYMENT.md`](docs/AB_DEPLOYMENT.md) for complete A/B deployment guide.

### Simple Single Deployment
Traditional single-instance deployment:

```bash
# Build and deploy in one command
npm run deploy:quick -- --host your-server.com

# Or step by step
npm run build:prod
./deploy_prod.sh coachpad-*.tar.gz --host your-server.com
```

See [DEPLOYMENT_QUICK_START.md](./DEPLOYMENT_QUICK_START.md) for simple deployment documentation.

## Automated Testing with <PERSON>wright

### Running Tests with Distrobox

This project uses <PERSON><PERSON> for end-to-end testing, executed through a fedora distrobox container to ensure consistent test environments across all development machines.

#### Prerequisites

1. [Distrobox](https://github.com/89luca89/distrobox) must be installed on your system
2. You must have the fedora container created and properly set up:
   ```
   distrobox create -i fedora:latest -n fedora
   ```

#### Installing Browser Binaries

Before running tests for the first time, install the required browser binaries:

```
npm run test:install-browsers
```

This will install the necessary browser engines inside the fedora container.

#### Running Tests

All test commands are configured to run through the fedora distrobox:

- Run all tests: `npm test`
- Run tests with UI: `npm run test:ui`
- Debug tests: `npm run test:debug`
- View test reports: `npm run test:report`

#### Test Location

Tests are located in the `tests/e2e` directory.

#### Troubleshooting

If you encounter issues running tests through distrobox:

1. Ensure the fedora container exists: `distrobox list`
2. Check that the container can run commands: `distrobox enter fedora -- echo "Hello World"`
