package matchmaker

import (
	"fmt"
	"testing"
	"time"
)

func TestGenerateRoundRobin(t *testing.T) {
	// Test case 1: Basic functionality with 4 players and 2 tables
	t.Run("Basic functionality with 4 players and 2 tables", func(t *testing.T) {
		startDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
		config := RoundRobinConfig{
			PlayerIDs:      []int32{1, 2, 3, 4},
			StartDate:      startDate,
			Frequency:      "weekly",
			AmountOfTables: 2,
		}

		matches := GenerateRoundRobin(config)

		// With 4 players, we expect 6 matches total (n*(n-1)/2)
		expectedMatches := 6
		if len(matches) != expectedMatches {
			t.Errorf("Expected %d matches, got %d", expectedMatches, len(matches))
		}

		// Check player combinations - each player should play against every other player
		expectedPairs := map[string]bool{
			"1-2": false,
			"1-3": false,
			"1-4": false,
			"2-3": false,
			"2-4": false,
			"3-4": false,
		}

		for _, match := range matches {
			key := ""
			if match.Player1ID < match.Player2ID {
				key = formatPair(match.Player1ID, match.Player2ID)
			} else {
				key = formatPair(match.Player2ID, match.Player1ID)
			}
			expectedPairs[key] = true
		}

		for pair, found := range expectedPairs {
			if !found {
				t.Errorf("Expected match pair %s was not generated", pair)
			}
		}

		// Check groups and dates
		// With 2 tables, we should have 3 groups (6 matches / 2 tables = 3 groups)
		groupCounts := make(map[int32]int)
		for _, match := range matches {
			groupCounts[match.Group]++
		}

		if len(groupCounts) != 3 {
			t.Errorf("Expected 3 groups, got %d", len(groupCounts))
		}

		// Check dates
		// Group 1 matches should be on the start date
		// Group 2 matches should be a week later
		// Group 3 matches should be two weeks later
		dateCounts := make(map[time.Time]int)
		for _, match := range matches {
			dateCounts[match.MatchDate]++
		}

		expectedDate1 := startDate
		expectedDate2 := startDate.AddDate(0, 0, 7)  // weekly
		expectedDate3 := startDate.AddDate(0, 0, 14) // weekly + weekly

		if dateCounts[expectedDate1] != 2 {
			t.Errorf("Expected 2 matches on %v, got %d", expectedDate1, dateCounts[expectedDate1])
		}
		if dateCounts[expectedDate2] != 2 {
			t.Errorf("Expected 2 matches on %v, got %d", expectedDate2, dateCounts[expectedDate2])
		}
		if dateCounts[expectedDate3] != 2 {
			t.Errorf("Expected 2 matches on %v, got %d", expectedDate3, dateCounts[expectedDate3])
		}
	})

	// Test case 2: Different frequency
	t.Run("Monthly frequency", func(t *testing.T) {
		startDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
		config := RoundRobinConfig{
			PlayerIDs:      []int32{1, 2, 3},
			StartDate:      startDate,
			Frequency:      "monthly",
			AmountOfTables: 1,
		}

		matches := GenerateRoundRobin(config)

		// With 3 players, we expect 3 matches total (n*(n-1)/2)
		expectedMatches := 3
		if len(matches) != expectedMatches {
			t.Errorf("Expected %d matches, got %d", expectedMatches, len(matches))
		}

		// Check dates
		// With 1 table and monthly frequency, matches should be on consecutive months
		expectedDate1 := startDate
		expectedDate2 := startDate.AddDate(0, 1, 0) // 1 month later
		expectedDate3 := startDate.AddDate(0, 2, 0) // 2 months later

		dateCounts := make(map[time.Time]int)
		for _, match := range matches {
			dateCounts[match.MatchDate]++
		}

		if dateCounts[expectedDate1] != 1 {
			t.Errorf("Expected 1 match on %v, got %d", expectedDate1, dateCounts[expectedDate1])
		}
		if dateCounts[expectedDate2] != 1 {
			t.Errorf("Expected 1 match on %v, got %d", expectedDate2, dateCounts[expectedDate2])
		}
		if dateCounts[expectedDate3] != 1 {
			t.Errorf("Expected 1 match on %v, got %d", expectedDate3, dateCounts[expectedDate3])
		}
	})

	// Test case 3: Large scale - 100 players and 23 tables
	t.Run("Large scale with 100 players and 23 tables", func(t *testing.T) {
		startDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

		// Generate 100 player IDs
		playerIDs := make([]int32, 100)
		for i := 0; i < 100; i++ {
			playerIDs[i] = int32(i + 1)
		}

		config := RoundRobinConfig{
			PlayerIDs:      playerIDs,
			StartDate:      startDate,
			Frequency:      "weekly",
			AmountOfTables: 23,
		}

		matches := GenerateRoundRobin(config)

		// With 100 players, we expect 100*(100-1)/2 = 4950 matches total
		expectedMatches := 4950
		if len(matches) != expectedMatches {
			t.Errorf("Expected %d matches, got %d", expectedMatches, len(matches))
		}

		// Check that all player combinations are present
		// This would be inefficient to check all 4950 combinations explicitly,
		// so we'll count unique pairs instead
		uniquePairs := make(map[string]bool)
		for _, match := range matches {
			key := ""
			if match.Player1ID < match.Player2ID {
				key = formatPair(match.Player1ID, match.Player2ID)
			} else {
				key = formatPair(match.Player2ID, match.Player1ID)
			}
			uniquePairs[key] = true
		}

		if len(uniquePairs) != expectedMatches {
			t.Errorf("Expected %d unique player pairs, got %d", expectedMatches, len(uniquePairs))
		}

		// Check match groups
		// With 23 tables and 4950 matches, we should have ceiling(4950/23) = 216 groups
		expectedGroups := 216
		groups := make(map[int32]bool)
		for _, match := range matches {
			groups[match.Group] = true
		}

		if len(groups) != expectedGroups {
			t.Errorf("Expected %d groups, got %d", expectedGroups, len(groups))
		}

		// Check that each group has the right number of matches
		// All groups except possibly the last one should have exactly 23 matches
		groupCounts := make(map[int32]int)
		for _, match := range matches {
			groupCounts[match.Group]++
		}

		// Check full groups (all except potentially the last group)
		for group, count := range groupCounts {
			if group < int32(expectedGroups) && count != 23 {
				t.Errorf("Group %d should have 23 matches, got %d", group, count)
			}
		}

		// Check the distribution of match dates
		// With 216 groups and weekly frequency, dates should span over 216 weeks
		uniqueDates := make(map[time.Time]bool)
		for _, match := range matches {
			uniqueDates[match.MatchDate] = true
		}

		// We expect dates from startDate to startDate+(expectedGroups-1)*7 days
		expectedDates := expectedGroups
		if len(uniqueDates) != expectedDates {
			t.Errorf("Expected %d unique match dates, got %d", expectedDates, len(uniqueDates))
		}
	})
}

// Helper function to create a consistent string representation of a player pair
func formatPair(player1ID, player2ID int32) string {
	return fmt.Sprintf("%d-%d", player1ID, player2ID)
}
