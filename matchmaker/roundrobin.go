package matchmaker

import (
	"time"
)

type Match struct {
	Player1ID int32
	Player2ID int32
	MatchDate time.Time
	Group     int32
}

type RoundRobinConfig struct {
	PlayerIDs      []int32
	StartDate      time.Time
	Frequency      string
	AmountOfTables int32
}

func GenerateRoundRobin(config RoundRobinConfig) []Match {
	matches := make([]Match, 0)
	matchDate := config.StartDate
	matchCount := 0

	// Calculate how many matches can be played simultaneously based on number of tables
	// Each table can host one match at a time
	matchesPerGroup := config.AmountOfTables

	// Generate all possible player combinations
	for i := 0; i < len(config.PlayerIDs); i++ {
		for j := i + 1; j < len(config.PlayerIDs); j++ {
			// Calculate which group this match belongs to based on available tables
			// Group numbers start from 1
			currentGroup := (int32(matchCount) / matchesPerGroup) + 1

			matches = append(matches, Match{
				Player1ID: config.PlayerIDs[i],
				Player2ID: config.PlayerIDs[j],
				MatchDate: matchDate,
				Group:     currentGroup,
			})

			matchCount++

			// If we've filled all tables for this group, move to the next match date
			if matchCount%int(matchesPerGroup) == 0 {
				matchDate = adjustMatchDate(matchDate, config.Frequency)
			}
		}
	}

	return matches
}

func adjustMatchDate(date time.Time, frequency string) time.Time {
	switch frequency {
	case "weekly":
		return date.AddDate(0, 0, 7)
	case "biweekly":
		return date.AddDate(0, 0, 14)
	case "monthly":
		return date.AddDate(0, 1, 0)
	case "quarterly":
		return date.AddDate(0, 3, 0)
	case "yearly":
		return date.AddDate(1, 0, 0)
	default:
		return date
	}
}
