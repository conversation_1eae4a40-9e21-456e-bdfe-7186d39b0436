-- name: GetUserByStripeID :one
SELECT * FROM users WHERE stripe_id = $1;
-- name: UpdateUserSubscriptionTier :exec
UPDATE users
SET subscription_tier = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $2;

-- name: UpdateMatchPlayer1 :exec
UPDATE matches
SET player_id1 = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: UpdateMatchPlayer2 :exec
UPDATE matches
SET player_id2 = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1;
-- query.sql with PostgreSQL parameter syntax

-- name: GetUserByEmail :one
SELECT id, email, is_verified
FROM users
WHERE email = $1;

-- name: GetUserByID :one
SELECT *
FROM users
WHERE id = $1;

-- name: CreateUser :one
INSERT INTO users (
    stytch_id, stripe_id, name, email, phone, country, birthday, lang, is_verified, picture_url
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
)
RETURNING *;

-- name: GetPlayers :many
SELECT * FROM players
WHERE user_id = $1 AND is_active = true;

-- name: CreatePlayer :one
INSERT INTO players (
    user_id, name, email, phone, preferred_match_group, email_notifications_enabled, team_id, picture_url
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING *;

-- name: GetPlayer :one
SELECT * FROM players
WHERE id = $1 AND user_id = $2;

-- name: UpdatePlayer :one
UPDATE players
SET name = $1,
    email = $2,
    phone = $3,
    preferred_match_group = $4,
    email_notifications_enabled = $5,
    is_active = $6,
    team_id = $7,
    picture_url = $8,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $9 AND user_id = $10
RETURNING *;

-- name: DeletePlayer :exec
DELETE FROM players
WHERE id = $1; 

-- name: GetSeasons :many
SELECT * FROM seasons
WHERE user_id = $1 AND is_active = true;

-- name: CreateSeason :one
INSERT INTO seasons (
    user_id, name, start_date, season_type, frequency
) VALUES (
    $1, $2, $3, $4, $5
)
RETURNING *;

-- name: GetSeason :one
SELECT * FROM seasons
WHERE id = $1 AND user_id = $2;

-- name: UpdateSeason :one
UPDATE seasons
SET name = $1,
    start_date = $2,
    season_type = $3,
    frequency = $4,
    is_active = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $6 AND user_id = $7
RETURNING *;

-- name: DeleteSeason :exec
DELETE FROM seasons
WHERE id = $1;

-- name: CreateMatch :one
INSERT INTO matches (
    season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, match_group
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
)
RETURNING *;

-- name: UpdateMatch :one
UPDATE matches
SET season_id = $1,
    player_id1 = $2,
    player_id1_points = $3,
    player_id2 = $4,
    player_id2_points = $5,
    match_date = $6,
    match_group = $7,
    is_active = $8,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $9
RETURNING *;

-- name: DeleteMatch :exec
DELETE FROM matches
WHERE id = $1;

-- name: GetMatch :one
SELECT * FROM matches
WHERE id = $1;

-- name: GetSeasonScoreboard :many
SELECT p.id as player_id, p.name as player_name, COUNT(m.winner_id) as wins
FROM players p
LEFT JOIN matches m ON p.id = m.winner_id AND m.season_id = $1
WHERE p.user_id = (SELECT s.user_id FROM seasons s WHERE s.id = $2)
GROUP BY p.id, p.name
ORDER BY wins DESC;

-- name: GetSeasonUpcomingMatches :many
SELECT * FROM matches
WHERE season_id = $1 AND match_date > CURRENT_TIMESTAMP
ORDER BY match_date ASC
LIMIT 5;

-- name: GetPlayerCustomColumns :many
SELECT * FROM player_custom_columns
WHERE user_id = $1 AND is_active = true
ORDER BY display_order;

-- name: CreatePlayerCustomColumn :one
INSERT INTO player_custom_columns (
    user_id, name, field_type, description, is_required, display_order
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING *;

-- name: GetPlayerCustomValues :many
SELECT pcv.*, pcc.name as column_name, pcc.field_type
FROM player_custom_values pcv
JOIN player_custom_columns pcc ON pcv.column_id = pcc.id
WHERE pcv.player_id = $1;

-- name: UpsertPlayerCustomValue :one
INSERT INTO player_custom_values (player_id, column_id, value)
VALUES ($1, $2, $3)
ON CONFLICT(player_id, column_id) DO UPDATE SET
    value = excluded.value,
    updated_at = CURRENT_TIMESTAMP
RETURNING *;

-- name: DeletePlayerCustomColumn :exec
DELETE FROM player_custom_columns
WHERE id = $1 AND user_id = $2;

-- name: GetUserAppSettings :one
SELECT json_settings FROM users
WHERE id = $1;

-- name: GetUserUserSettings :one
SELECT json_settings FROM users
WHERE id = $1;

-- name: UpdateUser :exec
UPDATE users
SET name = COALESCE(sqlc.narg('name'), name),
    email = COALESCE(sqlc.narg('email'), email),
    phone = COALESCE(sqlc.narg('phone'), phone),
    country = COALESCE(sqlc.narg('country'), country),
    birthday = COALESCE(sqlc.narg('birthday'), birthday),
    lang = COALESCE(sqlc.narg('lang'), lang),
    is_verified = COALESCE(sqlc.narg('is_verified'), is_verified),
    picture_url = COALESCE(sqlc.narg('picture_url'), picture_url),
    json_settings = COALESCE(sqlc.narg('json_settings'), json_settings),
    updated_at = CURRENT_TIMESTAMP
WHERE id = sqlc.arg('id');

-- name: GetUserSubscription :one
SELECT subscription_tier, stripe_id
FROM users
WHERE id = $1;

-- name: DeleteUserSubscription :exec
UPDATE users
SET subscription_tier = 'free',
    stripe_id = '',
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: DeleteUser :exec
UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1;

-- name: HardDeleteUser :exec
DELETE FROM users
WHERE id = $1;

-- name: CascadeDeleteUser :exec
-- This query performs a complete cascade delete of a user and all related data
-- Order matters: delete dependent tables first, then the user
WITH 
-- First, delete player custom values
deleted_player_custom_values AS (
    DELETE FROM player_custom_values
    WHERE player_id IN (SELECT id FROM players WHERE user_id = $1)
    RETURNING *
),
-- Next, delete match custom values for matches in user's seasons
deleted_match_custom_values AS (
    DELETE FROM match_custom_values
    WHERE match_id IN (
        SELECT m.id 
        FROM matches m
        JOIN seasons s ON m.season_id = s.id
        WHERE s.user_id = $1
    )
    RETURNING *
),
-- Delete all matches in user's seasons
deleted_matches AS (
    DELETE FROM matches
    WHERE season_id IN (SELECT id FROM seasons WHERE user_id = $1)
    RETURNING *
),
-- Delete all seasons
deleted_seasons AS (
    DELETE FROM seasons
    WHERE user_id = $1
    RETURNING *
),
-- Delete all players
deleted_players AS (
    DELETE FROM players
    WHERE user_id = $1
    RETURNING *
),
-- Delete all teams
deleted_teams AS (
    DELETE FROM teams
    WHERE user_id = $1
    RETURNING *
)
-- Finally delete the user
DELETE FROM users
WHERE users.id = $1;

-- name: UpdateMatchesBatch :exec
UPDATE matches
SET season_id = m.season_id,
    player_id1 = m.player_id1,
    player_id1_points = m.player_id1Points,
    player_id2 = m.player_id2,
    player_id2_points = m.player_id2Points,
    match_date = m.matchDate,
    match_group = m."group",
    is_active = m.isActive,
    updated_at = CURRENT_TIMESTAMP
FROM (SELECT * FROM UNNEST ($1::matches[])) AS m
WHERE matches.id = m.id;

-- name: GetMatchesBySeasonId :many
SELECT m.id, m.season_id, m.player_id1, p1.name AS player1_name, m.player_id1_points, m.player_id2, p2.name AS player2_name, m.player_id2_points, m.match_date, m.winner_id, m.created_at, m.updated_at, m.is_active, m.match_group
FROM matches m
LEFT JOIN players p1 ON m.player_id1 = p1.id
LEFT JOIN players p2 ON m.player_id2 = p2.id
WHERE m.season_id = $1 AND m.is_active = true
ORDER BY m.match_date DESC;

-- name: UpdateUserSideBarOpen :exec
UPDATE users
SET is_sidebar_open = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $2;

-- name: UpdateUserVerificationStatus :exec
UPDATE users
SET is_verified = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $2;

-- name: GetUserVerificationStatus :one
SELECT is_verified FROM users
WHERE id = $1;

-- name: GetUserByStytchID :one
SELECT * FROM users
WHERE stytch_id = $1;

-- name: GetMatchCustomColumns :many
SELECT * FROM match_custom_columns
WHERE is_active = true AND user_id = $1
ORDER BY display_order;

-- name: CreateMatchCustomColumn :one
INSERT INTO match_custom_columns (
    user_id, name, field_type, description, is_required, is_active, display_order
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
)
RETURNING *;

-- name: UpdatePlayerCustomColumn :one
UPDATE player_custom_columns
SET name = $1,
    field_type = $2,
    description = $3,
    is_required = $4,
    display_order = $5,
    is_active = $6,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $7 AND user_id = $8
RETURNING *;

-- name: UpdateMatchCustomColumn :one
UPDATE match_custom_columns
SET name = $1,
    field_type = $2,
    description = $3,
    is_required = $4,
    display_order = $5,
    is_active = $6,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $7
RETURNING *;

-- name: DeleteMatchCustomColumn :exec
DELETE FROM match_custom_columns
WHERE id = $1;

-- name: GetMatchCustomValues :many
SELECT mcv.*, mcc.name as column_name, mcc.field_type
FROM match_custom_values mcv
JOIN match_custom_columns mcc ON mcv.column_id = mcc.id
WHERE mcv.match_id = $1;

-- name: GetSeasonPublic :one
SELECT * FROM seasons
WHERE id = $1 AND is_active = true;

-- name: GetCustomMatchColumnsByUserID :many
SELECT * FROM match_custom_columns
WHERE user_id = $1 AND is_active = true
ORDER BY display_order;

-- name: CreateFeedback :one
INSERT INTO feedback (
    user_id, name, description, feedback_type
) VALUES (
    $1, $2, $3, $4
)
RETURNING *;

-- Email Reminders queries

-- name: ScheduleEmailReminder :one
INSERT INTO email_reminders (
    player_id, reminder_type, scheduled_at, match_id, season_id
) VALUES (
    $1, $2, $3, $4, $5
)
RETURNING *;

-- name: GetPendingEmailReminders :many
SELECT id, player_id, reminder_type, scheduled_at, retry_count, match_id, season_id
FROM email_reminders
WHERE status = 'pending'
  AND (scheduled_at <= $1 OR next_retry_at <= $1)
ORDER BY scheduled_at ASC
LIMIT $2;

-- name: MarkReminderSent :exec
UPDATE email_reminders
SET status = 'sent', sent_at = $2
WHERE id = $1;

-- name: ScheduleReminderRetry :exec
UPDATE email_reminders
SET retry_count = $2, next_retry_at = $3, last_error = $4, status = 'pending'
WHERE id = $1;

-- name: MoveReminderToDeadLetter :exec
WITH moved_reminder AS (
    UPDATE email_reminders
    SET status = 'dead_letter'
    WHERE email_reminders.id = $1
    RETURNING *
)
INSERT INTO email_dead_letter_queue
    (original_reminder_id, player_id, reminder_type, failure_reason, retry_count, last_attempt_at)
SELECT moved_reminder.id, moved_reminder.player_id, moved_reminder.reminder_type, $2, moved_reminder.retry_count, CURRENT_TIMESTAMP
FROM moved_reminder;

-- name: MarkReminderSkipped :exec
UPDATE email_reminders
SET status = 'skipped', last_error = $2
WHERE id = $1;

-- name: GetPlayersWithNotificationsEnabled :many
SELECT players.id, players.name, players.email, players.email_reminder_preferences
FROM players
WHERE players.user_id = (SELECT seasons.user_id FROM seasons WHERE seasons.id = $1)
  AND players.is_active = true
  AND players.email_notifications_enabled = true
  AND players.email IS NOT NULL AND players.email != '';

-- name: GetPlayerEmailPreferences :one
SELECT email_reminder_preferences
FROM players
WHERE id = $1;

-- name: UpdatePlayerEmailPreferences :exec
UPDATE players
SET email_reminder_preferences = $2,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: UpdatePlayerWithEmailPreferences :exec
UPDATE players
SET name = $1,
    email = $2,
    phone = $3,
    preferred_match_group = $4,
    email_notifications_enabled = $5,
    email_reminder_preferences = $6,
    is_active = $7,
    team_id = $8,
    picture_url = $9,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $10 AND user_id = $11;

-- name: GetPlayerByID :one
SELECT * FROM players
WHERE id = $1;

-- name: GetSeasonByID :one
SELECT * FROM seasons
WHERE id = $1;

-- Teams queries

-- name: GetTeams :many
SELECT * FROM teams
WHERE user_id = $1 AND is_active = true
ORDER BY name ASC;

-- name: CreateTeam :one
INSERT INTO teams (
    user_id, name, description, picture_url
) VALUES (
    $1, $2, $3, $4
)
RETURNING *;

-- name: GetTeam :one
SELECT * FROM teams
WHERE id = $1 AND user_id = $2;

-- name: UpdateTeam :one
UPDATE teams
SET name = $1,
    description = $2,
    picture_url = $3,
    is_active = $4,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $5 AND user_id = $6
RETURNING *;

-- name: DeleteTeam :exec
DELETE FROM teams
WHERE id = $1;

-- name: GetPlayersByTeam :many
SELECT * FROM players
WHERE team_id = $1 AND is_active = true
ORDER BY name ASC;

-- Notification queries

-- name: CreateNotification :one
INSERT INTO notifications (user_id, type, title, message, data, match_id, season_id, player_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
RETURNING *;

-- name: GetUserNotifications :many
SELECT * FROM notifications 
WHERE user_id = $1 
ORDER BY created_at DESC
LIMIT $2 OFFSET $3;

-- name: GetUnreadNotificationCount :one
SELECT COUNT(*) FROM notifications 
WHERE user_id = $1 AND is_read = FALSE;

-- name: MarkNotificationAsRead :exec
UPDATE notifications 
SET is_read = TRUE, read_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2;

-- name: MarkAllNotificationsAsRead :exec
UPDATE notifications 
SET is_read = TRUE, read_at = CURRENT_TIMESTAMP
WHERE user_id = $1 AND is_read = FALSE;

-- name: GetNotificationsByType :many
SELECT * FROM notifications 
WHERE user_id = $1 AND type = $2
ORDER BY created_at DESC
LIMIT $3 OFFSET $4;

-- name: GetNotificationPreferences :one
SELECT * FROM notification_preferences WHERE user_id = $1;

-- name: UpsertNotificationPreferences :one
INSERT INTO notification_preferences (user_id, in_app_enabled, match_updates, schedule_changes, results, announcements)
VALUES ($1, $2, $3, $4, $5, $6)
ON CONFLICT (user_id) 
DO UPDATE SET 
    in_app_enabled = EXCLUDED.in_app_enabled,
    match_updates = EXCLUDED.match_updates,
    schedule_changes = EXCLUDED.schedule_changes,
    results = EXCLUDED.results,
    announcements = EXCLUDED.announcements,
    updated_at = CURRENT_TIMESTAMP
RETURNING *;

-- name: DeleteNotification :exec
DELETE FROM notifications WHERE id = $1;

-- Season Permission queries

-- name: GetUserSeasonPermission :one
SELECT sp.permission_level, sp.granted_at, sp.granted_by, u.name as granted_by_name
FROM season_permissions sp
JOIN users u ON sp.granted_by = u.id
WHERE sp.season_id = $1 AND sp.user_id = $2 AND sp.is_active = true;

-- name: GetSeasonsWithPermissions :many
SELECT DISTINCT s.*, 
       COALESCE(sp.permission_level, 'owner'::permission_level_enum) as user_permission_level
FROM seasons s
LEFT JOIN season_permissions sp ON s.id = sp.season_id AND sp.user_id = $1 AND sp.is_active = true
WHERE (s.user_id = $1 OR sp.user_id = $1) AND s.is_active = true
ORDER BY s.created_at DESC;

-- name: GetSeasonPermissions :many
SELECT sp.*, u.name as user_name, u.email as user_email, 
       gb.name as granted_by_name
FROM season_permissions sp
JOIN users u ON sp.user_id = u.id
JOIN users gb ON sp.granted_by = gb.id
WHERE sp.season_id = $1 AND sp.is_active = true
ORDER BY sp.granted_at DESC;

-- name: GrantSeasonPermission :one
INSERT INTO season_permissions (season_id, user_id, permission_level, granted_by)
VALUES ($1, $2, $3, $4)
ON CONFLICT (season_id, user_id) 
DO UPDATE SET 
    permission_level = EXCLUDED.permission_level,
    granted_by = EXCLUDED.granted_by,
    granted_at = NOW(),
    is_active = true
RETURNING *;

-- name: RevokeSeasonPermission :exec
UPDATE season_permissions 
SET is_active = false
WHERE season_id = $1 AND user_id = $2;

-- name: UpdateSeasonPermission :one
UPDATE season_permissions 
SET permission_level = $3,
    granted_by = $4, 
    granted_at = NOW()
WHERE season_id = $1 AND user_id = $2 AND is_active = true
RETURNING *;

-- name: GetUserByEmailActive :one
SELECT id, name, email FROM users 
WHERE email = $1 AND is_active = true;

-- name: LogPermissionChange :exec
INSERT INTO season_permission_history 
(season_id, user_id, permission_level, action, performed_by)
VALUES ($1, $2, $3, $4, $5);

-- name: GetSeasonWithPermissionCheck :one
SELECT s.*, 
       COALESCE(sp.permission_level, 'owner'::permission_level_enum) as user_permission_level
FROM seasons s
LEFT JOIN season_permissions sp ON s.id = sp.season_id AND sp.user_id = $2 AND sp.is_active = true
WHERE s.id = $1 AND s.is_active = true
  AND (s.user_id = $2 OR sp.user_id = $2);

-- name: CheckSeasonAccess :one
SELECT 
    COALESCE(sp.permission_level, 'owner'::permission_level_enum) as permission_level
FROM seasons s
LEFT JOIN season_permissions sp ON s.id = sp.season_id AND sp.user_id = $2 AND sp.is_active = true
WHERE s.id = $1 AND s.is_active = true
  AND (s.user_id = $2 OR sp.user_id = $2);

-- API Key Management queries

-- name: CreateAPIKey :one
INSERT INTO api_keys (user_id, key_hash, name, prefix, expires_at)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: GetAPIKeysByUserID :many
SELECT id, user_id, name, prefix, last_used_at, expires_at, created_at, is_active 
FROM api_keys 
WHERE user_id = $1 AND is_active = true
ORDER BY created_at DESC;

-- name: GetAPIKeyByHash :one
SELECT * FROM api_keys 
WHERE key_hash = $1 AND is_active = true;

-- name: UpdateAPIKeyLastUsed :exec
UPDATE api_keys 
SET last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: DeleteAPIKey :exec
UPDATE api_keys 
SET is_active = false, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2;

-- name: GetAPIKeyByID :one
SELECT id, user_id, name, prefix, last_used_at, expires_at, created_at, is_active
FROM api_keys 
WHERE id = $1 AND user_id = $2 AND is_active = true;

-- name: UpdateAPIKeyName :exec
UPDATE api_keys 
SET name = $3, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2;

-- name: GetAllActiveAPIKeys :many
SELECT * FROM api_keys WHERE is_active = true;

-- Spending queries

-- name: CreateSpending :one
INSERT INTO spending (
    user_id, amount, description, date, category, file_urls
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING *;

-- name: GetSpending :one
SELECT * FROM spending
WHERE id = $1 AND user_id = $2 AND is_active = true;

-- name: GetUserSpending :many
SELECT * FROM spending
WHERE user_id = $1 AND is_active = true
ORDER BY date DESC, created_at DESC;

-- name: SearchUserSpending :many
SELECT * FROM spending
WHERE user_id = $1 AND is_active = true
  AND (
    description ILIKE '%' || $2 || '%' OR
    category ILIKE '%' || $2 || '%'
  )
ORDER BY date DESC, created_at DESC;

-- name: FilterUserSpendingByCategory :many
SELECT * FROM spending
WHERE user_id = $1 AND is_active = true
  AND category = $2
ORDER BY date DESC, created_at DESC;

-- name: FilterUserSpendingByDateRange :many
SELECT * FROM spending
WHERE user_id = $1 AND is_active = true
  AND date >= $2 AND date <= $3
ORDER BY date DESC, created_at DESC;

-- name: SearchAndFilterUserSpending :many
SELECT * FROM spending
WHERE user_id = $1 AND is_active = true
  AND (
    CASE WHEN $2::text = '' THEN true 
    ELSE (description ILIKE '%' || $2 || '%' OR category ILIKE '%' || $2 || '%')
    END
  )
  AND (
    CASE WHEN $3::text = '' THEN true 
    ELSE category = $3
    END
  )
  AND (
    CASE WHEN $4::date IS NULL THEN true 
    ELSE date >= $4
    END
  )
  AND (
    CASE WHEN $5::date IS NULL THEN true 
    ELSE date <= $5
    END
  )
ORDER BY date DESC, created_at DESC;

-- name: UpdateSpending :one
UPDATE spending
SET amount = $1,
    description = $2,
    date = $3,
    category = $4,
    file_urls = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $6 AND user_id = $7
RETURNING *;

-- name: UpdateSpendingAmount :exec
UPDATE spending
SET amount = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3;

-- name: UpdateSpendingDescription :exec
UPDATE spending
SET description = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3;

-- name: UpdateSpendingDate :exec
UPDATE spending
SET date = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3;

-- name: UpdateSpendingCategory :exec
UPDATE spending
SET category = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3;

-- name: DeleteSpending :exec
UPDATE spending
SET is_active = false, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2;

-- Spending association queries

-- name: CreateSpendingPlayerAssociation :exec
INSERT INTO spending_players (spending_id, player_id)
VALUES ($1, $2);

-- name: CreateSpendingMatchAssociation :exec
INSERT INTO spending_matches (spending_id, match_id)
VALUES ($1, $2);

-- name: CreateSpendingSeasonAssociation :exec
INSERT INTO spending_seasons (spending_id, season_id)
VALUES ($1, $2);

-- name: CreateSpendingTeamAssociation :exec
INSERT INTO spending_teams (spending_id, team_id)
VALUES ($1, $2);

-- name: GetSpendingPlayerAssociations :many
SELECT p.id, p.name FROM players p
JOIN spending_players sp ON p.id = sp.player_id
WHERE sp.spending_id = $1;

-- name: GetSpendingMatchAssociations :many
SELECT m.id, m.match_date FROM matches m
JOIN spending_matches sm ON m.id = sm.match_id
WHERE sm.spending_id = $1;

-- name: GetSpendingSeasonAssociations :many
SELECT s.id, s.name FROM seasons s
JOIN spending_seasons ss ON s.id = ss.season_id
WHERE ss.spending_id = $1;

-- name: GetSpendingTeamAssociations :many
SELECT t.id, t.name FROM teams t
JOIN spending_teams st ON t.id = st.team_id
WHERE st.spending_id = $1;

-- name: DeleteSpendingPlayerAssociations :exec
DELETE FROM spending_players WHERE spending_id = $1;

-- name: DeleteSpendingMatchAssociations :exec
DELETE FROM spending_matches WHERE spending_id = $1;

-- name: DeleteSpendingSeasonAssociations :exec
DELETE FROM spending_seasons WHERE spending_id = $1;

-- name: DeleteSpendingTeamAssociations :exec
DELETE FROM spending_teams WHERE spending_id = $1;

-- name: GetSpendingWithAssociations :many
SELECT DISTINCT s.id, s.user_id, s.amount, s.description, s.date, s.category, s.file_urls, s.created_at, s.updated_at
FROM spending s
LEFT JOIN spending_players sp ON s.id = sp.spending_id
LEFT JOIN spending_matches sm ON s.id = sm.spending_id
LEFT JOIN spending_seasons ss ON s.id = ss.spending_id
LEFT JOIN spending_teams st ON s.id = st.spending_id
WHERE s.user_id = $1 AND s.is_active = true
ORDER BY s.date DESC, s.created_at DESC;

-- Player column visibility queries

-- name: GetPlayerColumnVisibility :many
SELECT column_name, is_visible
FROM player_column_visibilities
WHERE user_id = $1
ORDER BY column_name;

-- name: UpsertPlayerColumnVisibility :exec
INSERT INTO player_column_visibilities (user_id, column_name, is_visible)
VALUES ($1, $2, $3)
ON CONFLICT (user_id, column_name) 
DO UPDATE SET 
    is_visible = EXCLUDED.is_visible,
    updated_at = CURRENT_TIMESTAMP;

-- name: DeletePlayerColumnVisibility :exec
DELETE FROM player_column_visibilities
WHERE user_id = $1 AND column_name = $2;

-- Access limits counting queries
-- name: CountUserPlayers :one
SELECT COUNT(*)::int FROM players
WHERE user_id = $1 AND is_active = true;

-- name: CountUserSeasons :one
SELECT COUNT(*)::int FROM seasons
WHERE user_id = $1 AND is_active = true;

-- name: CountUserMatches :one
SELECT COUNT(*)::int FROM matches m
JOIN seasons s ON m.season_id = s.id
WHERE s.user_id = $1 AND m.is_active = true;

-- name: CountUserTeams :one
SELECT COUNT(*)::int FROM teams
WHERE user_id = $1 AND is_active = true;
