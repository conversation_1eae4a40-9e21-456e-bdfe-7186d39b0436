# Project Structure

## Root Directory Organization

```
coachpad/
├── main.go                 # Application entry point
├── go.mod/go.sum          # Go module dependencies
├── package.json           # Node.js dependencies and scripts
├── vite.config.mjs        # Vite build configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── sqlc.yaml              # SQLC configuration for code generation
└── .air.toml              # Air hot reload configuration
```

## Core Application Directories

### `/handlers/`
HTTP request handlers organized by feature:
- `auth.go` - Authentication (sign-in, sign-up, logout)
- `players.go` - Player management CRUD operations
- `teams.go` - Team management functionality
- `seasons.go` - Season creation and management
- `matches.go` - Match scheduling and results
- `spending.go` - Financial tracking
- `settings.go` - User and app settings
- `api_*.go` - API endpoints for external integrations

### `/templates/`
gomponents-based HTML templates:
- `/app/` - Authenticated application pages
- `/public/` - Public pages (landing, sign-in)
- `/components/` - Reusable UI components
- `/ui/` - Base UI component library
- `/layouts/` - Page layout templates

### `/frontend/`
Client-side JavaScript and CSS:
- `main.js` - Entry point for Vite bundling
- `main.css` - Global styles and Tailwind imports
- `/components/` - Alpine.js components
- Individual feature modules (datepicker, validation, etc.)

### `/db/`
Database layer:
- `db.go` - Database connection and configuration
- `models.go` - Data structures matching database schema
- `queries.go` - Hand-written SQL queries
- `query.sql.go` - SQLC-generated type-safe query functions

### `/middleware/`
HTTP middleware components:
- `auth.go` - JWT authentication middleware
- `permissions.go` - Role-based access control
- `logging.go` - Request logging
- `api_auth.go` - API key authentication

### `/utils/`
Utility packages organized by functionality:
- `/pagination/` - Database pagination helpers
- `/hx/` - HTMX-specific utilities
- `/responseutils/` - HTTP response helpers
- `/matchutils/` - Match scheduling algorithms

## Infrastructure Directories

### `/migrations/`
Database schema migrations managed by Goose:
- Timestamped SQL files for schema changes
- Both up and down migrations supported

### `/deployment/`
Production deployment configurations:
- Nginx configuration files
- Systemd service files
- Environment-specific configs
- Deployment scripts and validation

### `/tests/e2e/`
Playwright end-to-end tests:
- Feature-based test organization
- Authentication state management
- Cross-browser testing setup

## Static Assets

### `/public/images/`
Static image assets:
- Logos, icons, and UI graphics
- Favicon and web app manifest files

### `/dist/` (Generated)
Vite build output:
- Bundled and minified JavaScript/CSS
- Asset manifest for cache busting
- Embedded in Go binary for production

## Configuration Files

### Environment Configuration
- `.env.*` - Environment-specific variables
- `.env.development`, `.env.staging`, `.env.production`

### Development Tools
- `.prettierrc` - Code formatting rules
- `playwright.config.js` - Test configuration
- `.gitignore` - Version control exclusions

## Architectural Patterns

### Handler Pattern
Each feature has a dedicated handler struct with methods for different HTTP operations:
```go
type PlayersHandler struct {
    Queries *db.Queries
}

func (h *PlayersHandler) RegisterRoutes(g *echo.Group) {
    g.GET("/players", h.ListPlayers)
    g.POST("/players", h.CreatePlayer)
    // ...
}
```

### Template Organization
Templates follow a hierarchical structure:
- Layouts provide page structure
- Components are reusable across features
- Feature templates compose layouts and components

### Database Layer
- SQLC generates type-safe Go code from SQL queries
- Raw SQL in `query.sql`, generated code in `query.sql.go`
- Database models defined separately from query functions

### Frontend Architecture
- Alpine.js for reactive components
- HTMX for server communication
- Vite for modern build tooling
- Tailwind for utility-first styling

## Naming Conventions

### Go Code
- PascalCase for exported functions and types
- camelCase for unexported functions and variables
- Package names are lowercase, single words when possible

### Templates
- kebab-case for HTML attributes and CSS classes
- PascalCase for gomponents function names
- Descriptive names for template files (e.g., `playerEditModal.go`)

### Database
- snake_case for table and column names
- Plural table names (e.g., `players`, `teams`)
- Foreign key naming: `{table}_id` (e.g., `team_id`)

### Frontend
- camelCase for JavaScript variables and functions
- kebab-case for CSS classes and HTML attributes
- Component files named after their primary function