# Technology Stack

## Backend
- **Language**: Go 1.24.2
- **Web Framework**: Echo v4 (HTTP router and middleware)
- **Database**: PostgreSQL with pgx/v5 driver and connection pooling
- **Query Builder**: SQLC for type-safe SQL code generation
- **Authentication**: Stytch for user authentication and JWT tokens
- **Payments**: Stripe integration for subscription management
- **Email**: Mailgun for transactional emails and notifications
- **Real-time**: Server-Sent Events (SSE) for live updates

## Frontend
- **Build Tool**: Vite 6.2.5 for asset bundling and development
- **CSS Framework**: Tailwind CSS 4.1.3 for styling
- **JavaScript Framework**: Alpine.js 3.14.9 for reactive components
- **HTMX**: 2.0.4 for dynamic HTML interactions without full page reloads
- **UI Components**: Custom component library using gomponents

## Template Engine
- **gomponents**: Type-safe HTML components in Go
- **gomponents-htmx**: HTMX integration for gomponents
- **gomponents-heroicons**: Icon library integration

## Development Tools
- **Testing**: Playwright for end-to-end testing
- **Code Formatting**: Prettier with Go template support
- **Database Migrations**: Goose for schema management
- **Hot Reload**: Air for development server auto-restart
- **Environment Management**: godotenv for configuration

## Deployment
- **Containerization**: Distrobox for consistent test environments
- **Build System**: Custom shell scripts for production builds
- **Web Server**: Nginx for reverse proxy and static file serving
- **Deployment Strategy**: Blue/Green deployment support

## Common Commands

### Development
```bash
# Start development server
npm run start

# Run tests
npm test
npm run test:ui          # Interactive test runner
npm run test:debug       # Debug mode

# Install test browsers
npm run test:install-browsers
```

### Database
```bash
# Setup development database
./bootstrap-db-dev.sh

# Run migrations
goose -dir migrations postgres $DATABASE_URL up

# Generate SQLC code
sqlc generate
```

### Build & Deploy
```bash
# Build for production
npm run build:prod
./build_prod.sh

# Quick deploy
npm run deploy:quick -- --host your-server.com

# A/B deployment
./deploy_prod.sh app.tar.gz --environment staging --host your-server.com
./deploy_prod.sh app.tar.gz --environment production --host your-server.com
```

### Code Quality
```bash
# Format Go code
go fmt ./...
goimports -w .
go mod tidy

# Format frontend code
npx prettier --write .
```

## Environment Variables
Key environment variables required for operation:
- `DATABASE_URL`: PostgreSQL connection string
- `STYTCH_PROJECT_ID`, `STYTCH_SECRET`: Authentication
- `STRIPE_PUBLISHABLE_KEY`, `STRIPE_WEBHOOK_SECRET`: Payments
- `MAILGUN_API_KEY`, `MAILGUN_DOMAIN`: Email services
- `APP_ENV`: Environment mode (development/staging/production)