# Product Overview

CoachPad is a sports team management application designed for coaches and team administrators. The platform provides comprehensive tools for managing players, teams, seasons, matches, and team finances.

## Core Features

- **Player Management**: Add, edit, and organize player information with custom fields and visibility controls
- **Team Organization**: Create and manage teams with detailed player assignments
- **Season Planning**: Multi-step season creation with automated match scheduling using round-robin algorithms
- **Match Tracking**: Record match results, scores, and statistics with real-time updates
- **Financial Management**: Track team spending, expenses, and budget management
- **Notifications**: Real-time notifications for important events and updates
- **Multi-language Support**: Available in English and French with i18n infrastructure
- **Public Schedules**: Share match schedules publicly with stakeholders
- **Email Reminders**: Automated email notifications for upcoming matches and events

## User Types

- **Coaches**: Primary users who manage teams, players, and seasons
- **Team Administrators**: Users with elevated permissions for team management
- **Players**: Can view schedules and receive notifications (via public schedule feature)

## Subscription Model

- **Free Tier**: Basic functionality for small teams
- **Pro Tier**: Advanced features with Stripe payment integration

## Technical Architecture

Built as a modern web application with server-side rendering, HTMX for dynamic interactions, and real-time features via Server-Sent Events (SSE).