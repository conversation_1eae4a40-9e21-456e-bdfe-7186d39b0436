#!/bin/bash

CONTAINER_NAME=postgres
DB_USER=coachpad
DB_PASSWORD=coachpad

# Create databases and user inside the running container
podman exec $CONTAINER_NAME psql -U postgres <<EOF
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = '$DB_USER') THEN
        CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
    END IF;
END$$;

-- Create databases if they do not exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'coachpad_development') THEN
        CREATE DATABASE coachpad_development OWNER $DB_USER;
    END IF;
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'coachpad_staging') THEN
        CREATE DATABASE coachpad_staging OWNER $DB_USER;
    END IF;
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'coachpad_production') THEN
        CREATE DATABASE coachpad_production OWNER $DB_USER;
    END IF;
END$$;

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE coachpad_development TO $DB_USER;
GRANT ALL PRIVILEGES ON DATABASE coachpad_staging TO $DB_USER;
GRANT ALL PRIVILEGES ON DATABASE coachpad_production TO $DB_USER;
EOF

echo "Databases and user setup complete."
