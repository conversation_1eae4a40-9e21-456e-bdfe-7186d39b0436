package embed

import (
	"embed"
	"io/fs"
	"log"
	"strings"
)

// embeddedFiles will be set by the main package
var embeddedFiles embed.FS

// SetEmbeddedFiles allows the main package to set the embedded filesystem
func SetEmbeddedFiles(fs embed.FS) {
	embeddedFiles = fs
}

// GetDistFS returns the embedded dist filesystem for use with Vite
func GetDistFS() fs.FS {
	log.Printf("using embedded dist filesystem")
	fsys, err := fs.Sub(embeddedFiles, "dist")
	if err != nil {
		log.Printf("error accessing embedded dist files: %v", err)
		// This should not happen in production, but fallback gracefully
		panic(err)
	}
	return fsys
}

// GetPublicFS returns the embedded public filesystem for static assets
func GetPublicFS() fs.FS {
	log.Printf("using embedded public filesystem")
	fsys, err := fs.Sub(embeddedFiles, "public")
	if err != nil {
		log.Printf("error accessing embedded public files: %v", err)
		panic(err)
	}
	return fsys
}

// GetTemplatesFS returns the embedded templates filesystem
func GetTemplatesFS() fs.FS {
	if embeddedFiles != (embed.FS{}) {
		fsys, err := fs.Sub(embeddedFiles, "templates")
		if err != nil {
			log.Printf("error accessing embedded templates files: %v", err)
			return nil
		}
		return fsys
	}
	return nil
}

// GetLocaleFile reads a locale file from embedded filesystem
func GetLocaleFile(path string) ([]byte, error) {
	// Convert "./templates/app/players/players.locales.json" -> "templates/app/players/players.locales.json"
	embeddedPath := strings.TrimPrefix(path, "./")
	return fs.ReadFile(embeddedFiles, embeddedPath)
}
