package utils

import (
	"encoding/csv"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

// CSVExportConfig holds configuration for CSV export
type CSVExportConfig struct {
	Filename    string
	ContentType string
}

// NewCSVExportConfig creates a new CSV export configuration
func NewCSVExportConfig(filename string) CSVExportConfig {
	return CSVExportConfig{
		Filename:    filename,
		ContentType: "text/csv",
	}
}

// SetupCSVResponse sets up the HTTP response headers for CSV export
func SetupCSVResponse(c echo.Context, config CSVExportConfig) *csv.Writer {
	c.Response().Header().Set("Content-Type", config.ContentType)
	c.Response().Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", config.Filename))

	writer := csv.NewWriter(c.Response().Writer)
	return writer
}

// FormatCSVDate formats a pgtype.Date for CSV output
func FormatCSVDate(date pgtype.Date) string {
	if date.Valid {
		return date.Time.Format("2006-01-02")
	}
	return ""
}

// FormatCSVInt32 formats a pgtype.Int4 for CSV output
func FormatCSVInt32(value pgtype.Int4) string {
	if value.Valid {
		return fmt.Sprintf("%d", value.Int32)
	}
	return ""
}

// FormatCSVInt64 formats a pgtype.Int8 for CSV output
func FormatCSVInt64(value pgtype.Int8) string {
	if value.Valid {
		return fmt.Sprintf("%d", value.Int64)
	}
	return ""
}

// FormatCSVBool formats a boolean for CSV output
func FormatCSVBool(value bool) string {
	if value {
		return "true"
	}
	return "false"
}

// FormatCSVTimestamp formats a pgtype.Timestamp for CSV output
func FormatCSVTimestamp(timestamp pgtype.Timestamp) string {
	if timestamp.Valid {
		return timestamp.Time.Format(time.RFC3339)
	}
	return ""
}

// FormatCSVText formats a pgtype.Text for CSV output
func FormatCSVText(text pgtype.Text) string {
	if text.Valid {
		return text.String
	}
	return ""
}

// FormatCSVNumeric formats a pgtype.Numeric for CSV output
func FormatCSVNumeric(numeric pgtype.Numeric) string {
	if numeric.Valid {
		return numeric.Int.String()
	}
	return ""
}
