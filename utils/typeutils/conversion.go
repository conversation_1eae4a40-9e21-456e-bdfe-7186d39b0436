package typeutils

import "strconv"

// GetInt32Value returns the value from an int32 pointer or 0 if nil
func GetInt32Value(ptr *int32) int32 {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// ConvertToInt32 converts interface{} to int32, handling various types
func ConvertToInt32(value interface{}) int32 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int32:
		return v
	case int:
		return int32(v)
	case float64:
		return int32(v)
	case string:
		if v == "" {
			return 0
		}
		if i, err := strconv.Atoi(v); err == nil {
			return int32(i)
		}
		return 0
	default:
		return 0
	}
}
