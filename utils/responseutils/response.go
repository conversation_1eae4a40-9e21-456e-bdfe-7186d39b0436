package responseutils

import (
	"github.com/j-em/coachpad/utils/jsonutils"
	"github.com/labstack/echo/v4"
)

// SendJSON is a helper function to marshal JSON responses using jsonutils.Marshal for CamelCase keys
func SendJSON(c echo.Context, statusCode int, data interface{}) error {
	jsonData, err := jsonutils.Marshal(data)
	if err != nil {
		return c.String(500, "Failed to marshal JSO<PERSON>: "+err.<PERSON>rror())
	}
	return c.Blob(statusCode, "application/json", jsonData)
}
