package jsonutils

import (
	"encoding/json"
	"io"
)

// Encoder wraps the standard JSON encoder but transforms field names to camelCase
type Encoder struct {
	encoder *json.Encoder
}

// NewEncoder returns a new encoder that writes to w
func NewEncoder(w io.Writer) *Encoder {
	return &Encoder{
		encoder: json.NewEncoder(w),
	}
}

// Encode writes the JSON encoding of v to the stream with camelCase field names
func (e *Encoder) Encode(v interface{}) error {
	return e.encoder.Encode(CamelCaseJSON{v})
}

// SetIndent controls formatting of subsequent encoded values
func (e *Encoder) SetIndent(prefix, indent string) {
	e.encoder.SetIndent(prefix, indent)
}

// SetEscapeHTML specifies whether problematic HTML characters should be escaped
func (e *Encoder) SetEscapeHTML(on bool) {
	e.encoder.SetEscapeHTML(on)
}
