package jsonutils

import (
	"bytes"
	"encoding/json"
	"unicode"
)

// CamelCaseJSON is a type that will marshal Go struct field names to camelCase in JSON
type CamelCaseJSON struct {
	Value interface{}
}

// MarshalJSON implements the json.Marshaler interface
func (c CamelCaseJSON) MarshalJSON() ([]byte, error) {
	// First marshal the original value
	b, err := json.Marshal(c.Value)
	if err != nil {
		return nil, err
	}

	// For nil values or primitives, return as is
	if c.Value == nil || !isStructOrMap(c.Value) {
		return b, nil
	}

	// For complex structures, parse JSON and transform keys
	var result interface{}
	if err := json.Unmarshal(b, &result); err != nil {
		return nil, err
	}

	// Transform to camelCase
	transformed := transformKeys(result)

	// Marshal the transformed value back to JSON
	return json.Marshal(transformed)
}

// isStructOrMap checks if the value is a struct or map
func isStructOrMap(v interface{}) bool {
	switch v.(type) {
	case map[string]interface{}, []interface{}:
		return true
	default:
		// Check if it's a struct using reflection indirectly via JSON marshaling
		b, err := json.Marshal(v)
		if err != nil {
			return false
		}
		return bytes.HasPrefix(b, []byte{'{'}) || bytes.HasPrefix(b, []byte{'['})
	}
}

// transformKeys recursively transforms keys in maps to camelCase
func transformKeys(v interface{}) interface{} {
	switch val := v.(type) {
	case map[string]interface{}:
		// Create a new map with camelCase keys
		m := make(map[string]interface{})
		for k, v := range val {
			m[toCamelCase(k)] = transformKeys(v)
		}
		return m
	case []interface{}:
		// Process each element in the array
		for i, item := range val {
			val[i] = transformKeys(item)
		}
		return val
	default:
		// Return primitive values as-is
		return val
	}
}

// toCamelCase converts PascalCase to camelCase
func toCamelCase(s string) string {

	if s == "" {
		return s
	}

	// if only two letters are there return two letters lower case
	if len(s) == 2 {
		return string(unicode.ToLower(rune(s[0]))) + string(unicode.ToLower(rune(s[1])))
	}

	// For PascalCase, just convert first character to lowercase
	runes := []rune(s)
	runes[0] = unicode.ToLower(runes[0])
	return string(runes)
}

// Marshal is a convenience function to marshal a value to camelCase JSON
func Marshal(v interface{}) ([]byte, error) {
	return CamelCaseJSON{v}.MarshalJSON()
}

// MarshalIndent is like Marshal but applies Indent to format the output
func MarshalIndent(v interface{}, prefix, indent string) ([]byte, error) {
	b, err := Marshal(v)
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	err = json.Indent(&buf, b, prefix, indent)
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}
