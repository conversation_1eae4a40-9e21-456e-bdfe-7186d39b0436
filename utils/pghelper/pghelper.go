package pghelper

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// NewInt4 creates a new pgtype.Int4 with the given value
func NewInt4(value int32) pgtype.Int4 {
	return pgtype.Int4{
		Int32: value,
		Valid: true,
	}
}

// NewText creates a new pgtype.Text with the given value
func NewText(value string) pgtype.Text {
	return pgtype.Text{
		String: value,
		Valid:  true,
	}
}

// GetInt32 safely extracts int32 from pgtype.Int4, returning fallback if invalid
func GetInt32(value pgtype.Int4, fallback int32) int32 {
	if value.Valid {
		return value.Int32
	}
	return fallback
}

// GetInt32OrZero safely extracts int32 from pgtype.Int4, returning 0 if invalid
func GetInt32OrZero(value pgtype.Int4) int32 {
	return GetInt32(value, 0)
}

// GetInt32Ptr safely extracts int32 from pgtype.Int4, returning nil if invalid
func GetInt32Ptr(value pgtype.Int4) *int32 {
	if value.Valid {
		return &value.Int32
	}
	return nil
}

// GetString safely extracts string from pgtype.Text, returning fallback if invalid
func GetString(value pgtype.Text, fallback string) string {
	if value.Valid {
		return value.String
	}
	return fallback
}

// GetStringOrEmpty safely extracts string from pgtype.Text, returning empty string if invalid
func GetStringOrEmpty(value pgtype.Text) string {
	return GetString(value, "")
}

// GetStringPtr safely extracts string from pgtype.Text, returning nil if invalid
func GetStringPtr(value pgtype.Text) *string {
	if value.Valid {
		return &value.String
	}
	return nil
}

// GetBool safely extracts bool from pgtype.Bool, returning fallback if invalid
func GetBool(value pgtype.Bool, fallback bool) bool {
	if value.Valid {
		return value.Bool
	}
	return fallback
}

// GetBoolOrFalse safely extracts bool from pgtype.Bool, returning false if invalid
func GetBoolOrFalse(value pgtype.Bool) bool {
	return GetBool(value, false)
}

// GetBoolPtr safely extracts bool from pgtype.Bool, returning nil if invalid
func GetBoolPtr(value pgtype.Bool) *bool {
	if value.Valid {
		return &value.Bool
	}
	return nil
}

// GetTime safely extracts time.Time from pgtype.Timestamp, returning fallback if invalid
func GetTime(value pgtype.Timestamp, fallback time.Time) time.Time {
	if value.Valid {
		return value.Time
	}
	return fallback
}

// GetTimeOrZero safely extracts time.Time from pgtype.Timestamp, returning zero time if invalid
func GetTimeOrZero(value pgtype.Timestamp) time.Time {
	return GetTime(value, time.Time{})
}

// GetTimePtr safely extracts time.Time from pgtype.Timestamp, returning nil if invalid
func GetTimePtr(value pgtype.Timestamp) *time.Time {
	if value.Valid {
		return &value.Time
	}
	return nil
}

// GetDate safely extracts time.Time from pgtype.Date, returning fallback if invalid
func GetDate(value pgtype.Date, fallback time.Time) time.Time {
	if value.Valid {
		return value.Time
	}
	return fallback
}

// GetDateOrZero safely extracts time.Time from pgtype.Date, returning zero time if invalid
func GetDateOrZero(value pgtype.Date) time.Time {
	return GetDate(value, time.Time{})
}

// GetDatePtr safely extracts time.Time from pgtype.Date, returning nil if invalid
func GetDatePtr(value pgtype.Date) *time.Time {
	if value.Valid {
		return &value.Time
	}
	return nil
}

// GetTimestamptz safely extracts time.Time from pgtype.Timestamptz, returning fallback if invalid
func GetTimestamptz(value pgtype.Timestamptz, fallback time.Time) time.Time {
	if value.Valid {
		return value.Time
	}
	return fallback
}

// GetTimestamptzOrZero safely extracts time.Time from pgtype.Timestamptz, returning zero time if invalid
func GetTimestamptzOrZero(value pgtype.Timestamptz) time.Time {
	return GetTimestamptz(value, time.Time{})
}

// GetTimestamptzPtr safely extracts time.Time from pgtype.Timestamptz, returning nil if invalid
func GetTimestamptzPtr(value pgtype.Timestamptz) *time.Time {
	if value.Valid {
		return &value.Time
	}
	return nil
}

// GetFloat64 safely extracts float64 from pgtype.Numeric, returning fallback if invalid
func GetFloat64(value pgtype.Numeric, fallback float64) float64 {
	if value.Valid {
		f64, err := value.Float64Value()
		if err != nil {
			return fallback
		}
		return f64.Float64
	}
	return fallback
}

// GetFloat64OrZero safely extracts float64 from pgtype.Numeric, returning 0.0 if invalid
func GetFloat64OrZero(value pgtype.Numeric) float64 {
	return GetFloat64(value, 0.0)
}

// GetFloat64Ptr safely extracts float64 from pgtype.Numeric, returning nil if invalid
func GetFloat64Ptr(value pgtype.Numeric) *float64 {
	if value.Valid {
		f64, err := value.Float64Value()
		if err != nil {
			return nil
		}
		val := f64.Float64
		return &val
	}
	return nil
}

// IsValid checks if a pgtype value is valid (generic interface check)
func IsValid(value interface{}) bool {
	switch v := value.(type) {
	case pgtype.Int4:
		return v.Valid
	case pgtype.Text:
		return v.Valid
	case pgtype.Bool:
		return v.Valid
	case pgtype.Timestamp:
		return v.Valid
	case pgtype.Date:
		return v.Valid
	case pgtype.Timestamptz:
		return v.Valid
	case pgtype.Numeric:
		return v.Valid
	default:
		return false
	}
}

// CoalesceInt32 returns the first valid int32 value, or fallback if all are invalid
func CoalesceInt32(fallback int32, values ...pgtype.Int4) int32 {
	for _, v := range values {
		if v.Valid {
			return v.Int32
		}
	}
	return fallback
}

// CoalesceString returns the first valid string value, or fallback if all are invalid
func CoalesceString(fallback string, values ...pgtype.Text) string {
	for _, v := range values {
		if v.Valid {
			return v.String
		}
	}
	return fallback
}

// CoalesceTime returns the first valid time value, or fallback if all are invalid
func CoalesceTime(fallback time.Time, values ...pgtype.Timestamp) time.Time {
	for _, v := range values {
		if v.Valid {
			return v.Time
		}
	}
	return fallback
}
