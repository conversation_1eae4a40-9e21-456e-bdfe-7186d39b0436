// Package utils contains utility constants and functions for htmx event handling.
//
// This package provides a type-safe way to dispatch HTMX events with structured data payloads.
// It defines common event types used throughout the application and provides helper functions
// for dispatching them with compile-time type safety and runtime validation.
//
// Basic Usage:
//
//	// Simple notification events (no data)
//	utils.SetTypedTrigger(c, utils.HXEventPlayersUpdated, utils.PlayersUpdatedEvent{})
//
//	// Events with structured data
//	utils.SetTypedTrigger(c, utils.HXEventUpdateMatch, utils.NewUpdateMatchEvent(123))
//
//	// Multiple events in one response
//	utils.SetMultipleTypedTriggers(c, []utils.TypedEventTrigger{
//	    {utils.HXEventPlayersUpdated, utils.PlayersUpdatedEvent{}},
//	    {utils.HXEventCloseAllModals, utils.CloseAllModalsEvent{}},
//	})
//
// Migration from Existing Code:
//
//	// Old way (untyped):
//	hx.SetTrigger(c, "playersUpdated", nil)
//	hx.SetTrigger(c, "updateMatch", matchID)
//
//	// New way (type-safe):
//	utils.SetTypedTrigger(c, utils.HXEventPlayersUpdated, utils.PlayersUpdatedEvent{})
//	utils.SetTypedTrigger(c, utils.HXEventUpdateMatch, utils.NewUpdateMatchEvent(matchID))
package utils

import (
	"encoding/json"
	"fmt"

	"github.com/labstack/echo/v4"
)

// HX event names dispatched in the app. Use these constants to avoid typos.
const (
	HXEventUpdateMatch         = "updateMatch"
	HXEventUpdateSeason        = "updateSeason"
	HXEventPlayersUpdated      = "playersUpdated"
	HXEventTeamsUpdated        = "teamsUpdated"
	HXEventUpdateSpendings     = "updateSpendings"
	HXEventSettingsUpdated     = "settingsUpdated"
	HXEventCloseAllModals      = "close-all-modals"
	HXEventCopyToClipboard     = "copyToClipboard"
	HXEventRefreshApiKeysTable = "refreshApiKeysTable"
	HXEventRefreshNavigation   = "refresh-navigation"
)

// EventData represents the base interface for all HTMX event data payloads.
// All event data types should implement this interface for type safety.
type EventData interface {
	// Validate ensures the event data is valid before dispatch
	Validate() error
}

// NoEventData represents events that don't carry any data payload.
// Used for simple notification events like "playersUpdated", "teamsUpdated", etc.
type NoEventData struct{}

func (NoEventData) Validate() error { return nil }

// IDEventData represents events that carry a single ID as payload.
// Common for update events like "updateMatch", "updateSeason", etc.
type IDEventData struct {
	ID int64 `json:"id"`
}

func (d IDEventData) Validate() error {
	if d.ID <= 0 {
		return fmt.Errorf("ID must be positive, got %d", d.ID)
	}
	return nil
}

// StringEventData represents events that carry a single string as payload.
// Used for events like "copyToClipboard", "showMessage", etc.
type StringEventData struct {
	Value string `json:"value"`
}

func (d StringEventData) Validate() error {
	if d.Value == "" {
		return fmt.Errorf("string value cannot be empty")
	}
	return nil
}

// Event type definitions for specific events
type (
	// UpdateMatchEvent carries match ID for match update notifications
	UpdateMatchEvent = IDEventData

	// UpdateSeasonEvent carries season ID for season update notifications
	UpdateSeasonEvent = IDEventData

	// PlayersUpdatedEvent signals that players table needs refresh (no data)
	PlayersUpdatedEvent = NoEventData

	// TeamsUpdatedEvent signals that teams table needs refresh (no data)
	TeamsUpdatedEvent = NoEventData

	// UpdateSpendingsEvent signals that spending table needs refresh (no data)
	UpdateSpendingsEvent = NoEventData

	// SettingsUpdatedEvent signals that settings have changed (no data)
	SettingsUpdatedEvent = NoEventData

	// CloseAllModalsEvent signals all modals should close (no data)
	CloseAllModalsEvent = NoEventData

	// CopyToClipboardEvent carries text to copy to clipboard
	CopyToClipboardEvent = StringEventData

	// RefreshApiKeysTableEvent signals that API keys table needs refresh (no data)
	RefreshApiKeysTableEvent = NoEventData

	// RefreshNavigationEvent signals that navigation needs refresh (no data)
	RefreshNavigationEvent = NoEventData
)

// Common event data constructors for convenience
func NewUpdateMatchEvent(matchID int64) UpdateMatchEvent {
	return UpdateMatchEvent{ID: matchID}
}

func NewUpdateSeasonEvent(seasonID int64) UpdateSeasonEvent {
	return UpdateSeasonEvent{ID: seasonID}
}

func NewCopyToClipboardEvent(text string) CopyToClipboardEvent {
	return CopyToClipboardEvent{Value: text}
}

// Type-safe event dispatch helpers
// These functions provide compile-time type safety when dispatching events

// SetTypedTrigger dispatches a typed HTMX event with validation.
// This provides compile-time type safety and runtime validation.
//
// Example usage:
//
//	// Dispatch a match update event with match ID
//	SetTypedTrigger(c, HXEventUpdateMatch, NewUpdateMatchEvent(123))
//
//	// Dispatch a simple notification event (no data)
//	SetTypedTrigger(c, HXEventPlayersUpdated, PlayersUpdatedEvent{})
//
//	// Dispatch a clipboard copy event
//	SetTypedTrigger(c, HXEventCopyToClipboard, NewCopyToClipboardEvent("https://example.com"))
func SetTypedTrigger(c echo.Context, eventName string, data EventData) error {
	if err := data.Validate(); err != nil {
		return fmt.Errorf("invalid event data for %s: %w", eventName, err)
	}

	var payload any
	switch data.(type) {
	case NoEventData:
		payload = nil
	default:
		payload = data
	}

	trigger := map[string]any{eventName: payload}
	b, err := json.Marshal(trigger)
	if err != nil {
		return fmt.Errorf("failed to marshal event %s: %w", eventName, err)
	}

	c.Response().Header().Set("HX-Trigger", string(b))
	return nil
}

// TypedEventTrigger represents a typed event trigger for use with SetMultipleTypedTriggers
type TypedEventTrigger struct {
	EventName string
	Data      EventData
}

// SetMultipleTypedTriggers dispatches multiple typed HTMX events with validation.
// This provides compile-time type safety and runtime validation for multiple events.
//
// Example usage:
//
//	// Close modal and refresh players table after successful creation
//	SetMultipleTypedTriggers(c, []TypedEventTrigger{
//	    {HXEventPlayersUpdated, PlayersUpdatedEvent{}},
//	    {HXEventCloseAllModals, CloseAllModalsEvent{}},
//	})
//
//	// Update multiple entities after a season change
//	SetMultipleTypedTriggers(c, []TypedEventTrigger{
//	    {HXEventUpdateSeason, NewUpdateSeasonEvent(seasonID)},
//	    {HXEventPlayersUpdated, PlayersUpdatedEvent{}},
//	    {HXEventTeamsUpdated, TeamsUpdatedEvent{}},
//	})
func SetMultipleTypedTriggers(c echo.Context, triggers []TypedEventTrigger) error {
	triggerMap := make(map[string]any)

	for _, trigger := range triggers {
		if err := trigger.Data.Validate(); err != nil {
			return fmt.Errorf("invalid event data for %s: %w", trigger.EventName, err)
		}

		var payload any
		switch trigger.Data.(type) {
		case NoEventData:
			payload = nil
		default:
			payload = trigger.Data
		}

		triggerMap[trigger.EventName] = payload
	}

	b, err := json.Marshal(triggerMap)
	if err != nil {
		return fmt.Errorf("failed to marshal events: %w", err)
	}

	c.Response().Header().Set("HX-Trigger", string(b))
	return nil
}
