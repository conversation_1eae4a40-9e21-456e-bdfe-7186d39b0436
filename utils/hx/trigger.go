package hx

import (
	"encoding/json"

	"github.com/labstack/echo/v4"
)

// Set<PERSON><PERSON><PERSON> sets the HX-Trigger header for htmx responses.
//
// This ensures the event is always sent as a JSON object, with the event name as the key
// and the argument as the value. If arg is nil, the value will be null in the JSON.
//
// Example usage:
//
//	hx.SetTrigger(c, hx.EventUpdateMatch, matchId) // {"updateMatch": 123}
//	hx.SetTrigger(c, hx.EventPlayersUpdated, nil) // {"playersUpdated": null}
func SetTrigger(c echo.Context, event string, arg interface{}) {
	trigger := map[string]interface{}{event: arg}
	b, _ := json.Marshal(trigger)
	c.Response().Header().Set("HX-Trigger", string(b))
}

// SetMultipleTriggers sets the HX-Trigger header with multiple events for htmx responses.
//
// This allows triggering multiple events in a single response.
//
// Example usage:
//
//	hx.SetMultipleTriggers(c, map[string]interface{}{
//		hx.EventPlayersUpdated: nil,
//		hx.EventCloseAllModals: nil,
//	})
func SetMultipleTriggers(c echo.Context, triggers map[string]interface{}) {
	b, _ := json.Marshal(triggers)
	c.Response().Header().Set("HX-Trigger", string(b))
}
