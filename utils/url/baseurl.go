package urlutil

import (
	"net/url"

	"github.com/labstack/echo/v4"
)

// GetBaseURL returns the scheme and host for the current request, considering proxies.
func GetBaseURL(c echo.Context) string {
	req := c.Request()
	scheme := "http"
	if req.TLS != nil {
		scheme = "https"
	}
	// Prefer X-Forwarded-Proto if present (for reverse proxies)
	if proto := req.Header.Get("X-Forwarded-Proto"); proto != "" {
		scheme = proto
	}
	host := req.Host
	// Prefer X-Forwarded-Host if present (for reverse proxies)
	if forwardedHost := req.Header.Get("X-Forwarded-Host"); forwardedHost != "" {
		host = forwardedHost
	}
	u := url.URL{
		Scheme: scheme,
		Host:   host,
	}
	return u.String()
}
