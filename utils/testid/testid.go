package testid

import "fmt"

// Generate creates a standardized test ID using kebab-case convention
// This helper function ensures consistent formatting and naming throughout the application
func Generate(parts ...interface{}) string {
	if len(parts) == 0 {
		return ""
	}

	// Convert first part to string
	result := fmt.Sprintf("%v", parts[0])

	// Append remaining parts with hyphens
	for i := 1; i < len(parts); i++ {
		result += fmt.Sprintf("-%v", parts[i])
	}

	return result
}

// GenerateWithPrefix creates a test ID with a common prefix
// Useful for component-specific test IDs
func GenerateWithPrefix(prefix string, parts ...interface{}) string {
	if prefix == "" {
		return Generate(parts...)
	}

	allParts := make([]interface{}, 0, len(parts)+1)
	allParts = append(allParts, prefix)
	allParts = append(allParts, parts...)

	return Generate(allParts...)
}

// Examples:
// Generate("player", 1, "name") => "player-1-name"
// GenerateWithPrefix("match", "player", 1, "dropdown") => "match-player-1-dropdown"
// Generate("season", "actions", "dropdown") => "season-actions-dropdown"
