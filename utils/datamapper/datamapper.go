package datamapper

import (
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/utils/pghelper"

	"github.com/jackc/pgx/v5/pgtype"
)

// ConvertSeasonsWithPermissions converts GetSeasonsWithPermissionsRow slice to Season slice
func ConvertSeasonsWithPermissions(rows []db.GetSeasonsWithPermissionsRow) []db.Season {
	if rows == nil {
		return nil
	}

	seasons := make([]db.Season, len(rows))
	for i, row := range rows {
		seasons[i] = ConvertSeasonWithPermissions(row)
	}
	return seasons
}

// ConvertSeasonWithPermissions converts a single GetSeasonsWithPermissionsRow to Season
func ConvertSeasonWithPermissions(row db.GetSeasonsWithPermissionsRow) db.Season {
	return db.Season{
		ID:         row.ID,
		UserID:     row.UserID,
		Name:       row.Name,
		StartDate:  row.StartDate,
		CreatedAt:  row.CreatedAt,
		UpdatedAt:  row.UpdatedAt,
		IsActive:   row.IsActive,
		SeasonType: row.SeasonType,
		Frequency:  row.Frequency,
	}
}

// ConvertSeasonsWithPermissionsToSeasonWithPermissions converts GetSeasonsWithPermissionsRow slice to custom struct
type SeasonWithPermissions struct {
	Season          db.Season
	PermissionLevel db.PermissionLevelEnum
}

func ConvertSeasonsWithPermissionsToSeasonWithPermissions(rows []db.GetSeasonsWithPermissionsRow) []SeasonWithPermissions {
	if rows == nil {
		return nil
	}

	result := make([]SeasonWithPermissions, len(rows))
	for i, row := range rows {
		result[i] = SeasonWithPermissions{
			Season:          ConvertSeasonWithPermissions(row),
			PermissionLevel: row.UserPermissionLevel,
		}
	}
	return result
}

// ConvertPlayersToPlayerSummary converts player rows to a summary format
type PlayerSummary struct {
	ID                        int32
	UserID                    int32
	TeamID                    *int32
	Name                      string
	Email                     *string
	Phone                     *string
	PictureUrl                *string
	PreferredMatchGroup       int32
	IsActive                  bool
	EmailNotificationsEnabled bool
}

func ConvertPlayersToPlayerSummary(rows []db.Player) []PlayerSummary {
	if rows == nil {
		return nil
	}

	players := make([]PlayerSummary, len(rows))
	for i, row := range rows {
		players[i] = PlayerSummary{
			ID:                        row.ID,
			UserID:                    row.UserID,
			TeamID:                    pghelper.GetInt32Ptr(row.TeamID),
			Name:                      row.Name,
			Email:                     pghelper.GetStringPtr(row.Email),
			Phone:                     pghelper.GetStringPtr(row.Phone),
			PictureUrl:                pghelper.GetStringPtr(row.PictureUrl),
			PreferredMatchGroup:       row.PreferredMatchGroup,
			IsActive:                  row.IsActive,
			EmailNotificationsEnabled: row.EmailNotificationsEnabled,
		}
	}
	return players
}

// ConvertMatchesToMatchSummary converts match rows to a summary format
type MatchSummary struct {
	ID            int32
	SeasonID      int32
	Player1ID     *int32
	Player1Name   string
	Player1Points *int32
	Player2ID     *int32
	Player2Name   string
	Player2Points *int32
	MatchDate     *string
	WinnerID      *int32
	WinnerName    string
	MatchGroup    int32
}

func ConvertMatchesToMatchSummary(rows []db.GetMatchesBySeasonIdRow) []MatchSummary {
	if rows == nil {
		return nil
	}

	matches := make([]MatchSummary, len(rows))
	for i, row := range rows {
		// Determine winner name based on winner ID
		var winnerName string
		if winnerID := pghelper.GetInt32Ptr(row.WinnerID); winnerID != nil {
			if player1ID := pghelper.GetInt32Ptr(row.PlayerId1); player1ID != nil && *winnerID == *player1ID {
				winnerName = pghelper.GetStringOrEmpty(row.Player1Name)
			} else if player2ID := pghelper.GetInt32Ptr(row.PlayerId2); player2ID != nil && *winnerID == *player2ID {
				winnerName = pghelper.GetStringOrEmpty(row.Player2Name)
			}
		}

		matches[i] = MatchSummary{
			ID:            row.ID,
			SeasonID:      pghelper.GetInt32OrZero(row.SeasonID),
			Player1ID:     pghelper.GetInt32Ptr(row.PlayerId1),
			Player1Name:   pghelper.GetStringOrEmpty(row.Player1Name),
			Player1Points: pghelper.GetInt32Ptr(row.PlayerId1Points),
			Player2ID:     pghelper.GetInt32Ptr(row.PlayerId2),
			Player2Name:   pghelper.GetStringOrEmpty(row.Player2Name),
			Player2Points: pghelper.GetInt32Ptr(row.PlayerId2Points),
			MatchDate:     getFormattedDate(row.MatchDate),
			WinnerID:      pghelper.GetInt32Ptr(row.WinnerID),
			WinnerName:    winnerName,
			MatchGroup:    row.MatchGroup,
		}
	}
	return matches
}

// ConvertUsersToUserSummary converts user rows to a summary format
type UserSummary struct {
	ID               int32
	StytchID         string
	Name             string
	Email            string
	Country          string
	Phone            *string
	Birthday         *string
	Lang             string
	PictureUrl       *string
	IsActive         bool
	IsVerified       bool
	SubscriptionTier string
	IsSidebarOpen    bool
}

func ConvertUsersToUserSummary(rows []db.User) []UserSummary {
	if rows == nil {
		return nil
	}

	users := make([]UserSummary, len(rows))
	for i, row := range rows {
		users[i] = UserSummary{
			ID:               row.ID,
			StytchID:         row.StytchID,
			Name:             row.Name,
			Email:            row.Email,
			Country:          row.Country,
			Phone:            pghelper.GetStringPtr(row.Phone),
			Birthday:         getFormattedDate(row.Birthday),
			Lang:             row.Lang,
			PictureUrl:       pghelper.GetStringPtr(row.PictureUrl),
			IsActive:         row.IsActive,
			IsVerified:       row.IsVerified,
			SubscriptionTier: row.SubscriptionTier,
			IsSidebarOpen:    row.IsSidebarOpen,
		}
	}
	return users
}

// ConvertTeamsToTeamSummary converts team rows to a summary format
type TeamSummary struct {
	ID          int32
	UserID      int32
	Name        string
	Description *string
	PictureUrl  *string
	IsActive    bool
}

func ConvertTeamsToTeamSummary(rows []db.Team) []TeamSummary {
	if rows == nil {
		return nil
	}

	teams := make([]TeamSummary, len(rows))
	for i, row := range rows {
		teams[i] = TeamSummary{
			ID:          row.ID,
			UserID:      row.UserID,
			Name:        row.Name,
			Description: pghelper.GetStringPtr(row.Description),
			PictureUrl:  pghelper.GetStringPtr(row.PictureUrl),
			IsActive:    row.IsActive,
		}
	}
	return teams
}

// Helper function to format dates consistently
func getFormattedDate(date interface{}) *string {
	switch d := date.(type) {
	case pgtype.Date:
		if d.Valid {
			formatted := d.Time.Format("2006-01-02")
			return &formatted
		}
	case pgtype.Timestamp:
		if d.Valid {
			formatted := d.Time.Format("2006-01-02 15:04:05")
			return &formatted
		}
	case pgtype.Timestamptz:
		if d.Valid {
			formatted := d.Time.Format("2006-01-02 15:04:05")
			return &formatted
		}
	}
	return nil
}

// Generic conversion utilities

// ConvertSliceWithFunc applies a conversion function to each element in a slice
func ConvertSliceWithFunc[T any, U any](slice []T, converter func(T) U) []U {
	if slice == nil {
		return nil
	}

	result := make([]U, len(slice))
	for i, item := range slice {
		result[i] = converter(item)
	}
	return result
}

// ConvertSliceWithFuncFiltered applies a conversion function to each element in a slice with filtering
func ConvertSliceWithFuncFiltered[T any, U any](slice []T, converter func(T) U, filter func(T) bool) []U {
	if slice == nil {
		return nil
	}

	var result []U
	for _, item := range slice {
		if filter(item) {
			result = append(result, converter(item))
		}
	}
	return result
}

// ConvertSliceWithError applies a conversion function that can return errors
func ConvertSliceWithError[T any, U any](slice []T, converter func(T) (U, error)) ([]U, error) {
	if slice == nil {
		return nil, nil
	}

	result := make([]U, len(slice))
	for i, item := range slice {
		converted, err := converter(item)
		if err != nil {
			return nil, err
		}
		result[i] = converted
	}
	return result, nil
}

// SafeConvertSlice converts a slice with error handling, returning partial results on error
func SafeConvertSlice[T any, U any](slice []T, converter func(T) (U, error)) ([]U, []error) {
	if slice == nil {
		return nil, nil
	}

	result := make([]U, 0, len(slice))
	errors := make([]error, 0)

	for _, item := range slice {
		converted, err := converter(item)
		if err != nil {
			errors = append(errors, err)
		} else {
			result = append(result, converted)
		}
	}

	return result, errors
}
