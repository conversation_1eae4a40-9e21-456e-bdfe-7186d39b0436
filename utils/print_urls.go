package utils

import (
	"fmt"
	"net/url"
)

// PrintURLParams represents common print URL parameters
type PrintURLParams struct {
	BaseURL      string
	Page         int
	ItemsPerPage int
	Sort         string
	Direction    string
	Search       string
	ExtraParams  map[string]string
}

// BuildPrintURL creates a JavaScript window.open call with query parameters
func BuildPrintURL(params PrintURLParams) string {
	queryParams := fmt.Sprintf("print=true&page=%d&per_page=%d&sort=%s&dir=%s",
		params.Page, params.ItemsPerPage,
		url.QueryEscape(params.Sort), url.QueryEscape(params.Direction))

	if params.Search != "" {
		queryParams += "&search=" + url.QueryEscape(params.Search)
	}

	// Add any extra parameters
	for key, value := range params.ExtraParams {
		queryParams += "&" + key + "=" + url.QueryEscape(value)
	}

	return fmt.Sprintf("window.open('%s?%s', '_blank', 'width=800,height=600')", params.BaseURL, queryParams)
}

// BuildPrintButton creates a consistent print button attribute
func BuildPrintButton(printURL string) string {
	return fmt.Sprintf(`onclick="%s"`, printURL)
}
