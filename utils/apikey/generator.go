package apikey

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"os"
	"strings"
)

// GenerateAPIKey creates a new API key with the format:
// cpb_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (production)
// cpb_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (development)
func GenerateAPIKey() (string, error) {
	// Determine environment
	environment := "live"
	if os.Getenv("ENVIRONMENT") == "development" {
		environment = "test"
	}

	// Generate 32 random bytes
	randomBytes := make([]byte, 32)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Encode to base64 and make URL-safe
	encoded := base64.URLEncoding.EncodeToString(randomBytes)
	// Remove padding
	encoded = strings.TrimRight(encoded, "=")

	// Create the full key
	fullKey := fmt.Sprintf("cpb_%s_%s", environment, encoded)

	return fullKey, nil
}

// ExtractPrefix extracts the prefix from an API key (e.g., "cpb_live_" from full key)
func ExtractPrefix(apiKey string) string {
	if len(apiKey) < 9 {
		return ""
	}
	return apiKey[:9] // "cpb_live_" or "cpb_test_"
}

// ValidateFormat validates that an API key has the correct format
func ValidateFormat(key string) bool {
	// Expected format: cpb_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx or cpb_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
	if len(key) != 44 {
		return false
	}

	if !strings.HasPrefix(key, "cpb_live_") && !strings.HasPrefix(key, "cpb_test_") {
		return false
	}

	return true
}

// MaskAPIKey returns a masked version of the API key for display (e.g., "cpb_live_abc123...")
func MaskAPIKey(key string) string {
	if len(key) < 12 {
		return key
	}

	prefix := key[:9]       // "cpb_live_" or "cpb_test_"
	firstThree := key[9:12] // First 3 chars after prefix

	return fmt.Sprintf("%s%s...", prefix, firstThree)
}
