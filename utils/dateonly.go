package utils

import (
	"encoding"
	"fmt"
	"strings"
	"time"
)

// DateOnly is a custom type for parsing date strings in RFC3339 or "2006-01-02" formats.
type DateOnly struct {
	time.Time
}

var _ encoding.TextUnmarshaler = (*DateOnly)(nil)

func (d *DateOnly) UnmarshalText(text []byte) error {
	s := strings.TrimSpace(string(text))
	if s == "" {
		d.Time = time.Time{}
		return nil
	}
	// Try RFC3339 first
	t, err := time.Parse(time.RFC3339, s)
	if err == nil {
		d.Time = t
		return nil
	}
	// Try YYYY-MM-DD
	t, err = time.Parse("2006-01-02", s)
	if err == nil {
		d.Time = t
		return nil
	}
	return fmt.Errorf("DateOnly: cannot parse '%s' as RFC3339 or 2006-01-02", s)
}
