package pagination

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

func TestParseBasePagination(t *testing.T) {
	tests := []struct {
		name     string
		params   url.Values
		expected BasePaginationParams
	}{
		{
			name:   "default values",
			params: url.Values{},
			expected: BasePaginationParams{
				Page:         1,
				ItemsPerPage: 10,
				Search:       "",
			},
		},
		{
			name: "all parameters set",
			params: url.Values{
				"page":     []string{"3"},
				"per_page": []string{"25"},
				"search":   []string{"test query"},
			},
			expected: BasePaginationParams{
				Page:         3,
				ItemsPerPage: 25,
				Search:       "test query",
			},
		},
		{
			name: "invalid page defaults to 1",
			params: url.Values{
				"page":     []string{"invalid"},
				"per_page": []string{"25"},
			},
			expected: BasePaginationParams{
				Page:         1,
				ItemsPerPage: 25,
				Search:       "",
			},
		},
		{
			name: "zero page defaults to 1",
			params: url.Values{
				"page": []string{"0"},
			},
			expected: BasePaginationParams{
				Page:         1,
				ItemsPerPage: 10,
				Search:       "",
			},
		},
		{
			name: "negative page defaults to 1",
			params: url.Values{
				"page": []string{"-5"},
			},
			expected: BasePaginationParams{
				Page:         1,
				ItemsPerPage: 10,
				Search:       "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create Echo context with query parameters
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/?"+tt.params.Encode(), nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			result := ParseBasePagination(c)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParseSortablePagination(t *testing.T) {
	tests := []struct {
		name        string
		params      url.Values
		defaultSort string
		expected    SortablePaginationParams
	}{
		{
			name:        "default values",
			params:      url.Values{},
			defaultSort: "name",
			expected: SortablePaginationParams{
				BasePaginationParams: BasePaginationParams{
					Page:         1,
					ItemsPerPage: 10,
					Search:       "",
				},
				Sort:      "name",
				Direction: "asc",
			},
		},
		{
			name: "all parameters set",
			params: url.Values{
				"page":     []string{"2"},
				"per_page": []string{"20"},
				"search":   []string{"test"},
				"sort":     []string{"created_at"},
				"dir":      []string{"desc"},
			},
			defaultSort: "name",
			expected: SortablePaginationParams{
				BasePaginationParams: BasePaginationParams{
					Page:         2,
					ItemsPerPage: 20,
					Search:       "test",
				},
				Sort:      "created_at",
				Direction: "desc",
			},
		},
		{
			name: "invalid direction defaults to asc",
			params: url.Values{
				"sort": []string{"name"},
				"dir":  []string{"invalid"},
			},
			defaultSort: "name",
			expected: SortablePaginationParams{
				BasePaginationParams: BasePaginationParams{
					Page:         1,
					ItemsPerPage: 10,
					Search:       "",
				},
				Sort:      "name",
				Direction: "asc",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create Echo context with query parameters
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/?"+tt.params.Encode(), nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			result := ParseSortablePagination(c, tt.defaultSort)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCalculateTotalPages(t *testing.T) {
	tests := []struct {
		name         string
		totalItems   int
		itemsPerPage int
		expected     int
	}{
		{
			name:         "zero items",
			totalItems:   0,
			itemsPerPage: 10,
			expected:     1,
		},
		{
			name:         "items fit exactly in pages",
			totalItems:   20,
			itemsPerPage: 10,
			expected:     2,
		},
		{
			name:         "items with remainder",
			totalItems:   23,
			itemsPerPage: 10,
			expected:     3,
		},
		{
			name:         "single item",
			totalItems:   1,
			itemsPerPage: 10,
			expected:     1,
		},
		{
			name:         "large dataset",
			totalItems:   9001,
			itemsPerPage: 25,
			expected:     361,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateTotalPages(tt.totalItems, tt.itemsPerPage)
			assert.Equal(t, tt.expected, result)
		})
	}
}
