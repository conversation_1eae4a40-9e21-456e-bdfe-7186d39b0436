package pagination

import (
	"strconv"

	"github.com/labstack/echo/v4"
)

// BasePaginationParams contains the core pagination parameters shared by all tables
type BasePaginationParams struct {
	Page         int
	ItemsPerPage int
	Search       string
}

// SortablePaginationParams extends BasePaginationParams with sorting capabilities
type SortablePaginationParams struct {
	BasePaginationParams
	Sort      string
	Direction string
}

// ParseBasePagination parses basic pagination parameters from query params
func ParseBasePagination(c echo.Context) BasePaginationParams {
	params := BasePaginationParams{
		Page:         1,
		ItemsPerPage: 10,
		Search:       "",
	}

	if page := c.QueryParam("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			params.Page = p
		}
	}

	if perPage := c.QueryParam("per_page"); perPage != "" {
		if pp, err := strconv.Atoi(perPage); err == nil && pp > 0 {
			params.ItemsPerPage = pp
		}
	}

	if search := c.QueryParam("search"); search != "" {
		params.Search = search
	}

	return params
}

// CalculateTotalPages calculates total pages based on total items and items per page
func CalculateTotalPages(totalItems, itemsPerPage int) int {
	if totalItems == 0 {
		return 1
	}
	return (totalItems + itemsPerPage - 1) / itemsPerPage
}

// ParseSortablePagination parses pagination parameters with sorting support from query params
func ParseSortablePagination(c echo.Context, defaultSort string) SortablePaginationParams {
	base := ParseBasePagination(c)

	params := SortablePaginationParams{
		BasePaginationParams: base,
		Sort:                 defaultSort,
		Direction:            "asc",
	}

	if sortField := c.QueryParam("sort"); sortField != "" {
		params.Sort = sortField
	}

	if direction := c.QueryParam("dir"); direction == "desc" {
		params.Direction = "desc"
	}

	return params
}
