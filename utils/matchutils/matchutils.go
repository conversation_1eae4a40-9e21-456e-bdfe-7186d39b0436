package matchutils

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/jackc/pgx/v5/pgtype"
)

// DateFormat represents different date formatting options
type DateFormat string

const (
	DateFormatYMD     DateFormat = "2006-01-02"       // Standard YYYY-MM-DD
	DateFormatYMDTime DateFormat = "2006-01-02 15:04" // With time
	DateFormatDisplay DateFormat = "Jan 2, 2006"      // Human readable
)

// FormatMatchDate converts pgtype.Date to string using specified format
func FormatMatchDate(date pgtype.Date, format DateFormat) string {
	if !date.Valid {
		return ""
	}
	return date.Time.Format(string(format))
}

// FormatMatchDateWithFallback converts pgtype.Date to string with a fallback value
func FormatMatchDateWithFallback(date pgtype.Date, format DateFormat, fallback string) string {
	if !date.Valid {
		return fallback
	}
	return date.Time.Format(string(format))
}

// FormatPlayerPoints converts pgtype.Int4 to string, handling null values
func FormatPlayerPoints(points pgtype.Int4) string {
	if points.Valid {
		return strconv.Itoa(int(points.Int32))
	}
	return "0"
}

// FormatPlayerPointsWithFallback converts pgtype.Int4 to string with custom fallback
func FormatPlayerPointsWithFallback(points pgtype.Int4, fallback string) string {
	if points.Valid {
		return strconv.Itoa(int(points.Int32))
	}
	return fallback
}

// FormatPlayerName formats a player name or ID with fallback for TBD cases
func FormatPlayerName(playerName pgtype.Text, playerID pgtype.Int4, locales map[string]string) string {
	if playerName.Valid && playerName.String != "" {
		return playerName.String
	}

	if playerID.Valid {
		if idFormat, ok := locales["player_format"]; ok {
			return fmt.Sprintf(idFormat, playerID.Int32)
		}
		return fmt.Sprintf("Player %d", playerID.Int32)
	}

	if tbd, ok := locales["tbd"]; ok {
		return tbd
	}
	return "TBD"
}

// FormatPlayerNameSimple formats a player name with simple fallback
func FormatPlayerNameSimple(playerName pgtype.Text, fallback string) string {
	if playerName.Valid && playerName.String != "" {
		return playerName.String
	}
	return fallback
}

// FormatScore formats match score from two player points
func FormatScore(player1Points, player2Points pgtype.Int4) string {
	if player1Points.Valid && player2Points.Valid {
		return fmt.Sprintf("%d - %d", player1Points.Int32, player2Points.Int32)
	}
	return ""
}

// FormatScoreWithFallback formats match score with fallback for incomplete scores
func FormatScoreWithFallback(player1Points, player2Points pgtype.Int4, fallback string) string {
	if player1Points.Valid && player2Points.Valid {
		return fmt.Sprintf("%d - %d", player1Points.Int32, player2Points.Int32)
	}
	return fallback
}

// GetWinnerName returns the name of the winning player from match data
func GetWinnerName(match db.GetMatchesBySeasonIdRow) string {
	if !match.WinnerID.Valid {
		return ""
	}
	if match.WinnerID.Int32 == match.PlayerId1.Int32 && match.Player1Name.Valid {
		return match.Player1Name.String
	}
	if match.WinnerID.Int32 == match.PlayerId2.Int32 && match.Player2Name.Valid {
		return match.Player2Name.String
	}
	return ""
}

// IsPlayerWinner checks if a player is the winner of a match
func IsPlayerWinner(match db.GetMatchesBySeasonIdRow, playerID pgtype.Int4) bool {
	return match.WinnerID.Valid && playerID.Valid && match.WinnerID.Int32 == playerID.Int32
}

// FormatMatchGroup converts match group to string
func FormatMatchGroup(group int32) string {
	return strconv.Itoa(int(group))
}
