-- +goose Up
-- +goose StatementBegin
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    stytch_id varchar(50) NOT NULL,
    stripe_id varchar(60) NOT NULL,
    name varchar(255) NOT NULL,
    email varchar(255) NOT NULL,
    country varchar(2) NOT NULL,
    phone varchar(20),
    birthday date,
    lang VARCHAR(2) NOT NULL DEFAULT 'en' CHECK (lang IN ('fr', 'en')),
    picture_url TEXT,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active boolean NOT NULL DEFAULT true,
    is_verified boolean NOT NULL DEFAULT false,
    subscription_tier VARCHAR(4) NOT NULL DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro')),
    is_sidebar_open boolean NOT NULL DEFAULT true,
    json_settings TEXT,
    UNIQUE (email)
);

CREATE TABLE teams (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id) NOT NULL,
    name varchar(255) NOT NULL,
    description TEXT,
    picture_url TEXT,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active boolean NOT NULL DEFAULT true,
    UNIQUE (user_id, name)
);

CREATE TABLE players (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id) NOT NULL,
    team_id INTEGER REFERENCES teams (id) ON DELETE SET NULL,
    name varchar(255) NOT NULL,
    email varchar(255),
    picture_url TEXT,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    preferred_match_group integer NOT NULL,
    is_active boolean NOT NULL DEFAULT true,
    email_notifications_enabled boolean NOT NULL DEFAULT false,
    email_reminder_preferences JSONB DEFAULT '{"match_24h": true, "match_2h": true, "schedule_updates": true, "results": false}',
    UNIQUE (name)
);

CREATE TABLE seasons (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id),
    name varchar(255) NOT NULL,
    start_date date NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active boolean NOT NULL DEFAULT true,
    season_type varchar(50) CHECK (
        season_type IN (
            'pool',
            'bowling',
            'other'
        )
    ) NOT NULL,
    frequency varchar(50) CHECK (
        frequency IN (
            'weekly',
            'biweekly',
            'monthly',
            'quarterly',
            'yearly'
        )
    ) NOT NULL,
    UNIQUE (name)
);

CREATE TABLE matches (
    id SERIAL PRIMARY KEY,
    season_id integer REFERENCES seasons (id),
    player_id1 integer REFERENCES players (id) ON DELETE SET NULL,
    player_id1_points integer,
    player_id2 integer REFERENCES players (id) ON DELETE SET NULL,
    player_id2_points integer,
    match_date date NOT NULL,
    winner_id integer GENERATED ALWAYS AS (
        CASE 
            WHEN player_id1_points IS NULL OR player_id2_points IS NULL THEN NULL
            WHEN player_id1_points > player_id2_points THEN player_id1
            WHEN player_id2_points > player_id1_points THEN player_id2
            ELSE NULL -- tie case
        END
    ) STORED,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active boolean NOT NULL DEFAULT true,
    match_group integer NOT NULL
);

CREATE TABLE player_custom_columns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (name)
);

CREATE TABLE player_custom_values (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players (id),
    column_id INTEGER REFERENCES player_custom_columns (id),
    value TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (player_id, column_id)
);

CREATE TABLE match_custom_columns (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id) NOT NULL,
    name varchar(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE match_custom_values (
    id SERIAL PRIMARY KEY,
    match_id INTEGER REFERENCES matches (id) NOT NULL,
    column_id INTEGER REFERENCES match_custom_columns (id) NOT NULL,
    value TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (match_id, column_id)
);

CREATE TABLE feedback (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    feedback_type VARCHAR(50) NOT NULL CHECK (feedback_type IN ('bug', 'feature_request', 'other')),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE email_reminders (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players (id) NOT NULL,
    reminder_type VARCHAR(50) NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    sent_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    retry_count INTEGER DEFAULT 0,
    next_retry_at TIMESTAMP NULL,
    last_error TEXT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'skipped', 'dead_letter')),
    match_id INTEGER REFERENCES matches (id) NULL,
    season_id INTEGER REFERENCES seasons (id) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_email_reminders_pending
ON email_reminders (scheduled_at, status)
WHERE status = 'pending';

CREATE TABLE email_dead_letter_queue (
    id SERIAL PRIMARY KEY,
    original_reminder_id INTEGER REFERENCES email_reminders (id) NOT NULL,
    player_id INTEGER REFERENCES players (id) NOT NULL,
    reminder_type VARCHAR(50) NOT NULL,
    failure_reason TEXT NOT NULL,
    retry_count INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_attempt_at TIMESTAMP NOT NULL
);

-- Core notifications table
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'match_update', 'schedule_change', 'result', 'announcement'
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB, -- Store contextual data (match_id, season_id, etc.)
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    
    -- Optional references for filtering/context
    match_id INTEGER REFERENCES matches (id) NULL,
    season_id INTEGER REFERENCES seasons (id) NULL,
    player_id INTEGER REFERENCES players (id) NULL
);

-- User notification preferences  
CREATE TABLE notification_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users (id) NOT NULL UNIQUE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    match_updates BOOLEAN DEFAULT TRUE,
    schedule_changes BOOLEAN DEFAULT TRUE,
    results BOOLEAN DEFAULT TRUE,
    announcements BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_notifications_user_unread ON notifications (user_id, is_read, created_at DESC);
CREATE INDEX idx_notifications_type ON notifications (type);

-- Permission system enums
CREATE TYPE permission_level_enum AS ENUM (
    'owner',     -- Full control (existing season.user_id becomes this)
    'admin',     -- Manage players, matches, settings (not permissions)
    'manager',   -- Manage matches, view all
    'viewer'     -- Read-only access
);

CREATE TYPE permission_action_enum AS ENUM (
    'granted',
    'revoked', 
    'modified'
);

-- Season permissions table
CREATE TABLE season_permissions (
    id SERIAL PRIMARY KEY,
    season_id INTEGER NOT NULL REFERENCES seasons(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_level permission_level_enum NOT NULL,
    granted_by INTEGER NOT NULL REFERENCES users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Prevent duplicate permissions for same user/season
    UNIQUE(season_id, user_id)
);

-- Audit log for permission changes
CREATE TABLE season_permission_history (
    id SERIAL PRIMARY KEY,
    season_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    permission_level permission_level_enum,
    action permission_action_enum NOT NULL,
    performed_by INTEGER NOT NULL REFERENCES users(id),
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_season_permissions_season ON season_permissions (season_id);
CREATE INDEX idx_season_permissions_user ON season_permissions (user_id);
CREATE INDEX idx_season_permissions_granted_by ON season_permissions (granted_by);
CREATE INDEX idx_season_permission_history_season ON season_permission_history (season_id);
CREATE INDEX idx_season_permission_history_user ON season_permission_history (user_id);
CREATE INDEX idx_season_permission_history_performed_at ON season_permission_history (performed_at);

-- API Keys table
CREATE TABLE api_keys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    key_hash TEXT NOT NULL, -- bcrypt hash of the full key
    name VARCHAR(255) NOT NULL, -- user-friendly name like "Production Bot"
    prefix VARCHAR(16) NOT NULL, -- first 8 chars for UI identification (e.g., "cpb_live_")
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL, -- optional expiration
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Indexes for performance
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_prefix ON api_keys(prefix);
CREATE INDEX idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_active ON api_keys(is_active) WHERE is_active = TRUE;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS api_keys;
DROP TABLE IF EXISTS season_permission_history;
DROP TABLE IF EXISTS season_permissions;
DROP TYPE IF EXISTS permission_action_enum;
DROP TYPE IF EXISTS permission_level_enum;
DROP INDEX IF EXISTS idx_notifications_type;
DROP INDEX IF EXISTS idx_notifications_user_unread;
DROP TABLE IF EXISTS notification_preferences;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS email_dead_letter_queue;
DROP INDEX IF EXISTS idx_email_reminders_pending;
DROP TABLE IF EXISTS email_reminders;
DROP TABLE IF EXISTS feedback;
DROP TABLE IF EXISTS match_custom_values;
DROP TABLE IF EXISTS match_custom_columns;
DROP TABLE IF EXISTS player_custom_values;
DROP TABLE IF EXISTS player_custom_columns;
DROP TABLE IF EXISTS matches;
DROP TABLE IF EXISTS seasons;
DROP TABLE IF EXISTS players;
DROP TABLE IF EXISTS teams;
DROP TABLE IF EXISTS users;
-- +goose StatementEnd