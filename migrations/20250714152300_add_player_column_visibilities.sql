-- +goose Up
-- Create table for player column visibility preferences
CREATE TABLE IF NOT EXISTS player_column_visibilities (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    column_name VARCHAR(255) NOT NULL,
    is_visible BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, column_name)
);

-- Create index for player column visibility
CREATE INDEX IF NOT EXISTS idx_player_column_visibilities_user_id ON player_column_visibilities(user_id);

-- +goose Down
DROP INDEX IF EXISTS idx_player_column_visibilities_user_id;
DROP TABLE IF EXISTS player_column_visibilities;
