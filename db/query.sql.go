// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: query.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const cascadeDeleteUser = `-- name: CascadeDeleteUser :exec
WITH 
deleted_player_custom_values AS (
    DELETE FROM player_custom_values
    WHERE player_id IN (SELECT id FROM players WHERE user_id = $1)
    RETURNING id, player_id, column_id, value, created_at, updated_at
),
deleted_match_custom_values AS (
    DELETE FROM match_custom_values
    WHERE match_id IN (
        SELECT m.id 
        FROM matches m
        JOIN seasons s ON m.season_id = s.id
        WHERE s.user_id = $1
    )
    RETURNING id, match_id, column_id, value, created_at, updated_at
),
deleted_matches AS (
    DELETE FROM matches
    WHERE season_id IN (SELECT id FROM seasons WHERE user_id = $1)
    RETURNING id, season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, winner_id, created_at, updated_at, is_active, match_group
),
deleted_seasons AS (
    DELETE FROM seasons
    WHERE user_id = $1
    RETURNING id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency
),
deleted_players AS (
    DELETE FROM players
    WHERE user_id = $1
    RETURNING id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences
),
deleted_teams AS (
    DELETE FROM teams
    WHERE user_id = $1
    RETURNING id, user_id, name, description, picture_url, created_at, updated_at, is_active
)
DELETE FROM users
WHERE users.id = $1
`

// This query performs a complete cascade delete of a user and all related data
// Order matters: delete dependent tables first, then the user
// First, delete player custom values
// Next, delete match custom values for matches in user's seasons
// Delete all matches in user's seasons
// Delete all seasons
// Delete all players
// Delete all teams
// Finally delete the user
func (q *Queries) CascadeDeleteUser(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, cascadeDeleteUser, id)
	return err
}

const checkSeasonAccess = `-- name: CheckSeasonAccess :one
SELECT 
    COALESCE(sp.permission_level, 'owner'::permission_level_enum) as permission_level
FROM seasons s
LEFT JOIN season_permissions sp ON s.id = sp.season_id AND sp.user_id = $2 AND sp.is_active = true
WHERE s.id = $1 AND s.is_active = true
  AND (s.user_id = $2 OR sp.user_id = $2)
`

type CheckSeasonAccessParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) CheckSeasonAccess(ctx context.Context, arg CheckSeasonAccessParams) (PermissionLevelEnum, error) {
	row := q.db.QueryRow(ctx, checkSeasonAccess, arg.ID, arg.UserID)
	var permission_level PermissionLevelEnum
	err := row.Scan(&permission_level)
	return permission_level, err
}

const countUserMatches = `-- name: CountUserMatches :one
SELECT COUNT(*)::int FROM matches m
JOIN seasons s ON m.season_id = s.id
WHERE s.user_id = $1 AND m.is_active = true
`

func (q *Queries) CountUserMatches(ctx context.Context, userID pgtype.Int4) (int32, error) {
	row := q.db.QueryRow(ctx, countUserMatches, userID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const countUserPlayers = `-- name: CountUserPlayers :one
SELECT COUNT(*)::int FROM players
WHERE user_id = $1 AND is_active = true
`

// Access limits counting queries
func (q *Queries) CountUserPlayers(ctx context.Context, userID int32) (int32, error) {
	row := q.db.QueryRow(ctx, countUserPlayers, userID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const countUserSeasons = `-- name: CountUserSeasons :one
SELECT COUNT(*)::int FROM seasons
WHERE user_id = $1 AND is_active = true
`

func (q *Queries) CountUserSeasons(ctx context.Context, userID pgtype.Int4) (int32, error) {
	row := q.db.QueryRow(ctx, countUserSeasons, userID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const countUserTeams = `-- name: CountUserTeams :one
SELECT COUNT(*)::int FROM teams
WHERE user_id = $1 AND is_active = true
`

func (q *Queries) CountUserTeams(ctx context.Context, userID int32) (int32, error) {
	row := q.db.QueryRow(ctx, countUserTeams, userID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const createAPIKey = `-- name: CreateAPIKey :one

INSERT INTO api_keys (user_id, key_hash, name, prefix, expires_at)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, user_id, key_hash, name, prefix, last_used_at, expires_at, created_at, updated_at, is_active
`

type CreateAPIKeyParams struct {
	UserID    int32
	KeyHash   string
	Name      string
	Prefix    string
	ExpiresAt pgtype.Timestamp
}

// API Key Management queries
func (q *Queries) CreateAPIKey(ctx context.Context, arg CreateAPIKeyParams) (ApiKey, error) {
	row := q.db.QueryRow(ctx, createAPIKey,
		arg.UserID,
		arg.KeyHash,
		arg.Name,
		arg.Prefix,
		arg.ExpiresAt,
	)
	var i ApiKey
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.KeyHash,
		&i.Name,
		&i.Prefix,
		&i.LastUsedAt,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const createFeedback = `-- name: CreateFeedback :one
INSERT INTO feedback (
    user_id, name, description, feedback_type
) VALUES (
    $1, $2, $3, $4
)
RETURNING id, user_id, name, description, feedback_type, created_at, updated_at
`

type CreateFeedbackParams struct {
	UserID       int32
	Name         string
	Description  string
	FeedbackType string
}

func (q *Queries) CreateFeedback(ctx context.Context, arg CreateFeedbackParams) (Feedback, error) {
	row := q.db.QueryRow(ctx, createFeedback,
		arg.UserID,
		arg.Name,
		arg.Description,
		arg.FeedbackType,
	)
	var i Feedback
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.Description,
		&i.FeedbackType,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createMatch = `-- name: CreateMatch :one
INSERT INTO matches (
    season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, match_group
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, winner_id, created_at, updated_at, is_active, match_group
`

type CreateMatchParams struct {
	SeasonID        pgtype.Int4
	PlayerId1       pgtype.Int4
	PlayerId1Points pgtype.Int4
	PlayerId2       pgtype.Int4
	PlayerId2Points pgtype.Int4
	MatchDate       pgtype.Date
	MatchGroup      int32
}

func (q *Queries) CreateMatch(ctx context.Context, arg CreateMatchParams) (Match, error) {
	row := q.db.QueryRow(ctx, createMatch,
		arg.SeasonID,
		arg.PlayerId1,
		arg.PlayerId1Points,
		arg.PlayerId2,
		arg.PlayerId2Points,
		arg.MatchDate,
		arg.MatchGroup,
	)
	var i Match
	err := row.Scan(
		&i.ID,
		&i.SeasonID,
		&i.PlayerId1,
		&i.PlayerId1Points,
		&i.PlayerId2,
		&i.PlayerId2Points,
		&i.MatchDate,
		&i.WinnerID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.MatchGroup,
	)
	return i, err
}

const createMatchCustomColumn = `-- name: CreateMatchCustomColumn :one
INSERT INTO match_custom_columns (
    user_id, name, field_type, description, is_required, is_active, display_order
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
)
RETURNING id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at
`

type CreateMatchCustomColumnParams struct {
	UserID       int32
	Name         string
	FieldType    string
	Description  pgtype.Text
	IsRequired   pgtype.Bool
	IsActive     pgtype.Bool
	DisplayOrder int32
}

func (q *Queries) CreateMatchCustomColumn(ctx context.Context, arg CreateMatchCustomColumnParams) (MatchCustomColumn, error) {
	row := q.db.QueryRow(ctx, createMatchCustomColumn,
		arg.UserID,
		arg.Name,
		arg.FieldType,
		arg.Description,
		arg.IsRequired,
		arg.IsActive,
		arg.DisplayOrder,
	)
	var i MatchCustomColumn
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.FieldType,
		&i.Description,
		&i.IsRequired,
		&i.IsActive,
		&i.DisplayOrder,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createNotification = `-- name: CreateNotification :one

INSERT INTO notifications (user_id, type, title, message, data, match_id, season_id, player_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
RETURNING id, user_id, type, title, message, data, is_read, created_at, read_at, match_id, season_id, player_id
`

type CreateNotificationParams struct {
	UserID   int32
	Type     string
	Title    string
	Message  string
	Data     []byte
	MatchID  pgtype.Int4
	SeasonID pgtype.Int4
	PlayerID pgtype.Int4
}

// Notification queries
func (q *Queries) CreateNotification(ctx context.Context, arg CreateNotificationParams) (Notification, error) {
	row := q.db.QueryRow(ctx, createNotification,
		arg.UserID,
		arg.Type,
		arg.Title,
		arg.Message,
		arg.Data,
		arg.MatchID,
		arg.SeasonID,
		arg.PlayerID,
	)
	var i Notification
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Type,
		&i.Title,
		&i.Message,
		&i.Data,
		&i.IsRead,
		&i.CreatedAt,
		&i.ReadAt,
		&i.MatchID,
		&i.SeasonID,
		&i.PlayerID,
	)
	return i, err
}

const createPlayer = `-- name: CreatePlayer :one
INSERT INTO players (
    user_id, name, email, phone, preferred_match_group, email_notifications_enabled, team_id, picture_url
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences
`

type CreatePlayerParams struct {
	UserID                    int32
	Name                      string
	Email                     pgtype.Text
	Phone                     pgtype.Text
	PreferredMatchGroup       int32
	EmailNotificationsEnabled bool
	TeamID                    pgtype.Int4
	PictureUrl                pgtype.Text
}

func (q *Queries) CreatePlayer(ctx context.Context, arg CreatePlayerParams) (Player, error) {
	row := q.db.QueryRow(ctx, createPlayer,
		arg.UserID,
		arg.Name,
		arg.Email,
		arg.Phone,
		arg.PreferredMatchGroup,
		arg.EmailNotificationsEnabled,
		arg.TeamID,
		arg.PictureUrl,
	)
	var i Player
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TeamID,
		&i.Name,
		&i.Email,
		&i.Phone,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PreferredMatchGroup,
		&i.IsActive,
		&i.EmailNotificationsEnabled,
		&i.EmailReminderPreferences,
	)
	return i, err
}

const createPlayerCustomColumn = `-- name: CreatePlayerCustomColumn :one
INSERT INTO player_custom_columns (
    user_id, name, field_type, description, is_required, display_order
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at
`

type CreatePlayerCustomColumnParams struct {
	UserID       int32
	Name         string
	FieldType    string
	Description  pgtype.Text
	IsRequired   pgtype.Bool
	DisplayOrder pgtype.Int4
}

func (q *Queries) CreatePlayerCustomColumn(ctx context.Context, arg CreatePlayerCustomColumnParams) (PlayerCustomColumn, error) {
	row := q.db.QueryRow(ctx, createPlayerCustomColumn,
		arg.UserID,
		arg.Name,
		arg.FieldType,
		arg.Description,
		arg.IsRequired,
		arg.DisplayOrder,
	)
	var i PlayerCustomColumn
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.FieldType,
		&i.Description,
		&i.IsRequired,
		&i.IsActive,
		&i.DisplayOrder,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createSeason = `-- name: CreateSeason :one
INSERT INTO seasons (
    user_id, name, start_date, season_type, frequency
) VALUES (
    $1, $2, $3, $4, $5
)
RETURNING id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency
`

type CreateSeasonParams struct {
	UserID     pgtype.Int4
	Name       string
	StartDate  pgtype.Date
	SeasonType string
	Frequency  string
}

func (q *Queries) CreateSeason(ctx context.Context, arg CreateSeasonParams) (Season, error) {
	row := q.db.QueryRow(ctx, createSeason,
		arg.UserID,
		arg.Name,
		arg.StartDate,
		arg.SeasonType,
		arg.Frequency,
	)
	var i Season
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.StartDate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.SeasonType,
		&i.Frequency,
	)
	return i, err
}

const createSpending = `-- name: CreateSpending :one

INSERT INTO spending (
    user_id, amount, description, date, category, file_urls
) VALUES (
    $1, $2, $3, $4, $5, $6
)
RETURNING id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active
`

type CreateSpendingParams struct {
	UserID      int32
	Amount      pgtype.Numeric
	Description string
	Date        pgtype.Date
	Category    string
	FileUrls    []string
}

// Spending queries
func (q *Queries) CreateSpending(ctx context.Context, arg CreateSpendingParams) (Spending, error) {
	row := q.db.QueryRow(ctx, createSpending,
		arg.UserID,
		arg.Amount,
		arg.Description,
		arg.Date,
		arg.Category,
		arg.FileUrls,
	)
	var i Spending
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Amount,
		&i.Description,
		&i.Date,
		&i.Category,
		&i.FileUrls,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const createSpendingMatchAssociation = `-- name: CreateSpendingMatchAssociation :exec
INSERT INTO spending_matches (spending_id, match_id)
VALUES ($1, $2)
`

type CreateSpendingMatchAssociationParams struct {
	SpendingID int32
	MatchID    int32
}

func (q *Queries) CreateSpendingMatchAssociation(ctx context.Context, arg CreateSpendingMatchAssociationParams) error {
	_, err := q.db.Exec(ctx, createSpendingMatchAssociation, arg.SpendingID, arg.MatchID)
	return err
}

const createSpendingPlayerAssociation = `-- name: CreateSpendingPlayerAssociation :exec

INSERT INTO spending_players (spending_id, player_id)
VALUES ($1, $2)
`

type CreateSpendingPlayerAssociationParams struct {
	SpendingID int32
	PlayerID   int32
}

// Spending association queries
func (q *Queries) CreateSpendingPlayerAssociation(ctx context.Context, arg CreateSpendingPlayerAssociationParams) error {
	_, err := q.db.Exec(ctx, createSpendingPlayerAssociation, arg.SpendingID, arg.PlayerID)
	return err
}

const createSpendingSeasonAssociation = `-- name: CreateSpendingSeasonAssociation :exec
INSERT INTO spending_seasons (spending_id, season_id)
VALUES ($1, $2)
`

type CreateSpendingSeasonAssociationParams struct {
	SpendingID int32
	SeasonID   int32
}

func (q *Queries) CreateSpendingSeasonAssociation(ctx context.Context, arg CreateSpendingSeasonAssociationParams) error {
	_, err := q.db.Exec(ctx, createSpendingSeasonAssociation, arg.SpendingID, arg.SeasonID)
	return err
}

const createSpendingTeamAssociation = `-- name: CreateSpendingTeamAssociation :exec
INSERT INTO spending_teams (spending_id, team_id)
VALUES ($1, $2)
`

type CreateSpendingTeamAssociationParams struct {
	SpendingID int32
	TeamID     int32
}

func (q *Queries) CreateSpendingTeamAssociation(ctx context.Context, arg CreateSpendingTeamAssociationParams) error {
	_, err := q.db.Exec(ctx, createSpendingTeamAssociation, arg.SpendingID, arg.TeamID)
	return err
}

const createTeam = `-- name: CreateTeam :one
INSERT INTO teams (
    user_id, name, description, picture_url
) VALUES (
    $1, $2, $3, $4
)
RETURNING id, user_id, name, description, picture_url, created_at, updated_at, is_active
`

type CreateTeamParams struct {
	UserID      int32
	Name        string
	Description pgtype.Text
	PictureUrl  pgtype.Text
}

func (q *Queries) CreateTeam(ctx context.Context, arg CreateTeamParams) (Team, error) {
	row := q.db.QueryRow(ctx, createTeam,
		arg.UserID,
		arg.Name,
		arg.Description,
		arg.PictureUrl,
	)
	var i Team
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.Description,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (
    stytch_id, stripe_id, name, email, phone, country, birthday, lang, is_verified, picture_url
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
)
RETURNING id, stytch_id, stripe_id, name, email, country, phone, birthday, lang, picture_url, created_at, updated_at, is_active, is_verified, subscription_tier, is_sidebar_open, json_settings
`

type CreateUserParams struct {
	StytchID   string
	StripeID   string
	Name       string
	Email      string
	Phone      pgtype.Text
	Country    string
	Birthday   pgtype.Date
	Lang       string
	IsVerified bool
	PictureUrl pgtype.Text
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.StytchID,
		arg.StripeID,
		arg.Name,
		arg.Email,
		arg.Phone,
		arg.Country,
		arg.Birthday,
		arg.Lang,
		arg.IsVerified,
		arg.PictureUrl,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.StytchID,
		&i.StripeID,
		&i.Name,
		&i.Email,
		&i.Country,
		&i.Phone,
		&i.Birthday,
		&i.Lang,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.IsVerified,
		&i.SubscriptionTier,
		&i.IsSidebarOpen,
		&i.JsonSettings,
	)
	return i, err
}

const deleteAPIKey = `-- name: DeleteAPIKey :exec
UPDATE api_keys 
SET is_active = false, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2
`

type DeleteAPIKeyParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) DeleteAPIKey(ctx context.Context, arg DeleteAPIKeyParams) error {
	_, err := q.db.Exec(ctx, deleteAPIKey, arg.ID, arg.UserID)
	return err
}

const deleteMatch = `-- name: DeleteMatch :exec
DELETE FROM matches
WHERE id = $1
`

func (q *Queries) DeleteMatch(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteMatch, id)
	return err
}

const deleteMatchCustomColumn = `-- name: DeleteMatchCustomColumn :exec
DELETE FROM match_custom_columns
WHERE id = $1
`

func (q *Queries) DeleteMatchCustomColumn(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteMatchCustomColumn, id)
	return err
}

const deleteNotification = `-- name: DeleteNotification :exec
DELETE FROM notifications WHERE id = $1
`

func (q *Queries) DeleteNotification(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteNotification, id)
	return err
}

const deletePlayer = `-- name: DeletePlayer :exec
DELETE FROM players
WHERE id = $1
`

func (q *Queries) DeletePlayer(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deletePlayer, id)
	return err
}

const deletePlayerColumnVisibility = `-- name: DeletePlayerColumnVisibility :exec
DELETE FROM player_column_visibilities
WHERE user_id = $1 AND column_name = $2
`

type DeletePlayerColumnVisibilityParams struct {
	UserID     int32
	ColumnName string
}

func (q *Queries) DeletePlayerColumnVisibility(ctx context.Context, arg DeletePlayerColumnVisibilityParams) error {
	_, err := q.db.Exec(ctx, deletePlayerColumnVisibility, arg.UserID, arg.ColumnName)
	return err
}

const deletePlayerCustomColumn = `-- name: DeletePlayerCustomColumn :exec
DELETE FROM player_custom_columns
WHERE id = $1 AND user_id = $2
`

type DeletePlayerCustomColumnParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) DeletePlayerCustomColumn(ctx context.Context, arg DeletePlayerCustomColumnParams) error {
	_, err := q.db.Exec(ctx, deletePlayerCustomColumn, arg.ID, arg.UserID)
	return err
}

const deleteSeason = `-- name: DeleteSeason :exec
DELETE FROM seasons
WHERE id = $1
`

func (q *Queries) DeleteSeason(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteSeason, id)
	return err
}

const deleteSpending = `-- name: DeleteSpending :exec
UPDATE spending
SET is_active = false, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2
`

type DeleteSpendingParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) DeleteSpending(ctx context.Context, arg DeleteSpendingParams) error {
	_, err := q.db.Exec(ctx, deleteSpending, arg.ID, arg.UserID)
	return err
}

const deleteSpendingMatchAssociations = `-- name: DeleteSpendingMatchAssociations :exec
DELETE FROM spending_matches WHERE spending_id = $1
`

func (q *Queries) DeleteSpendingMatchAssociations(ctx context.Context, spendingID int32) error {
	_, err := q.db.Exec(ctx, deleteSpendingMatchAssociations, spendingID)
	return err
}

const deleteSpendingPlayerAssociations = `-- name: DeleteSpendingPlayerAssociations :exec
DELETE FROM spending_players WHERE spending_id = $1
`

func (q *Queries) DeleteSpendingPlayerAssociations(ctx context.Context, spendingID int32) error {
	_, err := q.db.Exec(ctx, deleteSpendingPlayerAssociations, spendingID)
	return err
}

const deleteSpendingSeasonAssociations = `-- name: DeleteSpendingSeasonAssociations :exec
DELETE FROM spending_seasons WHERE spending_id = $1
`

func (q *Queries) DeleteSpendingSeasonAssociations(ctx context.Context, spendingID int32) error {
	_, err := q.db.Exec(ctx, deleteSpendingSeasonAssociations, spendingID)
	return err
}

const deleteSpendingTeamAssociations = `-- name: DeleteSpendingTeamAssociations :exec
DELETE FROM spending_teams WHERE spending_id = $1
`

func (q *Queries) DeleteSpendingTeamAssociations(ctx context.Context, spendingID int32) error {
	_, err := q.db.Exec(ctx, deleteSpendingTeamAssociations, spendingID)
	return err
}

const deleteTeam = `-- name: DeleteTeam :exec
DELETE FROM teams
WHERE id = $1
`

func (q *Queries) DeleteTeam(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteTeam, id)
	return err
}

const deleteUser = `-- name: DeleteUser :exec
UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1
`

func (q *Queries) DeleteUser(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteUser, id)
	return err
}

const deleteUserSubscription = `-- name: DeleteUserSubscription :exec
UPDATE users
SET subscription_tier = 'free',
    stripe_id = '',
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) DeleteUserSubscription(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteUserSubscription, id)
	return err
}

const filterUserSpendingByCategory = `-- name: FilterUserSpendingByCategory :many
SELECT id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active FROM spending
WHERE user_id = $1 AND is_active = true
  AND category = $2
ORDER BY date DESC, created_at DESC
`

type FilterUserSpendingByCategoryParams struct {
	UserID   int32
	Category string
}

func (q *Queries) FilterUserSpendingByCategory(ctx context.Context, arg FilterUserSpendingByCategoryParams) ([]Spending, error) {
	rows, err := q.db.Query(ctx, filterUserSpendingByCategory, arg.UserID, arg.Category)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Spending
	for rows.Next() {
		var i Spending
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Description,
			&i.Date,
			&i.Category,
			&i.FileUrls,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const filterUserSpendingByDateRange = `-- name: FilterUserSpendingByDateRange :many
SELECT id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active FROM spending
WHERE user_id = $1 AND is_active = true
  AND date >= $2 AND date <= $3
ORDER BY date DESC, created_at DESC
`

type FilterUserSpendingByDateRangeParams struct {
	UserID int32
	Date   pgtype.Date
	Date_2 pgtype.Date
}

func (q *Queries) FilterUserSpendingByDateRange(ctx context.Context, arg FilterUserSpendingByDateRangeParams) ([]Spending, error) {
	rows, err := q.db.Query(ctx, filterUserSpendingByDateRange, arg.UserID, arg.Date, arg.Date_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Spending
	for rows.Next() {
		var i Spending
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Description,
			&i.Date,
			&i.Category,
			&i.FileUrls,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAPIKeyByHash = `-- name: GetAPIKeyByHash :one
SELECT id, user_id, key_hash, name, prefix, last_used_at, expires_at, created_at, updated_at, is_active FROM api_keys 
WHERE key_hash = $1 AND is_active = true
`

func (q *Queries) GetAPIKeyByHash(ctx context.Context, keyHash string) (ApiKey, error) {
	row := q.db.QueryRow(ctx, getAPIKeyByHash, keyHash)
	var i ApiKey
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.KeyHash,
		&i.Name,
		&i.Prefix,
		&i.LastUsedAt,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const getAPIKeyByID = `-- name: GetAPIKeyByID :one
SELECT id, user_id, name, prefix, last_used_at, expires_at, created_at, is_active
FROM api_keys 
WHERE id = $1 AND user_id = $2 AND is_active = true
`

type GetAPIKeyByIDParams struct {
	ID     int32
	UserID int32
}

type GetAPIKeyByIDRow struct {
	ID         int32
	UserID     int32
	Name       string
	Prefix     string
	LastUsedAt pgtype.Timestamp
	ExpiresAt  pgtype.Timestamp
	CreatedAt  pgtype.Timestamp
	IsActive   bool
}

func (q *Queries) GetAPIKeyByID(ctx context.Context, arg GetAPIKeyByIDParams) (GetAPIKeyByIDRow, error) {
	row := q.db.QueryRow(ctx, getAPIKeyByID, arg.ID, arg.UserID)
	var i GetAPIKeyByIDRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.Prefix,
		&i.LastUsedAt,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.IsActive,
	)
	return i, err
}

const getAPIKeysByUserID = `-- name: GetAPIKeysByUserID :many
SELECT id, user_id, name, prefix, last_used_at, expires_at, created_at, is_active 
FROM api_keys 
WHERE user_id = $1 AND is_active = true
ORDER BY created_at DESC
`

type GetAPIKeysByUserIDRow struct {
	ID         int32
	UserID     int32
	Name       string
	Prefix     string
	LastUsedAt pgtype.Timestamp
	ExpiresAt  pgtype.Timestamp
	CreatedAt  pgtype.Timestamp
	IsActive   bool
}

func (q *Queries) GetAPIKeysByUserID(ctx context.Context, userID int32) ([]GetAPIKeysByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getAPIKeysByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAPIKeysByUserIDRow
	for rows.Next() {
		var i GetAPIKeysByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.Prefix,
			&i.LastUsedAt,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllActiveAPIKeys = `-- name: GetAllActiveAPIKeys :many
SELECT id, user_id, key_hash, name, prefix, last_used_at, expires_at, created_at, updated_at, is_active FROM api_keys WHERE is_active = true
`

func (q *Queries) GetAllActiveAPIKeys(ctx context.Context) ([]ApiKey, error) {
	rows, err := q.db.Query(ctx, getAllActiveAPIKeys)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ApiKey
	for rows.Next() {
		var i ApiKey
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.KeyHash,
			&i.Name,
			&i.Prefix,
			&i.LastUsedAt,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCustomMatchColumnsByUserID = `-- name: GetCustomMatchColumnsByUserID :many
SELECT id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at FROM match_custom_columns
WHERE user_id = $1 AND is_active = true
ORDER BY display_order
`

func (q *Queries) GetCustomMatchColumnsByUserID(ctx context.Context, userID int32) ([]MatchCustomColumn, error) {
	rows, err := q.db.Query(ctx, getCustomMatchColumnsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []MatchCustomColumn
	for rows.Next() {
		var i MatchCustomColumn
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.FieldType,
			&i.Description,
			&i.IsRequired,
			&i.IsActive,
			&i.DisplayOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMatch = `-- name: GetMatch :one
SELECT id, season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, winner_id, created_at, updated_at, is_active, match_group FROM matches
WHERE id = $1
`

func (q *Queries) GetMatch(ctx context.Context, id int32) (Match, error) {
	row := q.db.QueryRow(ctx, getMatch, id)
	var i Match
	err := row.Scan(
		&i.ID,
		&i.SeasonID,
		&i.PlayerId1,
		&i.PlayerId1Points,
		&i.PlayerId2,
		&i.PlayerId2Points,
		&i.MatchDate,
		&i.WinnerID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.MatchGroup,
	)
	return i, err
}

const getMatchCustomColumns = `-- name: GetMatchCustomColumns :many
SELECT id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at FROM match_custom_columns
WHERE is_active = true AND user_id = $1
ORDER BY display_order
`

func (q *Queries) GetMatchCustomColumns(ctx context.Context, userID int32) ([]MatchCustomColumn, error) {
	rows, err := q.db.Query(ctx, getMatchCustomColumns, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []MatchCustomColumn
	for rows.Next() {
		var i MatchCustomColumn
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.FieldType,
			&i.Description,
			&i.IsRequired,
			&i.IsActive,
			&i.DisplayOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMatchCustomValues = `-- name: GetMatchCustomValues :many
SELECT mcv.id, mcv.match_id, mcv.column_id, mcv.value, mcv.created_at, mcv.updated_at, mcc.name as column_name, mcc.field_type
FROM match_custom_values mcv
JOIN match_custom_columns mcc ON mcv.column_id = mcc.id
WHERE mcv.match_id = $1
`

type GetMatchCustomValuesRow struct {
	ID         int32
	MatchID    int32
	ColumnID   int32
	Value      pgtype.Text
	CreatedAt  pgtype.Timestamp
	UpdatedAt  pgtype.Timestamp
	ColumnName string
	FieldType  string
}

func (q *Queries) GetMatchCustomValues(ctx context.Context, matchID int32) ([]GetMatchCustomValuesRow, error) {
	rows, err := q.db.Query(ctx, getMatchCustomValues, matchID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetMatchCustomValuesRow
	for rows.Next() {
		var i GetMatchCustomValuesRow
		if err := rows.Scan(
			&i.ID,
			&i.MatchID,
			&i.ColumnID,
			&i.Value,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ColumnName,
			&i.FieldType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMatchesBySeasonId = `-- name: GetMatchesBySeasonId :many
SELECT m.id, m.season_id, m.player_id1, p1.name AS player1_name, m.player_id1_points, m.player_id2, p2.name AS player2_name, m.player_id2_points, m.match_date, m.winner_id, m.created_at, m.updated_at, m.is_active, m.match_group
FROM matches m
LEFT JOIN players p1 ON m.player_id1 = p1.id
LEFT JOIN players p2 ON m.player_id2 = p2.id
WHERE m.season_id = $1 AND m.is_active = true
ORDER BY m.match_date DESC
`

type GetMatchesBySeasonIdRow struct {
	ID              int32
	SeasonID        pgtype.Int4
	PlayerId1       pgtype.Int4
	Player1Name     pgtype.Text
	PlayerId1Points pgtype.Int4
	PlayerId2       pgtype.Int4
	Player2Name     pgtype.Text
	PlayerId2Points pgtype.Int4
	MatchDate       pgtype.Date
	WinnerID        pgtype.Int4
	CreatedAt       pgtype.Timestamp
	UpdatedAt       pgtype.Timestamp
	IsActive        bool
	MatchGroup      int32
}

func (q *Queries) GetMatchesBySeasonId(ctx context.Context, seasonID pgtype.Int4) ([]GetMatchesBySeasonIdRow, error) {
	rows, err := q.db.Query(ctx, getMatchesBySeasonId, seasonID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetMatchesBySeasonIdRow
	for rows.Next() {
		var i GetMatchesBySeasonIdRow
		if err := rows.Scan(
			&i.ID,
			&i.SeasonID,
			&i.PlayerId1,
			&i.Player1Name,
			&i.PlayerId1Points,
			&i.PlayerId2,
			&i.Player2Name,
			&i.PlayerId2Points,
			&i.MatchDate,
			&i.WinnerID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
			&i.MatchGroup,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getNotificationPreferences = `-- name: GetNotificationPreferences :one
SELECT id, user_id, in_app_enabled, match_updates, schedule_changes, results, announcements, created_at, updated_at FROM notification_preferences WHERE user_id = $1
`

func (q *Queries) GetNotificationPreferences(ctx context.Context, userID int32) (NotificationPreference, error) {
	row := q.db.QueryRow(ctx, getNotificationPreferences, userID)
	var i NotificationPreference
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.InAppEnabled,
		&i.MatchUpdates,
		&i.ScheduleChanges,
		&i.Results,
		&i.Announcements,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getNotificationsByType = `-- name: GetNotificationsByType :many
SELECT id, user_id, type, title, message, data, is_read, created_at, read_at, match_id, season_id, player_id FROM notifications 
WHERE user_id = $1 AND type = $2
ORDER BY created_at DESC
LIMIT $3 OFFSET $4
`

type GetNotificationsByTypeParams struct {
	UserID int32
	Type   string
	Limit  int32
	Offset int32
}

func (q *Queries) GetNotificationsByType(ctx context.Context, arg GetNotificationsByTypeParams) ([]Notification, error) {
	rows, err := q.db.Query(ctx, getNotificationsByType,
		arg.UserID,
		arg.Type,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Notification
	for rows.Next() {
		var i Notification
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Type,
			&i.Title,
			&i.Message,
			&i.Data,
			&i.IsRead,
			&i.CreatedAt,
			&i.ReadAt,
			&i.MatchID,
			&i.SeasonID,
			&i.PlayerID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPendingEmailReminders = `-- name: GetPendingEmailReminders :many
SELECT id, player_id, reminder_type, scheduled_at, retry_count, match_id, season_id
FROM email_reminders
WHERE status = 'pending'
  AND (scheduled_at <= $1 OR next_retry_at <= $1)
ORDER BY scheduled_at ASC
LIMIT $2
`

type GetPendingEmailRemindersParams struct {
	ScheduledAt pgtype.Timestamp
	Limit       int32
}

type GetPendingEmailRemindersRow struct {
	ID           int32
	PlayerID     int32
	ReminderType string
	ScheduledAt  pgtype.Timestamp
	RetryCount   pgtype.Int4
	MatchID      pgtype.Int4
	SeasonID     pgtype.Int4
}

func (q *Queries) GetPendingEmailReminders(ctx context.Context, arg GetPendingEmailRemindersParams) ([]GetPendingEmailRemindersRow, error) {
	rows, err := q.db.Query(ctx, getPendingEmailReminders, arg.ScheduledAt, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPendingEmailRemindersRow
	for rows.Next() {
		var i GetPendingEmailRemindersRow
		if err := rows.Scan(
			&i.ID,
			&i.PlayerID,
			&i.ReminderType,
			&i.ScheduledAt,
			&i.RetryCount,
			&i.MatchID,
			&i.SeasonID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPlayer = `-- name: GetPlayer :one
SELECT id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences FROM players
WHERE id = $1 AND user_id = $2
`

type GetPlayerParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) GetPlayer(ctx context.Context, arg GetPlayerParams) (Player, error) {
	row := q.db.QueryRow(ctx, getPlayer, arg.ID, arg.UserID)
	var i Player
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TeamID,
		&i.Name,
		&i.Email,
		&i.Phone,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PreferredMatchGroup,
		&i.IsActive,
		&i.EmailNotificationsEnabled,
		&i.EmailReminderPreferences,
	)
	return i, err
}

const getPlayerByID = `-- name: GetPlayerByID :one
SELECT id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences FROM players
WHERE id = $1
`

func (q *Queries) GetPlayerByID(ctx context.Context, id int32) (Player, error) {
	row := q.db.QueryRow(ctx, getPlayerByID, id)
	var i Player
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TeamID,
		&i.Name,
		&i.Email,
		&i.Phone,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PreferredMatchGroup,
		&i.IsActive,
		&i.EmailNotificationsEnabled,
		&i.EmailReminderPreferences,
	)
	return i, err
}

const getPlayerColumnVisibility = `-- name: GetPlayerColumnVisibility :many

SELECT column_name, is_visible
FROM player_column_visibilities
WHERE user_id = $1
ORDER BY column_name
`

type GetPlayerColumnVisibilityRow struct {
	ColumnName string
	IsVisible  bool
}

// Player column visibility queries
func (q *Queries) GetPlayerColumnVisibility(ctx context.Context, userID int32) ([]GetPlayerColumnVisibilityRow, error) {
	rows, err := q.db.Query(ctx, getPlayerColumnVisibility, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPlayerColumnVisibilityRow
	for rows.Next() {
		var i GetPlayerColumnVisibilityRow
		if err := rows.Scan(&i.ColumnName, &i.IsVisible); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPlayerCustomColumns = `-- name: GetPlayerCustomColumns :many
SELECT id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at FROM player_custom_columns
WHERE user_id = $1 AND is_active = true
ORDER BY display_order
`

func (q *Queries) GetPlayerCustomColumns(ctx context.Context, userID int32) ([]PlayerCustomColumn, error) {
	rows, err := q.db.Query(ctx, getPlayerCustomColumns, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []PlayerCustomColumn
	for rows.Next() {
		var i PlayerCustomColumn
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.FieldType,
			&i.Description,
			&i.IsRequired,
			&i.IsActive,
			&i.DisplayOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPlayerCustomValues = `-- name: GetPlayerCustomValues :many
SELECT pcv.id, pcv.player_id, pcv.column_id, pcv.value, pcv.created_at, pcv.updated_at, pcc.name as column_name, pcc.field_type
FROM player_custom_values pcv
JOIN player_custom_columns pcc ON pcv.column_id = pcc.id
WHERE pcv.player_id = $1
`

type GetPlayerCustomValuesRow struct {
	ID         int32
	PlayerID   pgtype.Int4
	ColumnID   pgtype.Int4
	Value      pgtype.Text
	CreatedAt  pgtype.Timestamp
	UpdatedAt  pgtype.Timestamp
	ColumnName string
	FieldType  string
}

func (q *Queries) GetPlayerCustomValues(ctx context.Context, playerID pgtype.Int4) ([]GetPlayerCustomValuesRow, error) {
	rows, err := q.db.Query(ctx, getPlayerCustomValues, playerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPlayerCustomValuesRow
	for rows.Next() {
		var i GetPlayerCustomValuesRow
		if err := rows.Scan(
			&i.ID,
			&i.PlayerID,
			&i.ColumnID,
			&i.Value,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ColumnName,
			&i.FieldType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPlayerEmailPreferences = `-- name: GetPlayerEmailPreferences :one
SELECT email_reminder_preferences
FROM players
WHERE id = $1
`

func (q *Queries) GetPlayerEmailPreferences(ctx context.Context, id int32) ([]byte, error) {
	row := q.db.QueryRow(ctx, getPlayerEmailPreferences, id)
	var email_reminder_preferences []byte
	err := row.Scan(&email_reminder_preferences)
	return email_reminder_preferences, err
}

const getPlayers = `-- name: GetPlayers :many
SELECT id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences FROM players
WHERE user_id = $1 AND is_active = true
`

func (q *Queries) GetPlayers(ctx context.Context, userID int32) ([]Player, error) {
	rows, err := q.db.Query(ctx, getPlayers, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Player
	for rows.Next() {
		var i Player
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.TeamID,
			&i.Name,
			&i.Email,
			&i.Phone,
			&i.PictureUrl,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.PreferredMatchGroup,
			&i.IsActive,
			&i.EmailNotificationsEnabled,
			&i.EmailReminderPreferences,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPlayersByTeam = `-- name: GetPlayersByTeam :many
SELECT id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences FROM players
WHERE team_id = $1 AND is_active = true
ORDER BY name ASC
`

func (q *Queries) GetPlayersByTeam(ctx context.Context, teamID pgtype.Int4) ([]Player, error) {
	rows, err := q.db.Query(ctx, getPlayersByTeam, teamID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Player
	for rows.Next() {
		var i Player
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.TeamID,
			&i.Name,
			&i.Email,
			&i.Phone,
			&i.PictureUrl,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.PreferredMatchGroup,
			&i.IsActive,
			&i.EmailNotificationsEnabled,
			&i.EmailReminderPreferences,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPlayersWithNotificationsEnabled = `-- name: GetPlayersWithNotificationsEnabled :many
SELECT players.id, players.name, players.email, players.email_reminder_preferences
FROM players
WHERE players.user_id = (SELECT seasons.user_id FROM seasons WHERE seasons.id = $1)
  AND players.is_active = true
  AND players.email_notifications_enabled = true
  AND players.email IS NOT NULL AND players.email != ''
`

type GetPlayersWithNotificationsEnabledRow struct {
	ID                       int32
	Name                     string
	Email                    pgtype.Text
	EmailReminderPreferences []byte
}

func (q *Queries) GetPlayersWithNotificationsEnabled(ctx context.Context, id int32) ([]GetPlayersWithNotificationsEnabledRow, error) {
	rows, err := q.db.Query(ctx, getPlayersWithNotificationsEnabled, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPlayersWithNotificationsEnabledRow
	for rows.Next() {
		var i GetPlayersWithNotificationsEnabledRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.EmailReminderPreferences,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSeason = `-- name: GetSeason :one
SELECT id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency FROM seasons
WHERE id = $1 AND user_id = $2
`

type GetSeasonParams struct {
	ID     int32
	UserID pgtype.Int4
}

func (q *Queries) GetSeason(ctx context.Context, arg GetSeasonParams) (Season, error) {
	row := q.db.QueryRow(ctx, getSeason, arg.ID, arg.UserID)
	var i Season
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.StartDate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.SeasonType,
		&i.Frequency,
	)
	return i, err
}

const getSeasonByID = `-- name: GetSeasonByID :one
SELECT id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency FROM seasons
WHERE id = $1
`

func (q *Queries) GetSeasonByID(ctx context.Context, id int32) (Season, error) {
	row := q.db.QueryRow(ctx, getSeasonByID, id)
	var i Season
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.StartDate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.SeasonType,
		&i.Frequency,
	)
	return i, err
}

const getSeasonPermissions = `-- name: GetSeasonPermissions :many
SELECT sp.id, sp.season_id, sp.user_id, sp.permission_level, sp.granted_by, sp.granted_at, sp.is_active, u.name as user_name, u.email as user_email, 
       gb.name as granted_by_name
FROM season_permissions sp
JOIN users u ON sp.user_id = u.id
JOIN users gb ON sp.granted_by = gb.id
WHERE sp.season_id = $1 AND sp.is_active = true
ORDER BY sp.granted_at DESC
`

type GetSeasonPermissionsRow struct {
	ID              int32
	SeasonID        int32
	UserID          int32
	PermissionLevel PermissionLevelEnum
	GrantedBy       int32
	GrantedAt       pgtype.Timestamptz
	IsActive        pgtype.Bool
	UserName        string
	UserEmail       string
	GrantedByName   string
}

func (q *Queries) GetSeasonPermissions(ctx context.Context, seasonID int32) ([]GetSeasonPermissionsRow, error) {
	rows, err := q.db.Query(ctx, getSeasonPermissions, seasonID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSeasonPermissionsRow
	for rows.Next() {
		var i GetSeasonPermissionsRow
		if err := rows.Scan(
			&i.ID,
			&i.SeasonID,
			&i.UserID,
			&i.PermissionLevel,
			&i.GrantedBy,
			&i.GrantedAt,
			&i.IsActive,
			&i.UserName,
			&i.UserEmail,
			&i.GrantedByName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSeasonPublic = `-- name: GetSeasonPublic :one
SELECT id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency FROM seasons
WHERE id = $1 AND is_active = true
`

func (q *Queries) GetSeasonPublic(ctx context.Context, id int32) (Season, error) {
	row := q.db.QueryRow(ctx, getSeasonPublic, id)
	var i Season
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.StartDate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.SeasonType,
		&i.Frequency,
	)
	return i, err
}

const getSeasonScoreboard = `-- name: GetSeasonScoreboard :many
SELECT p.id as player_id, p.name as player_name, COUNT(m.winner_id) as wins
FROM players p
LEFT JOIN matches m ON p.id = m.winner_id AND m.season_id = $1
WHERE p.user_id = (SELECT s.user_id FROM seasons s WHERE s.id = $2)
GROUP BY p.id, p.name
ORDER BY wins DESC
`

type GetSeasonScoreboardParams struct {
	SeasonID pgtype.Int4
	ID       int32
}

type GetSeasonScoreboardRow struct {
	PlayerID   int32
	PlayerName string
	Wins       int64
}

func (q *Queries) GetSeasonScoreboard(ctx context.Context, arg GetSeasonScoreboardParams) ([]GetSeasonScoreboardRow, error) {
	rows, err := q.db.Query(ctx, getSeasonScoreboard, arg.SeasonID, arg.ID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSeasonScoreboardRow
	for rows.Next() {
		var i GetSeasonScoreboardRow
		if err := rows.Scan(&i.PlayerID, &i.PlayerName, &i.Wins); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSeasonUpcomingMatches = `-- name: GetSeasonUpcomingMatches :many
SELECT id, season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, winner_id, created_at, updated_at, is_active, match_group FROM matches
WHERE season_id = $1 AND match_date > CURRENT_TIMESTAMP
ORDER BY match_date ASC
LIMIT 5
`

func (q *Queries) GetSeasonUpcomingMatches(ctx context.Context, seasonID pgtype.Int4) ([]Match, error) {
	rows, err := q.db.Query(ctx, getSeasonUpcomingMatches, seasonID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Match
	for rows.Next() {
		var i Match
		if err := rows.Scan(
			&i.ID,
			&i.SeasonID,
			&i.PlayerId1,
			&i.PlayerId1Points,
			&i.PlayerId2,
			&i.PlayerId2Points,
			&i.MatchDate,
			&i.WinnerID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
			&i.MatchGroup,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSeasonWithPermissionCheck = `-- name: GetSeasonWithPermissionCheck :one
SELECT s.id, s.user_id, s.name, s.start_date, s.created_at, s.updated_at, s.is_active, s.season_type, s.frequency, 
       COALESCE(sp.permission_level, 'owner'::permission_level_enum) as user_permission_level
FROM seasons s
LEFT JOIN season_permissions sp ON s.id = sp.season_id AND sp.user_id = $2 AND sp.is_active = true
WHERE s.id = $1 AND s.is_active = true
  AND (s.user_id = $2 OR sp.user_id = $2)
`

type GetSeasonWithPermissionCheckParams struct {
	ID     int32
	UserID int32
}

type GetSeasonWithPermissionCheckRow struct {
	ID                  int32
	UserID              pgtype.Int4
	Name                string
	StartDate           pgtype.Date
	CreatedAt           pgtype.Timestamp
	UpdatedAt           pgtype.Timestamp
	IsActive            bool
	SeasonType          string
	Frequency           string
	UserPermissionLevel PermissionLevelEnum
}

func (q *Queries) GetSeasonWithPermissionCheck(ctx context.Context, arg GetSeasonWithPermissionCheckParams) (GetSeasonWithPermissionCheckRow, error) {
	row := q.db.QueryRow(ctx, getSeasonWithPermissionCheck, arg.ID, arg.UserID)
	var i GetSeasonWithPermissionCheckRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.StartDate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.SeasonType,
		&i.Frequency,
		&i.UserPermissionLevel,
	)
	return i, err
}

const getSeasons = `-- name: GetSeasons :many
SELECT id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency FROM seasons
WHERE user_id = $1 AND is_active = true
`

func (q *Queries) GetSeasons(ctx context.Context, userID pgtype.Int4) ([]Season, error) {
	rows, err := q.db.Query(ctx, getSeasons, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Season
	for rows.Next() {
		var i Season
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.StartDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
			&i.SeasonType,
			&i.Frequency,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSeasonsWithPermissions = `-- name: GetSeasonsWithPermissions :many
SELECT DISTINCT s.id, s.user_id, s.name, s.start_date, s.created_at, s.updated_at, s.is_active, s.season_type, s.frequency, 
       COALESCE(sp.permission_level, 'owner'::permission_level_enum) as user_permission_level
FROM seasons s
LEFT JOIN season_permissions sp ON s.id = sp.season_id AND sp.user_id = $1 AND sp.is_active = true
WHERE (s.user_id = $1 OR sp.user_id = $1) AND s.is_active = true
ORDER BY s.created_at DESC
`

type GetSeasonsWithPermissionsRow struct {
	ID                  int32
	UserID              pgtype.Int4
	Name                string
	StartDate           pgtype.Date
	CreatedAt           pgtype.Timestamp
	UpdatedAt           pgtype.Timestamp
	IsActive            bool
	SeasonType          string
	Frequency           string
	UserPermissionLevel PermissionLevelEnum
}

func (q *Queries) GetSeasonsWithPermissions(ctx context.Context, userID int32) ([]GetSeasonsWithPermissionsRow, error) {
	rows, err := q.db.Query(ctx, getSeasonsWithPermissions, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSeasonsWithPermissionsRow
	for rows.Next() {
		var i GetSeasonsWithPermissionsRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.StartDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
			&i.SeasonType,
			&i.Frequency,
			&i.UserPermissionLevel,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSpending = `-- name: GetSpending :one
SELECT id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active FROM spending
WHERE id = $1 AND user_id = $2 AND is_active = true
`

type GetSpendingParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) GetSpending(ctx context.Context, arg GetSpendingParams) (Spending, error) {
	row := q.db.QueryRow(ctx, getSpending, arg.ID, arg.UserID)
	var i Spending
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Amount,
		&i.Description,
		&i.Date,
		&i.Category,
		&i.FileUrls,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const getSpendingMatchAssociations = `-- name: GetSpendingMatchAssociations :many
SELECT m.id, m.match_date FROM matches m
JOIN spending_matches sm ON m.id = sm.match_id
WHERE sm.spending_id = $1
`

type GetSpendingMatchAssociationsRow struct {
	ID        int32
	MatchDate pgtype.Date
}

func (q *Queries) GetSpendingMatchAssociations(ctx context.Context, spendingID int32) ([]GetSpendingMatchAssociationsRow, error) {
	rows, err := q.db.Query(ctx, getSpendingMatchAssociations, spendingID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSpendingMatchAssociationsRow
	for rows.Next() {
		var i GetSpendingMatchAssociationsRow
		if err := rows.Scan(&i.ID, &i.MatchDate); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSpendingPlayerAssociations = `-- name: GetSpendingPlayerAssociations :many
SELECT p.id, p.name FROM players p
JOIN spending_players sp ON p.id = sp.player_id
WHERE sp.spending_id = $1
`

type GetSpendingPlayerAssociationsRow struct {
	ID   int32
	Name string
}

func (q *Queries) GetSpendingPlayerAssociations(ctx context.Context, spendingID int32) ([]GetSpendingPlayerAssociationsRow, error) {
	rows, err := q.db.Query(ctx, getSpendingPlayerAssociations, spendingID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSpendingPlayerAssociationsRow
	for rows.Next() {
		var i GetSpendingPlayerAssociationsRow
		if err := rows.Scan(&i.ID, &i.Name); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSpendingSeasonAssociations = `-- name: GetSpendingSeasonAssociations :many
SELECT s.id, s.name FROM seasons s
JOIN spending_seasons ss ON s.id = ss.season_id
WHERE ss.spending_id = $1
`

type GetSpendingSeasonAssociationsRow struct {
	ID   int32
	Name string
}

func (q *Queries) GetSpendingSeasonAssociations(ctx context.Context, spendingID int32) ([]GetSpendingSeasonAssociationsRow, error) {
	rows, err := q.db.Query(ctx, getSpendingSeasonAssociations, spendingID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSpendingSeasonAssociationsRow
	for rows.Next() {
		var i GetSpendingSeasonAssociationsRow
		if err := rows.Scan(&i.ID, &i.Name); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSpendingTeamAssociations = `-- name: GetSpendingTeamAssociations :many
SELECT t.id, t.name FROM teams t
JOIN spending_teams st ON t.id = st.team_id
WHERE st.spending_id = $1
`

type GetSpendingTeamAssociationsRow struct {
	ID   int32
	Name string
}

func (q *Queries) GetSpendingTeamAssociations(ctx context.Context, spendingID int32) ([]GetSpendingTeamAssociationsRow, error) {
	rows, err := q.db.Query(ctx, getSpendingTeamAssociations, spendingID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSpendingTeamAssociationsRow
	for rows.Next() {
		var i GetSpendingTeamAssociationsRow
		if err := rows.Scan(&i.ID, &i.Name); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSpendingWithAssociations = `-- name: GetSpendingWithAssociations :many
SELECT DISTINCT s.id, s.user_id, s.amount, s.description, s.date, s.category, s.file_urls, s.created_at, s.updated_at
FROM spending s
LEFT JOIN spending_players sp ON s.id = sp.spending_id
LEFT JOIN spending_matches sm ON s.id = sm.spending_id
LEFT JOIN spending_seasons ss ON s.id = ss.spending_id
LEFT JOIN spending_teams st ON s.id = st.spending_id
WHERE s.user_id = $1 AND s.is_active = true
ORDER BY s.date DESC, s.created_at DESC
`

type GetSpendingWithAssociationsRow struct {
	ID          int32
	UserID      int32
	Amount      pgtype.Numeric
	Description string
	Date        pgtype.Date
	Category    string
	FileUrls    []string
	CreatedAt   pgtype.Timestamp
	UpdatedAt   pgtype.Timestamp
}

func (q *Queries) GetSpendingWithAssociations(ctx context.Context, userID int32) ([]GetSpendingWithAssociationsRow, error) {
	rows, err := q.db.Query(ctx, getSpendingWithAssociations, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSpendingWithAssociationsRow
	for rows.Next() {
		var i GetSpendingWithAssociationsRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Description,
			&i.Date,
			&i.Category,
			&i.FileUrls,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTeam = `-- name: GetTeam :one
SELECT id, user_id, name, description, picture_url, created_at, updated_at, is_active FROM teams
WHERE id = $1 AND user_id = $2
`

type GetTeamParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) GetTeam(ctx context.Context, arg GetTeamParams) (Team, error) {
	row := q.db.QueryRow(ctx, getTeam, arg.ID, arg.UserID)
	var i Team
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.Description,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const getTeams = `-- name: GetTeams :many

SELECT id, user_id, name, description, picture_url, created_at, updated_at, is_active FROM teams
WHERE user_id = $1 AND is_active = true
ORDER BY name ASC
`

// Teams queries
func (q *Queries) GetTeams(ctx context.Context, userID int32) ([]Team, error) {
	rows, err := q.db.Query(ctx, getTeams, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Team
	for rows.Next() {
		var i Team
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Name,
			&i.Description,
			&i.PictureUrl,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUnreadNotificationCount = `-- name: GetUnreadNotificationCount :one
SELECT COUNT(*) FROM notifications 
WHERE user_id = $1 AND is_read = FALSE
`

func (q *Queries) GetUnreadNotificationCount(ctx context.Context, userID int32) (int64, error) {
	row := q.db.QueryRow(ctx, getUnreadNotificationCount, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getUserAppSettings = `-- name: GetUserAppSettings :one
SELECT json_settings FROM users
WHERE id = $1
`

func (q *Queries) GetUserAppSettings(ctx context.Context, id int32) (pgtype.Text, error) {
	row := q.db.QueryRow(ctx, getUserAppSettings, id)
	var json_settings pgtype.Text
	err := row.Scan(&json_settings)
	return json_settings, err
}

const getUserByEmail = `-- name: GetUserByEmail :one

SELECT id, email, is_verified
FROM users
WHERE email = $1
`

type GetUserByEmailRow struct {
	ID         int32
	Email      string
	IsVerified bool
}

// query.sql with PostgreSQL parameter syntax
func (q *Queries) GetUserByEmail(ctx context.Context, email string) (GetUserByEmailRow, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i GetUserByEmailRow
	err := row.Scan(&i.ID, &i.Email, &i.IsVerified)
	return i, err
}

const getUserByEmailActive = `-- name: GetUserByEmailActive :one
SELECT id, name, email FROM users 
WHERE email = $1 AND is_active = true
`

type GetUserByEmailActiveRow struct {
	ID    int32
	Name  string
	Email string
}

func (q *Queries) GetUserByEmailActive(ctx context.Context, email string) (GetUserByEmailActiveRow, error) {
	row := q.db.QueryRow(ctx, getUserByEmailActive, email)
	var i GetUserByEmailActiveRow
	err := row.Scan(&i.ID, &i.Name, &i.Email)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, stytch_id, stripe_id, name, email, country, phone, birthday, lang, picture_url, created_at, updated_at, is_active, is_verified, subscription_tier, is_sidebar_open, json_settings
FROM users
WHERE id = $1
`

func (q *Queries) GetUserByID(ctx context.Context, id int32) (User, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.StytchID,
		&i.StripeID,
		&i.Name,
		&i.Email,
		&i.Country,
		&i.Phone,
		&i.Birthday,
		&i.Lang,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.IsVerified,
		&i.SubscriptionTier,
		&i.IsSidebarOpen,
		&i.JsonSettings,
	)
	return i, err
}

const getUserByStripeID = `-- name: GetUserByStripeID :one
SELECT id, stytch_id, stripe_id, name, email, country, phone, birthday, lang, picture_url, created_at, updated_at, is_active, is_verified, subscription_tier, is_sidebar_open, json_settings FROM users WHERE stripe_id = $1
`

func (q *Queries) GetUserByStripeID(ctx context.Context, stripeID string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByStripeID, stripeID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.StytchID,
		&i.StripeID,
		&i.Name,
		&i.Email,
		&i.Country,
		&i.Phone,
		&i.Birthday,
		&i.Lang,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.IsVerified,
		&i.SubscriptionTier,
		&i.IsSidebarOpen,
		&i.JsonSettings,
	)
	return i, err
}

const getUserByStytchID = `-- name: GetUserByStytchID :one
SELECT id, stytch_id, stripe_id, name, email, country, phone, birthday, lang, picture_url, created_at, updated_at, is_active, is_verified, subscription_tier, is_sidebar_open, json_settings FROM users
WHERE stytch_id = $1
`

func (q *Queries) GetUserByStytchID(ctx context.Context, stytchID string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByStytchID, stytchID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.StytchID,
		&i.StripeID,
		&i.Name,
		&i.Email,
		&i.Country,
		&i.Phone,
		&i.Birthday,
		&i.Lang,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.IsVerified,
		&i.SubscriptionTier,
		&i.IsSidebarOpen,
		&i.JsonSettings,
	)
	return i, err
}

const getUserNotifications = `-- name: GetUserNotifications :many
SELECT id, user_id, type, title, message, data, is_read, created_at, read_at, match_id, season_id, player_id FROM notifications 
WHERE user_id = $1 
ORDER BY created_at DESC
LIMIT $2 OFFSET $3
`

type GetUserNotificationsParams struct {
	UserID int32
	Limit  int32
	Offset int32
}

func (q *Queries) GetUserNotifications(ctx context.Context, arg GetUserNotificationsParams) ([]Notification, error) {
	rows, err := q.db.Query(ctx, getUserNotifications, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Notification
	for rows.Next() {
		var i Notification
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Type,
			&i.Title,
			&i.Message,
			&i.Data,
			&i.IsRead,
			&i.CreatedAt,
			&i.ReadAt,
			&i.MatchID,
			&i.SeasonID,
			&i.PlayerID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserSeasonPermission = `-- name: GetUserSeasonPermission :one

SELECT sp.permission_level, sp.granted_at, sp.granted_by, u.name as granted_by_name
FROM season_permissions sp
JOIN users u ON sp.granted_by = u.id
WHERE sp.season_id = $1 AND sp.user_id = $2 AND sp.is_active = true
`

type GetUserSeasonPermissionParams struct {
	SeasonID int32
	UserID   int32
}

type GetUserSeasonPermissionRow struct {
	PermissionLevel PermissionLevelEnum
	GrantedAt       pgtype.Timestamptz
	GrantedBy       int32
	GrantedByName   string
}

// Season Permission queries
func (q *Queries) GetUserSeasonPermission(ctx context.Context, arg GetUserSeasonPermissionParams) (GetUserSeasonPermissionRow, error) {
	row := q.db.QueryRow(ctx, getUserSeasonPermission, arg.SeasonID, arg.UserID)
	var i GetUserSeasonPermissionRow
	err := row.Scan(
		&i.PermissionLevel,
		&i.GrantedAt,
		&i.GrantedBy,
		&i.GrantedByName,
	)
	return i, err
}

const getUserSpending = `-- name: GetUserSpending :many
SELECT id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active FROM spending
WHERE user_id = $1 AND is_active = true
ORDER BY date DESC, created_at DESC
`

func (q *Queries) GetUserSpending(ctx context.Context, userID int32) ([]Spending, error) {
	rows, err := q.db.Query(ctx, getUserSpending, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Spending
	for rows.Next() {
		var i Spending
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Description,
			&i.Date,
			&i.Category,
			&i.FileUrls,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserSubscription = `-- name: GetUserSubscription :one
SELECT subscription_tier, stripe_id
FROM users
WHERE id = $1
`

type GetUserSubscriptionRow struct {
	SubscriptionTier string
	StripeID         string
}

func (q *Queries) GetUserSubscription(ctx context.Context, id int32) (GetUserSubscriptionRow, error) {
	row := q.db.QueryRow(ctx, getUserSubscription, id)
	var i GetUserSubscriptionRow
	err := row.Scan(&i.SubscriptionTier, &i.StripeID)
	return i, err
}

const getUserUserSettings = `-- name: GetUserUserSettings :one
SELECT json_settings FROM users
WHERE id = $1
`

func (q *Queries) GetUserUserSettings(ctx context.Context, id int32) (pgtype.Text, error) {
	row := q.db.QueryRow(ctx, getUserUserSettings, id)
	var json_settings pgtype.Text
	err := row.Scan(&json_settings)
	return json_settings, err
}

const getUserVerificationStatus = `-- name: GetUserVerificationStatus :one
SELECT is_verified FROM users
WHERE id = $1
`

func (q *Queries) GetUserVerificationStatus(ctx context.Context, id int32) (bool, error) {
	row := q.db.QueryRow(ctx, getUserVerificationStatus, id)
	var is_verified bool
	err := row.Scan(&is_verified)
	return is_verified, err
}

const grantSeasonPermission = `-- name: GrantSeasonPermission :one
INSERT INTO season_permissions (season_id, user_id, permission_level, granted_by)
VALUES ($1, $2, $3, $4)
ON CONFLICT (season_id, user_id) 
DO UPDATE SET 
    permission_level = EXCLUDED.permission_level,
    granted_by = EXCLUDED.granted_by,
    granted_at = NOW(),
    is_active = true
RETURNING id, season_id, user_id, permission_level, granted_by, granted_at, is_active
`

type GrantSeasonPermissionParams struct {
	SeasonID        int32
	UserID          int32
	PermissionLevel PermissionLevelEnum
	GrantedBy       int32
}

func (q *Queries) GrantSeasonPermission(ctx context.Context, arg GrantSeasonPermissionParams) (SeasonPermission, error) {
	row := q.db.QueryRow(ctx, grantSeasonPermission,
		arg.SeasonID,
		arg.UserID,
		arg.PermissionLevel,
		arg.GrantedBy,
	)
	var i SeasonPermission
	err := row.Scan(
		&i.ID,
		&i.SeasonID,
		&i.UserID,
		&i.PermissionLevel,
		&i.GrantedBy,
		&i.GrantedAt,
		&i.IsActive,
	)
	return i, err
}

const hardDeleteUser = `-- name: HardDeleteUser :exec
DELETE FROM users
WHERE id = $1
`

func (q *Queries) HardDeleteUser(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, hardDeleteUser, id)
	return err
}

const logPermissionChange = `-- name: LogPermissionChange :exec
INSERT INTO season_permission_history 
(season_id, user_id, permission_level, action, performed_by)
VALUES ($1, $2, $3, $4, $5)
`

type LogPermissionChangeParams struct {
	SeasonID        int32
	UserID          int32
	PermissionLevel NullPermissionLevelEnum
	Action          PermissionActionEnum
	PerformedBy     int32
}

func (q *Queries) LogPermissionChange(ctx context.Context, arg LogPermissionChangeParams) error {
	_, err := q.db.Exec(ctx, logPermissionChange,
		arg.SeasonID,
		arg.UserID,
		arg.PermissionLevel,
		arg.Action,
		arg.PerformedBy,
	)
	return err
}

const markAllNotificationsAsRead = `-- name: MarkAllNotificationsAsRead :exec
UPDATE notifications 
SET is_read = TRUE, read_at = CURRENT_TIMESTAMP
WHERE user_id = $1 AND is_read = FALSE
`

func (q *Queries) MarkAllNotificationsAsRead(ctx context.Context, userID int32) error {
	_, err := q.db.Exec(ctx, markAllNotificationsAsRead, userID)
	return err
}

const markNotificationAsRead = `-- name: MarkNotificationAsRead :exec
UPDATE notifications 
SET is_read = TRUE, read_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2
`

type MarkNotificationAsReadParams struct {
	ID     int32
	UserID int32
}

func (q *Queries) MarkNotificationAsRead(ctx context.Context, arg MarkNotificationAsReadParams) error {
	_, err := q.db.Exec(ctx, markNotificationAsRead, arg.ID, arg.UserID)
	return err
}

const markReminderSent = `-- name: MarkReminderSent :exec
UPDATE email_reminders
SET status = 'sent', sent_at = $2
WHERE id = $1
`

type MarkReminderSentParams struct {
	ID     int32
	SentAt pgtype.Timestamp
}

func (q *Queries) MarkReminderSent(ctx context.Context, arg MarkReminderSentParams) error {
	_, err := q.db.Exec(ctx, markReminderSent, arg.ID, arg.SentAt)
	return err
}

const markReminderSkipped = `-- name: MarkReminderSkipped :exec
UPDATE email_reminders
SET status = 'skipped', last_error = $2
WHERE id = $1
`

type MarkReminderSkippedParams struct {
	ID        int32
	LastError pgtype.Text
}

func (q *Queries) MarkReminderSkipped(ctx context.Context, arg MarkReminderSkippedParams) error {
	_, err := q.db.Exec(ctx, markReminderSkipped, arg.ID, arg.LastError)
	return err
}

const moveReminderToDeadLetter = `-- name: MoveReminderToDeadLetter :exec
WITH moved_reminder AS (
    UPDATE email_reminders
    SET status = 'dead_letter'
    WHERE email_reminders.id = $1
    RETURNING id, player_id, reminder_type, scheduled_at, sent_at, failed_at, retry_count, next_retry_at, last_error, status, match_id, season_id, created_at
)
INSERT INTO email_dead_letter_queue
    (original_reminder_id, player_id, reminder_type, failure_reason, retry_count, last_attempt_at)
SELECT moved_reminder.id, moved_reminder.player_id, moved_reminder.reminder_type, $2, moved_reminder.retry_count, CURRENT_TIMESTAMP
FROM moved_reminder
`

type MoveReminderToDeadLetterParams struct {
	ID            int32
	FailureReason string
}

func (q *Queries) MoveReminderToDeadLetter(ctx context.Context, arg MoveReminderToDeadLetterParams) error {
	_, err := q.db.Exec(ctx, moveReminderToDeadLetter, arg.ID, arg.FailureReason)
	return err
}

const revokeSeasonPermission = `-- name: RevokeSeasonPermission :exec
UPDATE season_permissions 
SET is_active = false
WHERE season_id = $1 AND user_id = $2
`

type RevokeSeasonPermissionParams struct {
	SeasonID int32
	UserID   int32
}

func (q *Queries) RevokeSeasonPermission(ctx context.Context, arg RevokeSeasonPermissionParams) error {
	_, err := q.db.Exec(ctx, revokeSeasonPermission, arg.SeasonID, arg.UserID)
	return err
}

const scheduleEmailReminder = `-- name: ScheduleEmailReminder :one

INSERT INTO email_reminders (
    player_id, reminder_type, scheduled_at, match_id, season_id
) VALUES (
    $1, $2, $3, $4, $5
)
RETURNING id, player_id, reminder_type, scheduled_at, sent_at, failed_at, retry_count, next_retry_at, last_error, status, match_id, season_id, created_at
`

type ScheduleEmailReminderParams struct {
	PlayerID     int32
	ReminderType string
	ScheduledAt  pgtype.Timestamp
	MatchID      pgtype.Int4
	SeasonID     pgtype.Int4
}

// Email Reminders queries
func (q *Queries) ScheduleEmailReminder(ctx context.Context, arg ScheduleEmailReminderParams) (EmailReminder, error) {
	row := q.db.QueryRow(ctx, scheduleEmailReminder,
		arg.PlayerID,
		arg.ReminderType,
		arg.ScheduledAt,
		arg.MatchID,
		arg.SeasonID,
	)
	var i EmailReminder
	err := row.Scan(
		&i.ID,
		&i.PlayerID,
		&i.ReminderType,
		&i.ScheduledAt,
		&i.SentAt,
		&i.FailedAt,
		&i.RetryCount,
		&i.NextRetryAt,
		&i.LastError,
		&i.Status,
		&i.MatchID,
		&i.SeasonID,
		&i.CreatedAt,
	)
	return i, err
}

const scheduleReminderRetry = `-- name: ScheduleReminderRetry :exec
UPDATE email_reminders
SET retry_count = $2, next_retry_at = $3, last_error = $4, status = 'pending'
WHERE id = $1
`

type ScheduleReminderRetryParams struct {
	ID          int32
	RetryCount  pgtype.Int4
	NextRetryAt pgtype.Timestamp
	LastError   pgtype.Text
}

func (q *Queries) ScheduleReminderRetry(ctx context.Context, arg ScheduleReminderRetryParams) error {
	_, err := q.db.Exec(ctx, scheduleReminderRetry,
		arg.ID,
		arg.RetryCount,
		arg.NextRetryAt,
		arg.LastError,
	)
	return err
}

const searchAndFilterUserSpending = `-- name: SearchAndFilterUserSpending :many
SELECT id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active FROM spending
WHERE user_id = $1 AND is_active = true
  AND (
    CASE WHEN $2::text = '' THEN true 
    ELSE (description ILIKE '%' || $2 || '%' OR category ILIKE '%' || $2 || '%')
    END
  )
  AND (
    CASE WHEN $3::text = '' THEN true 
    ELSE category = $3
    END
  )
  AND (
    CASE WHEN $4::date IS NULL THEN true 
    ELSE date >= $4
    END
  )
  AND (
    CASE WHEN $5::date IS NULL THEN true 
    ELSE date <= $5
    END
  )
ORDER BY date DESC, created_at DESC
`

type SearchAndFilterUserSpendingParams struct {
	UserID  int32
	Column2 string
	Column3 string
	Column4 pgtype.Date
	Column5 pgtype.Date
}

func (q *Queries) SearchAndFilterUserSpending(ctx context.Context, arg SearchAndFilterUserSpendingParams) ([]Spending, error) {
	rows, err := q.db.Query(ctx, searchAndFilterUserSpending,
		arg.UserID,
		arg.Column2,
		arg.Column3,
		arg.Column4,
		arg.Column5,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Spending
	for rows.Next() {
		var i Spending
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Description,
			&i.Date,
			&i.Category,
			&i.FileUrls,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchUserSpending = `-- name: SearchUserSpending :many
SELECT id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active FROM spending
WHERE user_id = $1 AND is_active = true
  AND (
    description ILIKE '%' || $2 || '%' OR
    category ILIKE '%' || $2 || '%'
  )
ORDER BY date DESC, created_at DESC
`

type SearchUserSpendingParams struct {
	UserID  int32
	Column2 pgtype.Text
}

func (q *Queries) SearchUserSpending(ctx context.Context, arg SearchUserSpendingParams) ([]Spending, error) {
	rows, err := q.db.Query(ctx, searchUserSpending, arg.UserID, arg.Column2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Spending
	for rows.Next() {
		var i Spending
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Description,
			&i.Date,
			&i.Category,
			&i.FileUrls,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAPIKeyLastUsed = `-- name: UpdateAPIKeyLastUsed :exec
UPDATE api_keys 
SET last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) UpdateAPIKeyLastUsed(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, updateAPIKeyLastUsed, id)
	return err
}

const updateAPIKeyName = `-- name: UpdateAPIKeyName :exec
UPDATE api_keys 
SET name = $3, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $2
`

type UpdateAPIKeyNameParams struct {
	ID     int32
	UserID int32
	Name   string
}

func (q *Queries) UpdateAPIKeyName(ctx context.Context, arg UpdateAPIKeyNameParams) error {
	_, err := q.db.Exec(ctx, updateAPIKeyName, arg.ID, arg.UserID, arg.Name)
	return err
}

const updateMatch = `-- name: UpdateMatch :one
UPDATE matches
SET season_id = $1,
    player_id1 = $2,
    player_id1_points = $3,
    player_id2 = $4,
    player_id2_points = $5,
    match_date = $6,
    match_group = $7,
    is_active = $8,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $9
RETURNING id, season_id, player_id1, player_id1_points, player_id2, player_id2_points, match_date, winner_id, created_at, updated_at, is_active, match_group
`

type UpdateMatchParams struct {
	SeasonID        pgtype.Int4
	PlayerId1       pgtype.Int4
	PlayerId1Points pgtype.Int4
	PlayerId2       pgtype.Int4
	PlayerId2Points pgtype.Int4
	MatchDate       pgtype.Date
	MatchGroup      int32
	IsActive        bool
	ID              int32
}

func (q *Queries) UpdateMatch(ctx context.Context, arg UpdateMatchParams) (Match, error) {
	row := q.db.QueryRow(ctx, updateMatch,
		arg.SeasonID,
		arg.PlayerId1,
		arg.PlayerId1Points,
		arg.PlayerId2,
		arg.PlayerId2Points,
		arg.MatchDate,
		arg.MatchGroup,
		arg.IsActive,
		arg.ID,
	)
	var i Match
	err := row.Scan(
		&i.ID,
		&i.SeasonID,
		&i.PlayerId1,
		&i.PlayerId1Points,
		&i.PlayerId2,
		&i.PlayerId2Points,
		&i.MatchDate,
		&i.WinnerID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.MatchGroup,
	)
	return i, err
}

const updateMatchCustomColumn = `-- name: UpdateMatchCustomColumn :one
UPDATE match_custom_columns
SET name = $1,
    field_type = $2,
    description = $3,
    is_required = $4,
    display_order = $5,
    is_active = $6,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $7
RETURNING id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at
`

type UpdateMatchCustomColumnParams struct {
	Name         string
	FieldType    string
	Description  pgtype.Text
	IsRequired   pgtype.Bool
	DisplayOrder int32
	IsActive     pgtype.Bool
	ID           int32
}

func (q *Queries) UpdateMatchCustomColumn(ctx context.Context, arg UpdateMatchCustomColumnParams) (MatchCustomColumn, error) {
	row := q.db.QueryRow(ctx, updateMatchCustomColumn,
		arg.Name,
		arg.FieldType,
		arg.Description,
		arg.IsRequired,
		arg.DisplayOrder,
		arg.IsActive,
		arg.ID,
	)
	var i MatchCustomColumn
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.FieldType,
		&i.Description,
		&i.IsRequired,
		&i.IsActive,
		&i.DisplayOrder,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateMatchPlayer1 = `-- name: UpdateMatchPlayer1 :exec
UPDATE matches
SET player_id1 = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
`

type UpdateMatchPlayer1Params struct {
	ID        int32
	PlayerId1 pgtype.Int4
}

func (q *Queries) UpdateMatchPlayer1(ctx context.Context, arg UpdateMatchPlayer1Params) error {
	_, err := q.db.Exec(ctx, updateMatchPlayer1, arg.ID, arg.PlayerId1)
	return err
}

const updateMatchPlayer2 = `-- name: UpdateMatchPlayer2 :exec
UPDATE matches
SET player_id2 = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
`

type UpdateMatchPlayer2Params struct {
	ID        int32
	PlayerId2 pgtype.Int4
}

func (q *Queries) UpdateMatchPlayer2(ctx context.Context, arg UpdateMatchPlayer2Params) error {
	_, err := q.db.Exec(ctx, updateMatchPlayer2, arg.ID, arg.PlayerId2)
	return err
}

const updateMatchesBatch = `-- name: UpdateMatchesBatch :exec
UPDATE matches
SET season_id = m.season_id,
    player_id1 = m.player_id1,
    player_id1_points = m.player_id1Points,
    player_id2 = m.player_id2,
    player_id2_points = m.player_id2Points,
    match_date = m.matchDate,
    match_group = m."group",
    is_active = m.isActive,
    updated_at = CURRENT_TIMESTAMP
FROM (SELECT unnest FROM UNNEST ($1::matches[])) AS m
WHERE matches.id = m.id
`

func (q *Queries) UpdateMatchesBatch(ctx context.Context, dollar_1 []interface{}) error {
	_, err := q.db.Exec(ctx, updateMatchesBatch, dollar_1)
	return err
}

const updatePlayer = `-- name: UpdatePlayer :one
UPDATE players
SET name = $1,
    email = $2,
    phone = $3,
    preferred_match_group = $4,
    email_notifications_enabled = $5,
    is_active = $6,
    team_id = $7,
    picture_url = $8,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $9 AND user_id = $10
RETURNING id, user_id, team_id, name, email, phone, picture_url, created_at, updated_at, preferred_match_group, is_active, email_notifications_enabled, email_reminder_preferences
`

type UpdatePlayerParams struct {
	Name                      string
	Email                     pgtype.Text
	Phone                     pgtype.Text
	PreferredMatchGroup       int32
	EmailNotificationsEnabled bool
	IsActive                  bool
	TeamID                    pgtype.Int4
	PictureUrl                pgtype.Text
	ID                        int32
	UserID                    int32
}

func (q *Queries) UpdatePlayer(ctx context.Context, arg UpdatePlayerParams) (Player, error) {
	row := q.db.QueryRow(ctx, updatePlayer,
		arg.Name,
		arg.Email,
		arg.Phone,
		arg.PreferredMatchGroup,
		arg.EmailNotificationsEnabled,
		arg.IsActive,
		arg.TeamID,
		arg.PictureUrl,
		arg.ID,
		arg.UserID,
	)
	var i Player
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TeamID,
		&i.Name,
		&i.Email,
		&i.Phone,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PreferredMatchGroup,
		&i.IsActive,
		&i.EmailNotificationsEnabled,
		&i.EmailReminderPreferences,
	)
	return i, err
}

const updatePlayerCustomColumn = `-- name: UpdatePlayerCustomColumn :one
UPDATE player_custom_columns
SET name = $1,
    field_type = $2,
    description = $3,
    is_required = $4,
    display_order = $5,
    is_active = $6,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $7 AND user_id = $8
RETURNING id, user_id, name, field_type, description, is_required, is_active, display_order, created_at, updated_at
`

type UpdatePlayerCustomColumnParams struct {
	Name         string
	FieldType    string
	Description  pgtype.Text
	IsRequired   pgtype.Bool
	DisplayOrder pgtype.Int4
	IsActive     pgtype.Bool
	ID           int32
	UserID       int32
}

func (q *Queries) UpdatePlayerCustomColumn(ctx context.Context, arg UpdatePlayerCustomColumnParams) (PlayerCustomColumn, error) {
	row := q.db.QueryRow(ctx, updatePlayerCustomColumn,
		arg.Name,
		arg.FieldType,
		arg.Description,
		arg.IsRequired,
		arg.DisplayOrder,
		arg.IsActive,
		arg.ID,
		arg.UserID,
	)
	var i PlayerCustomColumn
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.FieldType,
		&i.Description,
		&i.IsRequired,
		&i.IsActive,
		&i.DisplayOrder,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updatePlayerEmailPreferences = `-- name: UpdatePlayerEmailPreferences :exec
UPDATE players
SET email_reminder_preferences = $2,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
`

type UpdatePlayerEmailPreferencesParams struct {
	ID                       int32
	EmailReminderPreferences []byte
}

func (q *Queries) UpdatePlayerEmailPreferences(ctx context.Context, arg UpdatePlayerEmailPreferencesParams) error {
	_, err := q.db.Exec(ctx, updatePlayerEmailPreferences, arg.ID, arg.EmailReminderPreferences)
	return err
}

const updatePlayerWithEmailPreferences = `-- name: UpdatePlayerWithEmailPreferences :exec
UPDATE players
SET name = $1,
    email = $2,
    phone = $3,
    preferred_match_group = $4,
    email_notifications_enabled = $5,
    email_reminder_preferences = $6,
    is_active = $7,
    team_id = $8,
    picture_url = $9,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $10 AND user_id = $11
`

type UpdatePlayerWithEmailPreferencesParams struct {
	Name                      string
	Email                     pgtype.Text
	Phone                     pgtype.Text
	PreferredMatchGroup       int32
	EmailNotificationsEnabled bool
	EmailReminderPreferences  []byte
	IsActive                  bool
	TeamID                    pgtype.Int4
	PictureUrl                pgtype.Text
	ID                        int32
	UserID                    int32
}

func (q *Queries) UpdatePlayerWithEmailPreferences(ctx context.Context, arg UpdatePlayerWithEmailPreferencesParams) error {
	_, err := q.db.Exec(ctx, updatePlayerWithEmailPreferences,
		arg.Name,
		arg.Email,
		arg.Phone,
		arg.PreferredMatchGroup,
		arg.EmailNotificationsEnabled,
		arg.EmailReminderPreferences,
		arg.IsActive,
		arg.TeamID,
		arg.PictureUrl,
		arg.ID,
		arg.UserID,
	)
	return err
}

const updateSeason = `-- name: UpdateSeason :one
UPDATE seasons
SET name = $1,
    start_date = $2,
    season_type = $3,
    frequency = $4,
    is_active = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $6 AND user_id = $7
RETURNING id, user_id, name, start_date, created_at, updated_at, is_active, season_type, frequency
`

type UpdateSeasonParams struct {
	Name       string
	StartDate  pgtype.Date
	SeasonType string
	Frequency  string
	IsActive   bool
	ID         int32
	UserID     pgtype.Int4
}

func (q *Queries) UpdateSeason(ctx context.Context, arg UpdateSeasonParams) (Season, error) {
	row := q.db.QueryRow(ctx, updateSeason,
		arg.Name,
		arg.StartDate,
		arg.SeasonType,
		arg.Frequency,
		arg.IsActive,
		arg.ID,
		arg.UserID,
	)
	var i Season
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.StartDate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
		&i.SeasonType,
		&i.Frequency,
	)
	return i, err
}

const updateSeasonPermission = `-- name: UpdateSeasonPermission :one
UPDATE season_permissions 
SET permission_level = $3,
    granted_by = $4, 
    granted_at = NOW()
WHERE season_id = $1 AND user_id = $2 AND is_active = true
RETURNING id, season_id, user_id, permission_level, granted_by, granted_at, is_active
`

type UpdateSeasonPermissionParams struct {
	SeasonID        int32
	UserID          int32
	PermissionLevel PermissionLevelEnum
	GrantedBy       int32
}

func (q *Queries) UpdateSeasonPermission(ctx context.Context, arg UpdateSeasonPermissionParams) (SeasonPermission, error) {
	row := q.db.QueryRow(ctx, updateSeasonPermission,
		arg.SeasonID,
		arg.UserID,
		arg.PermissionLevel,
		arg.GrantedBy,
	)
	var i SeasonPermission
	err := row.Scan(
		&i.ID,
		&i.SeasonID,
		&i.UserID,
		&i.PermissionLevel,
		&i.GrantedBy,
		&i.GrantedAt,
		&i.IsActive,
	)
	return i, err
}

const updateSpending = `-- name: UpdateSpending :one
UPDATE spending
SET amount = $1,
    description = $2,
    date = $3,
    category = $4,
    file_urls = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $6 AND user_id = $7
RETURNING id, user_id, amount, description, date, category, file_urls, created_at, updated_at, is_active
`

type UpdateSpendingParams struct {
	Amount      pgtype.Numeric
	Description string
	Date        pgtype.Date
	Category    string
	FileUrls    []string
	ID          int32
	UserID      int32
}

func (q *Queries) UpdateSpending(ctx context.Context, arg UpdateSpendingParams) (Spending, error) {
	row := q.db.QueryRow(ctx, updateSpending,
		arg.Amount,
		arg.Description,
		arg.Date,
		arg.Category,
		arg.FileUrls,
		arg.ID,
		arg.UserID,
	)
	var i Spending
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Amount,
		&i.Description,
		&i.Date,
		&i.Category,
		&i.FileUrls,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const updateSpendingAmount = `-- name: UpdateSpendingAmount :exec
UPDATE spending
SET amount = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3
`

type UpdateSpendingAmountParams struct {
	ID     int32
	Amount pgtype.Numeric
	UserID int32
}

func (q *Queries) UpdateSpendingAmount(ctx context.Context, arg UpdateSpendingAmountParams) error {
	_, err := q.db.Exec(ctx, updateSpendingAmount, arg.ID, arg.Amount, arg.UserID)
	return err
}

const updateSpendingCategory = `-- name: UpdateSpendingCategory :exec
UPDATE spending
SET category = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3
`

type UpdateSpendingCategoryParams struct {
	ID       int32
	Category string
	UserID   int32
}

func (q *Queries) UpdateSpendingCategory(ctx context.Context, arg UpdateSpendingCategoryParams) error {
	_, err := q.db.Exec(ctx, updateSpendingCategory, arg.ID, arg.Category, arg.UserID)
	return err
}

const updateSpendingDate = `-- name: UpdateSpendingDate :exec
UPDATE spending
SET date = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3
`

type UpdateSpendingDateParams struct {
	ID     int32
	Date   pgtype.Date
	UserID int32
}

func (q *Queries) UpdateSpendingDate(ctx context.Context, arg UpdateSpendingDateParams) error {
	_, err := q.db.Exec(ctx, updateSpendingDate, arg.ID, arg.Date, arg.UserID)
	return err
}

const updateSpendingDescription = `-- name: UpdateSpendingDescription :exec
UPDATE spending
SET description = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND user_id = $3
`

type UpdateSpendingDescriptionParams struct {
	ID          int32
	Description string
	UserID      int32
}

func (q *Queries) UpdateSpendingDescription(ctx context.Context, arg UpdateSpendingDescriptionParams) error {
	_, err := q.db.Exec(ctx, updateSpendingDescription, arg.ID, arg.Description, arg.UserID)
	return err
}

const updateTeam = `-- name: UpdateTeam :one
UPDATE teams
SET name = $1,
    description = $2,
    picture_url = $3,
    is_active = $4,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $5 AND user_id = $6
RETURNING id, user_id, name, description, picture_url, created_at, updated_at, is_active
`

type UpdateTeamParams struct {
	Name        string
	Description pgtype.Text
	PictureUrl  pgtype.Text
	IsActive    bool
	ID          int32
	UserID      int32
}

func (q *Queries) UpdateTeam(ctx context.Context, arg UpdateTeamParams) (Team, error) {
	row := q.db.QueryRow(ctx, updateTeam,
		arg.Name,
		arg.Description,
		arg.PictureUrl,
		arg.IsActive,
		arg.ID,
		arg.UserID,
	)
	var i Team
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Name,
		&i.Description,
		&i.PictureUrl,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.IsActive,
	)
	return i, err
}

const updateUser = `-- name: UpdateUser :exec
UPDATE users
SET name = COALESCE($1, name),
    email = COALESCE($2, email),
    phone = COALESCE($3, phone),
    country = COALESCE($4, country),
    birthday = COALESCE($5, birthday),
    lang = COALESCE($6, lang),
    is_verified = COALESCE($7, is_verified),
    picture_url = COALESCE($8, picture_url),
    json_settings = COALESCE($9, json_settings),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $10
`

type UpdateUserParams struct {
	Name         pgtype.Text
	Email        pgtype.Text
	Phone        pgtype.Text
	Country      pgtype.Text
	Birthday     pgtype.Date
	Lang         pgtype.Text
	IsVerified   pgtype.Bool
	PictureUrl   pgtype.Text
	JsonSettings pgtype.Text
	ID           int32
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) error {
	_, err := q.db.Exec(ctx, updateUser,
		arg.Name,
		arg.Email,
		arg.Phone,
		arg.Country,
		arg.Birthday,
		arg.Lang,
		arg.IsVerified,
		arg.PictureUrl,
		arg.JsonSettings,
		arg.ID,
	)
	return err
}

const updateUserSideBarOpen = `-- name: UpdateUserSideBarOpen :exec
UPDATE users
SET is_sidebar_open = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $2
`

type UpdateUserSideBarOpenParams struct {
	IsSidebarOpen bool
	ID            int32
}

func (q *Queries) UpdateUserSideBarOpen(ctx context.Context, arg UpdateUserSideBarOpenParams) error {
	_, err := q.db.Exec(ctx, updateUserSideBarOpen, arg.IsSidebarOpen, arg.ID)
	return err
}

const updateUserSubscriptionTier = `-- name: UpdateUserSubscriptionTier :exec
UPDATE users
SET subscription_tier = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $2
`

type UpdateUserSubscriptionTierParams struct {
	SubscriptionTier string
	ID               int32
}

func (q *Queries) UpdateUserSubscriptionTier(ctx context.Context, arg UpdateUserSubscriptionTierParams) error {
	_, err := q.db.Exec(ctx, updateUserSubscriptionTier, arg.SubscriptionTier, arg.ID)
	return err
}

const updateUserVerificationStatus = `-- name: UpdateUserVerificationStatus :exec
UPDATE users
SET is_verified = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $2
`

type UpdateUserVerificationStatusParams struct {
	IsVerified bool
	ID         int32
}

func (q *Queries) UpdateUserVerificationStatus(ctx context.Context, arg UpdateUserVerificationStatusParams) error {
	_, err := q.db.Exec(ctx, updateUserVerificationStatus, arg.IsVerified, arg.ID)
	return err
}

const upsertNotificationPreferences = `-- name: UpsertNotificationPreferences :one
INSERT INTO notification_preferences (user_id, in_app_enabled, match_updates, schedule_changes, results, announcements)
VALUES ($1, $2, $3, $4, $5, $6)
ON CONFLICT (user_id) 
DO UPDATE SET 
    in_app_enabled = EXCLUDED.in_app_enabled,
    match_updates = EXCLUDED.match_updates,
    schedule_changes = EXCLUDED.schedule_changes,
    results = EXCLUDED.results,
    announcements = EXCLUDED.announcements,
    updated_at = CURRENT_TIMESTAMP
RETURNING id, user_id, in_app_enabled, match_updates, schedule_changes, results, announcements, created_at, updated_at
`

type UpsertNotificationPreferencesParams struct {
	UserID          int32
	InAppEnabled    pgtype.Bool
	MatchUpdates    pgtype.Bool
	ScheduleChanges pgtype.Bool
	Results         pgtype.Bool
	Announcements   pgtype.Bool
}

func (q *Queries) UpsertNotificationPreferences(ctx context.Context, arg UpsertNotificationPreferencesParams) (NotificationPreference, error) {
	row := q.db.QueryRow(ctx, upsertNotificationPreferences,
		arg.UserID,
		arg.InAppEnabled,
		arg.MatchUpdates,
		arg.ScheduleChanges,
		arg.Results,
		arg.Announcements,
	)
	var i NotificationPreference
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.InAppEnabled,
		&i.MatchUpdates,
		&i.ScheduleChanges,
		&i.Results,
		&i.Announcements,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const upsertPlayerColumnVisibility = `-- name: UpsertPlayerColumnVisibility :exec
INSERT INTO player_column_visibilities (user_id, column_name, is_visible)
VALUES ($1, $2, $3)
ON CONFLICT (user_id, column_name) 
DO UPDATE SET 
    is_visible = EXCLUDED.is_visible,
    updated_at = CURRENT_TIMESTAMP
`

type UpsertPlayerColumnVisibilityParams struct {
	UserID     int32
	ColumnName string
	IsVisible  bool
}

func (q *Queries) UpsertPlayerColumnVisibility(ctx context.Context, arg UpsertPlayerColumnVisibilityParams) error {
	_, err := q.db.Exec(ctx, upsertPlayerColumnVisibility, arg.UserID, arg.ColumnName, arg.IsVisible)
	return err
}

const upsertPlayerCustomValue = `-- name: UpsertPlayerCustomValue :one
INSERT INTO player_custom_values (player_id, column_id, value)
VALUES ($1, $2, $3)
ON CONFLICT(player_id, column_id) DO UPDATE SET
    value = excluded.value,
    updated_at = CURRENT_TIMESTAMP
RETURNING id, player_id, column_id, value, created_at, updated_at
`

type UpsertPlayerCustomValueParams struct {
	PlayerID pgtype.Int4
	ColumnID pgtype.Int4
	Value    pgtype.Text
}

func (q *Queries) UpsertPlayerCustomValue(ctx context.Context, arg UpsertPlayerCustomValueParams) (PlayerCustomValue, error) {
	row := q.db.QueryRow(ctx, upsertPlayerCustomValue, arg.PlayerID, arg.ColumnID, arg.Value)
	var i PlayerCustomValue
	err := row.Scan(
		&i.ID,
		&i.PlayerID,
		&i.ColumnID,
		&i.Value,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
