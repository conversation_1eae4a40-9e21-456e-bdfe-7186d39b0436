// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"database/sql/driver"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
)

type PermissionActionEnum string

const (
	PermissionActionEnumGranted  PermissionActionEnum = "granted"
	PermissionActionEnumRevoked  PermissionActionEnum = "revoked"
	PermissionActionEnumModified PermissionActionEnum = "modified"
)

func (e *PermissionActionEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = PermissionActionEnum(s)
	case string:
		*e = PermissionActionEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for PermissionActionEnum: %T", src)
	}
	return nil
}

type NullPermissionActionEnum struct {
	PermissionActionEnum PermissionActionEnum
	Valid                bool // Valid is true if PermissionActionEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullPermissionActionEnum) Scan(value interface{}) error {
	if value == nil {
		ns.PermissionActionEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.PermissionActionEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullPermissionActionEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.PermissionActionEnum), nil
}

type PermissionLevelEnum string

const (
	PermissionLevelEnumOwner   PermissionLevelEnum = "owner"
	PermissionLevelEnumAdmin   PermissionLevelEnum = "admin"
	PermissionLevelEnumManager PermissionLevelEnum = "manager"
	PermissionLevelEnumViewer  PermissionLevelEnum = "viewer"
)

func (e *PermissionLevelEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = PermissionLevelEnum(s)
	case string:
		*e = PermissionLevelEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for PermissionLevelEnum: %T", src)
	}
	return nil
}

type NullPermissionLevelEnum struct {
	PermissionLevelEnum PermissionLevelEnum
	Valid               bool // Valid is true if PermissionLevelEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullPermissionLevelEnum) Scan(value interface{}) error {
	if value == nil {
		ns.PermissionLevelEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.PermissionLevelEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullPermissionLevelEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.PermissionLevelEnum), nil
}

type ApiKey struct {
	ID         int32
	UserID     int32
	KeyHash    string
	Name       string
	Prefix     string
	LastUsedAt pgtype.Timestamp
	ExpiresAt  pgtype.Timestamp
	CreatedAt  pgtype.Timestamp
	UpdatedAt  pgtype.Timestamp
	IsActive   bool
}

type EmailDeadLetterQueue struct {
	ID                 int32
	OriginalReminderID int32
	PlayerID           int32
	ReminderType       string
	FailureReason      string
	RetryCount         int32
	CreatedAt          pgtype.Timestamp
	LastAttemptAt      pgtype.Timestamp
}

type EmailReminder struct {
	ID           int32
	PlayerID     int32
	ReminderType string
	ScheduledAt  pgtype.Timestamp
	SentAt       pgtype.Timestamp
	FailedAt     pgtype.Timestamp
	RetryCount   pgtype.Int4
	NextRetryAt  pgtype.Timestamp
	LastError    pgtype.Text
	Status       pgtype.Text
	MatchID      pgtype.Int4
	SeasonID     pgtype.Int4
	CreatedAt    pgtype.Timestamp
}

type Feedback struct {
	ID           int32
	UserID       int32
	Name         string
	Description  string
	FeedbackType string
	CreatedAt    pgtype.Timestamp
	UpdatedAt    pgtype.Timestamp
}

type Match struct {
	ID              int32
	SeasonID        pgtype.Int4
	PlayerId1       pgtype.Int4
	PlayerId1Points pgtype.Int4
	PlayerId2       pgtype.Int4
	PlayerId2Points pgtype.Int4
	MatchDate       pgtype.Date
	WinnerID        pgtype.Int4
	CreatedAt       pgtype.Timestamp
	UpdatedAt       pgtype.Timestamp
	IsActive        bool
	MatchGroup      int32
}

type MatchCustomColumn struct {
	ID           int32
	UserID       int32
	Name         string
	FieldType    string
	Description  pgtype.Text
	IsRequired   pgtype.Bool
	IsActive     pgtype.Bool
	DisplayOrder int32
	CreatedAt    pgtype.Timestamp
	UpdatedAt    pgtype.Timestamp
}

type MatchCustomValue struct {
	ID        int32
	MatchID   int32
	ColumnID  int32
	Value     pgtype.Text
	CreatedAt pgtype.Timestamp
	UpdatedAt pgtype.Timestamp
}

type Notification struct {
	ID        int32
	UserID    int32
	Type      string
	Title     string
	Message   string
	Data      []byte
	IsRead    pgtype.Bool
	CreatedAt pgtype.Timestamp
	ReadAt    pgtype.Timestamp
	MatchID   pgtype.Int4
	SeasonID  pgtype.Int4
	PlayerID  pgtype.Int4
}

type NotificationPreference struct {
	ID              int32
	UserID          int32
	InAppEnabled    pgtype.Bool
	MatchUpdates    pgtype.Bool
	ScheduleChanges pgtype.Bool
	Results         pgtype.Bool
	Announcements   pgtype.Bool
	CreatedAt       pgtype.Timestamp
	UpdatedAt       pgtype.Timestamp
}

type Player struct {
	ID                        int32
	UserID                    int32
	TeamID                    pgtype.Int4
	Name                      string
	Email                     pgtype.Text
	Phone                     pgtype.Text
	PictureUrl                pgtype.Text
	CreatedAt                 pgtype.Timestamp
	UpdatedAt                 pgtype.Timestamp
	PreferredMatchGroup       int32
	IsActive                  bool
	EmailNotificationsEnabled bool
	EmailReminderPreferences  []byte
}

type PlayerColumnVisibility struct {
	ID         int32
	UserID     int32
	ColumnName string
	IsVisible  bool
	CreatedAt  pgtype.Timestamp
	UpdatedAt  pgtype.Timestamp
}

type PlayerCustomColumn struct {
	ID           int32
	UserID       int32
	Name         string
	FieldType    string
	Description  pgtype.Text
	IsRequired   pgtype.Bool
	IsActive     pgtype.Bool
	DisplayOrder pgtype.Int4
	CreatedAt    pgtype.Timestamp
	UpdatedAt    pgtype.Timestamp
}

type PlayerCustomValue struct {
	ID        int32
	PlayerID  pgtype.Int4
	ColumnID  pgtype.Int4
	Value     pgtype.Text
	CreatedAt pgtype.Timestamp
	UpdatedAt pgtype.Timestamp
}

type Season struct {
	ID         int32
	UserID     pgtype.Int4
	Name       string
	StartDate  pgtype.Date
	CreatedAt  pgtype.Timestamp
	UpdatedAt  pgtype.Timestamp
	IsActive   bool
	SeasonType string
	Frequency  string
}

type SeasonPermission struct {
	ID              int32
	SeasonID        int32
	UserID          int32
	PermissionLevel PermissionLevelEnum
	GrantedBy       int32
	GrantedAt       pgtype.Timestamptz
	IsActive        pgtype.Bool
}

type SeasonPermissionHistory struct {
	ID              int32
	SeasonID        int32
	UserID          int32
	PermissionLevel NullPermissionLevelEnum
	Action          PermissionActionEnum
	PerformedBy     int32
	PerformedAt     pgtype.Timestamptz
}

type Spending struct {
	ID          int32
	UserID      int32
	Amount      pgtype.Numeric
	Description string
	Date        pgtype.Date
	Category    string
	FileUrls    []string
	CreatedAt   pgtype.Timestamp
	UpdatedAt   pgtype.Timestamp
	IsActive    bool
}

type SpendingMatch struct {
	ID         int32
	SpendingID int32
	MatchID    int32
	CreatedAt  pgtype.Timestamp
}

type SpendingPlayer struct {
	ID         int32
	SpendingID int32
	PlayerID   int32
	CreatedAt  pgtype.Timestamp
}

type SpendingSeason struct {
	ID         int32
	SpendingID int32
	SeasonID   int32
	CreatedAt  pgtype.Timestamp
}

type SpendingTeam struct {
	ID         int32
	SpendingID int32
	TeamID     int32
	CreatedAt  pgtype.Timestamp
}

type Team struct {
	ID          int32
	UserID      int32
	Name        string
	Description pgtype.Text
	PictureUrl  pgtype.Text
	CreatedAt   pgtype.Timestamp
	UpdatedAt   pgtype.Timestamp
	IsActive    bool
}

type User struct {
	ID               int32
	StytchID         string
	StripeID         string
	Name             string
	Email            string
	Country          string
	Phone            pgtype.Text
	Birthday         pgtype.Date
	Lang             string
	PictureUrl       pgtype.Text
	CreatedAt        pgtype.Timestamp
	UpdatedAt        pgtype.Timestamp
	IsActive         bool
	IsVerified       bool
	SubscriptionTier string
	IsSidebarOpen    bool
	JsonSettings     pgtype.Text
}
