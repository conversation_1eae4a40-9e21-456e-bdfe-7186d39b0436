#!/bin/bash
set -e

echo "Setting up CoachPad development environment..."

# Update system packages
sudo apt-get update

# Install essential build tools
sudo apt-get install -y curl wget git build-essential

# Install Node.js 20 (LTS)
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 20 ]]; then
    echo "Installing Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install Go 1.24.2 (as specified in go.mod)
if ! command -v go &> /dev/null || [[ $(go version | grep -o 'go[0-9]\+\.[0-9]\+\.[0-9]\+' | cut -d'o' -f2) != "1.24.2" ]]; then
    echo "Installing Go 1.24.2..."
    cd /tmp
    wget https://go.dev/dl/go1.24.2.linux-amd64.tar.gz
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf go1.24.2.linux-amd64.tar.gz
    echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
    echo 'export PATH=$PATH:$(go env GOPATH)/bin' >> $HOME/.profile
    export PATH=$PATH:/usr/local/go/bin
    export PATH=$PATH:$(go env GOPATH)/bin
fi

# Install Docker instead of Podman for better compatibility
if ! command -v docker &> /dev/null; then
    echo "Installing Docker..."
    sudo apt-get install -y ca-certificates curl gnupg lsb-release
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    sudo usermod -aG docker $USER
    sudo systemctl start docker
    sudo systemctl enable docker
fi

# Install PostgreSQL client tools
sudo apt-get install -y postgresql-client

# Install Playwright system dependencies
sudo apt-get install -y libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0 libcups2 libxkbcommon0 libatspi2.0-0 libxcomposite1 libxdamage1 libxfixes3 libxrandr2 libgbm1 libpango-1.0-0 libcairo2 libasound2

# Install goimports for Go code formatting
if ! command -v goimports &> /dev/null; then
    echo "Installing goimports..."
    go install golang.org/x/tools/cmd/goimports@latest
fi

# Install goose for database migrations
if ! command -v goose &> /dev/null; then
    echo "Installing goose..."
    go install github.com/pressly/goose/v3/cmd/goose@latest
fi

# Install sqlc for code generation
if ! command -v sqlc &> /dev/null; then
    echo "Installing sqlc..."
    go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest
fi

# Install air for hot reloading
if ! command -v air &> /dev/null; then
    echo "Installing air..."
    go install github.com/air-verse/air@latest
fi

# Change to workspace directory
cd /mnt/persist/workspace

# Install npm dependencies
echo "Installing npm dependencies..."
npm install

# Install Playwright browsers
echo "Installing Playwright browsers..."
npx playwright install

# Create .env.development file with all required environment variables
echo "Creating .env.development file..."
cat > .env.development << 'EOF'
DATABASE_URL=postgresql://coachpad@localhost:5432/coachpad_development
COACHPAD_BACKEND_PORT=8000
APP_ENV=development
COACHPAD_FREE_USER_MAX_PLAYERS=10
COACHPAD_FREE_USER_MAX_TEAMS=5
COACHPAD_FREE_USER_MAX_SEASONS=3
COACHPAD_FREE_USER_MAX_MATCHES=50
STYTCH_PROJECT_ID=test-project-id
STYTCH_SECRET=test-secret
STRIPE_SECRET_KEY=sk_test_test
STRIPE_PUBLISHABLE_KEY=pk_test_test
MAILGUN_DOMAIN=test.mailgun.org
MAILGUN_API_KEY=test-api-key
EOF

# Create dist directory and build frontend assets first
echo "Creating dist directory and building frontend assets..."
mkdir -p dist
npx vite build

# Set up database using Docker
echo "Setting up development database with Docker..."

# Stop any existing postgres container
sudo docker stop postgres 2>/dev/null || true
sudo docker rm postgres 2>/dev/null || true

# Start PostgreSQL container
echo "Starting PostgreSQL container..."
sudo docker run --name postgres \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  -e POSTGRES_PASSWORD=postgres \
  -d postgres:16

# Wait for PostgreSQL to start
echo "Waiting for PostgreSQL to start..."
sleep 10
until sudo docker exec postgres pg_isready; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "PostgreSQL is ready! Setting up databases..."

# Create databases and user
sudo docker exec postgres psql -U postgres <<EOF
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'coachpad') THEN
        CREATE USER coachpad WITH PASSWORD 'coachpad';
    END IF;
END\$\$;

-- Create databases if they do not exist
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'coachpad_development') THEN
        CREATE DATABASE coachpad_development OWNER coachpad;
    END IF;
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'coachpad_staging') THEN
        CREATE DATABASE coachpad_staging OWNER coachpad;
    END IF;
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'coachpad_production') THEN
        CREATE DATABASE coachpad_production OWNER coachpad;
    END IF;
END\$\$;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE coachpad_development TO coachpad;
GRANT ALL PRIVILEGES ON DATABASE coachpad_staging TO coachpad;
GRANT ALL PRIVILEGES ON DATABASE coachpad_production TO coachpad;
EOF

echo "Databases and user setup complete."

# Run database migrations
echo "Running database migrations..."
export DATABASE_URL="postgresql://coachpad@localhost:5432/coachpad_development"
cd migrations
goose -dir . postgres "$DATABASE_URL" up
cd ..

# Generate SQLC code
echo "Generating SQLC code..."
sqlc generate

# Build Go application
echo "Building Go application..."
go mod tidy
go build -o tmp/main .

echo "Setup completed successfully!"
echo ""
echo "Environment is ready for testing."
echo "Database URL: postgresql://coachpad@localhost:5432/coachpad_development"
echo "Backend will run on port 8000"