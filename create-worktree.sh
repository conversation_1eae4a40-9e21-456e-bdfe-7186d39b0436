#!/bin/bash

# Script to create a new git worktree and set up .env file with unique port
# Usage: ./create-worktree.sh <path>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored messages
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <path>"
    echo "  path: Relative or absolute path for the new worktree"
    echo ""
    echo "Examples:"
    echo "  $0 ../feature-branch"
    echo "  $0 /tmp/hotfix-branch"
    echo "  $0 worktree-dev"
}

# Check if argument is provided
if [ $# -eq 0 ]; then
    print_error "No path provided"
    show_usage
    exit 1
fi

WORKTREE_PATH="$1"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository"
    exit 1
fi

# Get the root directory of the git repository
GIT_ROOT=$(git rev-parse --show-toplevel)

# Check if .env file exists in the git root
ENV_FILE="$GIT_ROOT/.env"
if [ ! -f "$ENV_FILE" ]; then
    print_error ".env file not found at $ENV_FILE"
    exit 1
fi

# Function to get the next available port
get_next_port() {
    local current_port
    current_port=$(grep -E "^COACHPAD_BACKEND_PORT=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' | tr -d "'")

    if [ -z "$current_port" ]; then
        # Default starting port if not found
        echo "8080"
    else
        # Increment the port by 1
        echo $((current_port + 1))
    fi
}

# Get the next available port
NEW_PORT=$(get_next_port)

print_info "Creating git worktree at: $WORKTREE_PATH"

# Create the git worktree
if git worktree add "$WORKTREE_PATH"; then
    print_info "Git worktree created successfully"
else
    print_error "Failed to create git worktree"
    exit 1
fi

# Get the absolute path of the new worktree
WORKTREE_ABS_PATH=$(cd "$WORKTREE_PATH" && pwd)
NEW_ENV_FILE="$WORKTREE_ABS_PATH/.env"

print_info "Copying .env file to new worktree"

# Copy the .env file to the new worktree
if cp "$ENV_FILE" "$NEW_ENV_FILE"; then
    print_info ".env file copied successfully"
else
    print_error "Failed to copy .env file"
    exit 1
fi

print_info "Updating COACHPAD_BACKEND_PORT to $NEW_PORT"

# Update the port in the new .env file
if grep -q "^COACHPAD_BACKEND_PORT=" "$NEW_ENV_FILE"; then
    # Port exists, update it
    if sed -i "s/^COACHPAD_BACKEND_PORT=.*/COACHPAD_BACKEND_PORT=$NEW_PORT/" "$NEW_ENV_FILE"; then
        print_info "COACHPAD_BACKEND_PORT updated to $NEW_PORT"
    else
        print_error "Failed to update COACHPAD_BACKEND_PORT"
        exit 1
    fi
else
    # Port doesn't exist, add it
    echo "COACHPAD_BACKEND_PORT=$NEW_PORT" >> "$NEW_ENV_FILE"
    print_info "COACHPAD_BACKEND_PORT added with value $NEW_PORT"
fi

print_info "Worktree setup complete!"
print_info "Path: $WORKTREE_ABS_PATH"
print_info "Backend port: $NEW_PORT"
print_warning "Remember to check if port $NEW_PORT is available before starting the application"

# Optional: Show the worktree list
echo ""
print_info "Current git worktrees:"
git worktree list
