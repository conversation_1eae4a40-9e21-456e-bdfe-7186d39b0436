package sse

import (
	"encoding/json"
	"fmt"
	"slices"
	"sync"

	"github.com/labstack/echo/v4"
	"github.com/r3labs/sse/v2"
)

// Manager manages SSE connections using r3labs/sse
type Manager struct {
	server      *sse.Server
	userClients map[int32][]string // maps userID to list of clientIDs
	mutex       sync.RWMutex
}

// NewManager creates a new SSE manager
func NewManager() *Manager {
	server := sse.New()

	// Configure server settings
	server.AutoReplay = false // Don't replay old messages to new connections
	server.Headers = map[string]string{
		"Access-Control-Allow-Origin":  "*",
		"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
		"Access-Control-Allow-Headers": "Accept, Content-Type, Content-Length, Accept-Encoding, Authorization",
	}

	return &Manager{
		server:      server,
		userClients: make(map[int32][]string),
	}
}

// AddClient adds a new SSE client
func (m *Manager) AddClient(clientID string, userID int32) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Create user-specific stream if it doesn't exist
	streamName := fmt.Sprintf("user_%d", userID)
	if !m.server.StreamExists(streamName) {
		m.server.CreateStream(streamName)
	}

	m.userClients[userID] = append(m.userClients[userID], clientID)
}

// RemoveClient removes an SSE client
func (m *Manager) RemoveClient(clientID string, userID int32) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Remove from userClients map
	userClients := m.userClients[userID]
	for i, id := range userClients {
		if id == clientID {
			m.userClients[userID] = slices.Delete(userClients, i, i+1)
			break
		}
	}

	// Clean up empty user entries
	if len(m.userClients[userID]) == 0 {
		delete(m.userClients, userID)
	}
}

// Event represents a server-sent event
type Event struct {
	Event string `json:"event"`
	Data  any    `json:"data"`
	ID    string `json:"id,omitempty"`
}

// SendToUser sends an event to all connections for a specific user
func (m *Manager) SendToUser(userID int32, event Event) {
	var data []byte
	var err error

	// Check if data is already a string (like HTML), if so send it directly
	if str, ok := event.Data.(string); ok {
		data = []byte(str)
	} else {
		// Otherwise JSON marshal it
		data, err = json.Marshal(event.Data)
		if err != nil {
			fmt.Printf("Error marshaling SSE event data: %v\n", err)
			return
		}
	}

	// Publish to user-specific stream
	streamName := fmt.Sprintf("user_%d", userID)
	m.server.Publish(streamName, &sse.Event{
		Event: []byte(event.Event),
		Data:  data,
		ID:    []byte(event.ID),
	})
}

// SendToAll sends an event to all connected clients
func (m *Manager) SendToAll(event Event) {
	// Convert event data to JSON
	data, err := json.Marshal(event.Data)
	if err != nil {
		fmt.Printf("Error marshaling SSE event data: %v\n", err)
		return
	}

	// Send to main events stream
	m.server.Publish("events", &sse.Event{
		Event: []byte(event.Event),
		Data:  data,
		ID:    []byte(event.ID),
	})
}

// HandleConnection handles an SSE connection
func (m *Manager) HandleConnection(c echo.Context, clientID string, userID int32) error {
	// Add client to our tracking
	m.AddClient(clientID, userID)

	// Remove client when connection closes
	defer func() {
		m.RemoveClient(clientID, userID)
	}()

	fmt.Printf("SSE client %s connected for user %d\n", clientID, userID)

	// Use the r3labs/sse server to handle the connection
	// The r3labs/sse library will automatically handle the user-specific stream based on ?stream= parameter
	m.server.ServeHTTP(c.Response(), c.Request())

	return nil
}

// GetServer returns the underlying SSE server for direct access if needed
func (m *Manager) GetServer() *sse.Server {
	return m.server
}

// GetUserClientCount returns the number of active connections for a user
func (m *Manager) GetUserClientCount(userID int32) int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.userClients[userID])
}
