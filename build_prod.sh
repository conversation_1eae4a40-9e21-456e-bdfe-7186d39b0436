#!/bin/bash

# CoachPad Production Build Script
# This script builds the application for production deployment

set -e  # Exit on any error

# Configuration
BUILD_DIR="./build"
TARBALL_NAME="coachpad-production-$(date +%Y%m%d_%H%M%S).tar.gz"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Function to check if command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error "$1 command not found. Please install it."
        exit 1
    fi
}

# Usage function
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help             Show this help message"
    echo ""
    echo "This script will:"
    echo "  1. Format and organize Go code"
    echo "  2. Build frontend assets with Vite"
    echo "  3. Build Go binary for production (with embedded assets)"
    echo "  4. Build goose binary for database migrations"
    echo "  5. Create deployment tarball"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --help)
            usage
            ;;
        -*)
            error "Unknown option: $1"
            usage
            ;;
        *)
            error "Unexpected argument: $1"
            usage
            ;;
    esac
done

# Check required commands
check_command go
check_command npm
check_command tar
check_command git

log "Starting production build for CoachPad..."

# Clean previous build
if [ -d "$BUILD_DIR" ]; then
    log "Cleaning previous build directory..."
    rm -rf "$BUILD_DIR"
fi
mkdir -p "$BUILD_DIR"

# Step 1: Format and organize Go code
log "Formatting and organizing Go code..."
if ! go fmt ./...; then
    error "Go fmt failed"
    exit 1
fi

if command -v goimports &> /dev/null; then
    if ! goimports -w .; then
        error "goimports failed - this may indicate import issues"
        exit 1
    fi
    log "Go imports organized successfully"
else
    warn "goimports not found - skipping import organization"
fi

if ! go mod tidy; then
    error "go mod tidy failed - dependency issues detected"
    exit 1
fi
log "Go dependencies tidied successfully"

# Step 2: Regenerate SQLC code if needed
if [ -f "sqlc.yaml" ]; then
    log "Regenerating SQLC code..."
    if command -v sqlc &> /dev/null; then
        if ! sqlc generate; then
            error "SQLC code generation failed"
            exit 1
        fi
        log "SQLC code generated successfully"
    else
        error "sqlc not found but sqlc.yaml exists - install sqlc or remove sqlc.yaml"
        exit 1
    fi
fi

# Step 3: Install frontend dependencies
log "Installing frontend dependencies..."
if ! npm install; then
    error "npm install failed"
    exit 1
fi
log "Frontend dependencies installed successfully"

# Step 4: Build frontend assets
log "Building frontend assets with Vite..."
if ! npx vite build; then
    error "Vite build failed"
    exit 1
fi
log "Frontend assets built successfully"

# Verify frontend build
if [ ! -d "dist" ]; then
    error "Frontend build failed - dist directory not created"
    exit 1
fi

# Step 5: Build Go binary for production
log "Building Go binary for production..."
if ! CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o "$BUILD_DIR/coachpad" main.go; then
    error "Go build failed"
    exit 1
fi
log "Go binary built successfully"

# Verify Go build
if [ ! -f "$BUILD_DIR/coachpad" ]; then
    error "Go build failed - binary not created"
    exit 1
fi

# Step 5.5: Build goose binary for database migrations
log "Building goose binary for database migrations..."

# Use go install to build goose directly to the build directory
if ! CGO_ENABLED=0 GOOS=linux GOARCH=amd64 GOBIN="$(pwd)/$BUILD_DIR" go install github.com/pressly/goose/v3/cmd/goose@latest; then
    error "Failed to install goose binary"
    exit 1
fi
log "Goose binary installed successfully"

# Verify goose build
if [ ! -f "$BUILD_DIR/goose" ]; then
    error "Goose build failed - binary not created"
    exit 1
fi

# Step 6: Copy necessary files to build directory
log "Copying files for deployment..."

# Copy deployment configuration files (including nginx.conf)
if ! cp -r deployment/ "$BUILD_DIR/"; then
    error "Failed to copy deployment configuration files"
    exit 1
fi

# Verify nginx.conf was copied
if [ ! -f "$BUILD_DIR/deployment/nginx.conf" ]; then
    error "nginx.conf not found in deployment directory - required for production deployment"
    exit 1
fi
log "Deployment configuration files (including nginx.conf) copied successfully"

# Copy database migrations
if [ -d "migrations" ]; then
    if ! cp -r migrations/ "$BUILD_DIR/"; then
        error "Failed to copy database migrations"
        exit 1
    fi
    log "Database migrations copied successfully"
else
    error "Migrations directory not found - migrations are required for deployment"
    exit 1
fi

# Copy environment configuration files
log "Copying environment configuration files..."
for env_file in .env.development .env.staging .env.production; do
    if [ -f "$env_file" ]; then
        if ! cp "$env_file" "$BUILD_DIR/"; then
            error "Failed to copy $env_file"
            exit 1
        fi
        log "$env_file copied successfully"
    else
        warn "$env_file not found - environment may not be fully configured"
    fi
done

# Set default .env to production
if [ -f "$BUILD_DIR/.env.production" ]; then
    cp "$BUILD_DIR/.env.production" "$BUILD_DIR/.env"
    log "Default .env set to production configuration"
else
    error ".env.production file not found - production environment file is required for deployment"
    exit 1
fi

# Step 7: Create production tarball
log "Creating production tarball..."
cd "$BUILD_DIR"
if ! tar -czf "../$TARBALL_NAME" .; then
    error "Failed to create production tarball"
    cd ..
    exit 1
fi
cd ..
log "Production tarball created successfully"

# Clean up build directory
if ! rm -rf "$BUILD_DIR"; then
    warn "Failed to clean up build directory: $BUILD_DIR"
fi

# Verify tarball was created
if [ ! -f "$TARBALL_NAME" ]; then
    error "Failed to create production tarball"
    exit 1
fi

TARBALL_SIZE=$(du -sh "$TARBALL_NAME" | cut -f1)
log "Production build completed successfully!"
log "Tarball created: $TARBALL_NAME"
log "Tarball size: $TARBALL_SIZE"
log "Included binaries: coachpad, goose"
log "Included files: deployment/ (including nginx.conf), migrations/, environment configs (.env.*)"
