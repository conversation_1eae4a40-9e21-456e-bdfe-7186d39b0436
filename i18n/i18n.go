package i18n

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"log"
	"path/filepath"
	"strings"

	embedFS "github.com/j-em/coachpad/embed"
)

// LocaleData represents the structure of the locale data.
type LocaleData map[string]map[string]interface{}

func LoadLocaleFile(locales interface{}, path string) error {
	// Use embedded loader that falls back to disk
	data, err := embedFS.GetLocaleFile(path)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, locales)
}

// LoadLocales loads all locale files from the embedded templates directory.
func LoadLocales(templatesDir string) (map[string]map[string]map[string]interface{}, error) {
	localeMap := make(map[string]LocaleData)

	templatesFS := embedFS.GetTemplatesFS()
	if templatesFS == nil {
		return nil, fmt.Errorf("embedded templates filesystem not available")
	}

	err := fs.WalkDir(templatesFS, ".", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if strings.HasSuffix(path, ".locales.json") {
			// Read the embedded file
			data, err := fs.ReadFile(templatesFS, path)
			if err != nil {
				return fmt.Errorf("error reading embedded locale file %s: %w", path, err)
			}

			// Extract template name from path
			baseName := strings.TrimSuffix(filepath.Base(path), ".locales.json")
			templateName := baseName

			// Unmarshal the JSON data
			var localeData LocaleData
			if err := json.Unmarshal(data, &localeData); err != nil {
				return fmt.Errorf("error unmarshaling locale file %s: %w", path, err)
			}

			// Validate that the "en" and "fr" keys exist
			if _, ok := localeData["en"]; !ok {
				localeData["en"] = make(map[string]interface{})
			}
			if _, ok := localeData["fr"]; !ok {
				localeData["fr"] = make(map[string]interface{})
			}

			localeMap[templateName] = localeData
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("error walking embedded templates directory: %w", err)
	}

	transformedLocaleMap := make(map[string]map[string]map[string]interface{})
	for templateName, data := range localeMap {
		for locale, translations := range data {
			if _, ok := transformedLocaleMap[locale]; !ok {
				transformedLocaleMap[locale] = make(map[string]map[string]interface{})
			}
			transformedLocaleMap[locale][templateName] = translations
		}
	}

	return transformedLocaleMap, nil
}

// LoadTemplateLocales loads locales from a specified file path and returns the map for the given language or defaults to English.
func LoadTemplateLocales(filePath, lang string) (map[string]string, error) {
	var allLocales map[string]map[string]string
	if err := LoadLocaleFile(&allLocales, filePath); err != nil {
		return make(map[string]string), err
	}
	if langData, ok := allLocales[lang]; ok {
		return langData, nil
	}
	return allLocales["en"], nil // Default to English if language not found
}

// MustLoadTemplateLocales loads locales or returns empty map
func MustLoadTemplateLocales(path, lang string) map[string]string {
	locales, err := LoadTemplateLocales(path, lang)
	if err != nil {
		log.Printf("Error loading template locales from %s: %v", path, err)
		return make(map[string]string)
	}
	return locales
}
