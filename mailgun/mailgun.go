package mailgun

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/mailgun/mailgun-go/v4"
)

// MailgunClient holds the configuration and client for interacting with Mailgun API.
type MailgunClient struct {
	client               *mailgun.MailgunImpl
	domain               string
	apiKey               string
	IsEnabled            bool
	confirmationTemplate string
}

// EmailParams encapsulates the parameters needed to send an email.
type EmailParams struct {
	Recipient string
	Subject   string
	Template  string
	Variables map[string]string
}

// NewMailgunClient initializes a new Mailgun client with configuration from environment variables.
func NewMailgunClient() (*MailgunClient, error) {
	domain := os.Getenv("MAILGUN_DOMAIN")
	apiKey := os.Getenv("MAILGUN_API_KEY")
	enabledStr := os.Getenv("EMAIL_CONFIRMATION_ENABLED")
	template := os.Getenv("MAILGUN_CONFIRMATION_TEMPLATE_EN")

	if domain == "" || apiKey == "" {
		return nil, fmt.Errorf("missing required Mailgun configuration: domain or API key")
	}

	enabled, err := strconv.ParseBool(enabledStr)
	if err != nil {
		enabled = false // Default to disabled if not set or invalid
	}

	if template == "" {
		template = "confirmation-email" // Default template name if not specified
	}

	mg := mailgun.NewMailgun(domain, apiKey)

	return &MailgunClient{
		client:               mg,
		domain:               domain,
		apiKey:               apiKey,
		IsEnabled:            enabled,
		confirmationTemplate: template,
	}, nil
}

// IsEmailConfirmationEnabled returns whether email confirmation is enabled.
func (mc *MailgunClient) IsEmailConfirmationEnabled() bool {
	return mc.IsEnabled
}

// SendEmail sends an email using the Mailgun API with the provided parameters.
func (mc *MailgunClient) SendEmail(ctx context.Context, params EmailParams) error {
	if !mc.IsEnabled {
		return fmt.Errorf("email confirmation is disabled")
	}

	message := mc.client.NewMessage(
		fmt.Sprintf("noreply@%s", mc.domain),
		params.Subject,
		"", // Text body is empty since we're using a template
		params.Recipient,
	)

	// Set template if provided, otherwise use the default confirmation template
	template := params.Template
	if template == "" {
		template = mc.confirmationTemplate
	}
	message.SetTemplate(template)

	// Add template variables if any
	for key, value := range params.Variables {
		if err := message.AddVariable(key, value); err != nil {
			return fmt.Errorf("failed to add template variable %s: %v", key, err)
		}
	}

	// Set a timeout for the API call
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	// Send the message
	_, _, err := mc.client.Send(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send email to %s: %v", params.Recipient, err)
	}

	return nil
}
