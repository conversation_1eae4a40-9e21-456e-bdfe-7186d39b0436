#!/bin/bash

CONTAINER_NAME=postgres
DB_NAME=postgres
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/backup_${TIMESTAMP}.sql"

# Create backups directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Perform backup using pg_dump with username and password authentication
echo "Starting database backup..."
if ! podman exec $CONTAINER_NAME \
  pg_dump -U postgres \
  --clean \
  --create \
  --if-exists \
  --format=plain \
  --no-owner \
  --no-privileges \
  $DB_NAME > "$BACKUP_FILE" 2>/dev/null; then
    echo "Backup failed!"
    echo "This could indicate:"
    echo "  1. PostgreSQL container is not running"
    echo "  2. Database connection issues"
    echo "  3. Insufficient permissions"
    echo "  4. Disk space issues"
    rm -f "$BACKUP_FILE"
    exit 1
fi

if [ $? -eq 0 ]; then
    # Verify backup file is not empty
    if [ ! -s "$BACKUP_FILE" ]; then
        echo "Backup failed - backup file is empty!"
        rm -f "$BACKUP_FILE"
        exit 1
    fi
    
    echo "Backup completed successfully!"
    echo "Backup saved to: $BACKUP_FILE"
    echo "Backup size: $(du -sh "$BACKUP_FILE" | cut -f1)"
else
    echo "Backup failed!"
    rm -f "$BACKUP_FILE"
    exit 1
fi