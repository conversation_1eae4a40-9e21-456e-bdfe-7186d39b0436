# Deployment Procedure

This document outlines the deployment procedure for the CoachPad application - a server-side rendered Go web application with PostgreSQL database.

## ⚠️ Important: A/B Deployment Architecture Available

**For enhanced zero-downtime deployments**, CoachPad now supports A/B (Blue/Green) deployment architecture. This is recommended for production environments that require high availability.

- **Current deployment (this document)**: Simple single-deployment setup
- **Enhanced A/B deployment**: See [`docs/AB_DEPLOYMENT.md`](docs/AB_DEPLOYMENT.md) for zero-downtime deployments with staging isolation

Quick comparison:
- **Single deployment**: Simple, suitable for low-traffic or development environments
- **A/B deployment**: Zero-downtime, staging isolation, easy rollbacks, production-grade

## When to use this document

This comprehensive deployment guide should be used for:

- **Initial Production Deployment** - First-time deployment to a fresh production server
- **New Environment Setup** - Setting up staging/testing environments that mirror production
- **Server Migration** - Moving the application to a new server/hosting provider
- **Disaster Recovery** - Rebuilding the application after server failure or data loss
- **Infrastructure Overhaul** - Major changes to deployment architecture

This document assumes you're setting up everything from scratch (nginx, SSL, systemd service, database). For routine code updates, minor hotfixes, or database-only migrations, you typically only need to build the new version, stop the service, replace the binary, run migrations, and restart the service.

## Production Hosting Environment

The application is currently hosted on a **Vultr VPS** instance running on their free tier. This provides a cost-effective solution for the initial deployment phase.

### Server Access Requirements

- **SSH Access**: Developer SSH keys must be uploaded to the Vultr control panel for server access
- **Deployment Method**: Manual deployment via SSH using compiled Go binary
- **Server Management**: Direct server access through Vultr's management interface

### Vultr Setup

1. **SSH Key Upload**: Ensure your public SSH key is added to your Vultr account under "SSH Keys" section
2. **Server Access**: Use `ssh root@<server-ip>` or configured user account
3. **Firewall**: Configure Vultr firewall rules for HTTP (80), HTTPS (443), and SSH (22)

## Prerequisites

### Local Development Machine
- Go 1.24.2+
- Node.js and npm
- Homebrew (for installing goose): `brew install goose`
- SSH access to production server (keys uploaded to Vultr account)

### Production Server (Vultr VPS)
- PostgreSQL database (installed and running locally on the same system)
- Basic Debian/Ubuntu setup with typical HTTP (80), HTTPS (443), and SSH (22) ports open
- `/opt/coachpad` directory created during first deployment
- Environment variables configured in `/opt/coachpad/.env`
- **Database Authentication**: Uses PostgreSQL username and password authentication

## Pre-Deployment Checklist

### 1. Code Quality
- [ ] All tests pass: `npm test`
- [ ] Code is formatted: `go fmt ./...` and `goimports -w .`
- [ ] Dependencies are clean: `go mod tidy`
- [ ] SQLC code is regenerated: `sqlc generate` (if database queries changed)

### 2. Database
- [ ] Production database backup created
- [ ] Database migrations are ready to run
- [ ] Migration files are tested locally

### 3. Frontend Assets
- [ ] Vite build completes successfully: `npx vite build`
- [ ] Static assets are optimized
- [ ] Frontend dependencies are installed: `npm install`

## Environment Variables

The production environment file should be located at `/opt/coachpad/.env` on the production server.

Copy the environment template and configure for production:

```bash
# On the production server
cp /opt/coachpad/deployment/.env.example /opt/coachpad/.env

# Edit the file with your production values
nano /opt/coachpad/.env
```

Key variables to configure:
- `DATABASE_URL`: PostgreSQL connection string using username and password: `postgresql://coachpad:password@localhost:5432/coachpad_production`
- `STYTCH_PROJECT_ID` and `STYTCH_SECRET`: Authentication service credentials
- `MAILGUN_DOMAIN` and `MAILGUN_API_KEY`: Email service configuration
- `STRIPE_*`: Payment processing credentials
- `BASE_URL`: Your production domain (e.g., https://coachpad.ca)

See `deployment/.env.example` for all required environment variables.

## First-Time Production Setup

For the initial deployment to a fresh Vultr VPS:

### 1. Server Preparation

```bash
# SSH into your Vultr server
ssh root@your-server-ip

# Install required packages (Debian/Ubuntu)
apt update
apt install -y postgresql postgresql-contrib nginx certbot python3-certbot-nginx

# Install Go 1.24.2+
wget https://go.dev/dl/go1.24.2.linux-amd64.tar.gz
tar -C /usr/local -xzf go1.24.2.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile
source /etc/profile

# Install goose
curl -fsSL https://raw.githubusercontent.com/pressly/goose/master/install.sh | sh

# Create deployment directory
mkdir -p /opt/coachpad
```

### 2. Database Setup

```bash
# Start PostgreSQL service
systemctl start postgresql
systemctl enable postgresql

# Create application user (matches the system user that will run the app)
useradd --system --shell /bin/false coachpad

# Create application database and user
sudo -u postgres psql -c "CREATE DATABASE coachpad_production;"
sudo -u postgres psql -c "CREATE USER coachpad WITH PASSWORD 'your_secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE coachpad_production TO coachpad;"

# Configure PostgreSQL for username/password authentication
# Backup the original pg_hba.conf
sudo cp /etc/postgresql/*/main/pg_hba.conf /etc/postgresql/*/main/pg_hba.conf.backup

# Add md5 authentication for coachpad user (before the default local line)
sudo sed -i '/^local.*all.*all.*peer/i local   coachpad_production   coachpad                md5' /etc/postgresql/*/main/pg_hba.conf

# Reload PostgreSQL configuration
sudo systemctl reload postgresql

# Verify the setup
sudo -u coachpad psql -d coachpad_production -h localhost -U coachpad -c "SELECT current_user, current_database();"
```

### 3. Environment Configuration

```bash
# Create environment file on the server
nano /opt/coachpad/.env
```

Configure the `.env` file with your production values based on `/deployment/.env.example`.

### 4. Initial Deployment

Once the server is prepared, use the normal deployment process:

```bash
# From your local machine
./build_prod.sh
./deploy_prod.sh coachpad-production-YYYYMMDD_HHMMSS.tar.gz --host your-server.com
```

## Deployment Steps

### Quick Deployment (For Updates)

For routine code updates after initial deployment:

```bash
# 1. Build production tarball
./build_prod.sh

# 2. Deploy to production server
./deploy_prod.sh coachpad-production-YYYYMMDD_HHMMSS.tar.gz --host your-server.com
```

### Detailed Process

#### 1. Build for Production

```bash
# Build everything and create deployment tarball
./build_prod.sh

# Optional: Build without running tests (faster for hotfixes)
./build_prod.sh --skip-tests
```

This script will:
- Run tests (unless `--skip-tests` is used)
- Format and organize Go code (`go fmt`, `goimports`, `go mod tidy`)
- Regenerate SQLC code if needed
- Build frontend assets with Vite
- Build Go binary for Linux production
- Create a timestamped tarball with all deployment files

#### 2. Deploy to Production

```bash
# Deploy using the generated tarball
./deploy_prod.sh coachpad-production-YYYYMMDD_HHMMSS.tar.gz --host your-server.com

# Alternative: Set host via environment variable
DEPLOY_HOST=your-server.com ./deploy_prod.sh coachpad-production-YYYYMMDD_HHMMSS.tar.gz

# Options for deployment
./deploy_prod.sh tarball.tar.gz --host server.com --skip-migrations --skip-backup
```

The deployment script will:
- Test SSH connection to the server
- Create database backup (unless `--skip-backup`)
- Stop the CoachPad service
- Backup current binary
- Transfer and extract the new build
- Run database migrations (unless `--skip-migrations`)
- Update systemd service configuration
- Start the service and verify it's running

### 5. Reverse Proxy Setup (Nginx)

Copy and configure nginx:

```bash
# Copy nginx configuration
sudo cp deployment/nginx.conf /etc/nginx/sites-available/coachpad

# Edit the configuration to set your domain name
sudo vim /etc/nginx/sites-available/coachpad
```

```bash
# Enable site and restart nginx
sudo ln -s /etc/nginx/sites-available/coachpad /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 6. SSL Certificate (Let's Encrypt)

```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Test renewal
sudo certbot renew --dry-run
```

## Post-Deployment Verification

### 1. Health Checks
- [ ] Application starts without errors
- [ ] Database connection successful
- [ ] Web interface loads correctly
- [ ] Authentication works (Stytch integration)
- [ ] Email functionality works (Mailgun)
- [ ] Payment processing works (Stripe)

### 2. Monitoring
- [ ] Application logs are being written to `/tmp/app.log`
- [ ] System resources (CPU, memory, disk) are within normal ranges
- [ ] Database performance is acceptable

### 3. Functional Testing
- [ ] User registration and login
- [ ] Team and player management
- [ ] Season creation and match scheduling
- [ ] Notification system
- [ ] Payment/subscription features

## Rollback Procedure

If issues arise after deployment, you can quickly rollback:

### Quick Rollback

```bash
# SSH to production server
ssh <EMAIL>

# Stop the current application
systemctl stop coachpad

# Restore previous binary (created automatically during deployment)
cd /opt/coachpad
ls coachpad.backup.* | tail -1  # Find latest backup
cp coachpad.backup.YYYYMMDD_HHMMSS coachpad

# Start the application
systemctl start coachpad

# Verify functionality
systemctl status coachpad
```

### Database Rollback (if needed)

```bash
# Find recent database backup
ls -la backups/

# Restore database backup if schema changes need to be reverted
./deployment/restore-db.sh backups/coachpad_db_backup_YYYYMMDD_HHMMSS.sql.gz --force
```

## Quick Reference

### Common Commands

```bash
# Build and deploy in one go
./build_prod.sh && ./deploy_prod.sh coachpad-production-*.tar.gz --host your-server.com

# Emergency deployment (skip tests and backup)
./build_prod.sh --skip-tests
./deploy_prod.sh coachpad-production-*.tar.gz --host your-server.com --skip-backup --skip-migrations

# Check what would be deployed (dry run)
./deploy_prod.sh coachpad-production-*.tar.gz --host your-server.com --dry-run

# Check production service status
ssh <EMAIL> 'systemctl status coachpad'

# View production logs
ssh <EMAIL> 'journalctl -u coachpad -f'
ssh <EMAIL> 'tail -f /opt/coachpad/tmp/app.log'
```

## Monitoring and Maintenance

### Log Files
- Application logs: `/tmp/app.log`
- Stripe webhook logs: `/tmp/stripe_webhook.log`
- System logs: `sudo journalctl -u coachpad`

### Regular Maintenance
- Database backups: Use `./backup-db.sh` script
- Log rotation: Configure logrotate for application logs
- System updates: Keep server and dependencies updated
- SSL certificate renewal: Automated via certbot

### Performance Monitoring
- Monitor database query performance
- Watch for memory leaks in the Go application
- Monitor disk space usage
- Set up alerts for service downtime

## Troubleshooting

### Common Issues
1. **Database connection fails**: 
   - Check if PostgreSQL is running: `systemctl status postgresql`
   - Verify username/password authentication is configured: `sudo -u coachpad psql -d coachpad_production -h localhost -U coachpad -c "SELECT 1;"`
   - Check PostgreSQL logs: `sudo journalctl -u postgresql -n 20`
2. **Static assets not loading**: Verify nginx configuration and file permissions
3. **Authentication errors**: Check Stytch configuration and API keys
4. **Email not sending**: Verify Mailgun configuration and domain settings
5. **Payment issues**: Check Stripe webhook endpoint and secret keys

### Debug Commands
```bash
# Check application status
sudo systemctl status coachpad

# View recent logs
sudo journalctl -u coachpad -n 50

# Test database connection (using username/password authentication)
sudo -u coachpad psql -d coachpad_production -h localhost -U coachpad -c "SELECT 1;"

# Check nginx configuration
sudo nginx -t

# Test SSL certificate
curl -I https://your-domain.com
```