# Server-Rendered Modal Pattern

Server-rendered modals using `templates/ui/modal/modal.go` component with AlpineJS interactivity and HTMX dynamic content.

## Pattern Elements
- **Container**: Target `#modal-body-container` with `x-teleport`
- **State**: Use unique AlpineJS variables: `modalID_modalOpen`
- **Transitions**: Initialize with `modalOpen: false` + `x-init` with `setTimeout` for smooth animations
- **HTMX**: Use `@htmx:after-request` callbacks to close on successful operations

## Example
```go
func ExampleModal() gomponents.Node {
	return html.Div(
		gomponents.Attr("x-data", "{ exampleModal_modalOpen: false }"),
		gomponents.Attr("x-init", "setTimeout(() => { exampleModal_modalOpen = true }, 0)"),
		modal.Modal(modal.ModalConfig{
			ModalID: "exampleModal",
			Title:   "Example Modal Title",
			Content: html.Div(
				html.Button(
					gomponents.Attr("hx-post", "/app/example-action"),
					gomponents.Attr("hx-target", "#modal-body-container"),
					gomponents.Attr("hx-swap", "outerHTML"),
					gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { exampleModal_modalOpen = false }"),
					gomponents.Text("Submit"),
				),
				html.Button(
					gomponents.Attr("@click", "exampleModal_modalOpen = false"),
					gomponents.Text("Cancel"),
				),
			),
			IsUnclosable: false,
		}),
	)
}
```

## Best Practices
- Always use `templates/ui/modal/modal.go` component
- Include `data-testid` attributes for testing
- Use `setTimeout` pattern for immediate display with transitions

