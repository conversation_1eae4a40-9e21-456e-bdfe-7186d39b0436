# Client-Controlled Modal Pattern in Gameplan-HTMX

## Overview
This document outlines the pattern for implementing client-controlled modals that are pre-rendered in the initial HTML payload in the Gameplan-HTMX application. Unlike modals that load content dynamically via HTMX or other server requests, this approach includes the full modal content during the initial server render. Visibility and interaction are managed entirely on the client side using AlpineJS, without further server interaction to display the modal.

## Core Components
The pattern for client-controlled modals is exemplified by the `ForgotPasswordDialog` in [`templates/app/signin/signin.go`](templates/app/signin/signin.go). This modal is rendered as part of the initial page load and uses AlpineJS for state management to control when it is shown or hidden.

### Key Elements of the Client-Controlled Modal Pattern:
1. **Pre-Rendered Content**: The modal content is included in the initial HTML structure rendered by the server. This is achieved by directly calling the modal component function (e.g., `ForgotPasswordDialog(lang)`) within the page template.
2. **Client-Side State Management**: AlpineJS is used to manage the modal's visibility through a boolean state variable (e.g., `forgotPassword_modalOpen`). This variable is initialized as `false` to keep the modal hidden by default.
3. **Trigger Mechanism**: A button or link on the page toggles the state variable to `true` when clicked, showing the modal. This is done using AlpineJS event bindings like `@click="forgotPassword_modalOpen = true"`.
4. **Modal Component**: The modal itself is based on the reusable component in [`templates/ui/modal/modal.go`](templates/ui/modal/modal.go), which supports AlpineJS attributes for visibility and interaction (e.g., `x-show="forgotPassword_modalOpen"`).
5. **No Server Interaction**: Unlike other modal patterns in the application that may use HTMX to load content dynamically, this pattern does not require additional server requests to display the modal. All necessary content is available in the initial render.

## Integration into the Application
Client-controlled modals are integrated by:
- Embedding the modal component directly within the page's HTML structure during the server render.
- Defining an AlpineJS data scope in the parent container to include the state variable for the modal (e.g., `x-data="{ forgotPassword_modalOpen: false }"`).
- Adding a trigger element (e.g., a button) that updates the state variable to open the modal.
- Ensuring the modal component uses the appropriate AlpineJS attributes to respond to the state variable for visibility.

## Example Implementation
Below is a simplified example of how a client-controlled modal is implemented, based on the pattern observed in [`templates/app/signin/signin.go`](templates/app/signin/signin.go).

```go
package example

import (
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

func ExamplePageWithClientModal() gomponents.Node {
	return html.Div(
		html.Class("flex min-h-screen justify-center"),
		html.Div(
			html.Class("w-full max-w-sm p-8 space-y-8"),
			// Define AlpineJS state for modal control
			gomponents.Attr("x-data", "{ exampleModal_modalOpen: false }"),
			html.H2(
				html.Class("mt-6 text-center text-2xl font-bold"),
				gomponents.Text("Example Page"),
			),
			// Trigger button to open the modal
			html.Button(
				html.Type("button"),
				html.Class("text-sm text-blue-600 hover:underline"),
				gomponents.Text("Open Example Modal"),
				gomponents.Attr("@click", "exampleModal_modalOpen = true"),
			),
			// Modal component included in initial render
			ExampleClientModal(),
		),
	)
}

func ExampleClientModal() gomponents.Node {
	return modal.Modal(modal.ModalConfig{
		ModalID: "exampleModal",
		Title:   "Example Modal",
		Content: html.Div(
			html.Class("p-4"),
			html.P(gomponents.Text("This modal content is pre-rendered in the initial HTML.")),
			html.Button(
				gomponents.Attr("@click", "exampleModal_modalOpen = false"),
				html.Class("mt-4 px-4 py-2 bg-gray-300 text-gray-800 rounded"),
				gomponents.Text("Close"),
			),
		),
		IsUnclosable: false,
	})
}
```

## Usage Considerations
This pattern is particularly useful for modals that do not require dynamic content loading or server-side updates upon display. It is ideal for static content or forms where the initial data is sufficient, and interactions can be handled client-side or through separate server requests after the modal is shown.

## Best Practices
- **State Variable Naming**: Use unique names for the AlpineJS state variables (e.g., `modalID_modalOpen`) to avoid conflicts with other modals or components on the same page.
- **Performance**: Since the modal content is pre-rendered, ensure that it does not significantly increase the initial page load size. For complex or data-heavy modals, consider using dynamic loading with HTMX instead.
- **Accessibility**: Add ARIA attributes or ensure keyboard navigation works for the modal, as it is managed client-side with AlpineJS.
- **Testing**: Include `data-testid` attributes on the modal and trigger elements to facilitate end-to-end testing, as seen in other parts of the codebase.

This pattern provides a lightweight approach to modals where the content is static and known at render time, leveraging client-side state management for a seamless user experience without additional server calls.