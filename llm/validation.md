Example of how to validate forms using Echo and Go's validator package.

```go
import (
    "github.com/labstack/echo/v4"
    "github.com/go-playground/validator/v10"
    "net/http"
)

type CreateUserForm struct {
    Name  string `form:"name" validate:"required,max=20"`
    Email string `form:"email" validate:"required,email"`
}

var validate = validator.New()

func createUserHandler(c echo.Context) error {
    var form CreateUserForm
    if err := c.Bind(&form); err != nil {
        return c.JSON(http.StatusBadRequest, map[string]string{"error": "Binding failed"})
    }

    if err := validate.Struct(form); err != nil {
        return c.JSON(http.StatusBadRequest, map[string]string{"error": err.Error()})
    }

    return c.JSON(http.StatusOK, form)
}
```
