package titleservice

import "testing"

func TestNewTitleService(t *testing.T) {
	ts := NewTitleService()
	if ts == nil {
		t.<PERSON>("NewTitleService should not return nil")
	}

	if ts.GetDefaultTitle() != "GamePlan" {
		t.<PERSON>("Expected default title 'GamePlan', got '%s'", ts.GetDefaultTitle())
	}
}

func TestSetAndGetDefaultTitle(t *testing.T) {
	ts := NewTitleService()
	customTitle := "Custom Title"

	ts.SetDefaultTitle(customTitle)

	if ts.GetDefaultTitle() != customTitle {
		t.<PERSON>rf("Expected default title '%s', got '%s'", customTitle, ts.GetDefaultTitle())
	}
}

func TestGetTitleFromLocales(t *testing.T) {
	ts := NewTitleService()

	// Test with valid locales
	locales := map[string]string{
		"page_title": "Test Page Title",
		"other_key":  "Other Value",
	}

	title := ts.GetTitleFromLocales(locales, "page_title")
	if title != "Test Page Title" {
		t.<PERSON><PERSON>("Expected 'Test Page Title', got '%s'", title)
	}

	// Test with missing key - should return default
	title = ts.GetTitleFromLocales(locales, "missing_key")
	if title != "GamePlan" {
		t.Errorf("Expected default title 'GamePlan', got '%s'", title)
	}

	// Test with empty title - should return default
	locales["empty_title"] = ""
	title = ts.GetTitleFromLocales(locales, "empty_title")
	if title != "GamePlan" {
		t.Errorf("Expected default title 'GamePlan' for empty title, got '%s'", title)
	}
}

func TestGetTitleFromLocalesWithEmptyMap(t *testing.T) {
	ts := NewTitleService()

	// Test with empty locales map
	emptyLocales := map[string]string{}
	title := ts.GetTitleFromLocales(emptyLocales, "page_title")

	if title != "GamePlan" {
		t.Errorf("Expected default title 'GamePlan' for empty locales, got '%s'", title)
	}
}
