package titleservice

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
)

// TitleService provides centralized title management for the application
type TitleService struct {
	defaultTitle string
}

// NewTitleService creates a new title service with default fallback title
func NewTitleService() *TitleService {
	return &TitleService{
		defaultTitle: "Coachpad",
	}
}

// GetTitle resolves a title from locale files with fallback
// localeBase: path like "app/players/players" for loading locales
// titleKey: key to look up in the locales map (e.g., "page_title")
// lang: language code (e.g., "en", "fr")
func (ts *TitleService) GetTitle(localeBase, titleKey, lang string) string {
	localePath := fmt.Sprintf("./templates/%s.locales.json", localeBase)
	locales, err := i18n.LoadTemplateLocales(localePath, lang)
	if err != nil {
		return ts.defaultTitle
	}

	if title, exists := locales[titleKey]; exists && title != "" {
		return title
	}

	return ts.defaultTitle
}

// GetTitleFromLocales gets title from pre-loaded locales map
// This is used when locales are already loaded to avoid redundant file reads
func (ts *TitleService) GetTitleFromLocales(locales map[string]string, titleKey string) string {
	if title, exists := locales[titleKey]; exists && title != "" {
		return title
	}
	return ts.defaultTitle
}

// SetDefaultTitle allows customizing the fallback title
func (ts *TitleService) SetDefaultTitle(title string) {
	ts.defaultTitle = title
}

// GetTitleWithNotificationCount formats title with notification count
func (ts *TitleService) GetTitleWithNotificationCount(baseTitle string, unreadCount int64) string {
	if unreadCount == 0 {
		return baseTitle
	}

	var countStr string
	if unreadCount > 99 {
		countStr = "99+"
	} else {
		countStr = fmt.Sprintf("%d", unreadCount)
	}

	return fmt.Sprintf("%s (%s)", baseTitle, countStr)
}

// GetDefaultTitle returns the current default title
func (ts *TitleService) GetDefaultTitle() string {
	return ts.defaultTitle
}
